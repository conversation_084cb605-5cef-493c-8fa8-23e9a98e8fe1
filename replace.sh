#!/bin/bash

# 项目环境，new Guc的时候也要用到
__VUE_APP_ENV__=$VUE_APP_ENV
__VUE_APP_GLOB_APP_TITLE__=$VUE_APP_GLOB_APP_TITLE
# 手动传入guc地址
__VUE_APP_GUC_API_URL__=$VUE_APP_GUC_API_URL

__VUE_APP_GLOB_API_URL_PREFIX__=$VUE_APP_GLOB_API_URL_PREFIX

__VUE_APP_GLOB_API_URL__=$VUE_APP_GLOB_API_URL
__VUE_APP_PUBLIC_PATH__=$VUE_APP_PUBLIC_PATH

__VUE_APP_GLOB_UPLOAD_URL__=$VUE_APP_GLOB_UPLOAD_URL

__VUE_APP_GLOB_GUC_APP_ID__=$VUE_APP_GLOB_GUC_APP_ID

__VUE_APP_GLOB_CM_URL__=$VUE_APP_GLOB_CM_URL

__VUE_APP_GLOB_CM_APP_ID__=$VUE_APP_GLOB_CM_APP_ID

# WebSocket服务器地址
__VUE_APP_GLOB_SOCKET_URL__=$VUE_APP_GLOB_SOCKET_URL

# 训练视频地址
__VUE_APP_GLOB_TRAINING_URL__=$VUE_APP_GLOB_TRAINING_URL

# 文件预览服务地址
__VUE_APP_GLOB_PREVIEW_URL__=$VUE_APP_GLOB_PREVIEW_URL
# WebRtc地址
__VUE_APP_GLOB_WEBRTC_URL__=$VUE_APP_GLOB_WEBRTC_URL

# 左侧视频面板轮播间隔时间（分钟）
__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__=$VUE_APP_VIDEO_PANEL_ROTATION_MINUTES

echo  "正在替换环境变量..."
  echo  "__VUE_APP_ENV__:${__VUE_APP_ENV__}"
  sed -i "s|__VUE_APP_ENV__|${__VUE_APP_ENV__}|g" `grep __VUE_APP_ENV__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_APP_TITLE__:${__VUE_APP_GLOB_APP_TITLE__}"
  sed -i "s|__VUE_APP_GLOB_APP_TITLE__|${__VUE_APP_GLOB_APP_TITLE__}|g" `grep __VUE_APP_GLOB_APP_TITLE__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GUC_API_URL__:${__VUE_APP_GUC_API_URL__}"
  sed -i "s|__VUE_APP_GUC_API_URL__|${__VUE_APP_GUC_API_URL__}|g" `grep __VUE_APP_GUC_API_URL__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_API_URL_PREFIX__:${__VUE_APP_GLOB_API_URL_PREFIX__}"
  sed -i "s|__VUE_APP_GLOB_API_URL_PREFIX__|${__VUE_APP_GLOB_API_URL_PREFIX__}|g" `grep __VUE_APP_GLOB_API_URL_PREFIX__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_API_URL__:${__VUE_APP_GLOB_API_URL__}"
  sed -i "s|__VUE_APP_GLOB_API_URL__|${__VUE_APP_GLOB_API_URL__}|g" `grep __VUE_APP_GLOB_API_URL__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_UPLOAD_URL__:${__VUE_APP_GLOB_UPLOAD_URL__}"
  sed -i "s|__VUE_APP_GLOB_UPLOAD_URL__|${__VUE_APP_GLOB_UPLOAD_URL__}|g" `grep __VUE_APP_GLOB_UPLOAD_URL__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_GUC_APP_ID__:${__VUE_APP_GLOB_GUC_APP_ID__}"
  sed -i "s|__VUE_APP_GLOB_GUC_APP_ID__|${__VUE_APP_GLOB_GUC_APP_ID__}|g" `grep __VUE_APP_GLOB_GUC_APP_ID__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_CM_URL__:${__VUE_APP_GLOB_CM_URL__}"
  sed -i "s|__VUE_APP_GLOB_CM_URL__|${__VUE_APP_GLOB_CM_URL__}|g" `grep __VUE_APP_GLOB_CM_URL__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_GLOB_CM_APP_ID__:${__VUE_APP_GLOB_CM_APP_ID__}"
  sed -i "s|__VUE_APP_GLOB_CM_APP_ID__|${__VUE_APP_GLOB_CM_APP_ID__}|g" `grep __VUE_APP_GLOB_CM_APP_ID__ -rl /usr/share/nginx/html`
  echo  "__VUE_APP_PUBLIC_PATH__:${__VUE_APP_PUBLIC_PATH__}"
  sed -i "s|__VUE_APP_PUBLIC_PATH__|${__VUE_APP_PUBLIC_PATH__}|g" `grep __VUE_APP_PUBLIC_PATH__ -rl /usr/share/nginx/html`

  # WebSocket服务器地址替换
  echo  "__VUE_APP_GLOB_SOCKET_URL__:${__VUE_APP_GLOB_SOCKET_URL__}"
  sed -i "s|__VUE_APP_GLOB_SOCKET_URL__|${__VUE_APP_GLOB_SOCKET_URL__}|g" `grep __VUE_APP_GLOB_SOCKET_URL__ -rl /usr/share/nginx/html`

  # 培训视频地址
  echo  "__VUE_APP_GLOB_TRAINING_URL__:${__VUE_APP_GLOB_TRAINING_URL__}"
  sed -i "s|__VUE_APP_GLOB_TRAINING_URL__|${__VUE_APP_GLOB_TRAINING_URL__}|g" `grep __VUE_APP_GLOB_TRAINING_URL__ -rl /usr/share/nginx/html`

  # 文件预览服务地址
  echo  "__VUE_APP_GLOB_PREVIEW_URL__:${__VUE_APP_GLOB_PREVIEW_URL__}"
  sed -i "s|__VUE_APP_GLOB_PREVIEW_URL__|${__VUE_APP_GLOB_PREVIEW_URL__}|g" `grep __VUE_APP_GLOB_PREVIEW_URL__ -rl /usr/share/nginx/html`

  # WebRtc地址
  echo  "__VUE_APP_GLOB_WEBRTC_URL__:${__VUE_APP_GLOB_WEBRTC_URL__}"
  sed -i "s|__VUE_APP_GLOB_WEBRTC_URL__|${__VUE_APP_GLOB_WEBRTC_URL__}|g" `grep __VUE_APP_GLOB_WEBRTC_URL__ -rl /usr/share/nginx/html`

  # 左侧视频面板轮播间隔时间（分钟）
  echo  "__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__:${__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__}"
  sed -i "s|__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__|${__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__}|g" `grep __VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__ -rl /usr/share/nginx/html`

  # 前端项目部署地址
  echo  "前端访问地址：${__VUE_APP_PUBLIC_PATH__}"

echo  "替换环境变量完成"

nginx -g "daemon off;"
