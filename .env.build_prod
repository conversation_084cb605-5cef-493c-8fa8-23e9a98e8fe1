#
# START VUE CLI ENV CONFIG
#


# Whether to open mock
VUE_APP_USE_MOCK = true

# public path
VUE_APP_PUBLIC_PATH = /

# router base
VUE_APP_QIANKUN_ROUTER_BASE = /app/bsc
VUE_APP_ROUTER_BASE = /

# Delete console
VUE_APP_DROP_CONSOLE = true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VUE_APP_BUILD_COMPRESS = 'none'

# Basic interface address SPA
VUE_APP_GLOB_API_URL=/basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VUE_APP_GLOB_UPLOAD_URL=/upload

# Interface prefix
VUE_APP_GLOB_API_URL_PREFIX=

# Whether to enable image compression
VUE_APP_USE_IMAGEMIN= true

# use pwa
VUE_APP_USE_PWA = false

# Is it compatible with older browsers
VUE_APP_LEGACY = false
