{"sweetConfig": [{"packAgeFolderName": "bsc-template-crud", "sweetPackage": {"name": "@geega-cli-sweet/bsc-template-crud", "version": "0.0.25", "description": "增删改差套件", "main": "./src/index.js", "repository": {"type": "git", "url": ""}, "scripts": {}, "engines": {"node": ">=12.0"}, "files": ["src/*"], "license": "MIT", "keywords": ["geega-admin-template", "crud", "ant3", "geega-ui3"], "author": {"name": "hao.liu8", "email": "<EMAIL>"}, "contributors": [{"name": "Jie.Cai4", "email": "<EMAIL>"}, {"name": "e-Long<PERSON>Gong", "email": "<EMAIL>"}], "homepage": "http://npm.geega.com/-/web/detail/@geega-cli-sweet/bsc-template-crud", "bugs": {"email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "publishConfig": {"registry": "http://npm.geega.com", "access": "public"}, "sweetDevDependencies": {"zhName": "精减版本Ant3微服务子应用", "enName": "qiankun_micro_thin_ant3", "type": "web", "gitRepository": "https://git.geega.com/geega/front-end/geega-admin/geega-admin-template#qiankun_micro_thin_ant3", "description": "技术栈vue3+ant3+geegaui3 基于geega-template-admin开发"}, "sweetLibDependencies": ["Vue3", "Ant Design Vue3", "Geega UI3"], "sourceCodePathMapRule": [{"source": "src/api/demo/model/tableModel.ts", "traget": "src/api/demo/model/tableModel.ts"}, {"source": "src/api/demo/table.ts", "traget": "src/api/demo/table.ts"}, {"source": "src/router/menus/modules/projectBaseTemplate.ts", "traget": "src/router/menus/modules/projectBaseTemplate.ts"}, {"source": "src/router/routes/modules/projectBaseTemplate.ts", "traget": "src/router/routes/modules/projectBaseTemplate.ts"}, {"source": "src/views/demo/geega", "traget": "src/views/demo/geega"}]}, "readme": "build/sweet-template/READMECRUD.md", "files": ["src/api/demo/model/tableModel.ts", "src/api/demo/table.ts", "src/router/menus/modules/projectBaseTemplate.ts", "src/router/routes/modules/projectBaseTemplate.ts", "src/views/demo/geega"]}, {"packAgeFolderName": "bsc-template-home", "sweetPackage": {"name": "@geega-cli-sweet/bsc-template-home", "version": "0.0.4", "description": "首页模版", "main": "./src/index.js", "repository": {"type": "git", "url": ""}, "scripts": {}, "engines": {"node": ">=12.0"}, "files": ["src/*"], "license": "MIT", "keywords": ["geega-admin-home", "crud", "ant3", "geega-ui3"], "author": {"name": "hao.liu8", "email": "<EMAIL>"}, "contributors": [{"name": "Jie.Cai4", "email": "<EMAIL>"}, {"name": "e-Long<PERSON>Gong", "email": "<EMAIL>"}], "homepage": "http://npm.geega.com/-/web/detail/@geega-cli-sweet/bsc-template-home", "bugs": {"email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "publishConfig": {"registry": "http://npm.geega.com", "access": "public"}, "sweetDevDependencies": {"zhName": "精减版本Ant3微服务子应用", "enName": "qiankun_micro_thin_ant3", "type": "web", "gitRepository": "https://git.geega.com/geega/front-end/geega-admin/geega-admin-template#qiankun_micro_thin_ant3", "description": "技术栈vue3+ant3+geegaui3 基于geega-template-admin开发"}, "sweetLibDependencies": ["Vue3", "Ant Design Vue3", "Geega UI3"], "sourceCodePathMapRule": [{"source": "src/api/demo/model/tableModel.ts", "traget": "src/api/demo/model/tableModel.ts"}, {"source": "src/router/routes/modules/home.ts", "traget": "src/router/routes/modules/home.ts"}, {"source": "src/router/menus/modules/home.ts", "traget": "src/router/menus/modules/home.ts"}, {"source": "src/router/menus/modules/dashboard.ts", "traget": "src/router/menus/modules/dashboard.ts"}, {"source": "src/views/dashboard", "traget": "src/views/dashboard"}]}, "readme": "build/sweet-template/READMEINDEX.md", "files": ["src/api/demo/model/tableModel.ts", "src/router/routes/modules/home.ts", "src/router/menus/modules/home.ts", "src/router/menus/modules/dashboard.ts", "src/views/dashboard"]}]}