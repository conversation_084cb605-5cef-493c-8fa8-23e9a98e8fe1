import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/stream': {
        target: 'http://159.75.85.63:5680',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/stream/, '')
      }
    }
  },
  resolve: {
    alias: {
      '/@': resolve(__dirname, 'src')
    }
  }
})
