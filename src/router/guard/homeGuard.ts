import projectSetting from '/@/settings/projectSetting';
import { Router } from 'vue-router';
import { permissionStore } from '/@/store/modules/permission';
import { PermissionModeEnum } from '/@/enums/appEnum';
import { Menu } from '/@/router/types';
import { PageEnum } from '/@/enums/pageEnum';

function deepMenuList(menuList: Menu[], cb: (menu: Menu) => void) {
  menuList.forEach((menu) => {
    if (menu.children && menu.children.length > 0) {
      deepMenuList(menu.children, cb);
    } else {
      cb(menu);
    }
  });
}
/**
 * @description 创建首页守卫
 * @param router 路由实例
 */
export function createHomeGuard(router: Router) {
  router.beforeResolve(async (to) => {
    // 开启后端路由后，若没有配置首页权限，那么进入首页会404
    if (
      projectSetting.permissionMode === PermissionModeEnum.BACK &&
      to.path === PageEnum.BASE_HOME
    ) {
      const paths: string[] = [];
      deepMenuList(permissionStore.getBackMenuListState, (menu) => {
        paths.push(menu.path);
      });
      // 如果配置了页面权限，但未配置首页权限，则跳转到第一个页面
      if (paths.length > 0 && !paths.includes(to.path)) {
        return {
          path: paths[0],
        };
      }
    }
    const dashboardUrl = '/dashboard/index'; // 大屏路由
    // 新增：根据路由判断是否为工位端，并设置状态
    const stationRoutes = [
      '/cddc/home',
      '/cddc/report',
      '/cddc/training',
      '/cddc/skill-assessment',
      '/cddc/skill-competition',
      dashboardUrl,
    ];
    const isStationRoute = stationRoutes.includes(to.path);
    // 如果是工位端相关路由，则 isNormalUserState 为 true，否则为 false
    permissionStore.setIsNormalUserStateAction(isStationRoute);
    // 如果是大屏相关路由，则 isDashboardState 为 true，否则为 false
    permissionStore.setIsDashboardStateAction(to.path.includes(dashboardUrl));
    return true;
  });
}
