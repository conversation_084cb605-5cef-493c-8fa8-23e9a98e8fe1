import type { Router, RouteRecordRaw } from 'vue-router';

import { permissionStore } from '/@/store/modules/permission';

import { PageEnum } from '/@/enums/pageEnum';
import { userStore } from '/@/store/modules/user';

import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

import { useGlobSetting } from '/@/hooks/setting';

import geegaguc from '/@/plugins/geegaguc';

const globSetting = useGlobSetting();

const LOGIN_PATH = PageEnum.BASE_LOGIN;

const whitePathList: PageEnum[] = [LOGIN_PATH];

export function createPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {

    // Jump to the 404 page after processing the login
    if (from.path === LOGIN_PATH && to.name === PAGE_NOT_FOUND_ROUTE.name) {
      next();
      return;
    }

    // Whitelist can be directly entered
    if (whitePathList.includes(to.path as PageEnum)) {
      if(globSetting.cmApiUrl && globSetting.cmAppId)
      {
          window.__ClientTemplateMonitor__.setPerformance({})
      }
      next();
      return;
    }

    if (permissionStore.getIsDynamicAddedRouteState) {
      if(globSetting.cmApiUrl && globSetting.cmAppId)
      {
          window.__ClientTemplateMonitor__.setPerformance({})
      }
      next();
      return;
    }

    // keycloak ligin
    if(globSetting.enableGuc){
      await userStore.keycloakLogin();

      const token = await geegaguc.getToken();
      if (!token) {
        // You can access without permission. You need to set the routing meta.ignoreAuth to true
        if (to.meta.ignoreAuth) {
          next();
          return;
        }
      }
    }

    // add async routes
    const routes = await permissionStore.buildRoutesAction();
    routes.forEach((route) => {
      router.addRoute((route as unknown) as RouteRecordRaw);
    });
    const redirectPath = (from.query.redirect || to.path) as string;
    const redirect = decodeURIComponent(redirectPath);
    const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };
    permissionStore.commitDynamicAddedRouteState(true);
    next(nextData);
  });
  router.afterEach((to) => {
    // /home你的重定向默认页
    // if(window.__POWERED_BY_WUJIE__){
    //   const props = window.$wujie?.props;
    //   const activePath  =  to.path.startsWith('/gwj/vuewjdemo')?to.path:'/gwj/vuewjdemo' + to.path
    //   props?.activeMenuByMenuPathOrMenuID(activePath,null,true)
    // }

    // if(window.__MICRO_APP_ENVIRONMENT__){
    //   console.log('***vuewjdemo afterEach****',to)
    //   const globalData = window.microApp.getGlobalData();
    //   const activePath  =  to.path.startsWith('/micro/vuewjdemo')?to.path:'/micro/vuewjdemo' + to.path
    //   globalData?.activeMenuByMenuPathOrMenuID(activePath,null,true)
    // }
  });
}
