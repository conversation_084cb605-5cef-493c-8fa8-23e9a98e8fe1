import type { MenuModule } from '/@/router/types';

const builtinMenu: MenuModule[] = [
  {
    orderNo: 1,
    menu: {
      name: '首页',
      path: '/home',
      icon: 'g-ic-line-information-cws-management',
      meta: {
        icon: 'g-ic-line-information-cws-management',
        title: '首页',
      },
    },
  },
  // {
  //   orderNo: 2,
  //   menu: {
  //     name: '训练报告',
  //     path: '/report',
  //     icon: 'g-ic-line-text',
  //     meta: {
  //       icon: 'g-ic-line-text',
  //       title: '训练报告',
  //     },
  //   },
  // },
  {
    orderNo: 3,
    menu: {
      name: '开始训练',
      path: '/training',
      icon: 'g-ic-line-user5',
      meta: {
        icon: 'g-ic-line-user5',
        title: '开始训练',
      },
    },
  },
];

export default builtinMenu;
