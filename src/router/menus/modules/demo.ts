import type { MenuModule } from '/@/router/types';
import { t } from '/@/hooks/web/useI18n';

const adMenu: MenuModule = {
  orderNo: 3,
  menu: {
    name: t('routes.ad.first'),
    path: '/demo',
    meta: {
      icon: 'ion:scale-outline',
      title: t('routes.ad.firstTitle'),
    },
    children: [
      {
        path: 'list',
        name: t('routes.ad.secondary'),
        meta: {
          title:  t('routes.ad.secondaryTitle'),
        },
      },
      {
        path: 'virtual',
        name: t('routes.ad.virtual'),
        meta: {
          title:  t('routes.ad.virtualTitle'),
        },
      },
      {
        path: 'anttable',
        name: t('routes.ad.anttable'),
        meta: {
          title:  t('routes.ad.anttable'),
        },
      }
    ],
  },
};
export default adMenu;
