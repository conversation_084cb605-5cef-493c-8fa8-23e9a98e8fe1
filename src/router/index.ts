import type { RouteRecordRaw } from 'vue-router';
import type { App } from 'vue';

import { createRouter, createWebHashHistory } from 'vue-router';
import { basicRoutes } from './routes';
import { REDIRECT_NAME } from './constant';

const WHITE_NAME_LIST = [REDIRECT_NAME];

// 子应用推荐hash 模式， history也是支持的【如果已有业务依应用的history，可以使用history方式，例如GUC可以继续使用history】
export const history = createWebHashHistory(
  window.__MICRO_APP_BASE_ROUTE__
    ? window.__MICRO_APP_BASE_ROUTE__
    : window.__POWERED_BY_WUJIE__
    ? process.env.VUE_APP_ROUTER_BASE
    : process.env.VUE_APP_ROUTER_BASE
);


// app router
const router = createRouter({
  history,
  routes: (basicRoutes as unknown) as RouteRecordRaw[],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// reset router
export function resetRouter() {
  router.getRoutes().forEach((route) => {
    const { name } = route;
    if (name && !WHITE_NAME_LIST.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name);
    }
  });
}

// config router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
