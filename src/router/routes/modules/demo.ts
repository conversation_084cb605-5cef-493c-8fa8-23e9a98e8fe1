import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

import { t } from '/@/hooks/web/useI18n';

const adRoutes: AppRouteModule = {
  path: '/demo',
  name: 'DEMO',
  component: LAYOUT,
  redirect: '/demo/list',
  meta: {
    icon: 'ion:lightbulb',
    title: t('routes.ad.firstTabTitle'),
  },
  children: [
    {
      path: 'list',
      name: 'list',
      component: () => import('/@/views/demo/list/index.vue'),
      meta: {
        title: t('routes.ad.secondaryTabTitle'),
      },
    },
    {
      path: 'virtual',
      name: 'virtual',
      component: () => import('/@/views/demo/virtual/index.vue'),
      meta: {
        title: t('routes.ad.virtualTab'),
      },
    },
    {
      path: 'anttable',
      name: 'anttable',
      component: () => import('/@/views/demo/anttable/index.vue'),
      meta: {
        title: t('routes.ad.anttable'),
      },
    }

  ],
};

export default adRoutes;
