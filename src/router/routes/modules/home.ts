import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const dashboard: AppRouteModule = {
  path: '/cddc/home',
  name: t('routes.dashboard.welcome'),
  component: LAYOUT,
  redirect: '/cddc/home/<USER>',
  meta: {
    icon: 'ion:home-outline',
    title: t('routes.dashboard.welcome'),
  },
  children: [
    {
      path: 'welcome',
      name: 'Welcome',
      component: () => import('/@/views/cddc/dashboard/welcome/index.vue'),
      meta: {
        title: t('routes.dashboard.welcome'),
        affix: true,
        icon: 'bx:bx-home',
      },
    },
  ],
};

export default dashboard;
