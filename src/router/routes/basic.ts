import type { AppRouteRecordRaw } from '/@/router/types';
import { LAYOUT, EXCEPTION_COMPONENT, REDIRECT_NAME } from '/@/router/constant';

// 404 on a page
export const PAGE_NOT_FOUND_ROUTE: AppRouteRecordRaw = {
  path: '/:path(.*)*',
  name: 'ErrorPage',
  component: LAYOUT,
  meta: {
    title: 'ErrorPage',
    hideBreadcrumb: true,
  },
  children: [
    {
      path: '/:path(.*)*',
      name: 'ErrorPage',
      component: EXCEPTION_COMPONENT,
      meta: {
        title: 'ErrorPage',
        hideBreadcrumb: true,
      },
    },
  ],
};

export const REDIRECT_ROUTE: AppRouteRecordRaw = {
  path: '/redirect',
  name: REDIRECT_NAME,
  component: LAYOUT,
  meta: {
    title: REDIRECT_NAME,
    hideBreadcrumb: true,
  },
  children: [
    {
      path: '/redirect/:path(.*)',
      name: REDIRECT_NAME,
      component: () => import('/@/views/sys/redirect/index.vue'),
      meta: {
        title: REDIRECT_NAME,
        hideBreadcrumb: true,
      },
    },
  ],
};

export const LOGIN_ROUTE: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/sys/login/index.vue'),
  meta: {
    title: '登录',
    hideBreadcrumb: true,
    ignoreAuth: true,
  },
};

// Built-in routes that are always available
export const BUILT_IN_ROUTE: AppRouteRecordRaw[] = [
  {
    path: '/cddc/home',
    name: 'Home',
    component: LAYOUT,
    redirect: '/cddc/home/<USER>',
    meta: {
      title: '首页',
      icon: 'ion:home-outline',
    },
    children: [
      {
        path: 'index',
        name: 'HomeIndex',
        component: () => import('/@/views/cddc/home/<USER>'),
        meta: {
          title: '首页',
        },
      },
    ],
  },
  {
    path: '/cddc/report',
    name: 'Report',
    component: LAYOUT,
    redirect: '/cddc/report/index',
    meta: {
      title: '训练报告',
      icon: 'ion:document-text-outline',
    },
    children: [
      {
        path: 'index',
        name: 'ReportIndex',
        component: () => import('/@/views/cddc/report/index.vue'),
        meta: {
          title: '训练报告',
        },
      },
      {
        path: '/cddc/ranking',
        name: 'ReportRanking',
        component: () => import('/@/views/cddc/report/ranking/index.vue'),
        meta: {
          title: '比赛榜单',
        },
      },
    ],
  },
  {
    path: '/cddc/training',
    name: 'Training',
    component: LAYOUT,
    redirect: '/cddc/training/index',
    meta: {
      title: '开始训练',
      icon: 'ion:school-outline',
    },
    children: [
      {
        path: 'index',
        name: 'TrainingIndex',
        component: () => import('/@/views/cddc/training/index.vue'),
        meta: {
          title: '开始训练',
        },
      },
    ],
  },
  {
    path: '/cddc/skill-assessment',
    name: 'SkillAssessment',
    component: LAYOUT,
    redirect: '/cddc/skill-assessment/index',
    meta: {
      title: '技能考核',
      icon: 'ion:bar-chart-outline',
    },
    children: [
      {
        path: 'index',
        name: 'SkillAssessmentIndex',
        component: () => import('/@/views/cddc/skill-assessment/index.vue'),
        meta: {
          title: '技能考核',
        },
      },
    ],
  },
  {
    path: '/cddc/skill-competition',
    name: 'SkillCompetition',
    component: LAYOUT,
    redirect: '/cddc/skill-competition/index',
    meta: {
      title: '技能比赛',
      icon: 'ion:bar-chart-outline',
    },
    children: [
      {
        path: 'index',
        name: 'SkillCompetitionIndex',
        component: () => import('/@/views/cddc/skill-competition/index.vue'),
        meta: {
          title: '技能比赛',
        },
      },
    ],
  },
];
