import type { AppRouteRecordRaw, Menu } from '/@/router/types';

import store from '/@/store';
import { toRaw } from 'vue';
import { VuexModule, Mutation, Module, getModule, Action } from 'vuex-module-decorators';

import { hotModuleUnregisterModule } from '/@/utils/helper/vuexHelper';

import { PermissionModeEnum } from '/@/enums/appEnum';

import { appStore } from '/@/store/modules/app';
import { userStore } from '/@/store/modules/user';
import projectSetting from '/@/settings/projectSetting';

import { asyncRoutes } from '/@/router/routes';
import { BUILT_IN_ROUTE, LOGIN_ROUTE, PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { transformObjToRoute, flatMultiLevelRoutes } from '/@/router/helper/routeHelper';
import { transformRouteToMenu } from '/@/router/helper/menuHelper';

import { filter } from '/@/utils/helper/treeHelper';

import { getMenuListById } from '/@/api/sys/menu';
import { getPermCodeByUserId } from '/@/api/sys/user';

import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';

import { useGlobSetting } from '/@/hooks/setting';

import { isRunHostEnv } from '/@/utils/micro';

const globSetting = useGlobSetting();
const appId: any = globSetting.appId;

const NAME = 'app-permission';
hotModuleUnregisterModule(NAME);
@Module({ dynamic: true, namespaced: true, store, name: NAME })
class Permission extends VuexModule {
  // Permission code list
  private permCodeListState: string[] = [];

  // Whether the route has been dynamically added
  private isDynamicAddedRouteState = false;

  // To trigger a menu update
  private lastBuildMenuTimeState = 0;

  // Backstage menu list
  private backMenuListState: Menu[] = [];

  // 是否为工位端
  private isNormalUserState = false;

  // 是否为大屏
  private isDashboardState = false;

  get getPermCodeListState() {
    return this.permCodeListState;
  }

  get getBackMenuListState() {
    return this.backMenuListState;
  }

  get getLastBuildMenuTimeState() {
    return this.lastBuildMenuTimeState;
  }

  get getIsDynamicAddedRouteState() {
    return this.isDynamicAddedRouteState;
  }

  get getIsNormalUserState() {
    return this.isNormalUserState;
  }

  get getIsDashboardState() {
    return this.isDashboardState;
  }

  @Mutation
  commitIsNormalUserState(state: boolean): void {
    this.isNormalUserState = state;
  }

  @Mutation
  commitIsDashboardState(state: boolean): void {
    this.isDashboardState = state;
  }

  @Mutation
  commitPermCodeListState(codeList: string[]): void {
    this.permCodeListState = codeList;
  }

  @Mutation
  commitBackMenuListState(list: Menu[]): void {
    this.backMenuListState = list;
  }

  @Mutation
  commitLastBuildMenuTimeState(): void {
    this.lastBuildMenuTimeState = new Date().getTime();
  }

  @Mutation
  commitDynamicAddedRouteState(added: boolean): void {
    this.isDynamicAddedRouteState = added;
  }

  @Mutation
  commitResetState(): void {
    this.isDynamicAddedRouteState = false;
    this.permCodeListState = [];
    this.backMenuListState = [];
    this.lastBuildMenuTimeState = 0;
    this.isNormalUserState = false;
    this.isDashboardState = false;
  }

  @Action
  async setIsNormalUserStateAction(state: boolean) {
    this.commitIsNormalUserState(state);
  }

  @Action
  async setIsDashboardStateAction(state: boolean) {
    this.commitIsDashboardState(state);
  }

  @Action
  async changePermissionCode() {
    const codeList = await getPermCodeByUserId({ appId });
    this.commitPermCodeListState(codeList);
  }

  @Action
  async buildRoutesAction(id?: number | string): Promise<AppRouteRecordRaw[]> {
    const { t } = useI18n();
    let routes: AppRouteRecordRaw[] = [];
    const roleList = toRaw(userStore.getRoleListState);
    const { permissionMode = projectSetting.permissionMode } = appStore.getProjectConfig;

    // 接入guc路由权限处理
    if (globSetting.enableGuc) {
      // role permissions
      if (permissionMode === PermissionModeEnum.ROLE) {
        const routeFilter = (route: AppRouteRecordRaw) => {
          const { meta } = route;
          const { roles } = meta || {};
          if (!roles) return true;
          return roleList.some((role) => roles.includes(role));
        };
        routes = filter(asyncRoutes, routeFilter);
        routes = routes.filter(routeFilter);
        // Convert multi-level routing to level 2 routing
        routes = flatMultiLevelRoutes(routes);
        //  If you are sure that you do not need to do background dynamic permissions, please comment the entire judgment below
      } else if (permissionMode === PermissionModeEnum.BACK) {
        const { createMessage } = useMessage();

        if (isRunHostEnv()) {
          createMessage.loading({
            content: t('sys.app.menuLoading'),
            duration: 1,
          });
        }

        // !Simulate to obtain permission codes from the background,
        // this function may only need to be executed once, and the actual project can be put at the right time by itself
        try {
          this.changePermissionCode();
        } catch (error) {}

        // 获取菜单列表
        let routeList = (await getMenuListById({ appId })) as AppRouteRecordRaw[];

        // 如果返回空数组，说明是普通员工，不赋予额外菜单权限
        if (!routeList || routeList.length === 0) {
          routeList = BUILT_IN_ROUTE;
          this.commitBackMenuListState([]);
        } else {
          // 是管理员，处理菜单权限
          // Dynamically introduce components
          routeList = transformObjToRoute(routeList);
          //  Background routing to menu structure
          const backMenuList = transformRouteToMenu(routeList);
          this.commitBackMenuListState(backMenuList);
          // 把报告菜单添加到菜单列表
          // const reportMenu = BUILT_IN_ROUTE.find((item) => item.path === '/cddc/report');
          // if (reportMenu) {
          //   routeList = [reportMenu, ...routeList];
          // }
        }

        routeList = flatMultiLevelRoutes(routeList);
        routes = [PAGE_NOT_FOUND_ROUTE, LOGIN_ROUTE, ...routeList];
      }
    } else {
      // 不接入guc路由权限处理，仅ROLE模式
      routes = flatMultiLevelRoutes(asyncRoutes);
    }
    return routes;
  }
}
export const permissionStore = getModule<Permission>(Permission);
