import store from '/@/store/index';
import { VuexModule, Module, getModule, Mutation, Action } from 'vuex-module-decorators';
import { hotModuleUnregisterModule } from '/@/utils/helper/vuexHelper';
import { useMessage } from '/@/hooks/web/useMessage';
import { Persistent } from '/@/utils/cache/persistent';
import { useI18n } from '/@/hooks/web/useI18n';
import geegaguc from '/@/plugins/geegaguc';
import { RoleEnum } from '/@/enums/roleEnum';
import {
  ROLES_KEY,
  USER_INFO_KEY,
  GEEGAGUC_TOKEN_KEY,
  GEEGAGUC_REFRESH_TOKEN_KEY,
} from '/@/enums/cacheEnum';
import { isRunHostEnv } from '/@/utils/micro';
import { V1LocationHomeLoginSync } from '/@/api/cddc.req';
import { createLocalStorage } from '/@/utils/cache';
import { useGo } from '/@/hooks/web/usePage';
import { PageEnum } from '/@/enums/pageEnum';

const { setLocal, getLocal, setSession, clearLocal, clearSession } = Persistent;

export type UserInfo = Omit<any, 'roles'>;
const go = useGo();
const NAME = 'user';
hotModuleUnregisterModule(NAME);

function getCache<T>(key: any) {
  const fn = getLocal;
  return fn(key) as T;
}

function setCache(USER_INFO_KEY: any, info: any) {
  if (!info) return;
  // const fn = permissionCacheType === CacheTypeEnum.LOCAL ? setLocal : setSession;
  setLocal(USER_INFO_KEY, info, true);
  // TODO
  setSession(USER_INFO_KEY, info, true);
}

@Module({ namespaced: true, name: NAME, dynamic: true, store })
class User extends VuexModule {
  // keylocak token
  private keycloakToken = '';
  private keycloakRefreshToken = '';

  // roleList
  private roleListState: RoleEnum[] = [];

  private userFullInfo: any;

  get getUserFullInfo() {
    return this.userFullInfo;
  }

  get getUserInfoState(): UserInfo {
    return this.userFullInfo || getCache<UserInfo>(USER_INFO_KEY) || {};
  }

  get getKeycloakRefreshToken(): string {
    return this.keycloakRefreshToken || getCache<string>(GEEGAGUC_REFRESH_TOKEN_KEY);
  }

  get getRoleListState(): RoleEnum[] {
    return this.roleListState.length > 0
      ? this.roleListState
      : getCache<RoleEnum[]>(ROLES_KEY) || [];
  }

  @Mutation
  commitResetState(): void {
    this.keycloakToken = '';
    this.keycloakRefreshToken = '';
    this.roleListState = [];
  }

  @Mutation
  commitRoleListState(roleList: RoleEnum[]): void {
    this.roleListState = roleList;
    setCache(ROLES_KEY, roleList);
  }

  @Mutation
  commitKeycloakTokenState(token: string): void {
    this.keycloakToken = token;
    setCache(GEEGAGUC_TOKEN_KEY, token);
  }

  @Mutation
  commitKeycloakRefreshToken(info: string): void {
    this.keycloakRefreshToken = info;
    setCache(GEEGAGUC_REFRESH_TOKEN_KEY, info);
  }

  @Mutation
  commitUserFullInfo(info): void {
    this.userFullInfo = info;
  }

  @Action
  async commitUserInfo(gucInstance: any) {
    const token = await gucInstance.getToken();
    this.commitKeycloakTokenState(token);
    const userInfo = await gucInstance.getUserInfo();
    this.commitUserFullInfo(userInfo);
    setCache(USER_INFO_KEY, userInfo);
  }

  /**
   * @description keycloak 登录
   * @memberof User
   */
  @Action
  async keycloakLogin() {
    if (isRunHostEnv()) {
      const authenticated = await geegaguc.init(false);
      if (!authenticated) {
        //跳登录页
        go(PageEnum.BASE_LOGIN);
      } else {
        await this.commitUserInfo(geegaguc);
      }
    } else {
      await this.commitUserInfo(geegaguc);
    }
  }

  /**
   * 自定义登录成功后设置guctoken
   */
  @Action
  async setLoginToken(gucInfo) {
    try {
      await geegaguc.init(false); // 初始化guc
      const nowDataTime = new Date().getTime();
      const tokenExpireTime = nowDataTime + gucInfo.expiresIn * 1000;
      await geegaguc.storeCredentialsToHub(
        gucInfo.token,
        gucInfo.refreshToken,
        gucInfo.expiresIn,
        tokenExpireTime || ''
      );
      await this.commitUserInfo(geegaguc);
    } catch (error) {
      console.log('guc error', error);
    }
  }

  /**
   * @description 清除用户数据
   */
  @Action
  clearData() {
    const ls = createLocalStorage();
    this.commitResetState();
    clearLocal();
    clearSession();
    ls.remove('currProjectId');
    ls.remove('userId');
    ls.remove('locationId');
    ls.remove('currRecordId');
    ls.remove('workStationInfo');
    geegaguc.logout();
  }

  /**
   * @description keycloak 退出
   * @memberof User
   */
  @Action
  async gucLogout(msg?: string) {
    const { createConfirm } = useMessage();
    const { t } = useI18n();
    return new Promise((resolve) => {
      createConfirm({
        type: 'warning',
        title: t('sys.app.logoutTip'),
        content: msg || t('sys.app.logoutMessage'),
        onOk: async () => {
          this.clearData();
          resolve && resolve({ res: true });
        },
      });
    });
  }
  /**
   * @description 记录用户登录时间
   * @memberof User
   */
  @Action
  async recordLoginTime() {
    if (this.keycloakToken) {
      await V1LocationHomeLoginSync();
    }
  }
}

export const userStore = getModule<User>(User);
