import { defineStore } from 'pinia';
import { V1ManageTranProjectPagePost, V1ManageTranProjectDeleteIdDelete } from '@/api/cddc.req';

interface QuestionState {
  loading: boolean;
}

export const useQuestionStore = defineStore('question', {
  state: (): QuestionState => ({
    loading: false,
  }),

  actions: {
    // 获取已确认列表
    async getConfirmedList(params: any) {
      this.loading = true;
      try {
        return await V1ManageTranProjectPagePost({
          ...params,
          status: 'confirmed',
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取待确认列表
    async getPendingList(params: any) {
      this.loading = true;
      try {
        return await V1ManageTranProjectPagePost({
          ...params,
          status: 'pending',
        });
      } finally {
        this.loading = false;
      }
    },

    // 确认题目
    async confirmQuestion(id: string) {
      this.loading = true;
      try {
        // TODO: 调用确认接口
        await Promise.resolve();
      } finally {
        this.loading = false;
      }
    },

    // 删除题目
    async deleteQuestion(id: string) {
      this.loading = true;
      try {
        await V1ManageTranProjectDeleteIdDelete({
          id,
        });
      } finally {
        this.loading = false;
      }
    },
  },
});
