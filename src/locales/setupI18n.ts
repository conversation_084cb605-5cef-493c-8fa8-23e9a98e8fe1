import type { App } from 'vue';
import type { I18n, I18nOptions } from 'vue-i18n';

import { createI18n } from 'vue-i18n';

import { localeStore } from '/@/store/modules/locale';
import { localeSetting } from '/@/settings/localeSetting';

/**
 * 国际化修改同步，适用于@geega-ui-plus/geega-ui
 */
import en from '/@/locales/lang/en';
import zh_CN from '/@/locales/lang/zh_CN';

import { useLocale } from '/@/locales/useLocale';
const { getCurrentLocale } = useLocale();


const { fallback, availableLocales } = localeSetting;

const initI18n = function () {
  const options = createI18nOptions();
  return createI18n(options) as I18n;
};

const i18n = initI18n();

function createI18nOptions(): I18nOptions {
  let locale = localeStore.getLocale;
  const lang = getCurrentLocale();
  if(lang){
    locale = lang;
  }
  let message = zh_CN.message;
  //const defaultLocal = await import(`./lang/${locale}.ts`);
  if (locale == 'en') {
    message = en.message;
  }

  return {
    legacy: false,
    locale,
    fallbackLocale: fallback,
    messages: {
      [locale]: message,
    },
    availableLocales: availableLocales,
    sync: true, //If you don’t want to inherit locale from global scope, you need to set sync of i18n component option to false.
    silentTranslationWarn: true, // true - warning off
    missingWarn: false,
    silentFallbackWarn: true,
  };
}

export { i18n };
// setup i18n instance with glob
export function setupI18n(app: App) {
  app.use(i18n);
}
