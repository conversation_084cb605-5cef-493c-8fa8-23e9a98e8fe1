/**
 * Multi-language related operations
 */
import type { LocaleType } from '/#/config';

import { i18n } from './setupI18n';
import { localeStore } from '/@/store/modules/locale';
import { unref, computed } from 'vue';
import { isRunMicroEnv } from '/@/utils/micro'
import { getLangByHrefParams,changeNavUrl } from '/@/utils/urlUtil';

interface LangModule {
  message: Recordable;
  dateLocale: Recordable;
  dateLocaleName: string;
}

const loadLocalePool: LocaleType[] = [];

function setI18nLanguage(locale: LocaleType) {
  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale;
  } else {
    (i18n.global.locale as any).value = locale;
  }
  localeStore.setLocaleInfo({ locale });
  document.querySelector('html')?.setAttribute('lang', locale);
}

export function useLocale() {
  const getLocale = computed(() => localeStore.getLocale);
  const getShowLocalePicker = computed(() => localeStore.getShowPicker);

  const getAntdLocale = computed(() => {
    return i18n.global.getLocaleMessage(unref(getLocale))?.antdLocale;
  });

  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  async function changeLocale(locale: LocaleType) {
    const globalI18n = i18n.global;
    const currentLocale = unref(globalI18n.locale);
    if (currentLocale === locale) return locale;

    if (loadLocalePool.includes(locale)) {
      setI18nLanguage(locale);
      return locale;
    }
    const langModule = ((await import(`./lang/${locale}.ts`)) as any).default as LangModule;
    if (!langModule) return;

    const { message } = langModule;

    globalI18n.setLocaleMessage(locale, message);
    loadLocalePool.push(locale);

    setI18nLanguage(locale);

    // 如果 url 存在code 修改URL
    changeNavUrl(locale)

    return locale;
  }

// "zh_CN" "en"
 function getCurrentLocale() {
  const mapGeegaLang = {
    "zh-CN":"zh_CN",
    "en-US":"en"
  }
  if(isRunMicroEnv()){
    const momProjectConfig = localStorage.getItem("MOM-MAIN-WEBPROJ__CFG__KEY__")
    if(momProjectConfig){
      const local =  JSON.parse(momProjectConfig)?.value?.local; // zh_CN-->zh-CN en--->en-US
      return  mapGeegaLang[local] || local;
    }
  }
  const hrefLang = getLangByHrefParams();
  return mapGeegaLang[hrefLang] || hrefLang || unref(getLocale);
  }


  return {
    getLocale,
    getShowLocalePicker,
    changeLocale,
    getCurrentLocale,
    getAntdLocale,
  };
}
