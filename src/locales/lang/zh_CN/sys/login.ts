export default {
  backSignIn: '返回',
  signInFormTitle: '登录',
  mobileSignInFormTitle: '手机登录',
  qrSignInFormTitle: '二维码登录',
  signUpFormTitle: '注册',
  forgetFormTitle: '重置密码',

  signInTitle: '开箱即用的中后台管理系统',
  signInDesc: '输入您的个人详细信息开始使用！',
  policy: '我同意xxx隐私政策',
  scanSign: `扫码后点击"确认"，即可完成登录`,

  loginButton: '登录',
  registerButton: '注册',
  rememberMe: '记住我',
  forgetPassword: '忘记密码?',
  otherSignIn: '其他登录方式',

  // notify
  loginSuccessTitle: '登录成功',
  loginSuccessDesc: '欢迎回来',

  // placeholder
  accountPlaceholder: '请输入账号',
  passwordPlaceholder: '请输入密码',
  smsPlaceholder: '请输入验证码',
  mobilePlaceholder: '请输入手机号码',
  policyPlaceholder: '勾选后才能注册',
  diffPwd: '两次输入密码不一致',

  userName: '账号',
  password: '密码',
  confirmPassword: '确认密码',
  email: '邮箱',
  smsCode: '短信验证码',
  mobile: '手机号码',
};
