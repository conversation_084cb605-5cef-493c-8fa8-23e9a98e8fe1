export default {
  comp: 'Component',
  basic: 'Basic',
  transition: 'Animation',
  countTo: 'Count To',

  scroll: 'Scroll',
  scrollBasic: 'Basic',
  scrollAction: 'Scroll Function',
  virtualScroll: 'Virtual Scroll',

  tree: 'Tree',

  treeBasic: 'Basic',
  editTree: 'Searchable/toolbar',
  actionTree: 'Function operation',

  modal: 'Modal',
  drawer: 'Drawer',
  desc: 'Desc',

  lazy: 'Lazy',
  lazyBasic: 'Basic',
  lazyTransition: 'Animation',

  verify: 'Verify',
  verifyDrag: 'Drag ',
  verifyRotate: 'Picture Restore',

  qrcode: 'QR code',
  strength: 'Password strength',
  upload: 'Upload',

  loading: 'Loading',

  time: 'Time',
};
