export default {
  backSignIn: 'Back sign in',
  mobileSignInFormTitle: 'Mobile sign in',
  qrSignInFormTitle: 'Qr code sign in',
  signInFormTitle: 'Sign in',
  signUpFormTitle: 'Sign up',
  forgetFormTitle: 'Reset password',

  signInTitle: 'Backstage management system',
  signInDesc: 'Enter your personal details and get started!',
  policy: 'I agree to the xxx Privacy Policy',
  scanSign: `scanning the code to complete the login`,

  loginButton: 'Sign in',
  registerButton: 'Sign up',
  rememberMe: 'Remember me',
  forgetPassword: 'Forget Password?',
  otherSignIn: 'Sign in with',

  // notify
  loginSuccessTitle: 'Login successful',
  loginSuccessDesc: 'Welcome back',

  // placeholder
  accountPlaceholder: 'Please input username',
  passwordPlaceholder: 'Please input password',
  smsPlaceholder: 'Please input sms code',
  mobilePlaceholder: 'Please input mobile',
  policyPlaceholder: 'Register after checking',
  diffPwd: 'The two passwords are inconsistent',

  userName: 'Username',
  password: 'Password',
  confirmPassword: 'Confirm Password',
  email: 'Email',
  smsCode: 'SMS code',
  mobile: 'Mobile',
};
