export default {
  save: 'Save',
  upload: 'Upload',
  imgUpload: 'ImageUpload',
  uploaded: 'Uploaded',

  operating: 'Operating',
  del: 'Delete',
  download: 'download',
  saveWarn: 'Please wait for the file to upload and save!',
  saveError: 'There is no file successfully uploaded and cannot be saved!',

  preview: 'Preview',
  choose: 'Select the file',

  accept: 'Support {0} format',
  acceptUpload: 'Only upload files in {0} format',
  maxSize: 'A single file does not exceed {0}MB ',
  maxSizeMultiple: 'Only upload files up to {0}MB!',
  maxNumber: 'Only upload up to {0} files',

  legend: 'Legend',
  fileName: 'File name',
  fileSize: 'File size',
  fileStatue: 'File status',

  startUpload: 'Start upload',
  uploadSuccess: 'Upload successfully',
  uploadError: 'Upload failed',
  uploading: 'Uploading',
  uploadWait: 'Please wait for the file upload to finish',
  reUploadFailed: 'Re-upload failed files',
};
