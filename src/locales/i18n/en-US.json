{"api.demo.demo.4a934b2e": "no", "api.demo.demo.84877851": "yes", "api.demo.demo.c256a2e7": "Unknown state", "api.demo.demo.8444a061": "abnormal", "api.demo.demo.e04aa9ef": "normal", "api.demo.demo.178a4b0d": "end", "api.demo.demo.99b67645": "middle", "api.demo.demo.5f0b5c96": "high", "api.demo.demo.715ac1e4": "Safety valve", "components.GeegaBasicTitle.src.BasicTitle.5dd8e09c": "Basic Information", "components.GeegaEmpty.empty.abb9dc60": "No data", "components.GeegaEmpty.index.abb9dc60": "No data", "components.GeegaIconModal.index.963456f1": "Value", "components.GeegaIconModal.index.7aa89077": "Icon library", "components.GeegaIconModal.index.d33131f0": "Select icon", "components.GeegaIcons.src.iconJson.41d99592": "Account management", "components.GeegaIcons.src.iconJson.ad4b4781": "Log management", "components.GeegaIcons.src.iconJson.313d714f": "Role management", "components.GeegaIcons.src.iconJson.0f54e722": "Menu management", "components.GeegaIcons.src.iconJson.a4b63eae": "This is the side navigation icon, don’t upload other icons here", "dict.index.378ef0ff": "Data dictionary domain only supports level 2! If you want to use multiple levels, change it yourself! Intersection", "dict.modules.demo.cb95accf": "Published", "dict.modules.demo.68d4b919": "Rejected", "dict.modules.demo.5605ce3b": "To be reviewed", "dict.modules.demo.ec5f4145": "To be submitted", "hooks.web.useFullScreen.c73cb016": "The current browser does not support FullScreen API!", "layouts.default.header.components.notify.data.ff276dc5": "in progress", "layouts.default.header.components.notify.data.44371a04": "Assign <PERSON> to complete the update and release before 2017-01-09", "layouts.default.header.components.notify.data.d5020d63": "ABCD version released", "layouts.default.header.components.notify.data.a47e4b3b": "It took 8 days", "layouts.default.header.components.notify.data.15f5c522": "Information security test", "layouts.default.header.components.notify.data.75f54885": "Expire immediately", "layouts.default.header.components.notify.data.2e7262b9": "Guanlin needs to complete the code change task before 2017-01-07", "layouts.default.header.components.notify.data.1790288a": "Third -party emergency code change", "layouts.default.header.components.notify.data.2aca173f": "has not started", "layouts.default.header.components.notify.data.86772cbd": "The task needs to be started before 2017-01-12 20:00", "layouts.default.header.components.notify.data.20239f22": "mission name", "layouts.default.header.components.notify.data.e80a6e01": "Upcoming", "layouts.default.header.components.notify.data.01743473": "This template is used to remind who of you to interact with you", "layouts.default.header.components.notify.data.bb73a5ab": "title", "layouts.default.header.components.notify.data.f29caf2a": "<PERSON> replied to you", "layouts.default.header.components.notify.data.8586007b": "Description information description information description information", "layouts.default.header.components.notify.data.1b69c8bb": "<PERSON><PERSON> commented on you", "layouts.default.header.components.notify.data.a75f2bb4": "information", "layouts.default.header.components.notify.data.cd664804": "The left icon is used to distinguish different types", "layouts.default.header.components.notify.data.1f1b2cea": "This template can distinguish a variety of notification types", "layouts.default.header.components.notify.data.b36bb6df": "You recommended <PERSON><PERSON> have passed the third round of interviews", "layouts.default.header.components.notify.data.1d4a8f0e": "You received 14 new weekly reports", "layouts.default.header.components.notify.data.22c17472": "notify", "settings.localeSetting.472bff2e": "Simplified Chinese", "utils.regex.index.9bc5a65a": "Only allow input to input non -positive integer", "utils.regex.index.e0304a66": "Only allow input to input non -negative integer", "utils.regex.index.ddfa4c47": "Only allow input negative integers", "utils.regex.index.32ad9603": "Only allow input positive integer", "utils.regex.index.6454d7f5": "Only allow input integers", "utils.regex.index.4a18f346": "Forbidden to enter the character containing ~", "utils.regex.index.152753cb": "Forbidden input containing%& \\ ',; =? $ \"And other characters", "utils.regex.index.4fa39997": "Only symbols such as Chinese, English, numbers are allowed to enter Chinese, English, numbers, but excluding the next line", "utils.regex.index.745d89db": "Only allow to enter Chinese, English, and numbers including the next line", "utils.regex.index.47a79df2": "Only a string consisting of numbers, 26 English letters or underlines", "utils.regex.index.e901d53e": "Only allow input English and numbers", "utils.regex.index.d66f7814": "Only allowed input English", "utils.regex.index.dce8e585": "Only allow input English", "utils.regex.index.1fb75c84": "Only allow to enter English", "utils.regex.index.195a0dbb": "Only allow to enter Chinese characters", "utils.regex.index.d3cdeeab": "Only allow the letters to start, allow 5-16 bytes, and allow the letters to draw lines", "utils.regex.index.5f9de4b0": "Please enter the correct IP", "utils.regex.index.87f83d07": "Please enter the correct domain name", "utils.regex.index.6ad84202": "Please enter the correct fixed phone number", "utils.regex.index.a4b559a8": "please enter a valid phone number", "utils.regex.index.b679f1a3": "Please enter the correct mailbox address, the name allows Chinese characters, letters, numbers, and domain names only allow English domain names", "utils.regex.index.44db8ec8": "Please enter the correct mailbox address, only English letters, numbers, subordinate lines, English periods, and mid -line lines", "views.dashboard.welcome.Geega.aeec874b": "Interface code generator", "views.dashboard.welcome.Geega.e2af512d": "GXE-Table (Vue3) big data table (virtual table)", "views.dashboard.welcome.Geega.4216dcf5": "VS Code Geega Ant Design Auxiliary Tool", "views.dashboard.welcome.Geega.a23c1e13": "VS Code Geegaui Auxiliary Tool", "views.dashboard.welcome.Geega.55093dc7": "Component library document", "views.dashboard.welcome.Geega.1fd872a3": "Project use documentation", "views.dashboard.welcome.Geega.48176533": "The background management system template used for opening the box/also supports Micro-APP/Unbounded/qiankun", "views.demo.anttable.index.2535ccbc": "operate", "views.demo.anttable.index.66a06727": "Owner", "views.demo.anttable.index.db0b5974": "Name", "views.demo.anttable.index.9aedcc25": "Serial number", "views.demo.anttable.index.f97b5d9b": "delete", "views.demo.anttable.index.dd35e0c0": "edit", "views.demo.anttable.index.ae14ae82": "Export", "views.demo.anttable.index.73672dde": "New employee", "views.demo.anttable.index.7aa9138d": "Inquire", "views.demo.anttable.index.69187513": "Repossess", "views.demo.anttable.index.c234a9f6": "external", "views.demo.anttable.index.64db3919": "internal", "views.demo.anttable.index.981355ab": "Please select the employee type", "views.demo.anttable.index.b0943821": "Type", "views.demo.anttable.index.a5c989c7": "please enter", "views.demo.list.index.36cd28e8": "Pass parameter:", "views.demo.virtual.index.1a327f1f": "gender", "views.demo.virtual.index.2f4564cf": "Role", "views.demo.virtual.index.196b3c82": "Name", "views.demo.virtual.index.9aedcc25": "Serial number", "views.demo.virtual.index.9ef7879a": "Big data table use guide", "views.demo.virtual.index.bd0a7e73": "Virtual rolling (maximum can support 10W columns, 30W lines)"}