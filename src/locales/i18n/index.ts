import i18nVue from '@geega/i18n-vue';
import zhCN from './zh-CN.json';
import enUS from './en-US.json';
import { useLocale } from '/@/locales/useLocale';

const { getCurrentLocale } = useLocale();

const resources = {
  // 简体中文
  'zh-CN': {
    translation: zhCN
  },

  // English
  'en-US': {
    translation: enUS
  },
};

const mapil8nStr = {
   'en':'en-US',
   'zh_CN':'zh-CN'
}

const lang = mapil8nStr[getCurrentLocale()] || getCurrentLocale()

i18nVue.init({
  resources,
  lng: lang,
  supportedLngs: ['zh-CN','en-US',],
  fallbackLng: ['zh-CN','en-US',]
  // 若引入 @geega/i18n-browser-helper
  // port: 8099
});

// 请在app注册
// app.use(i18nVue)
export default i18nVue;
