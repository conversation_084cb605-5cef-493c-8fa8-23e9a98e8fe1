{"api.demo.demo.4a934b2e": "否", "api.demo.demo.84877851": "是", "api.demo.demo.c256a2e7": "未知状态", "api.demo.demo.8444a061": "异常", "api.demo.demo.e04aa9ef": "正常", "api.demo.demo.178a4b0d": "底", "api.demo.demo.99b67645": "中", "api.demo.demo.5f0b5c96": "高", "api.demo.demo.715ac1e4": "安全阀", "components.GeegaBasicTitle.src.BasicTitle.5dd8e09c": "基本信息", "components.GeegaEmpty.empty.abb9dc60": "暂无数据", "components.GeegaEmpty.index.abb9dc60": "暂无数据", "components.GeegaIconModal.index.963456f1": "传过来的值", "components.GeegaIconModal.index.7aa89077": "图标库", "components.GeegaIconModal.index.d33131f0": "选择图标", "components.GeegaIcons.src.iconJson.41d99592": "账号管理", "components.GeegaIcons.src.iconJson.ad4b4781": "日志管理", "components.GeegaIcons.src.iconJson.313d714f": "角色管理", "components.GeegaIcons.src.iconJson.0f54e722": "菜单管理", "components.GeegaIcons.src.iconJson.a4b63eae": "这个是侧边导航Icon的，其他的icon不要在这里上传", "dict.index.378ef0ff": "数据字典域只支持2级！想使用多级，自己来改！！", "dict.modules.demo.cb95accf": "已发布", "dict.modules.demo.68d4b919": "被驳回", "dict.modules.demo.5605ce3b": "待审核", "dict.modules.demo.ec5f4145": "待提交", "hooks.web.useFullScreen.c73cb016": "当前浏览器不支持Fullscreen API !", "layouts.default.header.components.notify.data.ff276dc5": "进行中", "layouts.default.header.components.notify.data.44371a04": "指派竹尔于 2017-01-09 前完成更新并发布", "layouts.default.header.components.notify.data.d5020d63": "ABCD 版本发布", "layouts.default.header.components.notify.data.a47e4b3b": "已耗时 8 天", "layouts.default.header.components.notify.data.15f5c522": "信息安全考试", "layouts.default.header.components.notify.data.75f54885": "马上到期", "layouts.default.header.components.notify.data.2e7262b9": "冠霖 需在 2017-01-07 前完成代码变更任务", "layouts.default.header.components.notify.data.1790288a": "第三方紧急代码变更", "layouts.default.header.components.notify.data.2aca173f": "未开始", "layouts.default.header.components.notify.data.86772cbd": "任务需要在 2017-01-12 20:00 前启动", "layouts.default.header.components.notify.data.20239f22": "任务名称", "layouts.default.header.components.notify.data.e80a6e01": "待办", "layouts.default.header.components.notify.data.01743473": "这种模板用于提醒谁与你发生了互动", "layouts.default.header.components.notify.data.bb73a5ab": "标题", "layouts.default.header.components.notify.data.f29caf2a": "朱偏右 回复了你", "layouts.default.header.components.notify.data.8586007b": "描述信息描述信息描述信息", "layouts.default.header.components.notify.data.1b69c8bb": "曲丽丽 评论了你", "layouts.default.header.components.notify.data.a75f2bb4": "消息", "layouts.default.header.components.notify.data.cd664804": "左侧图标用于区分不同的类型", "layouts.default.header.components.notify.data.1f1b2cea": "这种模板可以区分多种通知类型", "layouts.default.header.components.notify.data.b36bb6df": "你推荐的 曲妮妮 已通过第三轮面试", "layouts.default.header.components.notify.data.1d4a8f0e": "你收到了 14 份新周报", "layouts.default.header.components.notify.data.22c17472": "通知", "settings.localeSetting.472bff2e": "简体中文", "utils.regex.index.9bc5a65a": "只允许输入非正整数", "utils.regex.index.e0304a66": "只允许输入非负整数", "utils.regex.index.ddfa4c47": "只允许输入负整数", "utils.regex.index.32ad9603": "只允许输入正整数", "utils.regex.index.6454d7f5": "只允许输入整数", "utils.regex.index.4a18f346": "禁止输入含有~的字符", "utils.regex.index.152753cb": "禁止输入含有%&\\',;=?$\"等字符", "utils.regex.index.4fa39997": "只允许输入中文、英文、数字但不包括下划线等符号", "utils.regex.index.745d89db": "只允许输入中文、英文、数字包括下划线", "utils.regex.index.47a79df2": "只允许输入由数字、26个英文字母或者下划线组成的字符串", "utils.regex.index.e901d53e": "只允许输入英文和数字", "utils.regex.index.d66f7814": "只允许输入小写英文", "utils.regex.index.dce8e585": "只允许输入大写英文", "utils.regex.index.1fb75c84": "只允许输入英文", "utils.regex.index.195a0dbb": "只允许输入汉字", "utils.regex.index.d3cdeeab": "只允许字母开头，允许5-16字节，允许字母数字下划线", "utils.regex.index.5f9de4b0": "请输入正确的IP", "utils.regex.index.87f83d07": "请输入正确的域名", "utils.regex.index.6ad84202": "请输入正确的固定电话", "utils.regex.index.a4b559a8": "请输入正确的手机号", "utils.regex.index.b679f1a3": "请输入正确的邮箱地址，名称允许汉字、字母、数字，域名只允许英文域名", "utils.regex.index.44db8ec8": "请输入正确的邮箱地址，只允许英文字母、数字、下划线、英文句号、以及中划线", "views.dashboard.welcome.Geega.aeec874b": "接口代码生成器", "views.dashboard.welcome.Geega.e2af512d": "gxe-table(VUE3) 大数据 Table（虚拟表格）", "views.dashboard.welcome.Geega.4216dcf5": "VS Code Geega ant design 辅助工具", "views.dashboard.welcome.Geega.a23c1e13": "VS Code GeegaUI 辅助工具", "views.dashboard.welcome.Geega.55093dc7": "组件库文档", "views.dashboard.welcome.Geega.1fd872a3": "项目使用文档", "views.dashboard.welcome.Geega.48176533": "开箱即用的后台管理系统模板/同时支持Micro-app/无界/qiankun", "views.demo.anttable.index.2535ccbc": "操作", "views.demo.anttable.index.66a06727": "所属组织", "views.demo.anttable.index.db0b5974": "员工姓名", "views.demo.anttable.index.9aedcc25": "序号", "views.demo.anttable.index.f97b5d9b": "删除 ", "views.demo.anttable.index.dd35e0c0": "编辑 ", "views.demo.anttable.index.ae14ae82": "导出 ", "views.demo.anttable.index.73672dde": "新增员工 ", "views.demo.anttable.index.7aa9138d": "查询", "views.demo.anttable.index.69187513": "重置", "views.demo.anttable.index.c234a9f6": "外部", "views.demo.anttable.index.64db3919": "内部", "views.demo.anttable.index.981355ab": "请选择员工类型", "views.demo.anttable.index.b0943821": "员工类型", "views.demo.anttable.index.a5c989c7": "请输入", "views.demo.list.index.36cd28e8": "回传参数:", "views.demo.virtual.index.1a327f1f": "性别", "views.demo.virtual.index.2f4564cf": "角色", "views.demo.virtual.index.196b3c82": "姓名", "views.demo.virtual.index.9aedcc25": "序号", "views.demo.virtual.index.9ef7879a": "大数据表格使用指南", "views.demo.virtual.index.bd0a7e73": "虚拟滚动（最大可以支撑 10w 列、30w 行）"}