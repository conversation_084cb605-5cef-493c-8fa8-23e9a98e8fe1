/**
 * Application configuration
 */
import type { ProjectConfig } from '/#/config';
import { updateThemeMode } from './theme/updateThemeMode';
import { updateColorWeak } from '/@/logics/theme/updateColorWeak';
import { updateGrayMode } from '/@/logics/theme/updateGrayMode';

import { appStore } from '/@/store/modules/app';
import { localeStore } from '/@/store/modules/locale';

import { getCommonStoragePrefix, getStorageShortName } from '/@/utils/env';

import { deepMerge } from '/@/utils';
import { userStore } from '../store/modules/user';
import { tokenRefresher } from '/@/utils/auth/tokenRefresher';

// Initial project configuration
export function initAppConfigStore(projectCfg: any = {}) {
  const projectConfigState = appStore.getProjectConfig;
  const projCfg = deepMerge(projectConfigState, projectCfg) as ProjectConfig;
  try {
    const { colorWeak, grayMode, themeMode } = projCfg;
    themeMode && updateThemeMode(themeMode);
    grayMode && updateGrayMode(grayMode);
    colorWeak && updateColorWeak(colorWeak);
  } catch (error) {
    console.log(error);
  }
  appStore.commitProjectConfigState(projCfg);
  userStore.recordLoginTime();
  localeStore.initLocale();

  // 启动token刷新服务
  if (!tokenRefresher.isRunning()) {
    tokenRefresher.startRefreshTimer();
  }

  setTimeout(() => {
    clearObsoleteStorage();
  }, 16);
}

/**
 * As the version continues to iterate, there will be more and more cache keys stored in localStorage.
 * This method is used to delete useless keys
 */
export function clearObsoleteStorage() {
  const commonPrefix = getCommonStoragePrefix();
  const shortPrefix = getStorageShortName();

  [localStorage, sessionStorage].forEach((item: Storage) => {
    Object.keys(item).forEach((key) => {
      if (key && key.startsWith(commonPrefix) && !key.startsWith(shortPrefix)) {
        item.removeItem(key);
      }
    });
  });
}
