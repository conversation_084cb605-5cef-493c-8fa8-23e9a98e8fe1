import { ThemeEnum } from '/@/enums/appEnum';
import {
  enable as enableDarkMode,
  disable as disableDarkMode,
  auto as followSystemColorScheme,
  exportGeneratedCSS as collectCSS,
  isEnabled as isDarkReaderEnabled
} from '@geega/darkreader'
import { isRunMicroEnv } from '/@/utils/micro';
/**
 * Change themeMode
 * @param themeMode ‘auto’ ‘light’ ‘dark’
 */
export function updateThemeMode(themeMode:ThemeEnum) {
  // isRunMicroEnv code=> window.__POWERED_BY_WUJIE__ || window.__MICRO_APP_ENVIRONMENT__ || window.__POWERED_BY_QIANKUN__
  if(isRunMicroEnv()){
     // micro nothing
  } else {
    const isEnabled = isDarkReaderEnabled();
    if(themeMode === 'auto'){
      followSystemColorScheme({
        brightness: 100,
        contrast: 90,
        sepia: 10
       });
    } else {
      // 关闭自动
      //  followSystemColorScheme(false)
       if(ThemeEnum.DARK === themeMode) {
          if(!isEnabled) {
            enableDarkMode({
              ignoreColor: [
                '#00996b',
                // badge状态
                '#EC544D',
                '#E3C843',
                '#4080FF',
                '#00c07f',
                '#F76560',
                '#FF9A2E',
                '#4080FF'
              ],
              ignoreInlineStyle: ['.dark-ignore-style']
           });
          }
       } else {
        if(isEnabled){
          disableDarkMode();
        }
       }
    }
  }

}
