import { nextTick, ref } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import {
  V1ManageTranProjectDeleteIdDelete,
  V1ManageTranProjectPagePost,
  V1ManageTranProjectStatusPost,
} from '/@/api/cddc.req';

export type ProjectType = 1 | 2 | 3; // 1:训练、2:考核、3:比赛
export type ProjectRecord = {
  id: string | number;
  status: 'ENABLE' | 'DISABLE';
  [key: string]: any;
};

export interface UseProjectManagementOptions {
  projectType: ProjectType;
  columns: EnhancedColumn[];
  projectName: string;
  drawerRef: any;
}

export const useProjectManagement = (options: UseProjectManagementOptions) => {
  const { projectType, columns, projectName, drawerRef } = options;
  const drawerTitle = ref(`新增${projectName}`);

  const [registerTable, { reload }] = useTable({
    api: V1ManageTranProjectPagePost,
    columns,
    tableProps: {
      showIndexColumn: false,
      useSearchForm: false,
      searchInfo: {
        projectType,
      },
    },
  });

  // 新增项目
  const handleAdd = async () => {
    drawerTitle.value = `新增${projectName}`;
    nextTick(async () => {
      await drawerRef.value?.openDrawer();
      await drawerRef.value?.setFormData({});
      await drawerRef.value?.resetForm();
    });
  };

  // 停用项目
  const handleDeactivate = async (record: ProjectRecord) => {
    if (!record.id) return;
    await V1ManageTranProjectStatusPost({
      id: record.id + '',
      status: record.status === 'ENABLE' ? 'DISABLE' : 'ENABLE',
    });
    reload();
  };

  // 编辑项目
  const handleEdit = async (record: ProjectRecord) => {
    drawerTitle.value = `编辑${projectName}`;
    nextTick(async () => {
      await drawerRef.value?.openDrawer();
      await drawerRef.value?.setFormData(record);
    });
  };

  // 删除项目
  const handleDelete = async (record: ProjectRecord) => {
    if (!record.id) return;
    await V1ManageTranProjectDeleteIdDelete({ id: record.id + '' });
    reload();
  };

  // 提交成功
  const handleSuccess = () => {
    reload();
  };

  return {
    drawerTitle,
    registerTable,
    handleAdd,
    handleEdit,
    handleDeactivate,
    handleDelete,
    handleSuccess,
  };
};
