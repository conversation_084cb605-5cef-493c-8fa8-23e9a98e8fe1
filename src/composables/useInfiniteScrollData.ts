import { useInfiniteScroll, type ElementOf } from '@vueuse/core';
import { reactive, type MaybeRefOrGetter } from 'vue';

export function useInfiniteScrollData<ApiFn extends (...args: any) => any>(
  el: MaybeRefOrGetter<HTMLElement | undefined | null>,
  apiFn: ApiFn
) {
  type DataItem = ElementOf<Awaited<ReturnType<ApiFn>>['records']>;

  const state = reactive({
    data: [] as DataItem[],
    currentPage: 1,
    pageSize: 20,
    canLoadMore: true,
  });

  const infiniteScrollHandler = useInfiniteScroll(
    el,
    async () => {
      try {
        const resp = await apiFn({
          data: {},
          pageSize: state.pageSize,
          currentPage: state.currentPage,
        });

        const data = [...state.data, ...(resp.records || [])];

        state.data = data;
        state.canLoadMore = +resp.totalRows! > state.data.length;
        state.currentPage++;
      } catch (error) {
        console.error(error);
        state.canLoadMore = false;
      }
    },
    {
      distance: 10,
      canLoadMore() {
        return state.canLoadMore;
      },
    }
  );

  return {
    state,
    isLoading: infiniteScrollHandler.isLoading,
    reset: () => {
      state.currentPage = 1;
      state.data = [];
      state.canLoadMore = true;
      infiniteScrollHandler.reset();
    },
  };
}
