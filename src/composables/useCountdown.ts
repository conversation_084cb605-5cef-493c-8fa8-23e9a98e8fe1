import { ref, onBeforeUnmount, computed } from 'vue';
import type { ComputedRef } from 'vue';

export interface UseCountdownOptions {
  /**
   * 倒计时结束时的回调
   */
  onTimeUp?: () => void;
  /**
   * 时间更新时的回调
   */
  onTimeUpdate?: (remainingSeconds: number) => void;
  /**
   * 是否自动开始倒计时
   * @default false
   */
  autoStart?: boolean;
}

export interface UseCountdownReturn {
  /**
   * 剩余时间（秒）
   */
  remainingTime: ComputedRef<number>;
  /**
   * 是否处于最后一分钟
   */
  isLastMinute: ComputedRef<boolean>;
  /**
   * 格式化后的时间 (MM:SS)
   */
  formattedTime: ComputedRef<string>;
  /**
   * 开始倒计时
   * @param duration 持续时间（秒）
   */
  start: (duration: number) => void;
  /**
   * 重置倒计时
   */
  reset: () => void;
}

export const useCountdown = (options: UseCountdownOptions = {}): UseCountdownReturn => {
  const remainingTime = ref(0);
  const isLastMinute = ref(false);
  const timer = ref<NodeJS.Timer | null>(null);

  // 格式化剩余时间
  const formatTime = (seconds: number): string => {
    if (seconds <= 0) return '00:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 格式化后的时间字符串
  const formattedTime = computed(() => formatTime(remainingTime.value));

  // 清理定时器
  const clearTimer = () => {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }
  };

  // 重置倒计时
  const reset = () => {
    clearTimer();
    remainingTime.value = 0;
    isLastMinute.value = false;
  };

  // 开始倒计时
  const start = (duration: number) => {
    reset();
    if (duration <= 0) return;

    remainingTime.value = duration;
    isLastMinute.value = duration <= 60;

    if (options.autoStart !== false) {
      timer.value = setInterval(() => {
        if (remainingTime.value > 0) {
          remainingTime.value--;
          isLastMinute.value = remainingTime.value <= 60;
          options.onTimeUpdate?.(remainingTime.value);

          if (remainingTime.value === 0) {
            clearTimer();
            options.onTimeUp?.();
          }
        } else {
          clearTimer();
        }
      }, 1000);
    }
  };

  // 组件卸载前清理
  onBeforeUnmount(() => {
    reset();
  });

  return {
    remainingTime: computed(() => remainingTime.value),
    isLastMinute: computed(() => isLastMinute.value),
    formattedTime,
    start,
    reset,
  };
};
