import { ref } from 'vue';
import { message, Modal } from '@geega-ui-plus/ant-design-vue';
import { useWebSocket } from '/@/utils/websocket';
import { createLocalStorage } from '/@/utils/cache';

export interface WebSocketHandlerOptions {
  onDataUpdate: (data: any) => void;
  onExit: () => void;
  onPointUpdate?: (data: any) => void;
  exitMessage?: string;
  type: 'assessment' | 'training' | 'competition';
  messages?: {
    exitConfirm?: boolean;
    exitTitle?: string;
    exitContent?: string;
    overMessage?: string;
  };
}

export const useWebSocketHandler = (options: WebSocketHandlerOptions) => {
  const {
    onDataUpdate,
    onExit,
    onPointUpdate,
    type,
    messages = {},
    exitMessage = '检测到人员已离开作业，自动结束本次操作',
  } = options;

  const wsInstance = ref<any>(null);

  // 清理 WebSocket 连接
  const cleanupWebSocket = () => {
    if (wsInstance.value) {
      wsInstance.value.disconnect();
      wsInstance.value = null;
    }
  };

  // 初始化WebSocket连接
  const initWebSocket = (params: { socketUrl: string; detailId: string }) => {
    const ls = createLocalStorage();
    const userId = ls.get('userId');
    const locationId = ls.get('locationId');

    if (!userId || !params.detailId || !locationId) {
      console.warn('缺少必要的参数，无法建立WebSocket连接');
      return;
    }

    // 如果存在旧连接，先断开
    cleanupWebSocket();

    const { connect: wsConnect, disconnect: wsDisconnect, send: wsSend } = useWebSocket({
      url: `ws://${params.socketUrl}/ws`,
      userId,
      detailId: params.detailId,
      locationId,
      onMessage: handleWebSocketMessage,
      onError: (error) => console.error('WebSocket错误:', error),
      onClose: (event) => console.log('WebSocket连接已关闭:', event),
      onOpen: () => console.log('WebSocket连接已建立'),
    });

    wsInstance.value = {
      connect: wsConnect,
      disconnect: wsDisconnect,
      send: wsSend,
    };

    // 立即连接
    wsConnect();
  };

  // 处理点位数据更新
  const handlePointDataUpdate = (data: any) => {
    try {
      const points = JSON.parse(data.info);
      onPointUpdate?.(
        points?.map((item: any) => ({
          objectFlag: item.objectFlag,
          result: item.result,
          sort: item.sort,
          status: item.status,
        })) || []
      );
    } catch (error) {
      console.error('解析点位数据失败:', error);
    }
  };

  // 处理退出确认
  const handleExit = () => {
    if (messages.exitConfirm) {
      Modal.confirm({
        title: messages.exitTitle || '确认退出',
        content: messages.exitContent || '确定要退出吗？退出后当前进度将丢失。',
        okText: '确认',
        cancelText: '取消',
        onOk: onExit,
      });
    } else {
      onExit();
    }
  };

  const handleWebSocketMessage = (data: { action: string; info?: string; [key: string]: any }) => {
    if (!data) return;

    switch (data.action) {
      case 'push':
        onDataUpdate(data);
        break;
      case 'overTrain':
        message.warning(data.info || exitMessage);
        handleExit();
        break;
      case 'globalResult':
        if (onPointUpdate) {
          handlePointDataUpdate(data);
        }
        break;
      default:
        console.warn('未处理的WebSocket消息类型:', data.action);
    }
  };

  return {
    wsInstance,
    initWebSocket,
    cleanupWebSocket,
    handleWebSocketMessage,
    handleExit,
  };
};
