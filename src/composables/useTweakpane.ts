import { Pane } from 'tweakpane';
import { onMounted, onUnmounted } from 'vue';

export interface UseTweakPaneOption {
  enable?: boolean;
  setup: (pane: Pane) => void;
}

export function useTweakPane(opt: UseTweakPaneOption) {
  let pane: Pane | null = null;

  onMounted(() => {
    if (!opt.enable) {
      return;
    }

    pane = new Pane();
    opt.setup(pane);
  });

  onUnmounted(() => {
    pane?.dispose();
    pane = null;
  });
}
