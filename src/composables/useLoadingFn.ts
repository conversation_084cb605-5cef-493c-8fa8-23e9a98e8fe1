import { ref } from 'vue'

export function useLoadingFn<T extends (...args: any[]) => any>(fn: T) {
  const loading = ref(0)

  const wrapper = async (...args: any[]) => {
    loading.value++

    try {
      return await fn(...args)
    } catch (error) {
      throw error
    } finally {
      loading.value--
    }
  }

  Object.defineProperty(wrapper, 'loading', {
    get() {
      return !!loading.value
    },
  })

  return wrapper as T & { loading: boolean }
}
