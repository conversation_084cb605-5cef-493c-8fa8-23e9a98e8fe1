<script lang="ts" setup>
import { BasicForm, BasicModal, useForm, useModalInner } from '@geega-ui-plus/geega-ui';
import { V1ManageSysOrgUnitUserTreePost } from '/@/api/cddc.req';
import type { ChildElement } from '/@/api/cddc.model';
import { convertDateRange } from '/@/utils/dateUtil';
import { useAsyncData } from '/@/composables';
import { PaperTypeEnum } from '/@/enums/paperEnum';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import UserTreeSelect from '/@/components/UserTreeSelect.vue';

export interface ExamPublishProps {
  publishFn?: (data: any) => any;
}

const props = defineProps<ExamPublishProps>();

const emit = defineEmits(['submit']);

const treeList = useAsyncData(async () => {
  const resp = await V1ManageSysOrgUnitUserTreePost();

  return filterNoneEmptyNodes(resp);
}, []);

treeList.load();

const [registerForm, formActions] = useForm({
  schemas: [
    {
      field: 'date',
      label: '考试有效期',
      component: 'RangePicker',
      required: true,
      componentProps: {
        getPopupContainer: () => document.body,
        popupStyle: {
          width: '570px',
          zIndex: 9999,
        },
        disabledDate: (current: Dayjs) => current.isBefore(dayjs().startOf('d')),
      },
    },
    {
      field: 'userIds',
      label: '考试人员',
      required: true,
      component: 'TreeSelect',
      slot: 'userSelect',
    },
    {
      field: 'retryTimes',
      label: '重考次数',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 10,
        precision: 0,
        style: 'width: 100%',
      },
    },
  ],
  showActionButtonGroup: false,
});

function filterNoneEmptyNodes(nodes: ChildElement[]) {
  const _nodes: ChildElement[] = [];

  for (const node of nodes) {
    if (node.children) {
      const children = filterNoneEmptyNodes(node.children);
      node.children = children;
    }

    const shouldSkip = node.type === 'UNIT' && !node.children?.length;

    if (!shouldSkip) {
      _nodes.push(node);
    }
  }

  return _nodes;
}

const [registerModal, modalActions] = useModalInner(async (opt) => {
  const { type } = opt;

  await formActions.resetFields();
  await formActions.setFieldsValue({
    retryTimes: 1,
  });

  await formActions.updateSchema({
    field: 'retryTimes',
    show: type !== PaperTypeEnum.SKILL_ASSESSMENT,
  });
});

// const submit = _submit
async function submit() {
  try {
    modalActions.setModalProps({
      okButtonProps: {
        loading: true,
      },
    });
    await _submit();
  } catch (error) {
    console.error(error);
  } finally {
    modalActions.setModalProps({
      okButtonProps: {
        loading: false,
      },
    });
  }
}

async function _submit() {
  const values = await formActions.validate();

  const params = {
    ...values,
    ...convertDateRange(values.date),
  };

  await props.publishFn?.(params);

  emit('submit', params);
  modalActions.closeModal();
}
</script>

<template>
  <BasicModal @register="registerModal" title="发布考试" @ok="submit" :mask-closable="false">
    <BasicForm @register="registerForm">
      <template #userSelect="{ model }">
        <UserTreeSelect v-model:value="model.userIds" />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<style lang="less" scoped></style>
