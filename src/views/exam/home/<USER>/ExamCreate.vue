<script lang="ts" setup>
import { ref } from 'vue';
import { BasicForm, BasicModal, useForm, useModalInner } from '@geega-ui-plus/geega-ui';
import { examFormSchemas, calcAllScore } from './exam-form';
import QuestionTypes from './QuestionTypes.vue';
import { useLoadingFn } from '/@/composables';
import { V1ManageExamPaperPut } from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';
import ParamsConfig from './ParamsConfig.vue';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';
import { clamp } from 'lodash-es';

const paramsConfigRef = ref<InstanceType<typeof ParamsConfig>>();

const emit = defineEmits(['success']);

function createInitModel() {
  return {
    questionTypeList: [],
    questionPropertyList: [{}],
  };
}

const [registerForm, formActions] = useForm({
  model: createInitModel(),
  schemas: examFormSchemas({
    onChange: () => {
      paramsConfigRef.value?.clearConfig?.();
    },
  }),
  showActionButtonGroup: false,
});

const submit = useLoadingFn(_submit);

const [registerModal] = useModalInner(async () => {
  await formActions.resetFields();
  await formActions.setFieldsValue(createInitModel());
  await formActions.clearValidate();
});

async function _submit() {
  const values = await formActions.validate();

  const clonedValues = {
    ...values,
    questionPropertyList: values.questionPropertyList?.filter(
      (item) => item.columnKey && item.columnValue
    ),
  };

  const id = await V1ManageExamPaperPut(clonedValues);

  message.success('创建成功');
  emit('success', id);
}

async function onQuestionTypesChanged(types: ResultQuestionTypeList[]) {
  const maxValue = calcAllScore(types);

  await formActions.updateSchema({
    field: 'standardScore',
    componentProps: {
      min: 0,
      max: maxValue,
      style: {
        width: '100%',
      },
    },
  });

  const oldValue = formActions.getFieldsValue().standardScore;
  const newValue = clamp(oldValue, 0, maxValue);

  if (newValue !== oldValue) {
    await formActions.setFieldsValue({
      standardScore: newValue,
    });
  }
}
</script>

<template>
  <BasicModal
    @register="registerModal"
    title="创建试卷"
    width="500px"
    ok-text="一键生成"
    @ok="submit"
    :mask-closable="false"
  >
    <BasicForm @register="registerForm">
      <template #params-filter-slot="{ model }">
        <ParamsConfig
          v-model:value="model.questionPropertyList"
          ref="paramsConfigRef"
          :bankTypeId="model.bankTypeId"
          :control-column-key-once="false"
          @valueSplice="formActions.validateFields(['questionPropertyList'])"
        />
      </template>
      <template #question-type="{ model }">
        <QuestionTypes
          v-model:value="model.questionTypeList"
          @update:value="onQuestionTypesChanged"
        />
      </template>
      <template #all-score-slot="{ model }">
        <a-input :value="`${calcAllScore(model.questionTypeList)}`" disabled suffix="分"></a-input>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<style lang="less" scoped>
:deep(.@{ant-prefix}-form-item-control-input-content) > div > div {
  width: 100%;
}
</style>
