<script lang="ts" setup>
import { onMounted, reactive, watch, ref } from 'vue';
import {
  V1ManageExamPaperConfirmPost,
  V1ManageExamPaperId,
  V1ManageExamPaperPost,
  V1ManageExamPaperReleasePost,
  V1ManageExamPaperSearchQuestionMultiplePost,
  V1ManageExamPaperSearchQuestionSinglePost,
} from '/@/api/cddc.req';
import ExamPreview, {
  type RegeneratePromptOption,
} from '/@/components/Examination/ExamPreview.vue';
import { useAsyncData, useLoadingFn } from '/@/composables';
import { type IQuestion } from '/@/components/Examination/Questions';
import ExamPublish from './ExamPublish.vue';
import { BasicForm, useForm, useModal } from '@geega-ui-plus/geega-ui';
import { buildUUID } from '/@/utils/uuid';
import { message } from '@geega-ui-plus/ant-design-vue';
import { examFormSchemas, calcAllScore } from './exam-form';
import QuestionTypes from './QuestionTypes.vue';
import { useRouter } from 'vue-router';
import { tabStore } from '/@/store/modules/tab';
import ParamsConfig from './ParamsConfig.vue';
import { clamp, cloneDeep } from 'lodash-es';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';
import { calcPaperTotalScore } from './utils';
import { useMessage } from '/@/hooks/web/useMessage';

export interface ExamPreviewProps {
  id: string;
}

const router = useRouter();

const props = defineProps<ExamPreviewProps>();

const [publishModalRegister, publishModalActions] = useModal();
const { createConfirm } = useMessage();

const examInfo = useAsyncData(V1ManageExamPaperId, {});

const paramsConfigRef = ref<InstanceType<typeof ParamsConfig>>();

const state = reactive({
  questions: [] as IQuestion[],
  shouldUpdate: true,
});

const [registerForm, formActions] = useForm({
  model: {
    questionTypeList: [],
    questionPropertyList: [{}],
  },
  schemas: examFormSchemas({
    onChange: () => {
      paramsConfigRef.value?.clearConfig?.();
    },
  }),
  showActionButtonGroup: false,
});

watch(() => props.id, fetchInitData);

onMounted(fetchInitData);

const getQuestions = useLoadingFn(_getQuestions);

async function _getQuestions(overwrite = false) {
  const _questions = await V1ManageExamPaperSearchQuestionMultiplePost({
    paperId: props.id,
    overWrite: overwrite ? 1 : 0,
  });

  return _questions.map((n) => ({ ...n, id: n.id || buildUUID() }));
}

async function fetchInitData() {
  examInfo.update({});

  state.questions = [];

  await formActions.resetFields();
  await formActions.clearValidate();

  if (!props.id) {
    return;
  }

  await examInfo.load({
    id: props.id,
  });

  setForm();

  state.questions = await getQuestions(false);
}

async function setForm() {
  const newData = cloneDeep(examInfo.data.value);
  if ((newData.questionPropertyList?.length || 0) <= 0) {
    // @ts-ignore
    newData.questionPropertyList = [{}];
  }

  await formActions.setFieldsValue(newData);
}

async function saveExamInfo() {
  const values = await formActions.validate();

  await V1ManageExamPaperPost({
    ...values,
    questionPropertyList: values.questionPropertyList?.filter(
      (item) => item.columnKey && item.columnValue
    ),
    id: props.id,
  });

  examInfo.load({
    id: props.id,
  });
}

const saveQuestion = useLoadingFn(_saveQuestion);

async function _saveQuestion() {
  await saveExamInfo();

  await V1ManageExamPaperConfirmPost({
    id: props.id,
    syncBack: state.shouldUpdate ? 'YES' : 'NO',
    questionList: state.questions.map((n, idx) => ({
      ...n,
      paperId: props.id,
      sort: idx,
      questionBankOptionList: n.questionBankOptionList?.map((item, idx) => ({
        ...item,
        orderNum: idx,
      })),
    })),
  });

  message.success('保存成功');

  await gotoPaperPage();
}

async function publish() {
  const data = formActions.getFieldsValue();

  const standardScore = data.standardScore;
  const totalScore = calcPaperTotalScore(state.questions, examInfo.data.value.questionTypeList || []);

  if (totalScore < standardScore) {
    createConfirm({
      title: '提示',
      content: `当前试卷总分为 ${totalScore} 分，低于合格分数 ${standardScore} 分，是否继续发布？`,
      onOk() {
        publishModalActions.openModal(true, {
          type: data.type,
        });
      },
    });

    return;
  }

  publishModalActions.openModal(true, {
    type: data.type,
  });
}

async function generateNewQuestion(params: RegeneratePromptOption) {
  const resp = await V1ManageExamPaperSearchQuestionSinglePost({
    paperId: props.id,
    bankTypeId: examInfo.data.value.bankTypeId,
    question: {
      difficultDegree: params.question.difficultDegree!,
      questionType: params.question.questionType,
      questionNum: 1,
    },
    questionPoint: params.questionPoint,
    bankIds: state.questions.map((n) => n.bankId!),
  });

  if (resp) {
    resp.id ||= buildUUID();
  }

  return resp;
}

const regenerateQuestions = useLoadingFn(_regenerateQuestions);

async function _regenerateQuestions() {
  await saveExamInfo();

  state.questions = await getQuestions(true);

  setForm();

  message.success('已重新生成题目');
}

async function publishExam(values) {
  await saveQuestion();

  await V1ManageExamPaperReleasePost({
    id: props.id,
    ...values,
  });

  message.success('发布成功');
}

async function gotoPaperPage() {
  // tabStore.closeCurrentTab();

  router.replace({ path: '/exam/paper' });
}

async function onQuestionTypesChanged(types: ResultQuestionTypeList[]) {
  const maxValue = calcAllScore(types);

  await formActions.updateSchema({
    field: 'standardScore',
    componentProps: {
      min: 0,
      max: maxValue,
      style: {
        width: '100%',
      },
    },
  });

  const oldValue = formActions.getFieldsValue().standardScore;
  const newValue = clamp(oldValue, 0, maxValue);

  if (newValue !== oldValue) {
    await formActions.setFieldsValue({
      standardScore: newValue,
    });
  }
}
</script>

<template>
  <div class="h-full p-3">
    <div class="flex gap-4">
      <div class="form w-450px">
        <div class="form-title"> 试题要求 </div>
        <div class="form-content">
          <BasicForm @register="registerForm">
            <template #question-type="{ model }">
              <QuestionTypes
                v-model:value="model.questionTypeList"
                @update:value="onQuestionTypesChanged"
              />
            </template>
            <template #all-score-slot="{ model }">
              <a-input
                :value="`${calcAllScore(model.questionTypeList)}`"
                disabled
                suffix="分"
              ></a-input>
            </template>
            <template #params-filter-slot="{ model }">
              <ParamsConfig
                :bankTypeId="model.bankTypeId"
                ref="paramsConfigRef"
                v-model:value="model.questionPropertyList"
                :control-column-key-once="false"
                @valueSplice="formActions.validateFields(['questionPropertyList'])"
              />
            </template>
          </BasicForm>
          <div class="sticky bottom-0 py-8px bg-[#eee] z-200">
            <a-button
              type="primary"
              size="large"
              @click="regenerateQuestions"
              :loading="regenerateQuestions.loading"
            >
              重新生成
            </a-button>
          </div>
        </div>
      </div>
      <div class="flex-1 w-0">
        <a-spin
          :spinning="getQuestions.loading || regenerateQuestions.loading"
          tip="正在生成题目..."
        >
          <div class="px-60px min-h-90vh">
            <ExamPreview
              allow-edit
              :exam-data="examInfo.data.value"
              v-model:questions="state.questions"
              :generate-new-question="generateNewQuestion"
            />
          </div>
        </a-spin>

        <div class="tool-footer">
          <div class="flex justify-end gap-2">
            <a-checkbox v-model:checked="state.shouldUpdate">更新至题库</a-checkbox>
            <a-button @click="saveQuestion" :loading="saveQuestion.loading">确定</a-button>
            <a-button type="primary" @click="publish">发布</a-button>
          </div>
        </div>
      </div>
    </div>

    <ExamPublish
      @register="publishModalRegister"
      :publishFn="publishExam"
      @submit="gotoPaperPage"
    />
  </div>
</template>

<style lang="less" scoped>
.form {
  position: sticky;

  // height: fit-content;
  height: calc(100vh - 110px);
  top: 102px;

  background: #eee;

  overflow-y: auto;

  .form-title {
    font-size: 16px;
    font-weight: 500;
    padding: 8px 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .form-content {
    padding: 16px;
  }
}

.tool-footer {
  position: sticky;

  margin-top: 16px;
  bottom: 8px;
  right: 16px;
  padding: 16px;

  background: #eee;
}
:deep(.@{ant-prefix}-form-item-control-input-content) > div > div {
  width: 100%;
}
</style>
