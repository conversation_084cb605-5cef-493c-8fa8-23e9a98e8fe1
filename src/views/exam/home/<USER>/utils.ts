import type { QuestionBankDetailListElement, ResultQuestionTypeList } from '/@/api/cddc.model';
import { groupQuestions } from '/@/components/Examination/utils';

export function calcPaperTotalScore(
  questions: QuestionBankDetailListElement[],
  questionTypeList: ResultQuestionTypeList[]
) {
  const grouped = groupQuestions(questions);

  let totalScore = 0;

  for (const item of grouped) {
    const [questionType, difficulty] = item.orderType.split('-');

    const typeConfig = questionTypeList.find(
      (n) => n.questionType === questionType && n.difficultDegree?.toString() === difficulty
    );

    const singleScore = typeConfig?.singleScore || 0;

    totalScore += singleScore * item.questions.length;
  }

  return totalScore;
}
