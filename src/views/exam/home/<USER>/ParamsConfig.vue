<script setup lang="ts">
import { watch, onMounted, ref, nextTick } from 'vue';
import type { QuestionPropertyListElement } from '/@/api/cddc.model';
import {
  V1ManageQuestionBankTypeGetQuestionBankTypeById,
  V1ManageQuestionBankColumnMappingListPost,
} from '/@/api/cddc.req';
import { useVModel } from '@vueuse/core';
import { useInjectFormItemContext } from '@geega-ui-plus/ant-design-vue/es/form';
import { getPopupContainer } from '/@/utils';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';

export interface QuestionPropertyListElementProps {
  value: Partial<QuestionPropertyListElement>[];
  bankTypeId?: string;
  controlColumnKeyOnce?: boolean;
}

const props = defineProps<QuestionPropertyListElementProps>();

const emit = defineEmits(['update:value', 'valueSplice']);

const formItemCtx = useInjectFormItemContext();

const vValue = useVModel(props, 'value', emit, { defaultValue: [] });

interface Opt {
  label: string;
  value: string;
}

const questionBankColumn = ref<Opt[]>([]);

async function loadQuestionBankColumn() {
  questionBankColumn.value = [];

  if (!props.bankTypeId) return;

  const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeById({ typeId: props.bankTypeId });

  const _list = (resp.questionBankTypeColumns || []).filter((o) => !!o.questionBankColumnDTO);

  questionBankColumn.value = _list.map((o) => {
    const { columnKey, columnName, id } = o.questionBankColumnDTO || {};

    return {
      label: columnName!,
      value: columnKey!,
      id: id!,
    };
  });
}

watch(
  () => props.bankTypeId,
  () => {
    loadQuestionBankColumn();
  }
);

function clearConfig() {
  vValue.value = [
    {
      columnKey: undefined,
      columnValue: undefined,
    },
  ];
}

function clacAbleOpts(currentKey?: string) {
  if (!props.controlColumnKeyOnce) {
    return questionBankColumn.value;
  }
  const keys = vValue.value.map((o) => o.columnKey).filter(Boolean);

  return questionBankColumn.value.filter((item) => {
    return !keys.includes(item.value) || currentKey === item.value;
  });
}

const columnKeyOfColumnMappingList = ref<Record<string, Opt[]>>({});

async function questionBankColumnMappingListApi(columnKey?: string) {
  if (!columnKey) return;

  if (Object.keys(columnKeyOfColumnMappingList.value).includes(columnKey)) {
    return;
  }
  const resp = await V1ManageQuestionBankColumnMappingListPost({
    columnKey,
  });

  const _opts = [...new Set((resp || []).map((o) => o.columnValue!))];

  const opts = _opts.map((o) => ({
    label: o,
    value: o,
  }));

  columnKeyOfColumnMappingList.value[columnKey] = opts;
}

function clacColumnValueOpts(columnKey?: string, columnValue?: string) {
  if (!columnKey) return [];
  if (!Object.keys(columnKeyOfColumnMappingList.value).includes(columnKey)) {
    return [];
  }
  const opts = columnKeyOfColumnMappingList.value[columnKey];

  let _f = vValue.value.filter((o) => o.columnKey && o.columnValue).map((o) => o.columnValue);

  if (columnValue) {
    _f = _f.filter((o) => o !== columnValue);
  }

  return opts.filter((o) => !_f.includes(o.value));
}

onMounted(loadQuestionBankColumn);

defineExpose({ clearConfig });
</script>

<template>
  <div class="pt-10px pb-6px">
    <div class="pb-10px flex" v-for="(item, idx) in vValue">
      <div class="w-50px justify-end flex pt-2px pr-6px">字段</div>
      <div class="w-[calc(50%-62px)]">
        <a-select
          v-model:value="item.columnKey"
          placeholder="请选择"
          :options="clacAbleOpts(item.columnKey)"
          allow-clear
          style="width: 100%"
          show-search
          option-filter-prop="label"
          :getPopupContainer="getPopupContainer"
          @change="
            () => {
              item.columnValue = undefined;
              nextTick(() => formItemCtx.onFieldChange());
            }
          "
        >
          <template #notFoundContent v-if="bankTypeId && !questionBankColumn?.length">
            <div>
              <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
            </div>
          </template>
        </a-select>
      </div>
      <div class="flex-1 justify-end flex pt-2px pr-6px">值</div>
      <div class="w-[calc(50%-62px)]">
        <div style="display: none" :key="item.columnKey">{{
          questionBankColumnMappingListApi(item.columnKey)
        }}</div>
        <a-select
          v-model:value="item.columnValue"
          placeholder="请选择"
          :options="clacColumnValueOpts(item.columnKey, item.columnValue)"
          style="width: 100%"
          @change="
            () => {
              nextTick(() => formItemCtx.onFieldChange());
            }
          "
          allow-clear
          show-search
          option-filter-prop="label"
          :getPopupContainer="getPopupContainer"
        >
          <template
            #notFoundContent
            v-if="(bankTypeId && !questionBankColumn?.length) || (bankTypeId && item.columnKey)"
          >
            <div>
              <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
            </div>
          </template> </a-select
      ></div>
      <div class="w-30px justify-end flex">
        <a-button
          type="link"
          class="!px-4px !mx-0"
          :disabled="vValue.length <= 1"
          @click="
            () => {
              vValue.splice(idx, 1);
              nextTick(() => emit('valueSplice'));
            }
          "
        >
          <SvgIcon name="g-trash" />
        </a-button>
      </div>
    </div>
    <div class="flex">
      <AButton
        class="add-params"
        type="link"
        :disabled="vValue.length >= 20"
        @click="
          () => {
            vValue.push({
              columnKey: undefined,
              columnValue: undefined,
            });
          }
        "
      >
        新增
      </AButton>
    </div>
  </div>
</template>

<style lang="less" scoped>
.add-params {
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
