<script lang="ts" setup>
import { ref, toRaw } from 'vue';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';
import { QuestionTypeOptions } from '/@/components/Examination/Questions/enum';
import { useVModel, watchImmediate } from '@vueuse/core';
import { isEqual } from 'lodash-es';
import { useInjectFormItemContext } from '@geega-ui-plus/ant-design-vue/es/form';
import { QUESTION_DIFFICULT_DEGREE, QuestionDifficultOptions } from '/@/enums/questionEnum';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { getDictLabel } from '/@/dict';

export interface QuestionTypeConf {
  enabled: boolean;
  type: string;
  title: string;
  count: number;
  score: number;
  difficult: string;
}

export interface QuestionTypesProps {
  value?: ResultQuestionTypeList[];
}

const allAvailableTypes = QuestionTypeOptions.flatMap((type) => {
  return QuestionDifficultOptions.map((difficulty) => ({
    type: type.value,
    difficulty: difficulty.value,
  }));
});

const props = defineProps<QuestionTypesProps>();

const emit = defineEmits(['update:value']);

const formItemCtx = useInjectFormItemContext();

const vValue = useVModel(props, 'value', emit, { defaultValue: [] });

const questionsConfig = ref<QuestionTypeConf[]>([]);

watchImmediate(vValue, (questionTypeList) => {
  const configs = calcTypesConfig(questionTypeList || []);

  questionsConfig.value = configs;

  if (!questionsConfig.value.length) {
    addNewQuestionType();
  }
});

function isLossyEq<T>(a: T[], b: T[]) {
  if (a.length !== b.length) {
    return false;
  }

  return a.every((val, idx) => isEqual(val, b[idx]));
}

watchImmediate(
  questionsConfig,
  () => {
    const newValue = questionsConfig.value
      .filter((n) => n.enabled)
      .map((item) => {
        const _item: ResultQuestionTypeList = {
          questionNum: item.count,
          questionType: item.type,
          difficultDegree: item.difficult as any as number,
          singleScore: item.score,
        };

        return _item;
      });

    const oldValue = toRaw(vValue.value) || [];

    if (isLossyEq(newValue, oldValue)) {
      return;
    }

    vValue.value = newValue;

    formItemCtx?.onFieldChange();
  },
  { deep: true }
);

function calcTypesConfig(questionTypeList: ResultQuestionTypeList[]) {
  const conf = questionTypeList;

  const createConfig = (item: ResultQuestionTypeList) => {
    const _conf: QuestionTypeConf = {
      enabled: true,
      type: item.questionType,
      difficult: item?.difficultDegree?.toString() ?? QUESTION_DIFFICULT_DEGREE.NORMAL,
      title: getDictLabel(QuestionTypeOptions, item.questionType).toString(),
      count: item?.questionNum ?? 10,
      score: item?.singleScore ?? 10,
    };

    return _conf;
  };

  return conf.map((item) => createConfig(item));
}

function addNewQuestionType() {
  const availableTypes = allAvailableTypes.filter((o) => {
    return !questionsConfig.value.find((n) => n.type === o.type && n.difficult === o.difficulty);
  });

  const nextTypeOption = availableTypes.at(0);

  if (!nextTypeOption) return;

  questionsConfig.value.push({
    enabled: true,
    type: nextTypeOption.type,
    difficult: nextTypeOption.difficulty,
    title: getDictLabel(QuestionTypeOptions, nextTypeOption.type).toString(),
    count: 10,
    score: 10,
  });
}

function removeQuestionType(idx: number) {
  questionsConfig.value.splice(idx, 1);
}

function getTypeOptions(idx: number) {
  const existsTypes = questionsConfig.value.filter((_, oIdx) => oIdx !== idx);

  const availableTypes = allAvailableTypes.filter(
    (o) => !existsTypes.find((n) => n.type === o.type && n.difficult === o.difficulty)
  );

  return QuestionTypeOptions.map((o) => ({
    ...o,
    disabled: !availableTypes.find((n) => n.type === o.value),
  }));
}

function getDifficultOptions(idx: number) {
  const item = questionsConfig.value[idx];

  const existsDifficulties = questionsConfig.value
    .filter((o, oIdx) => oIdx !== idx && o.type === item.type)
    .map((o) => o.difficult);

  const options = QuestionDifficultOptions.map((o) => ({
    ...o,
    disabled: existsDifficulties.find((n) => n === o.value),
  }));

  return options;
}

function checkDuplication(idx: number) {
  const item = questionsConfig.value[idx];

  const existsDifficulties = questionsConfig.value
    .filter((o, oIdx) => oIdx !== idx && o.type === item.type)
    .map((o) => o.difficult);

  const availableDifficulties = QuestionDifficultOptions.filter(
    (o) => !existsDifficulties.includes(o.value)
  );

  const isAvailable = availableDifficulties.find((n) => n.value === item.difficult);

  if (!isAvailable) {
    item.difficult = availableDifficulties[0].value;
  }
}
</script>

<template>
  <div class="my-2 mx-1">
    <div class="question-types">
      <div class="question-type" v-for="(conf, idx) in questionsConfig">
        <div class="flex-1 flex justify-between">
          <a-select
            v-model:value="conf.type"
            :options="getTypeOptions(idx)"
            @change="checkDuplication(idx)"
            class="!w-80px"
          />

          <div>
            <span class="mr-1">难度</span>
            <a-select
              v-model:value="conf.difficult"
              :options="getDifficultOptions(idx)"
              class="!w-80px"
            />
          </div>

          <span class="flex items-center">
            <a-input-number
              class="!w-60px"
              v-model:value="conf.count"
              :min="1"
              :max="99"
              :precision="0"
            />
            <span class="ml-1">题</span>
          </span>

          <span class="flex items-center">
            <span>每题</span>
            <span class="mx-1">
              <a-input-number
                class="!w-60px"
                v-model:value="conf.score"
                :min="0.1"
                :max="99"
                :precision="1"
              />
            </span>
            <span>分</span>
          </span>
        </div>
        <a-button
          type="link"
          class="!px-4px !mx-0"
          @click="removeQuestionType(idx)"
          :disabled="questionsConfig.length < 2"
        >
          <SvgIcon name="g-trash" />
        </a-button>
      </div>
    </div>
    <div class="flex justify-center mt-2">
      <a-button
        ghost
        type="primary"
        class="!flex items-center"
        @click="addNewQuestionType"
        :disabled="questionsConfig.length >= allAvailableTypes.length"
      >
        <SvgIcon name="g-plus" /> 新增
      </a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.question-types {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.question-type {
  display: flex;
  align-items: center;
}
</style>
