import type { FormSchema } from '@geega-ui-plus/geega-ui';
import {
  V1ManageCommonPaperTypeEnum,
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
} from '/@/api/cddc.req';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';

interface Opt {
  onChange?: (...rest: any[]) => void;
  hideType?: boolean
}

export function examFormSchemas(opt: Opt = {}): FormSchema[] {
  return [
    {
      field: 'title',
      label: '试卷标题',
      component: 'Input',
      required: true,
      componentProps: {
        maxlength: 50,
      },
    },
    {
      field: 'bankTypeId',
      label: '题库',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: async () => {
          const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({});
          return resp?.map((o) => ({ label: o.name, value: o.id }));
        },
        onChange: (value?: string) => {
          opt.onChange?.('bankTypeId', value);
        },
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    {
      field: 'questionPropertyList',
      label: '参数筛选',
      // required: true,
      component: 'Select',
      slot: 'params-filter-slot',
      rules: [
        // {
        //   required: true,
        //   async validator(rule, value: QuestionPropertyListElement[] = []) {
        //     const everyFillComplete = value.every((o) => o.columnKey && o.columnValue);

        //     if (!everyFillComplete) {
        //       throw rule.message;
        //     }
        //   },
        //   message: '请完善参数筛选',
        // },
      ],
    },
    {
      field: 'questionTypeList',
      label: '题型',
      component: 'Select',
      rules: [
        {
          required: true,
          async validator(rule, value) {
            if (!value?.length) {
              throw rule.message;
            }
          },
          message: '请完善题型配置',
        },
      ],
      slot: 'question-type',
    },
    {
      field: 'allScore',
      label: '总分',
      component: 'Select',
      slot: 'all-score-slot',
    },
    {
      field: 'standardScore',
      label: '合格分数',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0,
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'type',
      label: '试卷类型',
      component: 'ApiSelect',
      required: true,
      ifShow: !opt.hideType,
      componentProps: {
        api: () =>
          V1ManageCommonPaperTypeEnum().then((resp) =>
            resp.map((item) => ({ label: item.desc, value: item.code }))
          ),
      },
    },
    {
      field: 'questionPoint',
      label: '考题要点',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxlength: 150,
      },
    },
  ];
}

export function calcAllScore(questionTypeList: ResultQuestionTypeList[] = []) {
  return questionTypeList.reduce((iter, item) => {
    return iter + (item.questionNum || 0) * (item.singleScore || 0);
  }, 0);
}
