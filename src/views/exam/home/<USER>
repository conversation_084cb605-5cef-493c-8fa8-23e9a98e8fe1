<script setup lang="ts">
import { SvgIcon, useModal } from '@geega-ui-plus/geega-ui';
import ScreenBox from './components/ScreenBox.vue';
import ExamCreateSimple from './components/ExamCreateSimple.vue';
import ExamPublishSimple from './components/ExamPublishSimple.vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { PaperTypeEnum } from '/@/enums/paperEnum';
import { V1ManageExamPaperQuicklyReleasePost } from '/@/api/cddc.req';

const [publishModalRegister, publishModalActions] = useModal();
const [registerModal, modalActions] = useModal();

interface IDescConfig {
  title: string;
  description: string;
  extra?: { title: string; description: string };
}

const formData = {
  createdData: null as Record<string, any> | null,
};

const configs: IDescConfig[] = [
  {
    title: '创建试卷',
    description: '基于车型、岗位创建新的试卷',
  },
  // {
  //   title: '一键生成试卷',
  //   description: '通过大模型，按要求一键生成考卷',
  // },
  // {
  //   title: '试卷预览与调整',
  //   description: '在线浏览考卷，支持手动调整或重新生成试题',
  //   extra: {
  //     title: '试卷确认',
  //     description: '若无问题则可确认试卷',
  //   },
  // },
  {
    title: '考试发布',
    description: '管理员设置考试人员、考试时间，发布至考试系统，供员工考试',
  },
  {
    title: '在线考试',
    description: '员工登录考试系统，在线考试、提交',
  },
];

function showCreateDialog() {
  formData.createdData = null;

  modalActions.openModal(true, {});
}

function showNextStep(createdData: any) {
  formData.createdData = createdData;
  publishModalActions.openModal(true, {
    type: PaperTypeEnum.SKILL_INSPECTION,
  });
}

async function publishExam(values) {
  console.log(values, formData);

  await V1ManageExamPaperQuicklyReleasePost({
    ...values,
    paperDTO: {
      ...formData.createdData as any,
      type: PaperTypeEnum.SKILL_INSPECTION
    }
  });

  message.success('发布成功');
}

async function gotoPaperPage() {
  modalActions.setModalProps({ visible: false });
}
</script>

<template>
  <div class="home-container">
    <div class="flex flex-col items-center">
      <div class="w-1100px">
        <div class="flex mb-50px">
          <div class="landing-content flex-1 w-0">
            <div class="title decoration">
              Hi～亲爱的小伙伴，您可以在线创建考试啦，快一起来体验吧！
            </div>
            <div class="description mt-4">
              我是基于大模型打造的AI知识库，可在线基于不同知识要点一键生成试卷，您可以基于我生成的结果进行调整、确认、发布，我会持续学习进步，成为您试卷快速生成的小助手。
            </div>
            <a-button class="create-btn" type="primary" @click="showCreateDialog">
              <SvgIcon name="g-plus" :size="22" />
              <span> 创建试卷 </span>
            </a-button>
          </div>
          <div class="img w-400px h-320px relative">
            <img class="h-full absolute right-0" src="./assets/kaoshi.png" />
          </div>
        </div>

        <div class="flex gap-8">
          <div class="c-box" v-for="(conf, idx) in configs">
            <div class="bg-number">
              <span class="number">0{{ idx + 1 }}</span>
              <SvgIcon name="g-header-decoration" :size="12" color="#45505C" />
              <SvgIcon name="g-header-decoration" :size="12" color="#828C9842" />
            </div>
            <ScreenBox class="c-content">
              <div class="title decoration">{{ conf.title }}</div>
              <div class="desc">{{ conf.description }}</div>

              <template v-if="conf.extra">
                <div class="title">{{ conf.extra.title }}</div>
                <div class="desc">{{ conf.extra.description }}</div>
              </template>
              <div class="flex-1"></div>
              <div class="img"></div>
            </ScreenBox>
          </div>
        </div>
      </div>
    </div>

    <ExamCreateSimple @register="registerModal" @success="showNextStep" />
    <ExamPublishSimple
      @register="publishModalRegister"
      :publishFn="publishExam"
      @submit="gotoPaperPage"
    />
  </div>
</template>

<style lang="less" scoped>
.home-container {
  width: 100%;
  height: 100%;
  min-height: 690px;

  display: flex;
  align-items: center;
  justify-content: center;
}

.create-btn {
  margin-top: 40px;

  width: 115px;
  height: 36px;
  padding: 8px 16px;

  display: flex;
  align-items: center;
  gap: 4px;
}

.landing-content {
  .title {
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0.24px;
  }

  .description {
    // color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    letter-spacing: 0.14px;
  }
}

.c-box {
  height: 300px;
  width: 200px;
  position: relative;

  .title {
    position: relative;

    font-size: 16px;

    font-weight: 500;
    letter-spacing: 0.16px;

    &.decoration {
      padding-left: 10px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;

        transform: translate(0, -50%);

        width: 3px;
        height: 70%;
        background: #00996b;
      }
    }
  }

  .desc {
    color: rgba(255, 255, 255, 0.75);
  }

  each(range(5), {
    &:nth-child(@{value}n) {
      .img {
        height: 130px;
        aspect-ratio: 164 / 132;

        --ignore-dark-image: url('./assets/img-@{value}.png');

        background: no-repeat center / auto 100% var(--ignore-dark-image);
      }
    }
  });

  .bg-number {
    position: absolute;
    top: 0;
    left: 20px;

    transform: translateY(-65%);
    display: flex;
    align-items: center;
    justify-content: center;

    .number {
      font-size: 64px;
      font-weight: 700;
      letter-spacing: 0px;
      font-family: monospace;

      -webkit-text-stroke: 1px rgba(41, 232, 223, 0.4);

      --ignore-dark-bg: radial-gradient(
        47.49% 29.22% at 50% 48.05%,
        rgba(255, 255, 255, 0.64) 0%,
        rgba(255, 255, 255, 0.16) 100%
      );
      background: var(--ignore-dark-bg);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .c-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 8px;
    position: relative;
    z-index: 10;

    --ignore-dark-bg: linear-gradient(
      180deg,
      rgba(178, 220, 253, 0.1) -29.42%,
      rgba(206, 233, 242, 0) 198.89%
    );
    background: var(--ignore-dark-bg);
    box-shadow: 0px 0.667px 1.333px 0px rgba(0, 0, 0, 0.25);

    backdrop-filter: blur(10px);
  }
}
</style>
