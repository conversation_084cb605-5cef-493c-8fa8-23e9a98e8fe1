<script setup lang="ts">
import ExamCreate from './ExamCreate.vue';
import { useRouter } from 'vue-router';
import { useModal } from '@geega-ui-plus/geega-ui';

const router = useRouter();

const [registerModal, modalActions] = useModal();

function showCreateDialog() {
  modalActions.openModal(true, {});
}

function toPreviewStep(id: string) {
  router.push({
    path: '/exam/home/<USER>',
    query: {
      id,
    },
  });
}
</script>

<template>
  <a-button class="create-btn" type="primary" @click="showCreateDialog">
    <span> 创建试卷 </span>
    <ExamCreate @register="registerModal" @success="toPreviewStep" />
  </a-button>
</template>

<style lang="less" scoped>
.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
</style>
