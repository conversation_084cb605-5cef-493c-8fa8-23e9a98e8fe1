import { h } from 'vue';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { questionStatusConfig, questionStatusOptions } from './constants';
import { V1ManageCommonQuestionTypeEnum } from '/@/api/cddc.req';
import type { BasicColumn, FormSchema } from '@geega-ui-plus/geega-ui';
import { QuestionDifficultOptions } from '/@/enums/questionEnum';
import { getDictItem } from '/@/dict';

// 表格列配置
export function getBasicColumns(dynamicFieldColumn: BasicColumn[] = []): BasicColumn[] {
  return [
    {
      title: '题干',
      dataIndex: 'questionStem',
      width: 300,

      customRender: ({ text }) => {
        return h('div', {}, text);
      },
    },
    {
      title: '题型',
      dataIndex: 'questionTypeName',
      width: 120,
      customRender: ({ text }) => {
        return h(Tag, { color: 'blue' }, () => text);
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ text }) => {
        const { color, text: statusText } = questionStatusConfig[text] || {
          color: 'default',
          text,
        };
        return h(Tag, { color }, () => statusText);
      },
    },
    {
      title: '难度',
      dataIndex: 'difficultDegree',
      width: 100,
      customRender: ({ text }) => {
        const item =
          getDictItem(QuestionDifficultOptions, String(text)) || QuestionDifficultOptions.at(0)!;

        const color = item.colorType;
        return h(Tag, { color }, () => item.label);
      },
    },
    ...dynamicFieldColumn,
    {
      title: '更新时间',
      dataIndex: 'lastUpdateTime',
      width: 180,
    },
    {
      title: '更新人',
      dataIndex: 'lastUpdateBy',
      width: 120,
    },
  ];
}

// 表格搜索配置
export function getFormSchema(dynamicFieldFormSchema: FormSchema[] = []): FormSchema[] {
  return [
    {
      label: '题干',
      field: 'questionStem',
      component: 'Input',
      componentProps: {
        placeholder: '请输入题干',
      },
    },
    ...dynamicFieldFormSchema,
    {
      label: '题型',
      field: 'questionType',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择题型',
        allowClear: true,
        api: V1ManageCommonQuestionTypeEnum,
        fieldNames: {
          label: 'desc',
          value: 'code',
          key: 'code',
        },
      },
    },
    {
      label: '状态',
      field: 'status',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: questionStatusOptions,
      },
    },
    {
      label: '难度',
      field: 'difficultDegree',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: QuestionDifficultOptions,
      },
    },
  ];
}
