<template>
  <div>
    <BasicTablePlus @register="registerTable">
      <template #toolbar>
        <div class="flex gap-8px fix-btn-gap" v-if="questionBankId">
          <a-button ghost type="primary" @click="openUploadDrawer(UploadTypeEnum.AI_GENERATE)">
            AI生成
          </a-button>
          <a-button ghost type="primary" @click="openUploadDrawer(UploadTypeEnum.FILE_IMPORT)">
            文件导入
          </a-button>
          <a-button ghost type="primary" @click="handleAdd">手动新增</a-button>
          <a-button
            type="primary"
            ghost
            @click="handleAllConfirm"
            :disabled="selectedRowKeys.length === 0"
          >
            批量确定
          </a-button>
        </div>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleEdit(record)">编辑</a>
            <a @click="handleDetail(record)">详情</a>
            <a @click="handleConfirm([record.id])" v-if="record.status === 0">确认</a>
            <BaseConfirm @confirm="handleDelete(record)"></BaseConfirm>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
    <EditQuestion
      :allowChangeType="true"
      :allowParamsConfig="allowParamsConfig"
      @register="editModalRegister"
      :beforeSubmitFn="updateQuestion"
    ></EditQuestion>
    <QuestionPreview @register="previewModalRegister"></QuestionPreview>
    <AiGenerationDrawer ref="aiGenerationDrawerRef" @reload="reload" />
    <QuestionsImportDrawer ref="questionsImportDrawerRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, watch } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import {
  V1ManageQuestionBankBatchCompleteQuestionBankPost,
  V1ManageQuestionBankDeleteQuestionBank,
  V1ManageQuestionBankGetQuestionBankPagePost,
  V1ManageQuestionBankUpdateQuestionBankPost,
  V1ManageQuestionBankInsertQuestionBankPost,
} from '/@/api/cddc.req';
import type { QuestionBankDetailListElement } from '/@/api/cddc.model';
import { message } from '@geega-ui-plus/ant-design-vue';
import EditQuestion from '/@/components/Examination/Questions/EditQuestion.vue';
import { useModal } from '@geega-ui-plus/geega-ui';
import QuestionPreview from '/@/components/Examination/Questions/QuestionPreview.vue';
import AiGenerationDrawer from './components/aiGeneration/drawer.vue';
import QuestionsImportDrawer from './components/questionsImport/drawer.vue';
import { getBasicColumns, getFormSchema } from './data';
import {
  createQuestionBankDynamicFieldFormSchema,
  creatSearchConfig,
  formatDynamicParams,
} from '/@/utils/questionBankFieldUtils';
import { QUESTION_TYPE } from '/@/components/Examination/Questions/enum';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { type UploadType, UploadTypeEnum } from '/@/views/exam/uploadManage/data';

const props = defineProps<{
  questionBankId?: string;
}>();

const selectedRowKeys = ref<(string | number)[]>([]);
const allowParamsConfig = ref<boolean>(false);
const [editModalRegister, editModalActions] = useModal();
const [previewModalRegister, previewModalActions] = useModal();

const aiGenerationDrawerRef = ref<InstanceType<typeof AiGenerationDrawer>>();
const questionsImportDrawerRef = ref<InstanceType<typeof QuestionsImportDrawer>>();

async function loadQuestionBankAynamic() {
  const { formSchema, fields } = await createQuestionBankDynamicFieldFormSchema(
    props.questionBankId,
    true
  );

  const formConfig = creatSearchConfig([...getFormSchema(formSchema)]);

  const dyCols = fields.map((o) => {
    return {
      title: o.columnName!,
      dataIndex: o.columnKey!,
      width: 200,
      customRender: ({ record }: { record: QuestionBankDetailListElement }) => {
        const _list = record.questionPropertyList || [];

        const _filters = _list.filter((i) => i.columnKey === o.columnKey);

        return h(
          'div',
          {},
          _filters.map((o) =>
            h(Tag, { color: 'blue', style: 'margin-right: 4px' }, () => o.columnValue)
          )
        );
      },
    };
  });

  setColumns(getBasicColumns(dyCols));

  const form = getForm();

  await form.setProps(formConfig);

  form.resetFields();
}

watch(
  () => props.questionBankId,
  () => {
    selectedRowKeys.value = [];
    loadQuestionBankAynamic();
  }
);

const [registerTable, { reload, setColumns, getForm }] = useTable({
  api: async (params) => {
    if (!props.questionBankId) {
      return [];
    }
    return await V1ManageQuestionBankGetQuestionBankPagePost(params);
  },
  columns: [],
  tableProps: {
    rowKey: 'id',
    showIndexColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 200,
    },
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onChange: (keys: (string | number)[]) => {
        selectedRowKeys.value = keys;
      },
      getCheckboxProps: (record: any) => ({
        disabled: record.status === 1,
      }),
    },
    beforeFetch: (params: Record<string, any>) => {
      params.bankTypeId = props.questionBankId;

      return formatDynamicParams(params, 'questionPropertyList');
    },
  },
});

const handleAllConfirm = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要确认的题目');
    return;
  }
  handleConfirm(selectedRowKeys.value);
};

// 处理编辑
const handleEdit = (record: any) => {
  allowParamsConfig.value = false;
  editModalActions.openModal(true, {
    question: record,
  });
};

function createEmptyFillQuestion() {
  return {
    questionType: QUESTION_TYPE.FILL_IN_THE_BLANK,
    questionStem: '',
    wesId: '',
    source: '',
    answerAnalysis: '',
    questionBankOptionList: [
      {
        optionDesc: '',
        correctAnswer: 1,
      },
    ],
    correctAnswerList: [],
    generated: 0,
  };
}

// 处理新增
const handleAdd = () => {
  allowParamsConfig.value = true;
  editModalActions.openModal(true, {
    question: createEmptyFillQuestion(),
    questionBankId: props.questionBankId,
  });
};

async function updateQuestion(question) {
  // 填空题
  question.correctAnswerList = question.questionBankOptionList.reduce((acc, item, idx) => {
    if (item.correctAnswer === 1) {
      acc.push(item.optionDesc);
    }
    return acc;
  }, []);
  const api = question.id
    ? V1ManageQuestionBankUpdateQuestionBankPost
    : V1ManageQuestionBankInsertQuestionBankPost;

  await api({ ...question });
  reload();
  message.success('操作成功');
}

// 处理详情
const handleDetail = (record: any) => {
  record.type = record.questionType;
  previewModalActions.openModal(true, {
    question: record,
  });
};

const handleConfirm = async (ids: any) => {
  // 处理确认
  await V1ManageQuestionBankBatchCompleteQuestionBankPost({
    data: ids,
  });
  // 清空选中状态
  selectedRowKeys.value = [];
  // 刷新列表
  reload();
};

const handleDelete = async (record: any) => {
  // 处理删除
  await V1ManageQuestionBankDeleteQuestionBank({ id: record.id });
  // 刷新表格
  reload();
};

// 打开上传面板
function openUploadDrawer(type: UploadType) {
  const comMapper = {
    [UploadTypeEnum.AI_GENERATE]: aiGenerationDrawerRef.value!,

    [UploadTypeEnum.FILE_IMPORT]: questionsImportDrawerRef.value!,
  };

  comMapper[type]?.openDrawer(props.questionBankId);
}
</script>


<style lang="less" scoped>
.fix-btn-gap {
  .cddc-ant-btn  {
    margin-right: 0 !important;
  }
}
</style>
