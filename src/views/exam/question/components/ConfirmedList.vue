<template>
  <BasicTablePlus @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <a-space>
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleDetail(record)">详情</a>
          <BaseConfirm @confirm="handleDelete(record)"></BaseConfirm>
        </a-space>
      </template>
    </template>
  </BasicTablePlus>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useTable } from '@/components/BasicTablePlus/useTable';
import { getBasicColumns } from '../data';

export default defineComponent({
  name: 'ConfirmedList',
  setup() {
    const [registerTable, { reload }] = useTable({
      api: null,
      columns: getBasicColumns(),
      tableProps: {
        rowKey: 'id',
        bordered: true,
        showIndexColumn: true,
        scroll: { x: 1200 },
        actionColumn: {
          width: 160,
        },
      },
    });

    const handleAdd = () => {
      // 处理新增
    };

    const handleEdit = (record: any) => {
      // 处理编辑
    };

    const handleDetail = (record: any) => {
      // 处理详情
    };

    const handleDelete = async (record: any) => {
      // 处理删除
      // 刷新表格
      reload();
    };

    return {
      registerTable,
      handleAdd,
      handleEdit,
      handleDetail,
      handleDelete,
    };
  },
});
</script>

<style lang="less" scoped>
.tool-left {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
</style>
