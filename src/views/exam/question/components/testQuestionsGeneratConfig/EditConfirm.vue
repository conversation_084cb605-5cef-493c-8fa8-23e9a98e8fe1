<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { type CommonEditProps, type IQuestion } from '/@/components/Examination/Questions/common';
import { number2alphaChar } from '/@/components/Examination/utils';
import { computed } from 'vue';
import type { Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from '/@/components/Examination/Questions/RichTextEditor.vue';
import type { QuestionOtherControlProps } from '../testQuestionsGeneratConfig/type';
import { sleep } from '/@/utils';

const props = defineProps<CommonEditProps & QuestionOtherControlProps>();

const emit = defineEmits(['update:value']);

const vValue = useVModel(props, 'value', emit);

function requiredMark() {
  const { questionStem } = vValue.value;

  if (questionStem && questionStem.trim()) {
    return true;
  }

  if (questionAnswer.value) {
    return true;
  }
  return false;
}

async function validateFields() {
  if (!props.disabled) {
    await sleep(0);
    const keys = Object.keys(questionFormRules.value);
    const pathNames = keys.map((o) => [...props.pathPre, o]);
    props.formRef?.validateFields(pathNames);
  }
}

const questionFormRules = computed((): Record<string, Rule | Rule[]> => {
  const required = requiredMark();
  validateFields();
  return {
    questionStem: [
      {
        required,
        message: '请输入题干',
        trigger: 'change',
      },
    ],
    questionBankOptionList: [
      {
        required,
        trigger: 'change',
        async validator(_rule, value: IQuestion['questionBankOptionList']) {
          if (!required) {
            return;
          }
          if (!value?.length) {
            throw new Error('请至少创建两个选项');
          }

          const hasEmptyOption = value.some((n) => !n.optionDesc);

          if (hasEmptyOption) {
            throw new Error('选项内容不能为空');
          }
        },
      },
    ],
    _answer: [
      {
        required,
        trigger: 'change',
        async validator() {
          if (!required) {
            return;
          }
          const hasAnswer = vValue.value.questionBankOptionList?.filter(
            (n) => n.correctAnswer === 1
          );
          if (!hasAnswer?.length) {
            throw new Error('请选择答案');
          }
        },
      },
    ],
  };
});

const questionAnswer = computed({
  get() {
    return vValue.value.questionBankOptionList?.find((item) => item.correctAnswer)?.id;
  },
  set(id: string) {
    vValue.value._answer = id;

    vValue.value.questionBankOptionList?.forEach((item) => {
      item.correctAnswer = item.id === id ? 1 : 0;
    });
  },
});
</script>

<template>
  <a-form-item
    label="判断题干"
    :name="[...pathPre, 'questionStem']"
    :rules="questionFormRules.questionStem"
  >
    <RichTextEditor
      :disabled="disabled"
      v-model:value="vValue.questionStem"
      :maxlength="200"
      showCount
      placeholder="请输入"
    />
  </a-form-item>
  <a-form-item
    label="选项"
    :name="[...pathPre, 'questionBankOptionList']"
    :rules="questionFormRules.questionBankOptionList"
  >
    <div
      class="flex-inline items-center gap-1 mr-4"
      v-for="(item, idx) in vValue.questionBankOptionList"
      :key="item.id"
    >
      <span> {{ number2alphaChar(idx) }} </span>
      <span> {{ item.optionDesc }} </span>
    </div>
  </a-form-item>

  <a-form-item label="答案" :name="[...pathPre, '_answer']" :rules="questionFormRules._answer">
    <a-radio-group v-model:value="questionAnswer" :disabled="disabled">
      <a-radio v-for="(item, idx) in vValue.questionBankOptionList" :key="item.id" :value="item.id">
        {{ number2alphaChar(idx) }}
      </a-radio>
    </a-radio-group>
  </a-form-item>
</template>

<style lang="less" scoped></style>
