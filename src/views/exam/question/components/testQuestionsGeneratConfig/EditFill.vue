<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { VueDraggable } from 'vue-draggable-plus';
import {
  type CommonEditProps,
  type IQuestion,
  type IQuestionOption,
} from '/@/components/Examination/Questions/common';
import { buildUUID } from '/@/utils/uuid';
import { remove } from 'lodash-es';
import { SvgIcon, type Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from '/@/components/Examination/Questions/RichTextEditor.vue';
import type { QuestionOtherControlProps } from '../testQuestionsGeneratConfig/type';
import { computed, nextTick } from 'vue';
import { sleep } from '/@/utils';

const props = defineProps<CommonEditProps & QuestionOtherControlProps>();

const emit = defineEmits(['update:value']);

const vValue = useVModel(props, 'value', emit);

function requiredMark() {
  const { questionStem, questionBankOptionList = [] } = vValue.value;

  if (questionStem && questionStem.trim()) {
    return true;
  }
  if (questionBankOptionList.some((n) => n.optionDesc && n.optionDesc.trim())) {
    return true;
  }
  return false;
}

async function validateFields() {
  if (!props.disabled) {
    await sleep(0);
    const keys = Object.keys(questionFormRules.value);
    const pathNames = keys.map((o) => [...props.pathPre, o]);
    props.formRef?.validateFields(pathNames);
  }
}

const questionFormRules = computed((): Record<string, Rule | Rule[]> => {
  const required = requiredMark();
  validateFields();
  return {
    questionStem: [
      {
        required,
        message: '请输入题干',
        trigger: 'change',
      },
    ],
    questionBankOptionList: [
      {
        required,
        trigger: 'change',
        async validator(_rule, value: IQuestion['questionBankOptionList']) {
          if (!required) {
            return;
          }
          if (!value?.length) {
            throw new Error('请至少创建一个填空');
          }

          const hasEmptyOption = value.some((n) => !n.optionDesc);

          if (hasEmptyOption) {
            throw new Error('填空内容不能为空');
          }
        },
      },
    ],
  };
});

function addOption() {
  vValue.value.questionBankOptionList ||= [];

  vValue.value.questionBankOptionList.push({
    id: buildUUID(),
    correctAnswer: 1,
    optionDesc: '',
  });
}

function deleteOption(item: IQuestionOption) {
  remove(vValue.value.questionBankOptionList || [], (n) => n.id === item.id);
  nextTick(() => {
    const pass = (vValue.value.questionBankOptionList || []).every((n) => !!n.optionDesc);
    if (pass) {
      props.formRef?.clearValidate([...props.pathPre, 'questionBankOptionList']);
    }
  })
}
</script>

<template>
  <a-form-item
    label="填空题干"
    :name="[...pathPre, 'questionStem']"
    :rules="questionFormRules.questionStem"
  >
    <RichTextEditor
      :disabled="disabled"
      v-model:value="vValue.questionStem"
      :maxlength="200"
      showCount
      placeholder="请输入"
    />
  </a-form-item>
  <div v-if="!disabled">
    <a-button class="add-question-btn" :disabled="(vValue.questionBankOptionList || []).length >= 20" type="link" @click="addOption">
      <SvgIcon name="g-plus" />
      <span> 添加填空 </span>
    </a-button>
  </div>

  <a-form-item
    label="答案"
    :name="[...pathPre, 'questionBankOptionList']"
    :rules="questionFormRules.questionBankOptionList"
  >
    <VueDraggable
      class="flex flex-col gap-1"
      v-model="vValue.questionBankOptionList!"
      handle=".darg-handle"
    >
      <div
        class="flex items-center gap-2"
        v-for="(item, idx) in vValue.questionBankOptionList"
        :key="item.id"
      >
        <span class="darg-handle">
          <SvgIcon name="g-sort-handle" />
        </span>
        <span> {{ idx + 1 }} </span>
        <span class="flex-1 w-0">
          <a-input v-model:value="item.optionDesc" placeholder="请输入" :maxlength="200" :disabled="disabled" />
        </span>
        <a-button
          v-if="!disabled"
          type="link"
          class="!pl-0"
          @click="deleteOption(item)"
          :disabled="(vValue.questionBankOptionList?.length || 0) < 2"
        >
          <SvgIcon name="g-trash" />
        </a-button>
      </div>
    </VueDraggable>
  </a-form-item>
</template>

<style lang="less" scoped>
.darg-handle {
  cursor: move;
  display: flex;

  color: #999999;
}

.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
