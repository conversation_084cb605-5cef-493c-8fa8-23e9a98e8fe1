import { QUESTION_TYPE, QuestionTypeOptions } from '/@/components/Examination/Questions/enum';
import { type IQuestion } from '/@/components/Examination/Questions/common';
import type { TestQuestionsGeneratConfigProps, QuestionCaseGroupConfig } from './type';
import { buildUUID } from '/@/utils/uuid';

export function creatEmptyQuestionItem(questionType: `${QUESTION_TYPE}`): IQuestion | undefined {
  const _uuid = buildUUID();
  switch (questionType) {
    // 填空题
    case QUESTION_TYPE.SHORT_ANSWER:
    case QUESTION_TYPE.FILL_IN_THE_BLANK:
      return {
        _uuid,
        questionType,
        questionStem: '',
        questionBankOptionList: [
          {
            optionDesc: '',
            correctAnswer: 1,
            id: buildUUID(),
          },
        ],
      };
    // 单选题
    case QUESTION_TYPE.SINGLE_CHOICE:
      return {
        _uuid,
        questionType,
        questionStem: '',
        questionBankOptionList: [
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
        ],
      };

    // 判断题
    case QUESTION_TYPE.JUDGMENT:
      return {
        _uuid,
        questionType,
        questionStem: '',
        questionBankOptionList: [
          {
            optionDesc: '对',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '错',
            correctAnswer: 0,
            id: buildUUID(),
          },
        ],
      };

    case QUESTION_TYPE.MULTIPLE_CHOICE:
      return {
        _uuid,
        questionType,
        questionStem: '',
        questionBankOptionList: [
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
          {
            optionDesc: '',
            correctAnswer: 0,
            id: buildUUID(),
          },
        ],
      };

    default: {
      return;
    }
  }
}

export function creatEmptyQuestionCaseGroupConfig(
  isBlankQuestionTypes?: boolean
): QuestionCaseGroupConfig {
  const initQuestionTypes = !isBlankQuestionTypes ? QuestionTypeOptions.map((n) => n.value) : [];

  return {
    stemContentList: [''],

    questionType: [...initQuestionTypes],

    stemPointNum: null,

    questionPoint: '',

    referenceCase: initQuestionTypes.map((qType) => creatEmptyQuestionItem(qType)!),

    preQuestionTypes: [...initQuestionTypes],
  };
}

export function creatInitGeneratConfig(
  isBlankQuestionTypes?: boolean
): TestQuestionsGeneratConfigProps {
  return {
    fileList: [],
    bankTypeId: undefined,

    questionBankTypeColumnMappingList: [{}, {}],

    questionBankGenerateConfigList: [creatEmptyQuestionCaseGroupConfig(isBlankQuestionTypes)],
  };
}
