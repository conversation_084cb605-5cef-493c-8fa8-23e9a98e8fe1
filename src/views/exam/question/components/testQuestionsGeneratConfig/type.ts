import { QUESTION_TYPE } from '/@/components/Examination/Questions/enum';
import {
  type IQuestion,
} from '/@/components/Examination/Questions/common';
import type { QuestionBankTypeColumnMappingListElement } from '/@/api/cddc.model'
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
export interface QuestionCaseGroupConfig {
  promptId?: string
  promptName?: string
  promptContent?: string;

  stemContentList: string [];

  questionType: `${QUESTION_TYPE}`[];

  stemPointNum?: null | number;

  questionPoint?: string;

  referenceCase: IQuestion[];

  [property: string]: any;

}

export interface TestQuestionsGeneratConfigProps {

  bankTypeId?: string;

  fileList: any[]

  questionBankTypeColumnMappingList: QuestionBankTypeColumnMappingListElement[];

  questionBankGenerateConfigList: QuestionCaseGroupConfig[]
}


export interface QuestionOtherControlProps {
   pathPre: (number | string) []
   disabled?: boolean
   formRef:FormInstance
}
