<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { VueDraggable } from 'vue-draggable-plus';
import {
  type CommonEditProps,
  type IQuestion,
  type IQuestionOption,
} from '/@/components/Examination/Questions/common';
import { number2al<PERSON><PERSON>har } from '/@/components/Examination/utils';
import { computed, nextTick } from 'vue';
import { buildUUID } from '/@/utils/uuid';
import { remove } from 'lodash-es';
import { SvgIcon, type Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from '/@/components/Examination/Questions/RichTextEditor.vue';
import type { QuestionOtherControlProps } from '../testQuestionsGeneratConfig/type';
import { sleep } from '/@/utils';

const props = defineProps<CommonEditProps & QuestionOtherControlProps>();

const emit = defineEmits(['update:value']);

const vValue = useVModel(props, 'value', emit);

function requiredMark() {
  const { questionStem, questionBankOptionList = [] } = vValue.value;

  if (questionStem && questionStem.trim()) {
    return true;
  }
  if (questionBankOptionList.some((n) => n.optionDesc && n.optionDesc.trim())) {
    return true;
  }
  if (questionAnswer.value?.length) {
    return true;
  }
  return false;
}

async function validateFields() {
  if (!props.disabled) {
    await sleep(0);
    const keys = Object.keys(questionFormRules.value);
    const pathNames = keys.map((o) => [...props.pathPre, o]);
    props.formRef?.validateFields(pathNames);
  }
}

const questionFormRules = computed((): Record<string, Rule | Rule[]> => {
  const required = requiredMark();
  validateFields();
  return {
    questionStem: [
      {
        required,
        message: '请输入题干',
        trigger: 'change',
      },
    ],
    questionBankOptionList: [
      {
        required,
        trigger: 'change',
        async validator(_rule, value: IQuestion['questionBankOptionList']) {
          if (!required) {
            return;
          }
          if (!value?.length) {
            throw new Error('请至少创建两个选项');
          }

          const hasEmptyOption = value.some((n) => !n.optionDesc);

          if (hasEmptyOption) {
            throw new Error('选项内容不能为空');
          }
        },
      },
    ],
    _answer: [
      {
        required,
        trigger: 'change',
        async validator() {
          if (!required) {
            return;
          }
          const hasAnswer = vValue.value.questionBankOptionList?.filter(
            (n) => n.correctAnswer === 1
          );
          if (!hasAnswer?.length) {
            throw new Error('请选择答案');
          }
        },
      },
    ],
  };
});

function addOption() {
  vValue.value.questionBankOptionList ||= [];

  vValue.value.questionBankOptionList.push({
    id: buildUUID(),
    correctAnswer: 0,
    optionDesc: '',
  });
}

function deleteOption(item: IQuestionOption) {
  remove(vValue.value.questionBankOptionList || [], (n) => n.id === item.id);
  nextTick(() => {
    const pass = (vValue.value.questionBankOptionList || []).every((n) => !!n.optionDesc);
    if (pass) {
      props.formRef?.validateFields([[...props.pathPre, 'questionBankOptionList']]);
    }
  })
}

const questionAnswer = computed({
  get() {
    return vValue.value.questionBankOptionList
      ?.filter((item) => item.correctAnswer)
      ?.map((item) => item.id);
  },
  set(ids: string[] = []) {
    vValue.value._answer = ids;

    vValue.value.questionBankOptionList?.forEach((item) => {
      item.correctAnswer = ids.includes(item.id!) ? 1 : 0;
    });
  },
});

const answerOpts = computed(() => {
  return vValue.value.questionBankOptionList?.map((item, idx) => {
    return {
      label: number2alphaChar(idx),
      value: item.id,
    };
  });
});
</script>

<template>
  <a-form-item
    label="多选题干"
    :name="[...pathPre, 'questionStem']"
    :rules="questionFormRules.questionStem"
  >
    <RichTextEditor
      :disabled="disabled"
      v-model:value="vValue.questionStem"
      :maxlength="200"
      placeholder="请输入"
      showCount
    />
  </a-form-item>
  <div v-if="!disabled">
    <a-button class="add-question-btn" :disabled="(vValue.questionBankOptionList || []).length >= 20" type="link" @click="addOption">
      <SvgIcon name="g-plus" />
      <span> 添加选项 </span>
    </a-button>
  </div>

  <a-form-item
    label="选项"
    :name="[...pathPre, 'questionBankOptionList']"
    :rules="questionFormRules.questionBankOptionList"
  >
    <VueDraggable
      class="flex flex-col gap-1"
      v-model="vValue.questionBankOptionList!"
      handle=".darg-handle"
      :disabled="disabled"
    >
      <div
        class="flex items-center gap-2"
        v-for="(item, idx) in vValue.questionBankOptionList"
        :key="item.id"
      >
        <span class="darg-handle" v-if="!disabled">
          <SvgIcon name="g-sort-handle" />
        </span>
        <span> {{ number2alphaChar(idx) }} </span>
        <span class="flex-1 w-0">
          <a-input v-model:value="item.optionDesc" placeholder="请输入" :disabled="disabled" :maxlength="200" />
        </span>
        <a-button
          v-if="!disabled"
          type="link"
          class="!pl-0"
          @click="deleteOption(item)"
          :disabled="(vValue.questionBankOptionList?.length || 0) < 3"
        >
          <SvgIcon name="g-trash" />
        </a-button>
      </div>
    </VueDraggable>
  </a-form-item>

  <a-form-item label="答案" :name="[...pathPre, '_answer']" :rules="questionFormRules._answer">
    <a-checkbox-group :disabled="disabled" v-model:value="questionAnswer" :options="answerOpts" />
  </a-form-item>
</template>

<style lang="less" scoped>
.darg-handle {
  cursor: move;
  display: flex;

  color: #999999;
}

.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
