<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import { QuestionTypeOptions } from '/@/components/Examination/Questions/enum';
import type {
  QuestionCaseGroupConfig,
  TestQuestionsGeneratConfigProps,
} from '../testQuestionsGeneratConfig/type';
import CommonFileUpload from '/@/components/Upload/CommonFileUpload.vue';
import ApiSelect from '@geega-ui-plus/geega-ui/admin-ui/Form/src/components/ApiSelect.vue';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { remove } from 'lodash-es';
import { QUESTION_TYPE } from '/@/components/Examination/Questions/enum';
import { creatEmptyQuestionCaseGroupConfig, creatEmptyQuestionItem } from './utils';
import { getPopupContainer } from '/@/utils';
import { type UploadRealFile } from '/@/components/Upload/types';
import {
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
  V1ManageQuestionBankTypeGetQuestionBankTypeById,
  V1ManageQuestionBankColumnGetQuestionBankColumnById,
  V1ManageQuestionBankColumnMappingListPost,
  V1ManagePromptsBankTypeId,
} from '/@/api/cddc.req';
import { getFileExt } from '/@/utils/file/download';
import { useAsyncData } from '/@/composables';
import { SUPPORTED_FILE_TYPE } from '/@/enums/promptConfigEnum';

const props = defineProps<{
  config: TestQuestionsGeneratConfigProps;
  disabled?: boolean;
  hideUploadFile?: boolean;
  fileName?: string;
}>();

const emit = defineEmits(['update:config']);

const formData = useVModel(props, 'config', emit);

interface Opt {
  label: string;
  value: string;
  [key: string]: string;
}

const questionBankColumn = ref<Opt[]>([]);

const formRef = ref<FormInstance>();

const formWrapRef = ref<HTMLElement>();

const commonFileUploadProps = {
  maxSize: 20 * 1024,
  maxCount: 1,
  accept: '.docx,.xlsx',
  fileItemActionBtns: [
    {
      btnName: '删除',
      key: 'delete',
    },
  ],
  fileItemClick: (key: string, _file: UploadRealFile, actions: any) => {
    if (key === 'delete') actions.remove();
  },
};

const promptData = useAsyncData(V1ManagePromptsBankTypeId, []);

function getPromptOptions(item: QuestionCaseGroupConfig) {
  const options = promptData.data.value;
  if (item.promptId && !options.find((n) => n.id == item.promptId)) {
    const promptItem = {
      id: item.promptId,
      name: item.promptName,
      content: item.promptContent,
    };

    return [promptItem, ...options];
  }

  const file: File | null = formData.value.fileList.at(0);

  if (!file?.name) {
    return options;
  }

  const suffix = file.name.split('.').pop();

  const fileType = suffix as SUPPORTED_FILE_TYPE;

  return options.filter((n) => n.fileType === fileType);
}

async function getFormData() {
  await formRef.value!.validateFields();

  const apiParamsData = {
    ...formData.value,
    questionBankGenerateConfigList: formData.value.questionBankGenerateConfigList.map((item) => {
      const promptItem = promptData.data.value.find((n) => n.id === item.promptId);

      return {
        ...item,
        promptName: promptItem?.name,
        promptContent: promptItem?.content,
      };
    }),
  };

  return apiParamsData;
}

function questionTypeChange(idx: number, value: string[] = []) {
  if (props.disabled) return;

  const item = formData.value.questionBankGenerateConfigList[idx];

  const preQuestionTypes = item.preQuestionTypes || [];

  const logicDiff = (longArr: string[], shortArr: string[]) => {
    return longArr.find((item) => !shortArr.includes(item))!;
  };

  if (value.length < preQuestionTypes.length) {
    const diffQuestionTypes = logicDiff(preQuestionTypes, value);

    remove(item.referenceCase || [], (i) => i.questionType === diffQuestionTypes);
  }
  if (value.length > preQuestionTypes.length) {
    const diffQuestionTypes = logicDiff(value, preQuestionTypes);

    const hasThisType = item.referenceCase.some((i) => i.questionType === diffQuestionTypes);

    if (!hasThisType) {
      const _q = creatEmptyQuestionItem(diffQuestionTypes as `${QUESTION_TYPE}`)!;

      if (_q) {
        item.referenceCase.push(_q);
      }
    }
  }
  item.preQuestionTypes = value;
}

async function getBankTypes() {
  const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({});
  return resp?.map((o) => ({ label: o.name!, value: o.id! }));
}

async function loadQuestionBankColumn() {
  questionBankColumn.value = [];

  if (!formData.value.bankTypeId) return;

  const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeById({
    typeId: formData.value.bankTypeId,
  });

  const _list = (resp.questionBankTypeColumns || []).filter((o) => !!o.questionBankColumnDTO);

  questionBankColumn.value = _list.map((o) => {
    const { columnKey, columnName, id } = o.questionBankColumnDTO || {};
    return {
      label: columnName!,
      value: columnKey!,
      id: id!,
    };
  });
}

async function onBankTypeChanged() {
  formData.value.questionBankTypeColumnMappingList = [{}];
  loadQuestionBankColumn();

  {
    // update prompt contents
    if (formData.value.bankTypeId) {
      await promptData.load({
        bankTypeId: formData.value.bankTypeId!,
      });
    }

    formData.value.questionBankGenerateConfigList?.forEach((item) => {
      item.promptContent = undefined;
    });
  }
}

function clacAbleOpts(currentKey?: string) {
  const keys = formData.value.questionBankTypeColumnMappingList
    .map((o) => o.questionBankColumnKey)
    .filter(Boolean);

  return questionBankColumn.value.filter((item) => {
    return !keys.some((key) => key === item.value) || currentKey === item.value;
  });
}
/**
 * 第三方字段名称
 */
const columnKeyOfColumnIdAndKey: Record<string, Opt[]> = {};

async function getByColumnIdAndKeyApi(columnKey?: string): Promise<Opt[]> {
  if (!columnKey || props.disabled) return [];

  if (Object.keys(columnKeyOfColumnIdAndKey).includes(columnKey)) {
    return columnKeyOfColumnIdAndKey[columnKey];
  }

  const columnId = questionBankColumn.value.find((o) => o.value === columnKey)?.id;

  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnById({
    columnId,
  });

  const opts = (resp.questionBankColumnPropertyList || []).map((o) => ({
    label: o.propertyName!,
    value: o.propertyKey!,
  }));

  columnKeyOfColumnIdAndKey[columnKey] = opts;

  return opts;
}

/**
 * 第三方字段值
 */
const columnPropertyKeyOfColumnIdAndValue: Record<string, Opt[]> = {};

async function getByColumnIdAndValueApi(columnKey?: string, propertyKey?: string): Promise<Opt[]> {
  if (!columnKey || !propertyKey || props.disabled) return [];

  const keyStr = `_${columnKey}__${propertyKey}_`;

  if (Object.keys(columnPropertyKeyOfColumnIdAndValue).includes(keyStr)) {
    return columnPropertyKeyOfColumnIdAndValue[keyStr];
  }

  const columnId = questionBankColumn.value.find((o) => o.value === columnKey)?.id;

  const resp = await V1ManageQuestionBankColumnMappingListPost({
    columnId,
  });

  const _list = (resp || []).filter((o) => o.propertyKey === propertyKey);

  const values = _list.map((o) => o.propertyValue!);

  const opts = [...new Set(values)].map((o) => {
    return {
      label: o,
      value: o,
    };
  });

  columnPropertyKeyOfColumnIdAndValue[keyStr] = opts;

  return opts;
}

const fileName = computed(() =>
  props.disabled ? props.fileName : (formData.value.fileList || [])[0]?.name
);

const thirdPartyFieldLable = computed(() => {
  const _fileName = fileName.value;

  const defaultName = '第三方字段';

  if (!_fileName) return defaultName;

  const ext = getFileExt(_fileName)!;

  if (['xlsx'].includes(ext)) {
    return '表格字段';
  } else if (['json'].includes(ext)) {
    return 'json字段';
  }
  return defaultName;
});

const showQuestionBankColumnPropertyValue = computed(() => {
  const _fileName = fileName.value;

  if (!_fileName) return false;

  const ext = getFileExt(_fileName)!;

  return ['docx'].includes(ext);
});

const showStemContentList = computed(() => {
  const _fileName = fileName.value;

  if (!_fileName) return false;

  const ext = getFileExt(_fileName)!;

  return ['xlsx', 'json'].includes(ext);
});

function getShowStemContentList() {
  return showStemContentList.value;
}

watch(
  () => formData.value.fileList,
  () => {
    const file: File = formData.value.fileList.at(0);

    if (!file) return;

    const configList = formData.value.questionBankGenerateConfigList || [];
    configList.map((item) => {
      item.promptId = undefined;
    });
  },
  {
    deep: true,
  }
);

defineExpose({ getFormData, changeBankType: onBankTypeChanged, getShowStemContentList });
</script>

<template>
  <div ref="formWrapRef">
    <AForm ref="formRef" layout="horizontal" labelAlign="left" :model="formData">
      <AFormItem
        v-if="!hideUploadFile"
        name="fileList"
        :rules="[{ required: true, message: '请上传文件' }]"
        label="上传文件"
      >
        <CommonFileUpload
          v-bind="{
            ...commonFileUploadProps,
            disabled,
          }"
          v-model="formData.fileList"
        />
      </AFormItem>
      <div class="font-size-14px font-500">生成题库配置</div>
      <!-- 题库 -->
      <div class="pb-10px">
        <AFormItem name="bankTypeId" :rules="[{ required: true, message: '请选择' }]" label="题库:">
          <ApiSelect
            v-model:value="formData.bankTypeId"
            placeholder="请选择"
            :api="getBankTypes"
            @change="onBankTypeChanged"
            allow-clear
            :disabled="disabled"
            show-search
            option-filter-prop="label"
            :getPopupContainer="getPopupContainer"
          />
        </AFormItem>
      </div>

      <template v-if="false">
        <div class="font-size-14px font-500">字段映射配置</div>
        <!-- 字段映射配置 -->
        <div class="flex">
          <div class="flex-1 !w-[100%]"
            ><AFormItem
              name="questionBankTypeColumnMappingList"
              :rules="[{ required: true, message: '请新增' }]"
            >
              <div
                class="pb-0px flex"
                v-for="(item, idx) in formData.questionBankTypeColumnMappingList"
              >
                <div
                  :class="[
                    showQuestionBankColumnPropertyValue
                      ? '!w-[calc(33%-25px)]'
                      : '!w-[calc(50%-35px)]',
                  ]"
                >
                  <AFormItem
                    :name="['questionBankTypeColumnMappingList', idx, 'questionBankColumnKey']"
                    :rules="[{ required: true, message: '请选择' }]"
                    label="题库字段:"
                  >
                    <a-select
                      v-model:value="item.questionBankColumnKey"
                      placeholder="请选择"
                      :options="clacAbleOpts(item.questionBankColumnKey)"
                      allow-clear
                      show-search
                      :disabled="disabled"
                      option-filter-prop="label"
                      :getPopupContainer="getPopupContainer"
                      @change="
                        () => {
                          if (!item.questionBankColumnKey) {
                            item.questionBankColumnName = undefined;
                          } else {
                            item.questionBankColumnName = questionBankColumn.find(
                              (o) => o.value === item.questionBankColumnKey
                            )?.label;
                          }
                          item.questionBankColumnPropertyName = undefined;
                          item.questionBankColumnPropertyKey = undefined;
                          item.questionBankColumnPropertyValue = undefined;
                        }
                      "
                    >
                      <template
                        #notFoundContent
                        v-if="formData.bankTypeId && !questionBankColumn?.length"
                      >
                        <div>
                          <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
                        </div>
                      </template>
                    </a-select>
                  </AFormItem>
                </div>
                <div class="flex-1 h-10px"></div>
                <div
                  :class="[
                    showQuestionBankColumnPropertyValue
                      ? '!w-[calc(33%-25px)]'
                      : '!w-[calc(50%-35px)]',
                  ]"
                >
                  <AFormItem
                    :name="[
                      'questionBankTypeColumnMappingList',
                      idx,
                      'questionBankColumnPropertyKey',
                    ]"
                    :rules="[{ required: true, message: '请选择' }]"
                    :label="`${thirdPartyFieldLable}:`"
                  >
                    <ApiSelect
                      :key="`_${item.questionBankColumnKey}_`"
                      v-model:value="item.questionBankColumnPropertyKey"
                      placeholder="请选择"
                      :api="() => getByColumnIdAndKeyApi(item.questionBankColumnKey)"
                      :disabled="disabled"
                      @change="
                        () => {
                          if (!item.questionBankColumnPropertyKey) {
                            item.questionBankColumnPropertyName = undefined;
                          } else {
                            const _opts = item.questionBankColumnKey
                              ? columnKeyOfColumnIdAndKey[item.questionBankColumnKey] || []
                              : [];
                            item.questionBankColumnPropertyName = _opts.find(
                              (o) => o.value === item.questionBankColumnPropertyKey
                            )?.label;
                          }
                          item.questionBankColumnPropertyValue = undefined;
                        }
                      "
                      allow-clear
                      show-search
                      option-filter-prop="label"
                      :getPopupContainer="getPopupContainer"
                    >
                      <template
                        #notFoundContent
                        v-if="
                          (formData.bankTypeId && !questionBankColumn?.length) ||
                          (formData.bankTypeId && item.questionBankColumnKey)
                        "
                      >
                        <div>
                          <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
                        </div>
                      </template> </ApiSelect></AFormItem
                ></div>
                <div v-if="showQuestionBankColumnPropertyValue" class="flex-1 h-10px"></div>
                <div v-if="showQuestionBankColumnPropertyValue" class="!w-[calc(33%-25px)]">
                  <AFormItem
                    :name="[
                      'questionBankTypeColumnMappingList',
                      idx,
                      'questionBankColumnPropertyValue',
                    ]"
                    :rules="[{ required: true, message: '请选择' }]"
                    :label="`${thirdPartyFieldLable}值:`"
                  >
                    <ApiSelect
                      :key="`_${item.questionBankColumnKey}_${item.questionBankColumnPropertyKey}_`"
                      v-model:value="item.questionBankColumnPropertyValue"
                      placeholder="请选择"
                      :api="
                        () =>
                          getByColumnIdAndValueApi(
                            item.questionBankColumnKey,
                            item.questionBankColumnPropertyKey
                          )
                      "
                      :disabled="disabled"
                      allow-clear
                      show-search
                      option-filter-prop="label"
                      :getPopupContainer="getPopupContainer" /></AFormItem
                ></div>
                <div class="!w-40px justify-end flex items-end pb-8px" v-if="!disabled">
                  <a-button
                    :disabled="formData.questionBankTypeColumnMappingList.length <= 1"
                    type="link"
                    class="!px-4px !mx-0"
                    @click="
                      () => {
                        formData.questionBankTypeColumnMappingList.splice(idx, 1);
                      }
                    "
                  >
                    <SvgIcon name="g-trash" />
                  </a-button>
                </div>
              </div> </AFormItem
          ></div>
        </div>
        <div class="flex justify-center pb-10px" v-if="!disabled"
          ><AButton
            type="link"
            class="add-question-btn"
            :disabled="formData.questionBankTypeColumnMappingList.length >= 20"
            @click="
              () => {
                formData.questionBankTypeColumnMappingList.push({});
              }
            "
          >
            <SvgIcon name="g-plus" /> 新增字段映射
          </AButton></div
        >
      </template>
      <div class="font-size-14px font-500">生成试题配置</div>

      <div
        class="question-item-wrap flex"
        v-for="(item, idx) in formData.questionBankGenerateConfigList"
      >
        <div class="flex-1">
          <div v-if="showStemContentList">
            <div
              v-for="(_stem, stemIndex) in formData.questionBankGenerateConfigList[idx]
                .stemContentList"
            >
              <AFormItem
                :name="['questionBankGenerateConfigList', idx, 'stemContentList', stemIndex]"
                :label="`${stemIndex === 0 ? '题干内容:' : ''}`"
              >
                <div class="flex">
                  <div class="flex-1">
                    <a-input
                      v-model:value="
                        formData.questionBankGenerateConfigList[idx].stemContentList[stemIndex]
                      "
                      :disabled="disabled"
                      placeholder="请输入"
                      :maxlength="40"
                      allow-clear
                    />
                  </div>
                  <div class="w-30px justify-end flex" v-if="!disabled">
                    <a-button
                      :disabled="
                        formData.questionBankGenerateConfigList[idx].stemContentList.length <= 1
                      "
                      type="link"
                      class="!px-4px !mx-0"
                      @click="
                        () => {
                          formData.questionBankGenerateConfigList[idx].stemContentList.splice(
                            stemIndex,
                            1
                          );
                        }
                      "
                    >
                      <SvgIcon name="g-trash" />
                    </a-button>
                  </div>
                </div>
              </AFormItem>
            </div>
            <div class="flex justify-center pb-10px" v-if="!disabled"
              ><AButton
                type="link"
                class="add-question-btn"
                :disabled="
                  formData.questionBankGenerateConfigList[idx].stemContentList.length >= 20
                "
                @click="
                  () => {
                    formData.questionBankGenerateConfigList[idx].stemContentList.push('');
                  }
                "
              >
                <SvgIcon name="g-plus" /> 新增题干内容
              </AButton></div
            >
          </div>
          <AFormItem
            :name="['questionBankGenerateConfigList', idx, 'questionType']"
            :rules="[{ required: true, message: '请选择' }]"
            label="生成考题类型:"
          >
            <a-checkbox-group
              :disabled="disabled"
              @change="(value: string[]) => questionTypeChange(idx, value)"
              v-model:value="item.questionType"
              :options="QuestionTypeOptions"
            />
          </AFormItem>
          <AFormItem
            :name="['questionBankGenerateConfigList', idx, 'stemPointNum']"
            :rules="[{ required: true, message: '请输入' }]"
            label="题干生成考点数量:"
          >
            <p class="!mb-0px">
              <a-input-number
                :disabled="disabled"
                v-model:value="item.stemPointNum"
                class="w-200px"
                :min="1"
                style="width: 100%"
                :max="10000"
                :precision="0"
                placeholder="请输入"
              />
            </p>
          </AFormItem>
          <AFormItem
            :name="['questionBankGenerateConfigList', idx, 'promptId']"
            :rules="[{ required: true, message: '请输入' }]"
            label="提示词组:"
          >
            <a-select
              :disabled="disabled"
              v-model:value="item.promptId"
              placeholder="请选择"
              :options="getPromptOptions(item)"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              allow-clear
            />
          </AFormItem>
        </div>
        <div class="w-30px justify-end flex" v-if="!disabled">
          <a-button
            type="link"
            :disabled="formData.questionBankGenerateConfigList.length <= 1"
            class="!px-4px !mx-0"
            @click="
              () => {
                formData.questionBankGenerateConfigList.splice(idx, 1);
              }
            "
          >
            <SvgIcon name="g-trash" />
          </a-button>
        </div>
      </div>
      <div class="flex justify-center pb-10px" v-if="!disabled"
        ><AButton
          type="link"
          class="add-question-btn"
          :disabled="formData.questionBankGenerateConfigList.length >= 20"
          @click="
            () => {
              formData.questionBankGenerateConfigList.push(creatEmptyQuestionCaseGroupConfig());
            }
          "
        >
          <SvgIcon name="g-plus" /> 新增生成题干
        </AButton></div
      >
    </AForm>
  </div>
</template>

<style lang="less" scoped>
.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
.question-item-wrap {
  padding: 10px 10px 0px 10px;
  margin: 5px 0 10px 0px;
  border: 1px dashed #bfbfbf;
  border-radius: 2px;
}
</style>
