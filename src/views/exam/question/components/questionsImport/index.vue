<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LeftConfig from './LeftConfig.vue';
import {
  V1ManageQuestionBankImportQuestionByExcelPost,
  V1ManageQuestionBankImportQuestionByWordPost,
  V1ManageQuestionBankImportQuestionBatchInsertPost,
} from '/@/api/cddc.req';
import type {
  V1ManageQuestionBankImportQuestionByExcelPostRequestBody,
  QuestionBankSaveDTOListElement,
} from '/@/api/cddc.model';
import { type IQuestion } from '/@/components/Examination/Questions';
import MultipleQuestionsPreview from '/@/components/Examination/MultipleQuestionsPreview.vue';
import { useLoadingFn } from '/@/composables';
import { cloneDeep, debounce } from 'lodash-es';
import { message } from '@geega-ui-plus/ant-design-vue';
import { getFileExt } from '/@/utils/file/download';
import { buildUUID } from '/@/utils/uuid';
import { ExclamationCircleOutlined } from '@geega-ui-plus/icons-vue';

const emit = defineEmits(['cancel', 'reload']);

const props = defineProps<{
  questionBankId?: string;
}>();

type FormParams = V1ManageQuestionBankImportQuestionByExcelPostRequestBody;

const leftConfigRef = ref<InstanceType<typeof LeftConfig>>();

const questions = ref<IQuestion[]>([]);

const storageForm = ref<FormParams>();

const leftCongfigForm = ref({
  fileList: [],
  bankTypeId: props.questionBankId,
  questionPropertyList: [{}]
})

onMounted(() => {
  leftConfigRef.value!.changeBankType()
})



// 生成试题
const buildQuestions = useLoadingFn(async () => {
  const _data = await leftConfigRef.value!.getFormData();

  const file = _data.fileList[0];

  const params = {
    bankTypeId: _data.bankTypeId,

    questionPropertyList: _data.questionPropertyList,

    fileId: file.response.id!,
  } as FormParams;

  const ext = getFileExt(file.name)!;

  const resp = await importQuestionApi(ext)(params);

  questions.value = (resp || []).map((o) => {
    return {
      ...o,
      id: buildUUID(),
    };
  });

  storageForm.value = cloneDeep(params);
});

function importQuestionApi(ext: string) {
  if (['xlsx'].includes(ext)) {
    return V1ManageQuestionBankImportQuestionByExcelPost;
  }
  return V1ManageQuestionBankImportQuestionByWordPost;
}

const save = debounce(async () => {
  const questionBankList = questions.value.map((item) => {
    const { id, status, _answer, questionBankOptionList = [], ...o } = item;

    const correctAnswerList = questionBankOptionList.reduce((acc, item) => {
      if (!!item.correctAnswer) {
        acc.push(item.optionDesc!);
      }
      return acc;
    }, []);
    return {
      ...o,
      questionBankOptionList,
      correctAnswerList,
    };
  }) as QuestionBankSaveDTOListElement[];

  await V1ManageQuestionBankImportQuestionBatchInsertPost({
    ...storageForm.value!,
    questionBankList,
  });
  message.success('操作成功');
  emit('reload')

  emit('cancel');
}, 300);
</script>

<template>
  <div class="w-[100%] flex h-[calc(100vh-60px)]">
    <div
      class="box-border flex w-[45%] flex-col border-1px border-color-[#f4f6f8] border-r-solid px-16px h-100%"
    >
      <div class="h-[calc(100%-43px)] overflow-y-auto">
        <LeftConfig ref="leftConfigRef" v-model:config="leftCongfigForm"/>
      </div>
      <div class="flex justify-end pt-10px">
        <a-button type="primary" @click="buildQuestions()"> 生成试题</a-button>
      </div>
    </div>
    <div class="flex-1 px-16px h-100%">
      <div class="h-[calc(100%-43px)] overflow-y-auto">
        <a-spin :spinning="buildQuestions.loading" tip="正在生成题目...">
          <div calss="min-h-400px">
            <div v-if="!storageForm" class="pt-100px color-[#999] flex justify-center">
              <div class="pr-4px">
                <ExclamationCircleOutlined />
              </div>
              <div>完成右侧表单后点击 生成试题 按钮生成题目</div>
            </div>
            <MultipleQuestionsPreview allow-edit v-model:questions="questions" />
          </div>
        </a-spin>
      </div>
      <div class="flex justify-end pt-10px">
        <a-button type="primary" :disabled="!storageForm || !questions.length" @click="save">
          完成
        </a-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
