<script lang="ts" setup>
import { ref } from 'vue';
import { useVModel } from '@vueuse/core';
import CommonFileUpload from '/@/components/Upload/CommonFileUpload.vue';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { type UploadRealFile } from '/@/components/Upload/types';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import ApiSelect from '@geega-ui-plus/geega-ui/admin-ui/Form/src/components/ApiSelect.vue';
import { getPopupContainer } from '/@/utils';
import {
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
  V1ManageQuestionBankColumnMappingListPost,
  V1ManageQuestionBankTypeGetQuestionBankTypeById,
} from '/@/api/cddc.req';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { defHttp } from '/@/utils/http/axios';
import { downloadFile, resolveFileName } from '/@/utils/file/download';
import {
  V1ManageQuestionBankDownloadImportQuestionForExcelUrl,
  V1ManageQuestionBankDownloadImportQuestionForWordUrl,
} from '/@/api/paper/paper';

const props = defineProps<{
  config: FormData;
  disabled?: boolean;
  hideUploadFile?: boolean;
}>();

interface Opt {
  label: string;
  value: string;
}

const questionBankColumn = ref<Opt[]>([]);

const formRef = ref<FormInstance>();

export interface FormData {
  fileList: any[];
  bankTypeId?: string;
  questionPropertyList: {
    columnKey?: string;
    columnValue?: string;
  }[];
}

const emit = defineEmits(['update:config']);

const formData = useVModel(props, 'config', emit);

const commonFileUploadProps = {
  maxSize: 20 * 1024,
  maxCount: 1,
  accept: '.docx,.xlsx',
  fileItemActionBtns: [
    {
      btnName: '删除',
      key: 'delete',
    },
  ],
  fileItemClick: (key: string, _file: UploadRealFile, actions: any) => {
    if (key === 'delete') actions.remove();
  },
};

async function handleDownloadFile(opt: { url: string; fileName?: string }) {
  const resp = await defHttp.getStream({
    url: opt.url,
  });
  const _contentDisposition = resp.headers?.['content-disposition'];

  const fileName = resolveFileName(_contentDisposition) || opt.fileName;

  downloadFile(resp.data, { fileName });
}

// 下载模版
function dowloadTem(fileType: string) {
  const opt = {
    url:
      fileType === 'doc'
        ? V1ManageQuestionBankDownloadImportQuestionForWordUrl
        : V1ManageQuestionBankDownloadImportQuestionForExcelUrl,
    fileName: `试题导入模版.${fileType}`,
  };

  handleDownloadFile(opt);
}

async function loadQuestionBankColumn() {
  questionBankColumn.value = [];

  if (!formData.value.bankTypeId) return;

  const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeById({
    typeId: formData.value.bankTypeId,
  });

  const _list = (resp.questionBankTypeColumns || []).filter((o) => !!o.questionBankColumnDTO);

  questionBankColumn.value = _list.map((o) => {

    const  { columnKey, columnName, id } = o.questionBankColumnDTO || {}

    return {
      label: columnName!,
      value: columnKey!,
      id: id!,
    };
  });
}

function changeBankType() {
  loadQuestionBankColumn();
  formData.value.questionPropertyList = [
    {
      columnKey: undefined,
      columnValue: undefined,
    },
  ];
}

async function getBankTypes() {
  const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({});
  return resp?.map((o) => ({ label: o.name!, value: o.id! }));
}

const columnKeyOfColumnMappingList: Record<string, Opt[]> = {};

async function questionBankColumnMappingListApi(columnKey?: string): Promise<Opt[]> {
  if (!columnKey || props.disabled) return [];

  if (Object.keys(columnKeyOfColumnMappingList).includes(columnKey)) {
    return columnKeyOfColumnMappingList[columnKey];
  }

  const resp = await V1ManageQuestionBankColumnMappingListPost({
    columnKey,
  });

  const _opts = [...new Set((resp || []).map((o) => o.columnValue!))];

  const opts = _opts.map((o) => ({
    label: o,
    value: o,
  }));

  columnKeyOfColumnMappingList[columnKey] = opts;

  return opts;
}

function clacAbleOpts(currentKey?: string) {
  const keys = formData.value.questionPropertyList.map((o) => o.columnKey).filter(Boolean);

  return questionBankColumn.value.filter((item) => {
    return !keys.some((key) => key === item.value) || currentKey === item.value;
  });
}

async function getFormData() {
  await formRef.value!.validate();
  return formData.value;
}

defineExpose({
  getFormData,
  loadQuestionBankColumn,
  changeBankType,
});
</script>

<template>
  <div>
    <AForm ref="formRef" layout="vertical" :model="formData">
      <AFormItem
        name="bankTypeId"
        :rules="[{ required: true, message: '请选择导入题库' }]"
        label="导入题库"
        ><ApiSelect
          v-model:value="formData.bankTypeId"
          placeholder="请选择"
          :api="getBankTypes"
          @change="changeBankType"
          allow-clear
          show-search
          :disabled="disabled"
          option-filter-prop="label"
          :getPopupContainer="getPopupContainer"
        />
      </AFormItem>
      <div class="pt-10px pb-6px">
        <div class="flex !w-[100%]" v-for="(item, idx) in formData.questionPropertyList">
          <div class="!w-[calc(50%-35px)]">
            <AFormItem
              :name="['questionPropertyList', idx, 'columnKey']"
              :rules="[{ required: true, message: '请选择' }]"
              label="字段"
            >
              <a-select
                v-model:value="item.columnKey"
                placeholder="请选择"
                :options="clacAbleOpts(item.columnKey)"
                allow-clear
                show-search
                :disabled="disabled"
                option-filter-prop="label"
                :getPopupContainer="getPopupContainer"
                @change="
                  () => {
                    item.columnValue = undefined;
                  }
                "
              >
                <template
                  #notFoundContent
                  v-if="formData.bankTypeId && !questionBankColumn?.length"
                >
                  <div>
                    <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
                  </div>
                </template>
              </a-select>
            </AFormItem>
          </div>
          <div class="flex-1 justify-end flex pt-2px"></div>
          <div class="!w-[calc(50%-35px)]">
            <AFormItem
              :name="['questionPropertyList', idx, 'columnValue']"
              :rules="[{ required: true, message: '请选择' }]"
              label="值"
            >
              <ApiSelect
                :key="`_${item.columnKey}_`"
                v-model:value="item.columnValue"
                placeholder="请选择"
                :api="() => questionBankColumnMappingListApi(item.columnKey)"
                allow-clear
                show-search
                :disabled="disabled"
                option-filter-prop="label"
                :getPopupContainer="getPopupContainer"
              >
                <template
                  #notFoundContent
                  v-if="(formData.bankTypeId && !questionBankColumn?.length) || (formData.bankTypeId && item.columnKey)"
                >
                  <div>
                    <GeegaEmpty description="未配置字段，请前往题库字段配置完成配置" />
                  </div>
                </template> </ApiSelect></AFormItem
          ></div>
          <div class="!w-40px justify-end flex pt-23px" v-if="!disabled">
            <a-button
              type="link"
              class="!px-4px !mx-0"
              :disabled="(formData.questionPropertyList.length || 0) <= 1"
              @click="
                () => {
                  formData.questionPropertyList.splice(idx, 1);
                }
              "
            >
              <SvgIcon name="g-trash" />
            </a-button>
          </div>
        </div>
        <div class="flex justify-center" v-if="!disabled"
          ><AButton
            class="add-params"
            type="link"
            :disabled="formData.questionPropertyList.length >=20"
            @click="
              () => {
                formData.questionPropertyList.push({
                  columnKey: undefined,
                  columnValue: undefined,
                });
              }
            "
          >
            <SvgIcon name="g-plus" /> 新增
          </AButton></div
        >
      </div>
      <div v-if="!hideUploadFile">
        <div class="flex justify-between pb-10px">
          <a-button type="primary" ghost @click="dowloadTem('xlsx')"> Excel模版下载</a-button>
          <a-button type="primary" ghost @click="dowloadTem('doc')"> Word模版下载</a-button>
        </div>
        <AFormItem
          name="fileList"
          :rules="[{ required: true, message: '请上传文件' }]"
          label="上传文件"
        >
          <CommonFileUpload v-bind="commonFileUploadProps" v-model="formData.fileList" />
        </AFormItem>
      </div>
    </AForm>
  </div>
</template>

<style lang="less" scoped>
.add-params {
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
