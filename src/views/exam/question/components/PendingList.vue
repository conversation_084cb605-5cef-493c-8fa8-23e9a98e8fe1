<template>
  <BasicTablePlus @register="registerTable">
    <template #toolbar>
      <a-button type="primary" ghost @click="handleAdd">批量确定</a-button>
    </template>

    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <a-space>
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleDetail(record)">详情</a>
          <a @click="handleConfirm(record)">确认</a>
          <BaseConfirm @confirm="handleDelete(record)"></BaseConfirm>
        </a-space>
      </template>
    </template>
  </BasicTablePlus>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useTable } from '@/components/BasicTablePlus/useTable';
import { getBasicColumns } from '../data';
import { V1ManageQuestionBankGetQuestionBankPagePost } from '/@/api/cddc.req';

export default defineComponent({
  name: 'PendingList',
  setup() {
    const selectedRowKeys = ref<(string | number)[]>([]);
    const [registerTable, { reload }] = useTable({
      api: V1ManageQuestionBankGetQuestionBankPagePost,
      columns: getBasicColumns(),
      tableProps: {
        rowKey: 'id',
        bordered: true,
        showIndexColumn: true,
        scroll: { x: 1200 },
        rowSelection: {
          type: 'checkbox',
          onChange: (keys: (string | number)[]) => {
            selectedRowKeys.value = keys;
          },
        },
        actionColumn: {
          width: 200,
        },
      },
    });

    const handleAdd = () => {
      // 处理新增
    };

    const handleEdit = (record: any) => {
      // 处理编辑
    };

    const handleDetail = (record: any) => {
      // 处理详情
    };

    const handleConfirm = async (record: any) => {
      // 处理确认
      // 刷新列表
      reload();
    };

    const handleDelete = async (record: any) => {
      // 处理删除
      console.log('record :>> ', record);
      // 刷新表格
      reload();
    };

    return {
      registerTable,
      handleAdd,
      handleEdit,
      handleDetail,
      handleConfirm,
      handleDelete,
    };
  },
});
</script>

<style lang="less" scoped>
.tool-left {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
</style>
