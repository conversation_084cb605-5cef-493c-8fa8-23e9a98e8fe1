<script lang="ts" setup>
import AiGeneration from '/@/views/exam/question/components/aiGeneration/index.vue';
import { BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import { ref } from 'vue'


const emit = defineEmits(['reload'])

// 注册抽屉
const [registerDrawer, drawerActions] = useDrawer();

const questionBankId = ref<string>()

function openDrawer(_questionBankId?:string) {
  questionBankId.value = _questionBankId
  drawerActions.openDrawer();
}

function cancel() {
  drawerActions.openDrawer(false);
}

function reload() {
  emit('reload')
}

defineExpose({
  openDrawer,
});
</script>

<template>
  <BasicDrawer
    width="1200px"
    title="AI生成"
    :mask-closable="false"
    @register="registerDrawer"
    :showFooter="false"
    destroy-on-close
  >
    <AiGeneration :questionBankId="questionBankId" @cancel="cancel" @reload="reload"/>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
