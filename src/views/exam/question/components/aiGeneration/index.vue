<script setup lang="ts">
import { ref, onBeforeUnmount, onMounted, nextTick } from 'vue';
import TestQuestionsGeneratConfig from '../testQuestionsGeneratConfig/index.vue';
import {
  V1ManageQuestionBankGenerateQuestionByFilePost,
  V1ManageQuestionBankUploadGetQuestionBankUploadDetailById,
} from '/@/api/cddc.req';
import type { V1ManageQuestionBankGenerateQuestionByFilePostRequestBody } from '/@/api/cddc.model';
import { type IQuestion } from '/@/components/Examination/Questions';
import MultipleQuestionsPreview from '/@/components/Examination/MultipleQuestionsPreview.vue';
import { useLoadingFn } from '/@/composables';
import type { TestQuestionsGeneratConfigProps } from '../testQuestionsGeneratConfig/type';
import { creatInitGeneratConfig } from '../testQuestionsGeneratConfig/utils';
import { SyncOutlined, CheckOutlined, ExclamationCircleOutlined } from '@geega-ui-plus/icons-vue';
import { UploadStatus } from '/@/views/exam/uploadManage/data';
import { cloneDeep } from 'lodash-es';
import { getFileExt } from '/@/utils/file/download';

const props = defineProps<{
  questionBankId?: string;
}>();

const emit = defineEmits(['cancel', 'reload']);

type FormParams = V1ManageQuestionBankGenerateQuestionByFilePostRequestBody;

const testQuestionsGeneratConfigRef = ref<InstanceType<typeof TestQuestionsGeneratConfig>>();

const testQuestionsGeneratConfig = ref<TestQuestionsGeneratConfigProps>(creatInitGeneratConfig());

onMounted(() => {
  testQuestionsGeneratConfig.value.bankTypeId = props.questionBankId;
  nextTick(() => {
    testQuestionsGeneratConfigRef.value!.changeBankType();
  });
});

const questions = ref<IQuestion[]>([]);

const uploadStatus = ref<`${UploadStatus}`>();

const alreadySubmitted = ref<boolean>(false);

const failText = ref<string>('');

let timer: NodeJS.Timeout | null = null;

// AI异步生成试题
const buildQuestions = useLoadingFn(async () => {
  const _data = await testQuestionsGeneratConfigRef.value!.getFormData();

  const { fileList, ...rest } = _data;

  const file = fileList[0];

  const _params = {
    ...rest,
    fileId: file.response.id!,
    fileUrl: file.response.url!,
  } as FormParams;

  const params = cloneDeep(_params);

  const _isShowStemContentList = testQuestionsGeneratConfigRef.value!.getShowStemContentList();

  const ext = getFileExt(file.name)!;

  params.questionBankTypeColumnMappingList?.forEach((o) => {
    if (!['docx'].includes(ext)) {
      o.questionBankColumnPropertyValue = '';
    }
  });

  params.questionBankGenerateConfigList!.forEach((item) => {
    if (!_isShowStemContentList) {
      item.stemContentList = [];
    }

    item.referenceCase = (item.referenceCase || []).filter(
      (o) => o.questionStem && o.questionStem.trim()
    );

    item.referenceCase = item.referenceCase.map((q) => {
      const { questionBankOptionList = [], ...o } = q;

      const correctAnswerList = questionBankOptionList.reduce((acc, item) => {
        if (!!item.correctAnswer) {
          acc.push(item.optionDesc!);
        }
        return acc;
      }, [] as string[]);
      return {
        ...o,
        questionBankOptionList,
        correctAnswerList,
      };
    });
  });

  const uploadId = await V1ManageQuestionBankGenerateQuestionByFilePost(params);

  alreadySubmitted.value = true;

  if (uploadId) {
    pollGenertQuestions(uploadId);
  }
});

async function pollGenertQuestions(uploadId: string) {
  const resp = await V1ManageQuestionBankUploadGetQuestionBankUploadDetailById({
    uploadId,
  });

  uploadStatus.value = resp.uploadStatus as `${UploadStatus}`;

  const needDull = resp.uploadStatus === UploadStatus.GENERATING;

  timer = needDull ? setTimeout(() => pollGenertQuestions(uploadId), 3000) : null;

  questions.value = resp.questionBankDetailList || [];

  failText.value = resp.uploadStatus === UploadStatus.GENERATE_FAIL ? '转换结果失败!' : '';
}

function clearTimer() {
  if (timer) {
    window.clearTimeout(timer);
    timer = null;
  }
}

function cancel() {
  emit('cancel');
}

onBeforeUnmount(() => {
  clearTimer();
  if (alreadySubmitted.value) {
    emit('reload');
  }
});
</script>

<template>
  <div class="w-[100%] flex h-[calc(100vh-60px)]">
    <div
      class="box-border flex w-[50%] flex-col border-1px border-color-[#f4f6f8] border-r-solid h-100%"
    >
      <div class="h-[calc(100%-43px)] overflow-y-auto px-16px">
        <TestQuestionsGeneratConfig
          ref="testQuestionsGeneratConfigRef"
          v-model:config="testQuestionsGeneratConfig"
        />
      </div>
      <div class="flex justify-end pt-10px pr-20px">
        <a-button type="primary" @click="buildQuestions()" :disabled="alreadySubmitted">
          智能生成</a-button
        >
      </div>
    </div>
    <div class="flex-1 px-16px h-100%">
      <div class="h-[calc(100%-43px)] overflow-y-auto relative">
        <div
          v-if="alreadySubmitted && uploadStatus === UploadStatus.GENERATE_FAIL && failText"
          class="bg-[#fff] py-10px sticky top-0 z-100"
        >
          <div class="color-[#FF3333]"
            ><ExclamationCircleOutlined /><span class="inline-block pl-4px break-all">{{
              failText
            }}</span></div
          >
        </div>
        <a-spin :spinning="buildQuestions.loading" tip="正在生成题目...">
          <div class="min-h-400px">
            <div v-if="!alreadySubmitted" class="pt-100px color-[#999] flex justify-center">
              <div class="pr-4px">
                <ExclamationCircleOutlined />
              </div>
              <div>完成右侧表单后点击 智能生成 按钮生成题目</div>
            </div>
            <MultipleQuestionsPreview :allow-edit="false" v-model:questions="questions" />
          </div>
        </a-spin>
      </div>
      <div class="flex justify-between pt-10px">
        <div>
          <div v-if="alreadySubmitted && uploadStatus === UploadStatus.GENERATING"
            ><SyncOutlined :spin="true" /><span class="inline-block pl-4px"
              >正在生成题目...</span
            ></div
          >
          <div
            class="color-[#00996b]"
            v-if="alreadySubmitted && uploadStatus === UploadStatus.GENERATE_COMPLETE"
            ><CheckOutlined /><span class="inline-block pl-4px">生成完成!</span></div
          >
        </div>
        <a-button type="primary" ghost @click="cancel"> 关闭 </a-button>
      </div>
    </div>
  </div>
</template>
