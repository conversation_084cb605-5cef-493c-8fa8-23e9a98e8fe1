<script setup lang="ts">
import { ref, onMounted } from 'vue';
import QuestionBankTable from './QuestionBankTable.vue';
import { V1ManageQuestionBankTypeGetQuestionBankTypeListPost } from '/@/api/cddc.req';
import type { QuestionBankTypeInfoElement } from '/@/api/cddc.model';

type QuestionBank = QuestionBankTypeInfoElement;

const selectQuestionBankId = ref<string>();

const questionBank = ref<QuestionBank[]>([]);

async function loadQuestionBank() {
  try {
    const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({});

    questionBank.value = resp || []

  } catch (error) {
    // questionBank.value = [];
  }
  selectQuestionBankId.value = questionBank.value[0]?.id
}

onMounted(loadQuestionBank);

</script>

<template>
  <div class="px-8px bg-[#fff]">
    <a-tabs v-model:activeKey="selectQuestionBankId">
      <a-tab-pane v-for="item in questionBank" :key="item.id" :tab="item.name"> </a-tab-pane>
    </a-tabs>
    <QuestionBankTable :questionBankId="selectQuestionBankId"/>
  </div>
</template>

<style lang="less" scoped></style>
