// 试题状态枚举
export enum QuestionStatus {
  PENDING = '0', // 待确认
  CONFIRMED = '1', // 已确认
}

// 试题状态配置
export const questionStatusConfig = {
  [QuestionStatus.PENDING]: { color: 'warning', text: '待确认' },
  [QuestionStatus.CONFIRMED]: { color: 'success', text: '已确认' },
} as const;

// 试题状态选项
export const questionStatusOptions = [
  { label: '待确认', value: QuestionStatus.PENDING },
  { label: '已确认', value: QuestionStatus.CONFIRMED },
] as const;
