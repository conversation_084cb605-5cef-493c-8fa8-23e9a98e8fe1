import type { BasicColumn, FormSchema } from '@geega-ui-plus/geega-ui';
import { h } from 'vue';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { V1ManageQuestionBankTypeGetQuestionBankTypeListPost } from '/@/api/cddc.req';

export enum UploadTypeEnum {
  // 考题导入
  FILE_IMPORT = 'FILE_IMPORT',
  // AI生成上传
  AI_GENERATE = 'AI_GENERATE',
}

export type UploadType = `${UploadTypeEnum}`;

export enum UploadStatus {
  // 进行中
  GENERATING = 'GENERATING',
  // 已完成
  GENERATE_COMPLETE = 'GENERATE_COMPLETE',

  // 生成失败
  GENERATE_FAIL = 'GENERATE_FAIL',
}

// 状态选项
export const UploadStatusOpts = [
  { label: '上传中', value: UploadStatus.GENERATING, tagProps: { color: 'warning' } },
  { label: '已完成', value: UploadStatus.GENERATE_COMPLETE, tagProps: { color: 'success' } },
];

// 状态选项
export const AiGenerteStatusOpts = [
  { label: '生成中', value: UploadStatus.GENERATING, tagProps: { color: 'warning' } },
  { label: '已完成', value: UploadStatus.GENERATE_COMPLETE, tagProps: { color: 'success' } },
  {
    label: '生成失败',
    value: UploadStatus.GENERATE_FAIL,
    tagProps: { color: 'error' },
  },
];

function getStatusOpts(uploadType: UploadType) {
  return uploadType === UploadTypeEnum.AI_GENERATE ? AiGenerteStatusOpts : UploadStatusOpts;
}

// 表格列配置
export function getBasicColumns(uploadType: UploadType): BasicColumn[] {
  return [
    {
      title: '时间',
      dataIndex: 'lastUpdateTime',
      width: 200,
    },
    {
      title: '上传题库',
      dataIndex: ['questionBankTypeInfo', 'name'],
      minWidth: 300,
    },
    {
      title: '上传文件名称',
      dataIndex: ['fileInfo', 'fileName'],
      minWidth: 300,
    },
    {
      title: '状态',
      dataIndex: 'uploadStatus',
      width: 100,
      customRender: ({ text }) => {
        const _opts = getStatusOpts(uploadType);
        const opt = _opts.find((o) => o.value === text) || {
          label: text,
          value: text,
          tagProps: { color: 'default' },
        };
        const { tagProps, label } = opt;
        return h(Tag, tagProps, () => label);
      },
    },
  ];
}

export function getFormSchemas(uploadType: UploadType): FormSchema[] {
  return [
    {
      field: 'fileName',
      label: '上传文件名',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      field: 'questionBankTypeId',
      label: '题库',
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({});
          return resp?.map((o) => ({ label: o.name, value: o.id }));
        },
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    {
      field: 'time',
      label: '时间',
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'uploadStatus',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: getStatusOpts(uploadType),
      },
    },
  ];
}
