<script setup lang="ts">
import { ref } from 'vue';
import UploadTaskTable from '../uploadManage/UploadTaskTable.vue'
import AiGenerationDrawer from './aiGeneration/drawer.vue';
import QuestionsImportDrawer from './questionsImport/drawer.vue';
import { UploadTypeEnum, type UploadType } from './data';
import type { V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult} from '/@/api/cddc.model'

const aiGenerationDrawerRef = ref<InstanceType<typeof AiGenerationDrawer>>();

const questionsImportDrawerRef = ref<InstanceType<typeof QuestionsImportDrawer>>();


type TableRecord = V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult;

const tabs = [
  {
    name: 'AI生成上传',
    uploadType: UploadTypeEnum.AI_GENERATE,
  },
  {
    name: '考题导入',
    uploadType: UploadTypeEnum.FILE_IMPORT,
  },
];

const uploadType = ref<UploadType>(tabs[0].uploadType);

function handleView(record: TableRecord) {

  const comMapper = {

    [UploadTypeEnum.AI_GENERATE] : aiGenerationDrawerRef.value!,

    [UploadTypeEnum.FILE_IMPORT] : questionsImportDrawerRef.value!
  }

  const uploadType = record.uploadType as keyof typeof comMapper

  comMapper[uploadType]?.openDrawer(record.id!)

}
</script>

<template>
  <div class="px-8px bg-[#fff]">
    <a-tabs v-model:activeKey="uploadType">
      <a-tab-pane v-for="item in tabs" :key="item.uploadType" :tab="item.name"> </a-tab-pane>
    </a-tabs>
    <UploadTaskTable :uploadType="uploadType" :key="uploadType" @handleView="handleView"/>
    <AiGenerationDrawer ref="aiGenerationDrawerRef" />
    <QuestionsImportDrawer ref="questionsImportDrawerRef" />
  </div>
</template>

<style lang="less" scoped></style>
