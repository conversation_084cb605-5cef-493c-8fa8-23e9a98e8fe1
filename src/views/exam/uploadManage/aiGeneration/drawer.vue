<script lang="ts" setup>
import { ref } from 'vue'
import Detail from './detail.vue';
import { BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';

const uploadId = ref<string>()
// 注册抽屉
const [registerDrawer, drawerActions] = useDrawer();

function openDrawer(_uploadId: string) {
  uploadId.value = _uploadId
  drawerActions.openDrawer();
}

function cancel() {
  drawerActions.openDrawer(false);
}

defineExpose({
  openDrawer,
});
</script>

<template>
  <BasicDrawer
    width="1200px"
    title="AI生成详情"
    @register="registerDrawer"
    :show-footer="false"
    destroy-on-close
  >
    <Detail :uploadId="uploadId!"  @cancel="cancel" />
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
