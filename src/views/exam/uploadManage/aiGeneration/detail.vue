<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { V1ManageQuestionBankUploadGetQuestionBankUploadDetailById } from '/@/api/cddc.req';
import type {
  V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult,
} from '/@/api/cddc.model';
import { useLoadingFn } from '/@/composables';
import MultipleQuestionsPreview from '/@/components/Examination/MultipleQuestionsPreview.vue';
import BasicTitle from '/@/components/GeegaBasicTitle/src/BasicTitle.vue';
import type { TestQuestionsGeneratConfigProps } from '/@/views/exam/question/components/testQuestionsGeneratConfig/type';
import { creatInitGeneratConfig } from '/@/views/exam/question/components/testQuestionsGeneratConfig/utils';
import TestQuestionsGeneratConfig from '/@/views/exam/question/components/testQuestionsGeneratConfig/index.vue';
import { downloadFileByUrl } from '/@/utils/file/download';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { buildUUID } from '/@/utils/uuid';
import { UploadStatus } from '/@/views/exam/uploadManage/data';
import { ExclamationCircleOutlined } from '@geega-ui-plus/icons-vue';

type DetailRes = V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult;

const props = defineProps<{
  uploadId: string;
}>();

const detailResData = ref<DetailRes>();

const failText = ref<string>('');

const testQuestionsGeneratConfig = ref<TestQuestionsGeneratConfigProps>(
  creatInitGeneratConfig(true)
);

const loadDetail = useLoadingFn(async () => {
  const resp = await V1ManageQuestionBankUploadGetQuestionBankUploadDetailById({
    uploadId: props.uploadId,
  });

  detailResData.value = resp;

  failText.value = resp.uploadStatus === UploadStatus.GENERATE_FAIL ? '转换结果失败!' : '';

  if (resp.questionBankInfo) {
    const questionBankInfo = JSON.parse(resp.questionBankInfo);

    const { questionBankTypeColumnMappingList, questionBankGenerateConfigList } = questionBankInfo;

    questionBankTypeColumnMappingList.forEach((o) => {
      o.questionBankColumnKey = o.questionBankColumnName;
      o.questionBankColumnPropertyKey = o.questionBankColumnPropertyName;
    });

    questionBankGenerateConfigList.forEach((o) => {
      o.referenceCase?.forEach((_q) => {
        _q.questionBankOptionList?.forEach((opt) => {
          opt.id = buildUUID();
          opt.correctAnswer = (_q.correctAnswerList || []).includes(opt.optionDesc) ? 1 : 0;
        });
      });
    });

    testQuestionsGeneratConfig.value = {
      ...questionBankInfo,
      bankTypeId: resp.questionBankTypeId,
      fileId: resp.fileId,
      fileList: [],
      questionBankTypeColumnMappingList,
      questionBankGenerateConfigList,
    };
  }
});

onMounted(loadDetail);

const questions = computed(() => detailResData.value?.questionBankDetailList || []);
</script>

<template>
  <a-spin :spinning="loadDetail.loading" tip="正在加载题目...">
    <div class="w-[100%] flex h-[calc(100vh-60px)]">
      <div
        class="box-border flex w-[50%] flex-col border-1px border-color-[#f4f6f8] border-r-solid h-100%"
      >
        <div class="h-[calc(100%-10px)] overflow-y-auto px-16px">
          <BasicTitle title="上传文件"></BasicTitle>
          <div class="flex justify-between">
            <div class="flex gap-12px pr-12px">
              <div class="color-[#999]">文件名称:</div>
              <div class="break-all flex-1">{{ detailResData?.fileInfo?.fileName }}</div>
            </div>
            <a-button
              type="primary"
              v-if="detailResData?.fileInfo"
              ghost
              @click="
                downloadFileByUrl(detailResData.fileInfo.url!, detailResData.fileInfo.fileName!)
              "
              >下载</a-button
            >
          </div>
          <BasicTitle title="AI考题要求"></BasicTitle>
          <TestQuestionsGeneratConfig
            :disabled="true"
            :fileName="detailResData?.fileInfo?.fileName"
            :hideUploadFile="true"
            v-model:config="testQuestionsGeneratConfig"
          />
        </div>
      </div>
      <div class="flex-1 px-16px h-100%">
        <div class="h-[calc(100%-10px)] overflow-y-auto relative">
          <BasicTitle title="考题内容"></BasicTitle>
          <div v-if="failText" class="bg-[#fff] py-10px sticky top-0 z-100">
            <div class="color-[#FF3333]"
              ><ExclamationCircleOutlined /><span class="inline-block pl-4px break-all">{{
                failText
              }}</span></div
            >
          </div>

          <MultipleQuestionsPreview :allow-edit="false" v-model:questions="questions" />
          <div v-if="!questions.length && !loadDetail.loading && !failText" class="pt-100px">
            <GeegaEmpty description="无数据" />
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style lang="less" scoped></style>
