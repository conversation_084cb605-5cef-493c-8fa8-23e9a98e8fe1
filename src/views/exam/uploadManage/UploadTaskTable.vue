<script setup lang="ts">
import { useTable, BasicTable } from '@geega-ui-plus/geega-ui';
import { V1ManageQuestionBankUploadGetQuestionBankUploadListPost } from '/@/api/cddc.req';
import type { V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult } from '/@/api/cddc.model';
import { getBasicColumns, getFormSchemas, type UploadType } from './data';

const emit = defineEmits(['handleView']);

const props = defineProps<{
  uploadType: UploadType;
}>();

type TableRecord = V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult;
// 注册表格
const [registerTable] = useTable({
  api: V1ManageQuestionBankUploadGetQuestionBankUploadListPost,
  columns: getBasicColumns(props.uploadType),
  showIndexColumn: false,
  pagination: false,
  bordered: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 80,
    baseColProps: {
      span: 6,
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    schemas: getFormSchemas(props.uploadType),
  },
  // 表格-操作栏属性
  actionColumn: {
    width: 60,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: 'right',
  },
  beforeFetch: (data = {}) => {
    const { time = [], ...rest } = data;
    return {
      ...rest,
      startTime: time[0] ? `${time[0]} 00:00:00` : undefined,
      endTime: time[1] ? `${time[1]} 23:59:59` : undefined,
      uploadType: props.uploadType,
    };
  },
});

function handleView(record: TableRecord) {
  emit('handleView', record);
}
</script>

<template>
  <div class="py-0 px-8px bg-[#fff]">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <a-button type="link" @click="handleView(record)">查看</a-button>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped></style>
