<script setup lang="ts">
import { ref, onMounted, computed,  nextTick } from 'vue';
import { V1ManageQuestionBankUploadGetQuestionBankUploadDetailById } from '/@/api/cddc.req';
import type { V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult, QuestionPropertyListElement } from '/@/api/cddc.model';
import { useLoadingFn } from '/@/composables';
import MultipleQuestionsPreview from '/@/components/Examination/MultipleQuestionsPreview.vue';
import BasicTitle from '/@/components/GeegaBasicTitle/src/BasicTitle.vue';
import LeftConfig, {type FormData} from '../../question/components/questionsImport/LeftConfig.vue';
import { downloadFileByUrl } from '/@/utils/file/download';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';

const props = defineProps<{
  uploadId: string;
}>();

type DetailRes = V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult;

const leftConfigRef = ref<InstanceType<typeof LeftConfig>>();

const leftCongfigForm = ref<FormData>({
  fileList: [],
  bankTypeId: undefined,
  questionPropertyList: [{}],
});

const detailResData = ref<DetailRes>();

const loadDetail = useLoadingFn(async () => {
  const resp = await V1ManageQuestionBankUploadGetQuestionBankUploadDetailById({
    uploadId: props.uploadId,
  });

  detailResData.value = resp;

  if (resp.questionBankInfo) {
    const _questionBankInfo = JSON.parse(resp.questionBankInfo || '[]') as QuestionPropertyListElement [];

    const _bankInfo = {
      questionPropertyList: _questionBankInfo,
      bankTypeId: resp.questionBankTypeId!,
      fileList: [],
    };
    leftCongfigForm.value = _bankInfo

    nextTick(leftConfigRef.value!.loadQuestionBankColumn)
  }
});

onMounted(loadDetail);

const questions = computed(() => detailResData.value?.questionBankDetailList || []);
</script>

<template>
  <a-spin :spinning="loadDetail.loading" tip="正在加载题目...">
    <div class="w-[100%] flex h-[calc(100vh-60px)]">
      <div
        class="box-border flex w-[45%] flex-col border-1px border-color-[#f4f6f8] border-r-solid h-100%"
      >
        <div class="h-[calc(100%-10px)] overflow-y-auto px-16px">
          <BasicTitle title="上传文件"></BasicTitle>
          <div class="flex justify-between pb-10px">
            <div class="flex gap-12px pr-12px">
              <div class="color-[#999]">文件名称:</div>
              <div class="break-all flex-1">{{ detailResData?.fileInfo?.fileName }}</div>
            </div>
            <a-button
              type="primary"
              v-if="detailResData?.fileInfo"
              ghost
              @click="
                downloadFileByUrl(detailResData.fileInfo.url!, detailResData.fileInfo.fileName!)
              "
              >下载</a-button
            >
          </div>
          <LeftConfig
            :hide-upload-file="true"
            :disabled="true"
            ref="leftConfigRef"
            v-model:config="leftCongfigForm"
          />
        </div>
      </div>
      <div class="flex-1 px-16px h-100%">
        <div class="h-[calc(100%-10px)] overflow-y-auto">
          <BasicTitle title="考题内容"></BasicTitle>
          <MultipleQuestionsPreview :allow-edit="false" v-model:questions="questions" />
          <div v-if="!questions.length && !loadDetail.loading" class="pt-100px">
            <GeegaEmpty description="无数据" />
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style lang="less" scoped></style>
