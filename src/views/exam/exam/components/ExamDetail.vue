<template>
  <BasicDrawer
    width="800px"
    :title="title"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <div class="exam-detail">
      <ExamPreview
        :examData="examInfo"
        :questions="paperDetail"
        :userAnswer="userAnswer"
        :allowEdit="false"
      />
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import { V1ManageExamRecordId } from '/@/api/cddc.req';
import ExamPreview from '/@/components/Examination/ExamPreview.vue';
import type { PaperElement, ExamRecord, ResultAnswerList } from '/@/api/cddc.model';
import type { IQuestion } from '/@/components/Examination/Questions';

const emit = defineEmits(['success']);

const title = ref('试卷详情');
const examInfo = ref<PaperElement>({} as PaperElement);
const paperDetail = ref<IQuestion[]>([]);
const userAnswer = ref<{
  info: ExamRecord;
  answers: ResultAnswerList[];
}>({
  info: {} as ExamRecord,
  answers: [],
});

// 注册抽屉
const [registerDrawer, { openDrawer }] = useDrawer();

// 获取试卷详情
async function getExamDetail(record: Recordable) {
  await openDrawer();
  try {
    // 获取试卷详情
    const result = await V1ManageExamRecordId({ id: record.id });
    const { answerList, examRecord: recordInfo, paper } = result;

    // 设置试卷信息
    if (paper?.paper) {
      examInfo.value = {
        ...paper.paper,
        unitNames: record.unitNames,
        userName: record.userName,
      };
    }

    // 设置答案信息
    userAnswer.value = {
      info: recordInfo,
      answers: answerList || [],
    };

    // 设置试题列表
    paperDetail.value = paper?.questionList || [];
  } catch (error) {
    console.error('获取试卷详情失败：', error);
  }
}

// 关闭
function handleSubmit() {
  openDrawer(false);
}

// 对外暴露方法
defineExpose({
  getExamDetail,
});
</script>

<style lang="less" scoped>
.exam-detail {
  padding: 0 8px;
}
</style>
