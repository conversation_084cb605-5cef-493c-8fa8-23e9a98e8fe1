<script setup lang="ts">
import { onMounted, h } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { V1ManageExamRecordPagePost } from '/@/api/cddc.req';
import { getSkillAssessmentColumns, getSkillAssessmentFormSchema } from '../data';
import {
  createQuestionBankDynamicFieldFormSchema,
  creatSearchConfig,
  formatDynamicParams,
} from '/@/utils/questionBankFieldUtils';
import type { V1MobileHomeListPostResponseResult } from '/@/api/cddc.model';
import { Tag } from '@geega-ui-plus/ant-design-vue';

const emit = defineEmits(['viewExam', 'success']);

async function loadQuestionBankAynamic() {
  const { formSchema, fields } = await createQuestionBankDynamicFieldFormSchema();

  const formConfig = creatSearchConfig([...getSkillAssessmentFormSchema(formSchema)]);

  const dyCols = fields.map((o) => {
    return {
      title: o.columnName!,
      dataIndex: o.columnKey!,
      width: 200,
      customRender: ({ record }: { record: V1MobileHomeListPostResponseResult }) => {
        const _list = record.questionPropertyList || [];

        const _filters = _list.filter((i) => i.columnKey === o.columnKey);

        return h('div', {}, _filters.map(o => h(Tag, { color: 'blue', style: 'margin-right: 4px' }, () => o.columnValue)))
      },
    };
  });

  setColumns(getSkillAssessmentColumns(dyCols));

  getForm().setProps(formConfig);
}

onMounted(loadQuestionBankAynamic);

const [registerTable, { reload, getForm, setColumns }] = useTable({
  api: V1ManageExamRecordPagePost,
  columns: [],
  tableProps: {
    rowKey: 'id',
    bordered: true,
    showIndexColumn: true,
    showTableSetting: false,
    clickToRowSelect: false,
    searchInfo: { type: 'SKILL_ASSESSMENT' },
    beforeFetch: (params: Record<string, any>) =>
      formatDynamicParams(params, 'questionPropertyList'),
  },
});

function handleViewExam(record: Recordable) {
  emit('viewExam', record);
}

function handleSuccess() {
  reload();
  emit('success');
}

defineExpose({
  reload,
});
</script>

<template>
  <BasicTablePlus
    @register="registerTable"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <a-button type="link" @click="handleViewExam(record)">点击查看</a-button>
      </template>
    </template>
  </BasicTablePlus>
</template>
