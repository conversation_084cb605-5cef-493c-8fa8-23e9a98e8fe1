import { h } from 'vue';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { V1ManageCommonUserListPost, V1ManageSysOrgTree } from '/@/api/cddc.req';
import type { BasicColumn, FormSchema } from '@geega-ui-plus/geega-ui';
import _ from 'lodash';
import { ExamStatusOptions } from '/@/enums/projectEnum';

export function getExamFormSchema(dynamicFieldFormSchema: FormSchema[] = []): FormSchema[] {
  return [
    ...dynamicFieldFormSchema,
    {
      field: 'unitId',
      label: '组织',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择组织',
        allowClear: true,
        showArrow: true,
        maxTagCount: 'responsive',
        api: V1ManageSysOrgTree,
        fieldNames: {
          label: 'name',
          value: 'id',
          key: 'id',
          children: 'childNodes',
        },
        treeNodeFilterProp: 'name',
      },
    },
    {
      label: '考试人员',
      field: 'userId',
      component: 'ApiSelect',
      componentProps: {
        api: V1ManageCommonUserListPost,
        placeholder: '请选择人员',
        showSearch: true,
        optionFilterProp: 'name',
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      label: '状态',
      field: 'status',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: ExamStatusOptions,
      },
    },
  ];
}

// 表格列配置
export function getExamColumns(dynamicFieldColumn: BasicColumn[] = []): BasicColumn[] {
  return [
    ...dynamicFieldColumn,
    {
      title: '组织',
      dataIndex: 'unitNames',
      width: 300,
      customRender: ({ text = [] }) => {
        const _text = text.join(',');
        return h('div', { title: _text, className: 'text-ellipsis' }, _text);
      },
    },
    {
      title: '考试人员',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '考试试卷',
      dataIndex: 'paperName',
      width: 160,
    },
    {
      title: '分数',
      dataIndex: 'score',
      width: 160,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 160,
      customRender: ({ text }) => {
        const item = ExamStatusOptions.find((item) => item.value === text);

        return h(Tag, { color: item?.colorType }, () => item?.label);
      },
    },
    {
      title: '考试时间',
      dataIndex: 'begTime',
      width: 180,
    },
  ];
}

export function getSkillAssessmentFormSchema(
  dynamicFieldFormSchema: FormSchema[] = []
): FormSchema[] {
  return [
    ...dynamicFieldFormSchema,
    {
      field: 'unitId',
      label: '组织',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择组织',
        allowClear: true,
        showArrow: true,
        maxTagCount: 'responsive',
        api: V1ManageSysOrgTree,
        fieldNames: {
          label: 'name',
          value: 'id',
          key: 'id',
          children: 'childNodes',
        },
        treeNodeFilterProp: 'name',
      },
    },
    {
      label: '考试人员',
      field: 'userId',
      component: 'ApiSelect',
      componentProps: {
        api: V1ManageCommonUserListPost,
        placeholder: '请选择人员',
        showSearch: true,
        optionFilterProp: 'name',
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
  ];
}

export function getSkillAssessmentColumns(dynamicFieldColumn: BasicColumn[] = []): BasicColumn[] {
  return [
    ...dynamicFieldColumn,
    {
      title: '组织',
      dataIndex: 'unitNames',
      width: 300,
      customRender: ({ text = [] }) => {
        const _text = text.join(',');
        return h('div', { title: _text, className: 'text-ellipsis' }, _text);
      },
    },
    {
      title: '考试人员',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '考试试卷',
      dataIndex: 'paperName',
      width: 160,
    },
    {
      title: '考试时间',
      dataIndex: 'begTime',
      width: 160,
    },
  ];
}
