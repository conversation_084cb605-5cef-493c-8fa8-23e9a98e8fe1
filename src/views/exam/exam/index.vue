<script setup lang="ts">
import { ref } from 'vue';
import ExamTable from './components/ExamTable.vue';
import SkillAssessmentTable from './components/SkillAssessmentTable.vue';
import ExamDetail from './components/ExamDetail.vue';

const activeKey = ref('SKILL_INSPECTION');
const examDetailRef = ref();
const examTableRef = ref();
const skillTableRef = ref();

function handleViewExam(record: Recordable) {
  examDetailRef.value?.getExamDetail(record);
}

function handleSuccess() {
  if (activeKey.value === 'SKILL_INSPECTION') {
    examTableRef.value?.reload();
  } else {
    skillTableRef.value?.reload();
  }
}
</script>

<template>
  <div class="exam-container">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="SKILL_INSPECTION" tab="技能检查">
        <ExamTable ref="examTableRef" @viewExam="handleViewExam" @success="handleSuccess" />
      </a-tab-pane>

      <a-tab-pane key="SKILL_ASSESSMENT" tab="技能考核">
        <SkillAssessmentTable
          ref="skillTableRef"
          @viewExam="handleViewExam"
          @success="handleSuccess"
        />
      </a-tab-pane>
    </a-tabs>

    <ExamDetail ref="examDetailRef" @success="handleSuccess" />
  </div>
</template>

<style lang="less" scoped>
.exam-container {
  padding: 0 8px;
}
</style>
