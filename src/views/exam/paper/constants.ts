// 试卷状态枚举
export enum PaperStatus {
  GENERATED = 'GENERATED', // 初始化
  CONFIRM = 'CONFIRM', // 待发布
  RELEASE = 'RELEASE', // 已发布
}

// 试卷状态配置
export const paperStatusConfig = {
  [PaperStatus.CONFIRM]: { color: 'warning', text: '待发布' },
  [PaperStatus.RELEASE]: { color: 'success', text: '已发布' },
} as const;

// 试卷状态选项
export const paperStatusOptions = [
  { label: '待发布', value: PaperStatus.CONFIRM },
  { label: '已发布', value: PaperStatus.RELEASE },
] as const;
