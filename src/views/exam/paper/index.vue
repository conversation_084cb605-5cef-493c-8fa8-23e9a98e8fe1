<template>
  <div class="paper-container">
    <BasicTablePlus
      @register="registerTable"
    >
    <template #toolbar>
        <div class="flex gap-8px">
          <ExamCreateButton ghost />
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleCopy(record)" v-if="record.status === 'RELEASE'"
              >复制</a-button
            >
            <a-button type="link" @click="handlePublish(record)" v-else> 发布 </a-button>
            <a-button type="link" @click="handleDownload(record)">下载</a-button>
            <a-button type="link" @click="handleView(record)">详情</a-button>
            <BaseConfirm
              title="确定要删除该试卷吗？"
              @confirm="handleDelete(record)"
              v-if="record.status !== 'RELEASE'"
            ></BaseConfirm>
          </a-space>
        </template>

        <!-- 试卷类型 -->
        <template v-if="column.dataIndex === 'type'">
          <a-tag color="purple">{{
            paperType.find((item) => item.code === record.type)?.desc || '-'
          }}</a-tag>
        </template>
        <!-- 查看来源 -->
        <template v-if="column.dataIndex === 'source'">
          <a-button type="link" @click="handleViewSource(record)">点击查看</a-button>
        </template>
      </template>
    </BasicTablePlus>
    <!-- 下载试卷 -->
    <BasicModal
      centered
      title="试卷下载"
      :mask-closable="false"
      :minHeight="50"
      @register="registerDownloadModal"
      @ok="handleDownloadModalOk"
    >
      <!-- 含答案下载、不含答案下载 -->
      <a-radio-group v-model:value="downloadType">
        <a-radio :value="true">含答案下载</a-radio>
        <a-radio :value="false">不含答案下载</a-radio>
      </a-radio-group>
    </BasicModal>
    <!-- 试卷详情 -->
    <PaperDetail
      ref="paperDetailRef"
      :id="currentRecord.id"
      @success="reload"
      :allowEdit="currentRecord.status !== 'RELEASE'"
    />
    <!-- 发布试卷 -->
    <ExamPublish @register="publishModalRegister" :publishFn="publishExam" />
    <!-- 查看来源 -->
    <PaperSource ref="paperSourceRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, h } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { getBasicColumns } from './data';
import { paperStatusOptions } from './constants';
import {
  V1ManageCommonPaperTypeEnum,
  V1ManageExamPaperCopyInitPost,
  V1ManageExamPaperIdDelete,
  V1ManageExamPaperPagePost,
  V1ManageExamPaperReleasePost,
} from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import PaperDetail from './components/PaperDetail.vue';
import { getPaper } from '/@/api/paper/paper';
import ExamPublish from '../home/<USER>/ExamPublish.vue';
import PaperSource from './components/PaperSource.vue';
import router from '/@/router';
import type { PaperElement } from '/@/api/cddc.model';
import _ from 'lodash';
import {
  createQuestionBankDynamicFieldFormSchema,
  creatSearchConfig,
  formatDynamicParams,
} from '/@/utils/questionBankFieldUtils';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import ExamCreateButton from '../home/<USER>/ExamCreateButton.vue';
import { PaperTypeOptions } from '/@/enums/paperEnum';

async function loadQuestionBankAynamic() {
  const { formSchema, fields } = await createQuestionBankDynamicFieldFormSchema();

  const formConfig = creatSearchConfig([
    ...formSchema,
    {
      label: '试卷类型',
      field: 'type',
      component: 'Select',
      componentProps: {
        placeholder: '请选择',
        options: PaperTypeOptions,
      },
    },
    {
      label: '发布状态',
      field: 'status',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: paperStatusOptions,
      },
    },
  ]);

  const dyCols = fields.map((o) => {
    return {
      title: o.columnName!,
      dataIndex: o.columnKey!,
      width: 200,
      customRender: ({ record }: { record: PaperElement }) => {
        const _list = record.questionPropertyList || [];

        const _filters = _list.filter((i) => i.columnKey === o.columnKey);

        return h('div', {}, _filters.map(o => h(Tag, { color: 'blue', style: 'margin-right: 4px' }, () => o.columnValue)))
      },
    };
  });

  setColumns(getBasicColumns(dyCols));

  getForm().setProps(formConfig);
}


onMounted(loadQuestionBankAynamic);

// 试卷类型
const paperType = ref();
// 下载类型
const downloadType = ref(true);
const currentRecord = ref<Recordable>({});
// 试卷详情抽屉
const paperDetailRef = ref();
// 查看来源抽屉
const paperSourceRef = ref();
const [publishModalRegister, publishModalActions] = useModal();

// 注册表格
const [registerTable, { reload, getForm, setColumns }] = useTable({
  api: V1ManageExamPaperPagePost,
  columns: [],
  tableProps: {
    rowKey: 'id',
    bordered: true,
    showIndexColumn: true,
    clickToRowSelect: false,
    actionColumn: {
      width: 200,
    },
    beforeFetch: (params: Record<string, any>) =>
      formatDynamicParams(params, 'questionPropertyList'),
  },
});
// 试卷类型查询
const carTypeList = async () => {
  paperType.value = await V1ManageCommonPaperTypeEnum();
};
carTypeList();

// 查看来源
async function handleViewSource(record: Recordable) {
  paperSourceRef.value?.getSourceData(record);
}

// 复制试卷
async function handleCopy(record: Recordable) {
  const res = await V1ManageExamPaperCopyInitPost({
    paperId: record.id,
  });
  router.push({
    path: '/exam/home/<USER>',
    query: {
      id: res,
    },
  });
}

// 发布试卷
async function handlePublish(record: PaperElement) {
  try {
    if (!record.id) {
      message.error('试卷Id不存在');
      return;
    }
    currentRecord.value = record;
    publishModalActions.openModal(true, {
      type: record.type
    });
  } catch (error) {
    console.error('发布失败：', error);
  }
}

async function publishExam(values) {
  await V1ManageExamPaperReleasePost({
    id: currentRecord.value.id,
    ...values,
  });
  publishModalActions.openModal(false, {});
  reload();
  message.success('发布成功');
}

const [registerDownloadModal, { openModal: openDownloadModal }] = useModal();
// 下载试卷
async function handleDownload(record: Recordable) {
  currentRecord.value = record;
  openDownloadModal(true);
}

async function handleDownloadModalOk() {
  try {
    if (!currentRecord.value.id) {
      message.error('试卷Id不存在');
      return;
    }
    const res = await getPaper({
      id: currentRecord.value.id,
      includeAnswer: downloadType.value,
    });
    const blob = new Blob([res.data], { type: 'application/vnd.ms-word' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = currentRecord.value.name + '.doc';
    link.click();
    window.URL.revokeObjectURL(url);
    openDownloadModal(false);
  } catch (error) {
    console.error('下载失败：', error);
    openDownloadModal(false);
  }
}

// 查看详情
async function handleView(record: Recordable) {
  currentRecord.value = record;
  paperDetailRef.value?.getExamDetail(record);
}

// 删除试卷
async function handleDelete(record: Recordable) {
  try {
    if (!record.id) {
      message.error('试卷Id不存在');
      return;
    }
    await V1ManageExamPaperIdDelete({
      id: record.id,
    });
    reload();
  } catch (error) {
    console.error('删除失败：', error);
  }
}
</script>

<style lang="less" scoped>
.paper-container {
  padding: 0 8px;
  background: #fff;

  .tab-container {
  }

  :deep(.ant-tabs-nav) {
    margin-bottom: 16px;
  }
}
</style>
