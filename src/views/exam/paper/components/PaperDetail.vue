<template>
  <BasicDrawer
    width="800px"
    :title="title"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <div class="exam-detail">
      <ExamPreview
        :examData="examInfo"
        v-model:questions="paperDetail"
        :allowEdit="allowEdit"
        :generate-new-question="generateNewQuestion"
      />
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import {
  V1ManageExamPaperConfirmPost,
  V1ManageExamPaperId,
  V1ManageExamPaperQuestionListPaperId,
  V1ManageExamPaperSearchQuestionSinglePost,
} from '/@/api/cddc.req';
import ExamPreview, {
  type RegeneratePromptOption,
} from '/@/components/Examination/ExamPreview.vue';
import type { PaperElement } from '/@/api/cddc.model';
import type { IQuestion } from '/@/components/Examination/Questions';
import { message } from '@geega-ui-plus/ant-design-vue';
import { buildUUID } from '/@/utils/uuid';

export interface PaperDetailProps {
  id: string;
  allowEdit?: boolean;
}

const props = defineProps<PaperDetailProps>();

const emit = defineEmits(['success']);

const title = ref('试卷详情');
const examInfo = ref<PaperElement>({} as PaperElement);
const paperDetail = ref<IQuestion[]>([]);

// 注册抽屉
const [registerDrawer, { openDrawer }] = useDrawer();

// 获取试卷详情
async function getExamDetail(record: Recordable) {
  await openDrawer();
  try {
    // 获取试卷详情
    const result = await V1ManageExamPaperId({ id: record.id });
    const paperList = await V1ManageExamPaperQuestionListPaperId({
      paperId: record.id,
    });

    // 设置试卷信息
    examInfo.value = result;

    // 设置试题列表
    paperDetail.value = paperList || [];
  } catch (error) {
    console.error('获取试卷详情失败：', error);
  }
}

async function generateNewQuestion(params: RegeneratePromptOption) {
  const resp = await V1ManageExamPaperSearchQuestionSinglePost({
    paperId: props.id,
    bankTypeId: examInfo.value.bankTypeId,
    question: {
      questionType: params.question.questionType,
      difficultDegree: params.question.difficultDegree!,
      questionNum: 1,
    },
    questionPoint: params.questionPoint,
    bankIds: paperDetail.value.map((n) => n.bankId!),
  });

  if (resp) {
    resp.id ||= buildUUID();
  }

  return resp;
}

// 关闭
async function handleSubmit() {
  if (!props.allowEdit) {
    openDrawer(false);
    return;
  }
  await V1ManageExamPaperConfirmPost({
    id: props.id,
    syncBack: 'NO',
    questionList: paperDetail.value.map((n, idx) => ({
      ...n,
      paperId: props.id,
      sort: idx,
      questionBankOptionList: n.questionBankOptionList?.map((item, idx) => ({
        ...item,
        orderNum: idx,
      })),
    })),
  });

  message.success('保存成功');
  emit('success');
  openDrawer(false);
}

// 对外暴露方法
defineExpose({
  getExamDetail,
});
</script>

<style lang="less" scoped>
.exam-detail {
  padding: 0 8px;
}
</style>
