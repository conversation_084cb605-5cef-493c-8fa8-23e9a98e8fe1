<template>
  <BasicDrawer
    centered
    title="试卷数据来源"
    :minHeight="400"
    :width="800"
    @register="register"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <BasicTablePlus :columns="columns" :dataSource="dataSource"> </BasicTablePlus>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, h } from 'vue';
import { BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import {
  V1ManageCommonQuestionTypeEnum,
  V1ManageExamPaperPaperSourceRecordId,
} from '/@/api/cddc.req';

const columns = [
  {
    title: '类型',
    dataIndex: 'typeName',
    width: 100,
  },
  {
    title: '题干',
    dataIndex: 'questionStem',
    width: 150,
    customRender: ({ text }) => {
      return h('div', {}, text);
    },
  },
  {
    title: '来源',
    dataIndex: 'source',
    width: 120,
    customRender: ({ text }) => {
      return text || '-';
    },
  },
];

const dataSource = ref();

const [register, { openDrawer }] = useDrawer();
const typeList = ref();
const getTypeData = async () => {
  typeList.value = await V1ManageCommonQuestionTypeEnum();
};

getTypeData();

async function getSourceData(record: Recordable) {
  openDrawer(true);
  try {
    let res = await V1ManageExamPaperPaperSourceRecordId({
      recordId: record.id,
    });
    res.forEach((it) => {
      it.typeName = typeList.value.find((item) => item.code == it.questionType)?.desc;
    });
    dataSource.value = res || [];
  } catch (error) {
    console.error('获取来源数据失败：', error);
  }
}

defineExpose({
  getSourceData,
});
</script>
