import { h } from 'vue';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { paperStatusConfig } from './constants';
import type  { BasicColumn } from '@geega-ui-plus/geega-ui';

// 表格列配置

export function getBasicColumns(dynamicFieldColumn: BasicColumn[] = []): BasicColumn[] {
  return [
    {
      title: '试卷名称',
      dataIndex: 'name',
      width: 200,

    },
    ...dynamicFieldColumn,
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      sorter: true,
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      width: 120,
    },
    {
      title: '试卷类型',
      dataIndex: 'type',
      width: 120,
    },
    {
      title: '发布状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ text }) => {
        const { color, text: statusText } = paperStatusConfig[text] || { color: 'default', text };
        return h(Tag, { color }, () => statusText);
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      width: 120,
    },
  ];
}
