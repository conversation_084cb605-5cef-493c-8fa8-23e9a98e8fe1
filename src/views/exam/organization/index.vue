<template>
  <div class="organization-container">
    <div class="org-layout">
      <!-- 左侧树形结构 -->
      <div class="org-tree">
        <div class="search-box">
          <a-input-search
            v-model:value="searchText"
            placeholder="输入部门名称"
            style="width: 100%"
            allow-clear
            @search="handleSearch"
          />
        </div>
        <div class="tree-actions">
          <a-button type="link" @click="handleAdd">新增</a-button>
          <a-button type="link" @click="handleEdit">编辑</a-button>
          <BaseConfirm
            title="删除部门信息后，不可恢复，是否确认删除"
            @confirm="handleDelete"
            :disabled="!selectedKeys.length"
          ></BaseConfirm>
        </div>
        <a-tree
          v-model:selectedKeys="selectedKeys"
          v-model:expandedKeys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          :tree-data="treeData"
          treeNodeFilterProp="name"
          :height="600"
          :field-names="{
            title: 'name',
            key: 'id',
            children: 'childNodes',
          }"
          @expand="onExpand"
          @select="onSelect"
        />
      </div>

      <!-- 右侧表单 -->
      <div class="org-form">
        <BasicForm @register="registerForm" />
        <div class="form-actions" v-if="currStatus !== 'preview'">
          <a-space>
            <a-button @click="handleReset">重置</a-button>
            <a-button type="primary" @click="handleSubmit">保存</a-button>
          </a-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { BasicForm, useForm } from '@geega-ui-plus/geega-ui';
import { formSchema } from './data';
import { message } from '@geega-ui-plus/ant-design-vue';
import {
  V1ManageSysOrgDeleteIdDelete,
  V1ManageSysOrgPost,
  V1ManageSysOrgPut,
  V1ManageSysOrgTree,
} from '/@/api/cddc.req';

// 树相关状态
const searchText = ref('');
const selectedKeys = ref<string[]>([]);
const autoExpandParent = ref<boolean>(true);
const expandedKeys = ref<string[]>([]);
const treeData = ref<any[]>([]);
const currStatus = ref('add');
const currNode = ref<any>({});

// 注册表单
const [registerForm, { validate, resetFields, updateSchema, setProps, setFieldsValue }] = useForm({
  schemas: formSchema,
  showActionButtonGroup: false,
  layout: 'vertical',
  labelWidth: 100,
});

const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
};

// 获取树形数据
const fetchTreeData = async () => {
  const result = await V1ManageSysOrgTree({});
  treeData.value = result || [];
  if (result && result.length > 0) {
    expandedKeys.value = [result[0].id];
    autoExpandParent.value = true;
  }
};

// 组件挂载时获取数据
onMounted(async () => {
  await fetchTreeData();
  await updateFormSchemas();
  setProps({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
});

// 搜索处理
const handleSearch = async (value: string) => {
  const result = await V1ManageSysOrgTree({ name: value });
  treeData.value = result || [];

  if (result && result.length > 0) {
    // 收集所有节点的ID用于展开
    const getAllIds = (nodes: any[]): string[] => {
      let ids: string[] = [];
      nodes.forEach((node) => {
        ids.push(node.id);
        if (node.childNodes && node.childNodes.length) {
          ids = ids.concat(getAllIds(node.childNodes));
        }
      });
      return ids;
    };

    // 展开所有包含搜索结果的节点
    expandedKeys.value = getAllIds(result);
    autoExpandParent.value = true;
  }
};

// 选择树节点
function onSelect(keys: string[], info: any) {
  if (keys.length > 0) {
    setProps({
      disabled: true,
    });
    currNode.value = info.node;
    setFieldsValue({
      code: info.node.code,
      name: info.node.name,
      parentId: info.node.parentId,
    });
    currStatus.value = 'preview';
  }
}

// 新增
function handleAdd() {
  currStatus.value = 'add';
  handleReset();
  setProps({
    disabled: false,
  });
}

// 编辑
function handleEdit() {
  currStatus.value = 'edit';
  if (selectedKeys.value.length === 0) {
    message.warning('请先选择一个组织');
    return;
  }
  setProps({
    disabled: false,
  });
}

// 删除
async function handleDelete() {
  if (selectedKeys.value.length === 0) {
    message.warning('请先选择一个组织');
    return;
  }
  // 调用删除接口
  await V1ManageSysOrgDeleteIdDelete({
    id: selectedKeys.value[0],
  });
  message.success('删除成功');
  handleReset();
  // 更新数据
  const result = await V1ManageSysOrgTree({});
  treeData.value = result || [];
  await updateFormSchemas();
}

// 重置
function handleReset() {
  resetFields();
  selectedKeys.value = [];
}

// 动态更新表单下拉选项
const updateFormSchemas = async () => {
  const newData = [{ id: '-1', name: '顶级', childNodes: [...treeData.value] }];
  await updateSchema({
    field: 'parentId',
    componentProps: {
      treeData: newData,
    },
  });
};

// 提交
async function handleSubmit() {
  try {
    const values = await validate();
    console.log('提交数据：', values, selectedKeys.value);
    if (currStatus.value === 'edit') {
      await V1ManageSysOrgPost({ ...values, id: currNode.value.id, type: 'NODE' });
    } else {
      await V1ManageSysOrgPut({ ...values, type: 'NODE' });
    }
    message.success('操作成功');
    await fetchTreeData();
    await updateFormSchemas();
    handleReset();
  } catch (error) {
    console.error('验证失败：', error);
  }
}
</script>

<style lang="less" scoped>
.organization-container {
  padding: 16px;
  background: #fff;
  height: 100%;

  .org-layout {
    display: flex;
    gap: 16px;
    height: 100%;

    .org-tree {
      width: 280px;
      border-right: 1px solid #f0f0f0;

      .search-box {
        margin-bottom: 16px;
      }

      .tree-actions {
        margin-bottom: 16px;
        display: flex;
        gap: 8px;
      }
    }

    .org-form {
      flex: 1;
      padding: 24px;
      background: #fff;

      .form-actions {
        margin-top: 24px;
        text-align: left;
      }
    }
  }
}
</style>
