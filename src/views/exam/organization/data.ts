import { FormSchema } from '@geega-ui-plus/geega-ui';
import { V1ManageSysOrgTree } from '/@/api/cddc.req';

// 表单配置
export const formSchema: FormSchema[] = [
  {
    field: 'code',
    label: '组织编号',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
      placeholder: '请输入组织编号',
    },
  },
  {
    field: 'name',
    label: '组织名称',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
      placeholder: '请输入组织名称',
    },
  },
  {
    field: 'parentId',
    label: '上级组织',
    component: 'TreeSelect',
    required: true,
    componentProps: {
      placeholder: '组织架构/添加节点',
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'childNodes',
      },
      maxTagCount: 'responsive',
      treeNodeFilterProp: 'name',
    },
  },
];
