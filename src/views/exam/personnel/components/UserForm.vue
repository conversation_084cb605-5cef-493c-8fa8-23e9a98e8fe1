<template>
  <BasicDrawer
    width="500px"
    @register="registerDrawer"
    :title="title"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #passwordGroup="{ model, field, schema }">
        <a-input-group compact>
          <a-input-password
            v-model:value="model[field]"
            class="password-input"
            placeholder="请输入密码"
            :disabled="schema.componentProps?.disabled"
          />
          <a-button type="primary" @click="resetPassword">重置密码</a-button>
        </a-input-group>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts">
import { defineComponent, nextTick, ref } from 'vue';
import { BasicDrawer, BasicForm, FormSchema, useDrawer, useForm } from '@geega-ui-plus/geega-ui';
import { useMessage } from '/@/hooks/web/useMessage';
import { V1ManageSysOrgTree } from '/@/api/cddc.req';
import { getPopupContainer } from '/@/utils';

export default defineComponent({
  name: 'UserForm',
  components: {
    BasicDrawer,
    BasicForm,
  },
  emits: ['success', 'register'],
  setup(_, { emit }) {
    const { createMessage } = useMessage();
    // 抽屉标题
    const title = ref('新增用户');

    // 表单配置
    const formSchemas: FormSchema[] = [
      {
        field: 'name',
        label: '用户名',
        component: 'Input',
        required: true,
        componentProps: {
          placeholder: '请输入用户名',
        },
      },
      {
        field: 'phone',
        label: '手机号',
        component: 'Input',
        required: false,
        rules: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
          },
        ],
        componentProps: {
          placeholder: '请输入手机号',
          maxLength: 11,
        },
      },
      {
        field: 'unitIdList',
        label: '所属部门',
        component: 'ApiTreeSelect',
        required: true,
        componentProps: {
          api: V1ManageSysOrgTree,
          multiple: true,
          allowClear: true,
          showArrow: true,
          maxTagCount: 'responsive',
          placeholder: '请选择所属部门',
          fieldNames: {
            label: 'name',
            value: 'id',
            children: 'childNodes',
          },
          treeNodeFilterProp: 'name',
          getPopupContainer: () => getPopupContainer(),
        },
      },
      {
        field: 'idCard',
        label: '身份证号',
        component: 'Input',
        required: false,
        rules: [
          {
            pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
            message: '请输入正确的身份证号',
          },
        ],
        componentProps: {
          placeholder: '请输入身份证号',
        },
      },
      {
        field: 'password',
        label: '密码',
        component: 'InputGroup',
        required: true,
        rules: [
          {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/,
            message: '密码长度至少6位，至少包含字母+数字',
          },
        ],
        slot: 'passwordGroup',
        colProps: { span: 24 },
      },
    ];

    const [
      registerForm,
      { validate, resetFields, setProps, updateSchema, clearValidate, setFieldsValue },
    ] = useForm({
      schemas: formSchemas,
      showActionButtonGroup: false,
      rowProps: {
        gutter: [24, 12],
      },
      labelWidth: 100,
    });
    // 编辑数据
    const editData = ref<any>({});
    const [registerDrawer, { openDrawer }] = useDrawer();

    // 重置密码
    const resetPassword = () => {
      const randomPassword = Math.random().toString(36).slice(-8);
      setFieldsValue({
        password: randomPassword,
      });
    };
    // 重置表单
    const resetForm = async () => {
      await nextTick();
      resetFields();
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const values = await validate();
        // await V1ManageQuestionUserUpdatePost({
        //   id: editData.value.id,
        //   ...values,
        // });
        createMessage.success('编辑成功');
        resetFields();
        emit('success', values);
        openDrawer(false);
      } catch (error) {
        console.error('表单错误:', error);
      }
    };

    // 设置表单数据
    const setFormData = async (data: any) => {
      await nextTick();
      // 密码输入框不可编辑
      if (Object.keys(data).length > 0) {
        editData.value = data;
        title.value = '编辑用户';
        //所在组织
        if (!data.unitList || !data.unitList.length) {
          setFieldsValue({ unitIdList: [] });
          clearValidate('unitIdList');
        } else {
          data.unitIdList = data.unitList?.map((item) => item.id) || [];
          updateSchema({
            field: 'unitIdList',
            componentProps: {
              treeDefaultExpandedKeys: data.unitIdList,
            },
          });
        }
        // 隐藏密码字段
        updateSchema({
          field: 'password',
          required: false,
          show: false,
        });
        // 禁用基础信息字段
        const disabledFields = ['name', 'phone'];
        updateSchema(
          disabledFields.map((field) => ({
            field,
            componentProps: { disabled: true },
          }))
        );
      } else {
        title.value = '新增用户';
        setProps({
          disabled: false,
        });
        updateSchema({
          field: 'password',
          required: true,
          show: true,
        });
      }
      setFieldsValue(data);
    };

    return {
      title,
      resetForm,
      resetPassword,
      openDrawer,
      registerForm,
      setFormData,
      updateSchema,
      registerDrawer,
      handleSubmit,
    };
  },
});
</script>

<style lang="less" scoped>
.cddc-ant-input-group.cddc-ant-input-group-compact {
  .password-input {
    width: calc(100% - 78px);
    margin-right: 8px;
  }
}
</style>
