<template>
  <div class="personnel-container">
    <div class="layout">
      <!-- 左侧树形结构 -->
      <div class="tree-section">
        <div class="search-box">
          <a-input-search
            v-model:value="treeSearchText"
            placeholder="输入部门名称"
            style="width: 100%"
            allow-clear
            @search="fetchTreeData"
          />
        </div>
        <a-tree
          v-model:selectedKeys="selectedKeys"
          v-model:expandedKeys="expandedKeys"
          :tree-data="treeData"
          :height="600"
          :field-names="{ title: 'name', key: 'id', children: 'childNodes' }"
          @select="onSelect"
        />
      </div>

      <!-- 右侧内容区 -->
      <div class="content-section">
        <!-- 表格区域 -->
        <BasicTablePlus @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            </template>
          </template>
        </BasicTablePlus>

        <!-- 新增用户抽屉 -->
        <UserForm ref="drawerRef" @success="handleSuccess"></UserForm>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { getBasicColumns } from './data';
import { V1ManageQuestionUserPagePost, V1ManageSysOrgTree } from '/@/api/cddc.req';
import UserForm from './components/UserForm.vue';

const treeSearchText = ref('');
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);
const treeData = ref<any[]>([]);
const drawerRef = ref();

// 获取树形数据
const fetchTreeData = async (name?: string) => {
  const result = await V1ManageSysOrgTree({ name });
  treeData.value = result || [];
  if (result && result.length > 0) {
    expandedKeys.value = [result[0].id];
  }
};

// 注册表格
const [registerTable, { reload, getForm }] = useTable({
  api: V1ManageQuestionUserPagePost,
  columns: getBasicColumns(),
  tableProps: {
    rowKey: 'id',
    bordered: true,
    showIndexColumn: true,
    clickToRowSelect: false,
    handleSearchInfoFn: (params) => {
      // 添加部门ID到查询参数
      const unitId = selectedKeys.value[0];
      return {
        ...params,
        unitId: unitId || undefined,
      };
    },
  },
});

// 提交成功
const handleSuccess = () => {
  reload();
};

// 选择树节点
const onSelect = (keys: string[]) => {
  selectedKeys.value = keys;
  getForm().submit();
};

// 编辑
const handleEdit = async (record: Recordable) => {
  await drawerRef.value?.openDrawer();
  await drawerRef.value?.setFormData(record);
};

// 初始化加载树形数据
onMounted(() => {
  fetchTreeData();
});
</script>

<style lang="less" scoped>
.personnel-container {
  padding: 0 8px;
  background: #fff;
  height: 100%;

  .layout {
    display: flex;
    height: 100%;
    gap: 16px;

    .tree-section {
      width: 250px;
      min-width: 200px;
      border-right: 1px solid #f0f0f0;

      .search-box {
        margin-bottom: 16px;
      }
    }

    .content-section {
      flex: 1;

      .search-area {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
