import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

export const getBasicColumns = (): EnhancedColumn[] => {
  return [
    {
      title: '用户名',
      dataIndex: 'name',
      width: 120,
      search: {
        component: 'Input',
        colProps: { span: 6 },
        componentProps: {
          placeholder: '请输入用户名',
        },
      },
    },
    {
      title: '所在组织',
      dataIndex: 'unitList',
      width: 120,
      customRender: ({ text }) => {
        const names = text.map((item) => item.name);
        return names.join(',');
      },
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
      width: 180,
    },
    // {
    //   title: '要求项目',
    //   dataIndex: 'requirement',
    //   width: 150,
    // },
    {
      title: '最近登录时间',
      dataIndex: 'lastLoginTime',
      width: 180,
    },
  ];
};
