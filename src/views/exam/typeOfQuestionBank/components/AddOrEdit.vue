<script lang="ts" setup>
import { BasicForm, BasicModal, useForm, useModalInner } from '@geega-ui-plus/geega-ui';
import { useLoadingFn } from '/@/composables';
import {
  V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePost,
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
} from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';
import { ref, computed } from 'vue';
import regexMap from '/@/utils/regex/index';

const innerProps = ref<Record<string, any>>({});

const emit = defineEmits(['success']);

const modalTitle = computed(() => {
  return `${!innerProps.value?.id ? '新增' : '编辑'}题库类型`;
});

const [registerForm, form] = useForm({
  schemas: [
    {
      field: 'name',
      label: '题库类型名称',
      component: 'Input',
      required: true,
      rules: [
        {
          ...regexMap.chineseEnglishNumberAndNoUnderscore,
          message: '只能输入中文、数字、字母',
        },
      ],
      componentProps: {
        maxlength: 12,
      },
    },
    {
      field: 'columnIds',
      label: '题库关联字段',
      component: 'ApiSelect',
      required: true,
      rules: [
        {
          trigger: 'change',
          async validator(_rule, value: string[]) {
            const _value = value || [];
            if (_value.length > 20) {
              throw new Error('最多选择20个关联字段');
            }
          },
        },
      ],
      componentProps: {
        notFoundContent: '未配置字段，请前往题库字段配置完成配置。',
        maxlength: 2,
        api: async () => {
          const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnListPost({});
          return (resp || []).map((i) => ({ label: i.columnName, value: i.id }));
        },
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        getPopupContainer: () => document.body,
        maxTagCount: 'responsive',
      },
    },
  ],
  showActionButtonGroup: false,
});

const [registerModal, modalActions] = useModalInner(async (data: Record<string, any>) => {
  innerProps.value = data;

  await form.resetFields();

  if (data.id) {
    await form.setFieldsValue(data);
  }
  await form.clearValidate();
});

async function _submit() {
  const values = await form.validate();

  const params = {
    id: innerProps.value.id,
    name: values.name,
    questionBankTypeColumnSaveDTO: (values.columnIds || []).map((columnId) => ({
      columnId,
    })),
  };

  await V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePost(params);

  message.success('操作成功');
  emit('success');
  modalActions.closeModal();
}

const submit = useLoadingFn(_submit);
</script>

<template>
  <BasicModal @register="registerModal" :title="modalTitle" @ok="submit" :mask-closable="false">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<style lang="less" scoped></style>
