<script lang="ts" setup>
import { BasicModal, SvgIcon, useModalInner } from '@geega-ui-plus/geega-ui';
import { useLoadingFn } from '/@/composables';
import { message } from '@geega-ui-plus/ant-design-vue';
import { computed, nextTick, reactive, ref } from 'vue';
import type { FormExpose } from '@geega-ui-plus/ant-design-vue/es/form/Form';
import { V1ManagePromptsBankTypeId, V1ManagePromptsBatchPost } from '/@/api/cddc.req';
import type { V1ManagePromptsBankTypeIDGetResponseResult } from '/@/api/cddc.model';
import { SupportedFileTypeOptions } from '/@/enums/promptConfigEnum';

const state = reactive({
  id: '',
});

const form = ref<FormExpose>();
const addBtn = ref<HTMLElement>();

const formState = ref<V1ManagePromptsBankTypeIDGetResponseResult[]>([]);

const emit = defineEmits(['success']);

const modalTitle = computed(() => {
  return `${!state.id ? '新增' : '编辑'}题库提示词`;
});

const [registerModal, modalActions] = useModalInner(async (data: Record<string, any>) => {
  state.id = data.id;

  form.value?.resetFields();
  await initFormData();
  form.value?.clearValidate();
});

async function initFormData() {
  formState.value = [];

  const resp = await V1ManagePromptsBankTypeId({ bankTypeId: state.id });

  formState.value = resp;

  if (formState.value.length === 0) {
    addPrompt();
  }
}

async function _submit() {
  await form.value?.validate();
  const params = formState.value.map((item) => ({
    name: item.name!,
    content: item.content!,
    bankTypeId: state.id!,
    fileType: item.fileType!,
  }));

  await V1ManagePromptsBatchPost(params);

  message.success('操作成功');
  emit('success');
  modalActions.closeModal();
}

const submit = useLoadingFn(_submit);

function deletePrompt(idx: number) {
  formState.value.splice(idx, 1);
}

async function addPrompt() {
  formState.value.push({});
  await nextTick();

  addBtn.value?.scrollIntoView({
    behavior: 'smooth',
  });
}
</script>

<template>
  <BasicModal
    @register="registerModal"
    :title="modalTitle"
    @ok="submit"
    :mask-closable="false"
    :height="400"
  >
    <a-form :model="formState" ref="form" label-align="left" :colon="false">
      <div v-for="(item, idx) in formState" class="prompt-item">
        <div class="close-icon">
          <a-button
            type="link"
            class="!pl-0"
            @click="deletePrompt(idx)"
            :disabled="formState.length <= 1"
          >
            <SvgIcon name="g-trash" />
          </a-button>
        </div>
        <a-form-item
          :name="[idx, 'name']"
          :label="`组 ${idx + 1} 名称`"
          :rules="{ required: true, message: '请输入名称' }"
        >
          <a-input v-model:value="item.name" placeholder="请输入" :maxlength="20" />
        </a-form-item>
        <a-form-item
          :name="[idx, 'fileType']"
          label="文件类型"
          :rules="{ required: true, message: '请选择文件类型' }"
        >
          <a-select
            v-model:value="item.fileType"
            placeholder="请选择"
            :options="SupportedFileTypeOptions"
          >
          </a-select>
        </a-form-item>
        <a-form-item
          :name="[idx, 'content']"
          label="内容"
          :rules="{ required: true, message: '请输入内容' }"
        >
          <a-textarea
            v-model:value="item.content"
            placeholder="请输入"
            :maxlength="10000"
            :rows="5"
            showCount
          />
        </a-form-item>
      </div>
      <div class="add-btn" ref="addBtn">
        <a-button class="!w-full" type="primary" ghost @click="addPrompt">
          <SvgIcon name="g-plus" />
          <span> 添加 </span>
        </a-button>
      </div>
    </a-form>
  </BasicModal>
</template>

<style lang="less" scoped>
.prompt-item {
  position: relative;
  padding: 8px;
  padding-bottom: 16px;
  margin-bottom: 8px;

  border: 1px dashed #bfbfbf;
  border-radius: 2px;
}

.close-icon {
  position: absolute;
  z-index: 10;
  right: 4px;
  top: 4px;
}
</style>
