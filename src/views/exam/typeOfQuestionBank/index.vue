<script lang="ts" setup>
import { h } from 'vue';
import { useTable, BasicTable } from '@geega-ui-plus/geega-ui';
import {
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
  V1ManageQuestionBankTypeDeleteById,
} from '/@/api/cddc.req';
import { useModal } from '@geega-ui-plus/geega-ui';
import AddOrEdit from './components/AddOrEdit.vue';
import type { QuestionBankTypeInfoElement } from '/@/api/cddc.model';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import AddOrEditPrompt from './components/AddOrEditPrompt.vue';

type TableRecord = QuestionBankTypeInfoElement;
// 注册表格
const [registerTable, table] = useTable({
  api: V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
  columns: [
    {
      title: '题库类型',
      dataIndex: 'name',

      width: 400,
    },
    {
      title: '关联字段',
      align: 'center',
      dataIndex: 'questionBankTypeColumns',
      customRender: ({ text }) => {
        const _columns = text || [];

        const _columnsName = _columns
          .map((i) => i.questionBankColumnDTO?.columnName)
          .filter(Boolean);

        return h(
          'div',
          {},
          _columnsName.map((o) => h(Tag, { color: 'blue', style: 'margin-right: 4px' }, () => o))
        );
      },
    },
    {
      title: '编辑时间',

      dataIndex: 'lastUpdateTime',
      width: 200,
    },
  ],
  rowKey: 'id',
  showIndexColumn: false,
  pagination: false,
  bordered: true,
  useSearchForm: true,
  formConfig: {
    labelWidth: 60,
    baseColProps: {
      span: 6,
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    schemas: [
      {
        field: 'name',
        label: '题库类型',
        component: 'Input',
        componentProps: {
          placeholder: '请输入',
          maxlength: 12,
        },
      },
    ],
  },
  // 表格-操作栏属性
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: 'right',
  },
});

const [registerModal, modalActions] = useModal();
const [registerPromptModal, promptModalActions] = useModal();

function handleAdd() {
  modalActions.openModal(true, {});
}

function handleEdit(record: TableRecord) {
  modalActions.openModal(true, {
    id: record.id,
    name: record.name,
    columnIds: (record.questionBankTypeColumns || [])
      .map((i) => i.questionBankColumnDTO?.id)
      .filter(Boolean),
  });
}

// async function handleDelete(record: TableRecord) {
//   try {
//     await V1ManageQuestionBankTypeDeleteById({
//       typeId: record.id,
//     });
//     reload();
//   } catch (error) {
//     console.error('删除失败：', error);
//   }
// }

function handleEditPrompt(record: TableRecord) {
  promptModalActions.openModal(true, {
    id: record.id,
  });
}

function reload() {
  table.reload();
}
</script>

<template>
  <div class="py-0 px-8px bg-[#fff]">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" class="!flex !items-center" ghost @click="handleAdd"
          ><SvgIcon name="g-plus" />新增题库类型</a-button
        >
      </template>
      <template #action="{ record }">
        <a-space>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" @click="handleEditPrompt(record)">编辑提示词</a-button>
          <!-- <BaseConfirm title="确定要删除吗？" @confirm="handleDelete(record)"></BaseConfirm> -->
        </a-space>
      </template>
    </BasicTable>
    <AddOrEdit @register="registerModal" @success="reload" />
    <AddOrEditPrompt @register="registerPromptModal" />
  </div>
</template>

<style lang="less" scoped></style>
