<script lang="ts" setup>
import { useTable, BasicTable } from '@geega-ui-plus/geega-ui';
import {
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  V1ManageQuestionBankColumnDeleteById,
} from '/@/api/cddc.req';
import { useRouter } from 'vue-router';
import { useModal } from '@geega-ui-plus/geega-ui';
import AddOrEdit from './AddOrEdit.vue';
import type { QuestionBankColumnDTOElement } from '/@/api/cddc.model';

type TableRecord = QuestionBankColumnDTOElement;

const router = useRouter();

// 注册表格
const [registerTable, table] = useTable({
  api: V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  columns: [
    {
      title: '字段名称',
      dataIndex: 'columnName',

      width: 500,
    },
    {
      title: '编辑时间',

      dataIndex: 'lastUpdateTime',
    },
  ],
  rowKey: 'id',
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 100,
    baseColProps: {
      span: 6,
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    schemas: [
      {
        field: 'columnName',
        label: '题库字段名称',
        component: 'Input',
        componentProps: {
          placeholder: '请输入',
          maxlength: 12,
        },
      },
    ],
  },
  // 表格-操作栏属性
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: 'right',
  },
});

const [registerModal, modalActions] = useModal();

function handleAdd() {
  modalActions.openModal(true, {});
}

function handleEdit(record: TableRecord) {
  modalActions.openModal(true, {
    id: record.id,
    columnKey: record.columnKey,
    columnName: record.columnName,
    questionBankColumnPropertySaveDTO: record.questionBankColumnPropertyList || [],
  });
}

async function handleDelete(record: TableRecord) {
  try {
    await V1ManageQuestionBankColumnDeleteById({
      columnId: record.id,
    });
    reload();
  } catch (error) {
    console.error('删除失败：', error);
  }
}

function reload() {
  table.reload();
}

function jumpMapManagePage(id: string, columnName: string) {
  router.push({
    path: '/exam/question-bank-field-config/relation-config',
    query: {
      id,
      columnName,
    },
  });
}
</script>

<template>
  <div class="py-0 px-8px bg-[#fff]">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" class="!flex !items-center" ghost @click="handleAdd">
          新增题库字段
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-button type="link" @click="jumpMapManagePage(record.id!, record.columnName!)">
            映射管理
          </a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <!-- <BaseConfirm title="确定要删除吗？" @confirm="handleDelete(record)"></BaseConfirm> -->
        </a-space>
      </template>
    </BasicTable>
    <AddOrEdit @register="registerModal" @success="reload" />
  </div>
</template>

<style lang="less" scoped></style>
