<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { useTable, BasicTable, type BasicColumn } from '@geega-ui-plus/geega-ui';
import {
  V1ManageQuestionBankColumnMappingListPost,
  V1ManageQuestionBankColumnGetQuestionBankColumnById,
} from '/@/api/cddc.req';
import { downloadFile, resolveFileName } from '/@/utils/file/download';
import ImportExcelFile from '/@/components/ImportFile/index.vue';
import { useRoute, useRouter } from 'vue-router';
import { defHttp } from '/@/utils/http/axios';
import {
  V1ManageQuestionBankColumnMappingExportUrl,
  V1ManageQuestionBankColumnMappingExportTemplateUrl,
  V1ManageQuestionBankColumnMappingImportColumnIdPostUrl,
} from '/@/api/paper/paper';
import { useGlobSetting } from '/@/hooks/setting';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import { message } from '@geega-ui-plus/ant-design-vue';
import { DownloadOutlined, LeftOutlined } from '@geega-ui-plus/icons-vue';

defineProps<{
  id: string;
}>();

const route = useRoute();

const router = useRouter()

const globSetting = useGlobSetting();

const id = computed(() => route.query.id as string);

const columnName = computed(() => route.query.columnName as string || '');

const labelWidth = computed(() => {

  const len = columnName.value.length

  return len > 8 ?  (len - 8) * 15 + 110 : 110
})


async function loadDetail() {
  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnById({
    columnId: id.value,
  });

  const _colunms = [
    {
      title: resp.columnName!,
      dataIndex: resp.columnKey!,

      width: 400,
    },
    ...(resp.questionBankColumnPropertyList || []).map((item) => ({
      title: item.propertyName!,
      dataIndex: item.propertyKey!,

    })),
  ];

  table.setColumns(_colunms as BasicColumn[]);
}

onMounted(loadDetail);

// 注册表格
const [registerTable, table] = useTable({
  api: async (params = {}) => {
    const resp = await V1ManageQuestionBankColumnMappingListPost({
      ...params,
      columnId: id.value,
    });
    const _list = resp?.reduce(
      (iter, item) => {
        const sameColumnValue = iter.find((o) => o[item.columnKey!] === item.columnValue);

        if (sameColumnValue) {
          sameColumnValue[item.propertyKey!] = item.propertyValue;
        } else {
          const _item = {
            [item.columnKey!]: item.columnValue,
            [item.propertyKey!]: item.propertyValue,
          };
          iter.push(_item);
        }
        return iter;
      },
      [] as Record<string, any>
    );

    return _list || [];
  },
  columns: [],
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: labelWidth.value,
    baseColProps: {
      span: 6,
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    schemas: [
      {
        field: 'columnValue',
        label: columnName.value!,
        component: 'Input',
        componentProps: {
          placeholder: '请输入',
          maxlength: 12,
        },
      },
      {
        field: 'propertyValue',
        label: '三方字段',
        component: 'Input',
        componentProps: {
          placeholder: '请输入',
          maxlength: 40,
        },
      },
    ],
  },
});

function reload() {
  table.reload();
}

//导入
function importSuccess() {
  reload();
}

async function handleDownloadFile(opt: { url: string; fileName?: string }, otherParams: Record<string, any> = {}) {
  const params = { columnId: id.value }

  const resp = await defHttp.getStream({
    url: opt.url,
    params: {
      ...params,
      ...otherParams
    }
  });
  const _contentDisposition = resp.headers?.['content-disposition'];

  const fileName = resolveFileName(_contentDisposition) || opt.fileName;

  downloadFile(resp.data, { fileName });
}
//导出
function handleExport() {
  if (!table.getDataSource()?.length) {
    message.info('无数据,无法导出');
    return;
  }
  const otherParams = table.getForm().getFieldsValue() || {}
  handleDownloadFile({
    url: V1ManageQuestionBankColumnMappingExportUrl,
    fileName: `题库${columnName.value}映射数据.xlsx`,
  }, otherParams);
}

//模板下载
function tplExportEvent() {
  handleDownloadFile({
    url: V1ManageQuestionBankColumnMappingExportTemplateUrl,
    fileName: `题库${columnName.value}映射数据上传模版.xlsx`,
  });
}

async function importApi(params: FormData) {
  const resp = await defHttp.getAxios().request({
    url: `${globSetting.urlPrefix}${V1ManageQuestionBankColumnMappingImportColumnIdPostUrl}/${id.value}`,
    method: 'POST',
    data: params,
    headers: {
      'Content-type': ContentTypeEnum.FORM_DATA,
      ignoreCancelToken: true,
    },
  });
  const data = resp?.data || {}
  if (data.code !== '0000') {
    throw new Error(data.msg || '上传失败！')
  } else {
    return data
  }
}
function goBack() {
  router.replace('/exam/question-bank-field-config')
}
</script>

<template>
  <div class="py-0 px-8px bg-[#fff]">
    <div class="py-2px cursor-pointer color-[#00996b]" @click="goBack"><LeftOutlined /><span>返回</span></div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <div class="flex gap-8px">
          <ImportExcelFile
            @success="importSuccess"
            :tpl-export-event="tplExportEvent"
            :api="importApi"
          >
          </ImportExcelFile>
          <a-button type="primary" ghost @click="handleExport"><DownloadOutlined />导出</a-button>
        </div>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped></style>
