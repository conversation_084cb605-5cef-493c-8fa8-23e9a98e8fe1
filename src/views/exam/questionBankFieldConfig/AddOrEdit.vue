<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import { BasicModal, useModalInner } from '@geega-ui-plus/geega-ui';
import { useLoadingFn } from '/@/composables';
import { V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPost } from '/@/api/cddc.req';
import type { V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody } from '/@/api/cddc.model';
import { message } from '@geega-ui-plus/ant-design-vue';
import regexMap from '/@/utils/regex/index';
import { cloneDeep } from 'lodash-es';

const formRef = ref<FormInstance>();

const ele = ref<HTMLElement>();

const emit = defineEmits(['success']);

type FormData = V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody;

function creatEmptyForm(): FormData {
  return {
    columnKey: '',
    columnName: '',
    questionBankColumnPropertySaveDTO: [
      {
        propertyKey: '',
        propertyName: '',
      },
    ],
  };
}

const formData = ref<FormData>(creatEmptyForm());

const modalTitle = computed(() => {
  return `${!formData.value?.id ? '新增' : '编辑'}题库字段`;
});

const [registerModal, modalActions] = useModalInner((data: FormData) => {
  if (data.id) {
    formData.value = cloneDeep(data);
  } else {
    formData.value = creatEmptyForm();
  }
  formRef.value?.clearValidate();
});

async function getFormData() {
  await formRef.value!.validateFields();
  return formData;
}

function add() {
  formData.value.questionBankColumnPropertySaveDTO.push({
    propertyKey: '',
    propertyName: '',
  });
  nextTick(() => {
    const eles = ele.value!.querySelectorAll('.third-party');

    const lastEle = eles[eles.length - 1];
    lastEle?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });
  });
}

async function _submit() {
  const values = await getFormData();

  await V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPost(values.value);
  message.success('操作成功');
  emit('success', values);
  modalActions.closeModal();
}

const submit = useLoadingFn(_submit);
</script>

<template>
  <BasicModal
    @register="registerModal"
    :title="modalTitle"
    @ok="submit"
    width="600px"
    :mask-closable="false"
  >
    <div class="py-0 px-8px relative" ref="ele">
      <AForm ref="formRef" layout="horizontal" labelAlign="left" :model="formData">
        <div class="flex pb-10px">
          <div class="flex-1">
            <AFormItem
              name="columnName"
              :rules="[
                { required: true, message: '请输入题库字段名称' },
                {
                  ...regexMap.chineseEnglishNumberAndNoUnderscore,
                  message: '只能输入中文、数字、字母',
                },
              ]"
              label="题库字段名称:"
            >
              <a-input v-model:value="formData.columnName" :maxlength="12" placeholder="请输入" />
            </AFormItem>
          </div>
          <div class="w-20px"></div>
          <div class="flex-1">
            <AFormItem
              name="columnKey"
              :rules="[
                { required: true, message: '请输入字段KEY' },
                {
                  ...regexMap.englishNumberAndUnderscore,
                },
              ]"
              label="字段KEY:"
            >
              <a-input v-model:value="formData.columnKey" :maxlength="20" placeholder="请输入" />
            </AFormItem>
          </div>
          <div class="w-30px"></div>
        </div>
        <div
          class="flex third-party"
          v-for="(item, idx) in formData.questionBankColumnPropertySaveDTO"
        >
          <div class="flex-1">
            <AFormItem
              :name="['questionBankColumnPropertySaveDTO', idx, 'propertyName']"
              :rules="[{ required: true, message: '请输入三方字段名称' }]"
              label="三方字段名称:"
            >
              <a-input v-model:value="item.propertyName" :maxlength="40" placeholder="请输入" />
            </AFormItem>
          </div>
          <div class="w-20px"></div>
          <div class="flex-1">
            <AFormItem
              :name="['questionBankColumnPropertySaveDTO', idx, 'propertyKey']"
              :rules="[
                { required: true, message: '请输入三方字段KEY' },
                {
                  ...regexMap.englishNumberAndUnderscore,
                },
              ]"
              label="三方字段KEY:"
            >
              <a-input v-model:value="item.propertyKey" :maxlength="20" placeholder="请输入" />
            </AFormItem>
          </div>
          <div class="w-30px justify-end flex pt-24px">
            <a-button
              type="link"
              class="!px-4px !mx-0"
              :disabled="formData.questionBankColumnPropertySaveDTO.length <= 1"
              @click="
                () => {
                  formData.questionBankColumnPropertySaveDTO.splice(idx, 1);
                }
              "
            >
              <SvgIcon name="g-trash" />
            </a-button>
          </div>
        </div>
      </AForm>
      <div class="flex justify-center sticky bottom-0px bg-[#fff]">
        <AButton
          class="add-question-btn"
          :disabled="formData.questionBankColumnPropertySaveDTO.length >= 20"
          type="link"
          @click="add"
        >
          <SvgIcon name="g-plus" /> 新增
        </AButton>
      </div>
    </div>
  </BasicModal>
</template>

<style lang="less" scoped>
.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
