<script lang="ts" setup>
import { ref, computed } from 'vue';
import {
  AuditOutlined,
  ReadOutlined,
  SolutionOutlined,
  BarChartOutlined,
  CalendarOutlined,
  FundOutlined,
} from '@geega-ui-plus/icons-vue';
import { useRouter } from 'vue-router';
import { usePermission } from '/@/hooks/web/usePermission';
import { useMessage } from '/@/hooks/web/useMessage';

const router = useRouter();
const { createMessage } = useMessage();
const cardList = ref([
  {
    icon: AuditOutlined,
    title: '智能技能道场',
    subtitle: '基于智能化的技能培训与评估系统',
    color: '#D8A24A',
    url: '/cddc/home',
    permission: 'home:index:cddc',
  },
  {
    icon: SolutionOutlined,
    title: '智能考试系统',
    subtitle: '智能化的在线考试与评估平台',
    color: '#A3C97A',
    url: '/exam/home',
    permission: 'home:index:exam',
  },
  {
    icon: ReadOutlined,
    title: '智能学习系统',
    subtitle: '个性化的在线学习与培训平台',
    color: '#7AB8CC',
    url: '/learn/home',
    permission: 'home:index:learn',
  },
  {
    icon: BarChartOutlined,
    title: '智能生产评定',
    subtitle: '智能化的生产绩效评估系统',
    color: '#D87A6E',
    url: '/judgement/list',
    permission: 'home:index:judgement',
  },
  {
    icon: CalendarOutlined,
    title: '虚拟班组长',
    subtitle: '个性化在线问题排查训练系统',
    color: '#A98ABE',
    url: '/virtual/test/index',
    permission: 'home:index:virtual-test',
  },
  {
    icon: FundOutlined,
    title: '智能数字大屏',
    subtitle: '一站式数据看板，提供实时监控和分析',
    color: '#84C27F',
    url: '/dashboard/index',
    permission: 'home:index:data-dashboard',
  },
]);
const go = (url: string) => {
  // 排班智能评估，暂时不支持
  if (url === '/scheduling/home') {
    createMessage.warning('排班智能评估，功能暂未开放');
    return;
  }
  router.push(url);
};
const { hasPermission } = usePermission();

// 过滤有权限的卡片
const filteredCardList = computed(() => {
  return cardList.value.filter((card) => hasPermission(card.permission));
});

// 前4个卡片
const topCards = computed(() => {
  return filteredCardList.value.slice(0, 4);
});

// 第5个卡片（如果存在）
const bottomCard = computed(() => {
  return filteredCardList.value.length > 4 ? filteredCardList.value[4] : null;
});
</script>

<template>
  <div class="home-container">
    <div class="card-grid">
      <div
        v-for="(card, index) in filteredCardList"
        :key="index"
        class="card-item"
        @click="go(card.url)"
      >
        <div class="card-icon-wrapper">
          <component :is="card.icon" :style="{ color: card.color, fontSize: '48px' }" />
        </div>
        <div class="card-title">{{ card.title }}</div>
        <div class="card-subtitle">{{ card.subtitle }}</div>
      </div>
    </div>
    <!-- <div class="card-grid-bottom">
      <div v-if="bottomCard" class="card-item card-item-bottom" @click="go(bottomCard.url)">
        <div class="card-icon-wrapper">
          <component :is="bottomCard.icon" :style="{ color: bottomCard.color, fontSize: '48px' }" />
        </div>
        <div class="card-title">{{ bottomCard.title }}</div>
        <div class="card-subtitle">{{ bottomCard.subtitle }}</div>
      </div>
    </div> -->
  </div>
</template>

<style lang="less" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px); // Adjust based on header/footer height
  padding: 40px;
  box-sizing: border-box;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  width: 100%;
  max-width: 1000px; // Adjust as needed
  margin-bottom: 30px;
}

.card-grid-bottom {
  display: flex;
  width: 100%;
  max-width: 1000px;
}

.card-item {
  background-color: #333333; // Darker card background from image
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 220px; // Ensure cards have a good height
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }
}

.card-item-bottom {
  width: calc(50% - 15px); // Match width of one card in the 2-column grid
}

.card-icon-wrapper {
  margin-bottom: 20px;
  height: 50px; // Fixed height for icon area
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9); // 调整标题颜色以增强对比度
  margin-bottom: 10px;
}

.card-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.65); // 调整副标题颜色以优化视觉层级
  line-height: 1.5;
}

// Responsive adjustments if needed
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
  .card-item-bottom {
    width: 100%;
  }
}
</style>
