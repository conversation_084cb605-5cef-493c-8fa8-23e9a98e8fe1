<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import {
  PositionLevelOptions,
  PositionLevelTypeEnum,
  ProjectType,
  TrainItemStatusOptions,
} from '/@/enums/projectEnum';
import {
  V1EvaluateConfigSelect,
  V1ManageExamPaperPagePost,
  V1ManageJobLevelsJobLevelId,
  V1ManageJobLevelsMaxLevelJobId,
  V1ManageJobLevelsPost,
  V1ManageJobLevelsPut,
  V1ManageJobsPagePost,
  V1ManageLearningProjectsPagePost,
  V1ManageTranProjectPagePost,
} from '/@/api/cddc.req';
import type {
  ResultLevelDetail,
  V1ManageJobLevelsJobLevelIDGetResponseResult,
  V1ManageJobLevelsPostRequestBodyLevelDetail,
} from '/@/api/cddc.model';
import DynamicItemList from '/@/components/DynamicItemList.vue';
import { useAsyncData, useLoadingFn } from '/@/composables';
import { groupBy } from 'lodash-es';
import { PaperTypeEnum } from '/@/enums/paperEnum';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '编辑岗级配置',
});

const currentProps = ref<{ id?: string }>({});

const currentData = ref<V1ManageJobLevelsJobLevelIDGetResponseResult>({});

const trainSelectOptionsApi = useAsyncData(async () => {
  const resp = await V1ManageTranProjectPagePost({
    currentPage: 1,
    pageSize: 1000,
    data: {
      projectType: +ProjectType.Training,
    },
  });

  return resp.records?.map((item) => ({
    value: item.id,
    label: item.name,
  }));
}, []);

const learningSelectOptionsApi = useAsyncData(async () => {
  const resp = await V1ManageLearningProjectsPagePost({
    currentPage: 1,
    pageSize: 1000,
    data: {},
  });

  return resp.records?.map((item) => ({
    value: item.id,
    label: item.name,
  }));
}, []);

const examSelectOptionsApi = useAsyncData(async () => {
  const resp = await V1ManageExamPaperPagePost({
    currentPage: 1,
    pageSize: 1000,
    data: {
      questionPropertyList: [],
      type: PaperTypeEnum.SKILL_INSPECTION,
      status: 'RELEASE',
    },
  });

  return resp.records?.map((item) => ({
    value: item.id,
    label: item.name,
  }));
}, []);

const onlineJudgementSelectOptionsApi = useAsyncData(async () => {
  const resp = await V1EvaluateConfigSelect();

  return resp?.map((item) => ({
    value: item.id,
    label: item.evaluateName,
  }));
}, []);

const [registerDrawer, drawerActions] = useDrawerInner(async (props: any) => {
  state.drawerTitle = props?.id ? '编辑岗级配置' : '新增岗级配置';
  currentProps.value = props || {};

  await formActions.resetFields();
  currentData.value = {};

  await init();
  formActions.clearValidate();
});

const trainSelectOptions = computed(() => {
  return trainSelectOptionsApi.data.value?.map((item) => ({
    ...item,
    disabled: currentData.value.levelDetails?.find((n) => n.id === item.value)?.readOnly,
  }));
});

const [registerForm, formActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'jobId',
      label: '岗位名称',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择岗位名称',
        disabled: computed(() => currentProps.value.id),
        api: async () => {
          const result = await V1ManageJobsPagePost({ currentPage: 1, pageSize: 1000, data: {} });
          return (result.records || [])?.map((item) => ({ label: item.name, value: item.id }));
        },
        onChange(jobId?: string) {
          updateFormFields(jobId);
        },
      },
      rules: [{ required: true, trigger: 'blur' }],
    },
    {
      field: 'level',
      label: '岗级',
      required: true,
      component: 'Select',
      componentProps: {
        // disabled: computed(() => currentProps.value.id),
        disabled: true,
        options: computed(() => {
          if (currentProps.value.id) {
            return PositionLevelOptions;
          }

          return PositionLevelOptions.map((item) => ({
            ...item,
            disabled: currentData.value.level! >= +item.value,
          }));
        }),
        placeholder: '请选择岗级',
      },
    },
    {
      field: PositionLevelTypeEnum.TRAIN,
      label: '道场训练项目',
      component: 'Input',
      slot: 'select-project',
      rules: [
        {
          async validator(rule, value) {
            const valid = (value || [])
              .filter((item) => item.itemId || item.extra)
              .every((item) => item.itemId && item.extra);

            if (!valid) {
              throw new Error('请选择项目以及状态!');
            }
          },
        },
      ],
    },
    {
      field: PositionLevelTypeEnum.LEARNING,
      label: '学习项目',
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        allowClear: false,
        showArrow: true,
        optionFilterProp: 'label',
        options: computed(() => {
          return learningSelectOptionsApi.data.value?.map((item) => ({
            ...item,
            disabled: currentData.value.levelDetails?.find((n) => n.itemId === item.value)
              ?.readOnly,
          }));
        }),
      },
    },
    {
      field: PositionLevelTypeEnum.EXAM,
      label: '考试项目',
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        showArrow: true,
        allowClear: false,
        optionFilterProp: 'label',
        options: computed(() => {
          return examSelectOptionsApi.data.value?.map((item) => ({
            ...item,
            disabled: currentData.value.levelDetails?.find((n) => n.itemId === item.value)
              ?.readOnly,
          }));
        }),
      },
    },
    {
      field: PositionLevelTypeEnum.ONLINE,
      label: '在线考评项目',
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        allowClear: false,
        showArrow: true,
        optionFilterProp: 'label',
        options: computed(() => {
          return onlineJudgementSelectOptionsApi.data.value?.map((item) => ({
            ...item,
            disabled: currentData.value.levelDetails?.find((n) => n.itemId === item.value)
              ?.readOnly,
          }));
        }),
      },
    },
  ],
  showActionButtonGroup: false,
});

const updateFormFields = useLoadingFn(_updateFormFields);

initSelectOptions();

async function initSelectOptions() {
  trainSelectOptionsApi.load();
  learningSelectOptionsApi.load();
  examSelectOptionsApi.load();
  onlineJudgementSelectOptionsApi.load();
}

async function init() {
  const id = currentProps.value.id;

  if (!id) {
    await formActions.setFieldsValue({
      [PositionLevelTypeEnum.TRAIN]: [{}],
    });
    return;
  }

  await updateFormFields();
}

/**
 * 如果有 jobId, 则是通过修改职位触发的更新， 则 level 需要取下一个 level 的值
 * @param jobId
 */
async function _updateFormFields(jobId?: string) {
  let data: V1ManageJobLevelsJobLevelIDGetResponseResult = {};

  if (jobId) {
    data = (await V1ManageJobLevelsMaxLevelJobId({ jobId })) || {};
    data.levelDetails?.forEach((item) => {
      item.readOnly = true;
    });
  } else if (currentProps.value.id) {
    data = await V1ManageJobLevelsJobLevelId({
      jobLevelId: currentProps.value.id,
    });
  }

  currentData.value = data || {};

  const levelDetails = groupBy(data.levelDetails, (item) => item.itemType);

  let level = (jobId ? (data.level ?? -1) + 1 : data.level)?.toString();

  // 如果职级不存在，就设置成最后一级
  if (!PositionLevelOptions.find((n) => n.value == level)) {
    level = PositionLevelOptions.at(-1)?.value;
  }

  await formActions.setFieldsValue({
    ...data,
    level: level,
    [PositionLevelTypeEnum.TRAIN]: levelDetails[PositionLevelTypeEnum.TRAIN] || [{}],
    [PositionLevelTypeEnum.LEARNING]: (levelDetails[PositionLevelTypeEnum.LEARNING] || []).map(
      (item) => item.itemId
    ),
    [PositionLevelTypeEnum.EXAM]: (levelDetails[PositionLevelTypeEnum.EXAM] || []).map(
      (item) => item.itemId
    ),
    [PositionLevelTypeEnum.ONLINE]: (levelDetails[PositionLevelTypeEnum.ONLINE] || []).map(
      (item) => item.itemId
    ),
  });
}

function calcTrainOptions(index: number) {
  const selected: ResultLevelDetail[] = (
    formActions.getFieldsValue()[PositionLevelTypeEnum.TRAIN] || []
  ).filter((_, idx) => idx !== index);

  return trainSelectOptions.value?.map((item) => ({
    ...item,
    disabled: selected.find((n) => n.itemId === item.value),
  }));
}

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const _data = await formActions.validate();
    const id = currentProps.value.id;

    const details: V1ManageJobLevelsPostRequestBodyLevelDetail[] = [
      ...(_data[PositionLevelTypeEnum.TRAIN] || []).map((item) => ({
        ...item,
        itemType: PositionLevelTypeEnum.TRAIN,
      })),
      ...(_data[PositionLevelTypeEnum.LEARNING] || []).map((item) => ({
        itemId: item,
        itemType: PositionLevelTypeEnum.LEARNING,
      })),
      ...(_data[PositionLevelTypeEnum.EXAM] || []).map((item) => ({
        itemId: item,
        itemType: PositionLevelTypeEnum.EXAM,
      })),
      ...(_data[PositionLevelTypeEnum.ONLINE] || []).map((item) => ({
        itemId: item,
        itemType: PositionLevelTypeEnum.ONLINE,
      })),
    ].filter((n) => n.itemId && n.itemType);

    const data = {
      ..._data,
      levelDetails: details,
    };

    if (id) {
      await V1ManageJobLevelsPut({
        ...data,
        id,
      });
      message.success('修改成功');
    } else {
      await V1ManageJobLevelsPost(data);
      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <a-spin :spinning="updateFormFields.loading">
      <BasicForm @register="registerForm">
        <template #select-project="{ model, field }">
          <DynamicItemList v-model:value="model[field]">
            <template #default="{ item, removeItem, index }">
              <div class="flex gap-1">
                <a-select
                  class="w-0! flex-1"
                  v-model:value="item.itemId"
                  :options="calcTrainOptions(index)"
                  :disabled="item.readOnly"
                  show-search
                  optionFilterProp="label"
                  placeholder="请选择项目"
                />
                <a-select
                  class="w-0! flex-1"
                  v-model:value="item.extra"
                  :disabled="item.readOnly"
                  :options="TrainItemStatusOptions"
                  placeholder="请选择项目状态"
                />
                <a-button type="link" danger :disabled="item.readOnly" @click="removeItem()">
                  删除
                </a-button>
              </div>
            </template>
          </DynamicItemList>
        </template>
      </BasicForm>
    </a-spin>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
