<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import { PositionLevelOptions } from '/@/enums/projectEnum';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import EditDrawer from './components/EditDrawer.vue';
import {
  V1ManageJobLevelsJobLevelIdDelete,
  V1ManageJobLevelsPagePost,
  V1ManageJobsPagePost,
} from '/@/api/cddc.req';
import type { V1ManageJobLevelsJobLevelIDGetResponseResult } from '/@/api/cddc.model';
import { getDictLabel } from '/@/dict';

const columns: EnhancedColumn[] = [
  {
    title: '岗位名称',
    dataIndex: 'jobName',
    width: 200,
    search: {
      field: 'jobId',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        style: 'max-width: 100%',
        showSearch: true,
        optionFilterProp: 'label',
        api: async () => {
          const result = await V1ManageJobsPagePost({ currentPage: 1, pageSize: 1000, data: {} });
          return (result.records || [])?.map((item) => ({ label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '岗级',
    dataIndex: 'level',
    width: 150,
    customRender(opt) {
      return getDictLabel(PositionLevelOptions, opt.text?.toString());
    },
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择岗级',
        options: PositionLevelOptions,
      },
    },
  },
  {
    title: '编辑时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
  {
    title: '编辑人',
    dataIndex: 'lastUpdateBy',
    width: 150,
  },
];

const [registerTable, tableActions] = useTable({
  columns,
  api: V1ManageJobLevelsPagePost,
});

const { createDeteleConfirm } = useMessage();

const [registerDrawer, drawerActions] = useDrawer();

function handleAdd() {
  drawerActions.openDrawer(true, {});
}

function handleEdit(record: V1ManageJobLevelsJobLevelIDGetResponseResult) {
  drawerActions.openDrawer(true, record);
}

function handleDelete(record: V1ManageJobLevelsJobLevelIDGetResponseResult) {
  createDeteleConfirm({
    content: `​同岗位的当前岗级及以上岗级会全部删除，请确定是否删除岗级？`,
    onOk: async () => {
      await V1ManageJobLevelsJobLevelIdDelete({ jobLevelId: record.id! });
      message.success('删除成功');
      tableActions.reload();
    },
  });
}

function onSubmit() {
  tableActions.reload();
}
</script>

<template>
  <div class="position-level-page">
    <BasicTablePlus @register="registerTable">
      <template #toolbar>
        <a-button type="primary" ghost @click="handleAdd">+新增岗级</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
    <EditDrawer @register="registerDrawer" @success="onSubmit" />
  </div>
</template>

<style lang="less" scoped>
.position-level-page {
  padding: 0 8px;
}
</style>
