<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { Button, message } from '@geega-ui-plus/ant-design-vue';
import { PlusOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import EditDrawer from './components/EditDrawer.vue';

import { V1ManageJobsPagePost, V1ManageJobsJobIdDelete } from '/@/api/cddc.req';
import type { V1ManageJobsJobIDGetResponseResult } from '/@/api/cddc.model';

const columns: EnhancedColumn[] = [
  {
    title: '岗位名称',
    dataIndex: 'name',
    width: 200,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请选择岗位名称',
      },
    },
  },
  {
    title: '编辑时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
  {
    title: '编辑人',
    dataIndex: 'lastUpdateBy',
    width: 150,
  },
];

const [registerTable, tableActions] = useTable({
  columns,
  api: V1ManageJobsPagePost,
});

const { createDeteleConfirm } = useMessage();

const [registerDrawer, drawerActions] = useDrawer();

function handleAdd() {
  drawerActions.openDrawer(true, {});
}

function handleEdit(record: V1ManageJobsJobIDGetResponseResult) {
  drawerActions.openDrawer(true, record);
}

function handleDelete(record: V1ManageJobsJobIDGetResponseResult) {
  createDeteleConfirm({
    content: `请确定是否删除岗位?`,
    onOk: async () => {
      await V1ManageJobsJobIdDelete({ jobId: record.id! });
      message.success('删除成功');
      tableActions.reload();
    },
  });
}

async function onSubmit() {
  tableActions.reload();
}
</script>

<template>
  <div class="position-level-page">
    <BasicTablePlus @register="registerTable">
      <template #toolbar>
        <Button type="primary" ghost @click="handleAdd" style="margin-bottom: 12px">
          <PlusOutlined /> 新增岗位
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
    <EditDrawer @register="registerDrawer" @success="onSubmit" />
  </div>
</template>

<style lang="less" scoped>
.position-level-page {
  padding: 0 8px;
}
</style>
