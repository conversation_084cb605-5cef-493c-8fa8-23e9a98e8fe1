<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import { V1ManageJobsPost, V1ManageJobsPut } from '/@/api/cddc.req';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '编辑岗位配置',
});

const currentData = ref<any>({});

const [registerDrawer, drawerActions] = useDrawerInner(async (props: any) => {
  props = props || {};
  state.drawerTitle = props?.id ? '编辑岗位配置' : '新增岗位配置';
  currentData.value = props;

  await fromActions.resetFields();
  await fromActions.setFieldsValue({
    ...currentData.value,
  });
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'name',
      label: '岗位名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 20,
      }
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();
    const id = currentData.value.id;

    if (id) {
      await V1ManageJobsPut({
        ...data,
        id,
      });
      message.success('修改成功');
    } else {
      await V1ManageJobsPost(data);
      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
