<script lang="ts" setup>
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { Button, message } from '@geega-ui-plus/ant-design-vue';
import { PlusOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import EditDrawer from './components/EditDrawer.vue';
import { V1EvaluateConfigPagePost, V1EvaluateConfigDeleteIdDelete } from '/@/api/cddc.req';
import type { V1EvaluateConfigSelectGetResponseResult } from '/@/api/cddc.model';

const columns: EnhancedColumn[] = [
  {
    title: '评定项目名称',
    dataIndex: 'evaluateName',
    width: 200,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入评定项目名称',
      },
    },
  },
  {
    title: '编辑人',
    dataIndex: 'editor',
    width: 100,
  },
  {
    title: '编辑时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
];

const [registerTable, tableActions] = useTable({
  columns,
  api: V1EvaluateConfigPagePost,
});

const { createDeteleConfirm } = useMessage();

const [registerDrawer, drawerActions] = useDrawer();

function handleEdit(record: any) {
  drawerActions.openDrawer(true, record);
}

function handleDelete(record: V1EvaluateConfigSelectGetResponseResult) {
  createDeteleConfirm({
    content: `请确定是否删除评定项目?`,
    onOk: async () => {
      await V1EvaluateConfigDeleteIdDelete({ id: record.id! });
      message.success('删除成功');
      tableActions.reload();
    },
  });
}

function handleAdd() {
  drawerActions.openDrawer(true, {});
}

async function onSubmit() {
  tableActions.reload();
}
</script>

<template>
  <div class="online-judgement-page">
    <BasicTablePlus @register="registerTable">
      <template #toolbar>
        <Button type="primary" ghost @click="handleAdd" style="margin-bottom: 12px">
          <PlusOutlined /> 新增评定项目
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
    <EditDrawer @register="registerDrawer" @success="onSubmit" />
  </div>
</template>

<style lang="less" scoped>
.online-judgement-page {
  padding: 0 8px;
}
</style>
