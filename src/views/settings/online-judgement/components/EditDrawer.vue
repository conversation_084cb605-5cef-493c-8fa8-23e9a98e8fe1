<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import DynamicItemList from '/@/components/DynamicItemList.vue';
import { V1EvaluateConfigSavePost } from '/@/api/cddc.req';
import type { V1EvaluateConfigSelectGetResponseResult } from '/@/api/cddc.model';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '编辑评定项目',
});

const currentData = ref<V1EvaluateConfigSelectGetResponseResult>({});

const [registerDrawer, drawerActions] = useDrawerInner(
  async (props: V1EvaluateConfigSelectGetResponseResult) => {
    props = props || {};

    state.drawerTitle = props?.id ? '编辑评定项目' : '新增评定项目';
    currentData.value = props;

    await fromActions.resetFields();
    await fromActions.setFieldsValue({
      ...currentData.value,
      evaluateProjectList: (props.evaluateProjectList || [undefined]).map((item) => ({
        name: item,
      })),

      // 展示用
      _result: 'pass',
    });
  }
);

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'evaluateName',
      label: '项目名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 20,
        placeholder: '请输入项目名称',
      },
    },
    {
      field: 'remark',
      label: '项目说明',
      component: 'InputTextArea',
      componentProps: {
        maxLength: 200,
        placeholder: '请输入项目说明',
        rows: 4,
      },
    },
    {
      field: 'evaluateProjectList',
      label: '评审项目',
      component: 'Input',
      slot: 'projects',
    },
    {
      field: '_result',
      label: '评定结果',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '不通过', value: 'reject' },
        ],
        disabled: true,
      },
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();
    const id = currentData.value.id;

    const evaluateProjectList = (data.evaluateProjectList || [])
      .map((item) => item.name)
      .filter((item) => item);

    await V1EvaluateConfigSavePost({
      ...data,
      id,
      evaluateProjectList,
    });

    message.success(id ? '修改成功' : '创建成功');

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #projects="{ model, field }">
        <DynamicItemList v-model:value="model[field]" :maxCount="10">
          <template #default="{ item, removeItem }">
            <div class="flex gap-1">
              <a-input v-model:value="item.name" :maxlength="20" placeholder="请输入" />
              <a-button type="link" danger @click="removeItem()">删除</a-button>
            </div>
          </template>
        </DynamicItemList>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
