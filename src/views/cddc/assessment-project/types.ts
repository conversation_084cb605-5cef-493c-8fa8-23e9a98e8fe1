export type AssessmentProjectStatus = 'ENABLE' | 'DISABLE';

export interface AlgorithmItem {
  modelCode: string | null;
  modelJson?: string;
  modelJsonObj?: {
    isIndicator: number;
    isQualified: boolean;
    isActionQualified: boolean;
  };
}

export interface AssessmentProjectFormData {
  id?: number;
  name: string;
  locationList?: [] | null;
  locationIds: string[];
  requiredTime: number;
  requiredCount: number;
  qualifiedRate: number;
  status?: AssessmentProjectStatus;
  actionQualifiedRate: number;
  countMethod: string | null;
  effectRequirement: number;
  modelList: AlgorithmItem[];
  // Assessment-specific fields
  assessmentType: 'INDIVIDUAL' | 'GROUP';
  maxParticipants?: number;
  minScore: number;
  maxScore: number;
  passingScore: number;
  assessmentDuration: number; // in minutes
}

export interface AssessmentProjectRecord
  extends Omit<AssessmentProjectFormData, 'countMethod' | 'effectRequirement'> {
  method: string;
  status: AssessmentProjectStatus;
  updateTime: string;
  updater: string;
}

export interface AssessmentProjectSubmitData extends AssessmentProjectFormData {
  algorithms: AlgorithmItem[];
}

export type AlgorithmValidateError = {
  type?: string;
  checkbox?: string;
};

export type AlgorithmValidateResult = {
  isValid: boolean;
  errors: AlgorithmValidateError[];
  firstErrorMessage?: string;
};
