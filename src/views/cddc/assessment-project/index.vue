<template>
  <BaseProjectManagement
    :project-type="2"
    project-name="考核项目"
    :columns="columns"
    :drawer-component="AssessmentProjectDrawer"
  />
</template>

<script lang="ts" setup>
import { columns } from './data';
import AssessmentProjectDrawer from './components/ProjectDrawer.vue';
import BaseProjectManagement from '/@/components/project/BaseProjectManagement.vue';
</script>

<style lang="less" scoped>
.assessment-project-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
