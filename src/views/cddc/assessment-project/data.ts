import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

export const columns: EnhancedColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入名称',
      },
    },
  },
  {
    title: '关联工位',
    dataIndex: 'locationList',
    width: 150,
    customRender: ({ text }) => {
      return text?.map((item) => item.name).join(', ') || '-';
    },
  },
  {
    title: '考核时长(min)',
    dataIndex: 'requestDuration',
    width: 120,
  },
  {
    title: '要求次数',
    dataIndex: 'requestFrequency',
    width: 120,
  },
  {
    title: '合格率要求(%)',
    dataIndex: 'requestQualificationRate',
    width: 120,
  },
  {
    title: '动作达标率要求(%)',
    dataIndex: 'requestActionRate',
    width: 140,
  },
  {
    title: '关联算法',
    dataIndex: 'modelList',
    width: 200,
    customRender: ({ text }) => {
      return text?.map((item) => item.modelName).join(', ') || '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return text === 'ENABLE' ? '启用' : '停用';
    },
  },
  {
    title: '最近更新时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
  {
    title: '最近操作人',
    dataIndex: 'lastUpdateBy',
    width: 120,
  },
];
