<template>
  <div :class="prefixCls">
    <a-result title="Geega Admin VUE3 Template" :sub-title="$t('views.dashboard.welcome.Geega.48176533', { defaultValue: '开箱即用的后台管理系统模板/同时支持Micro-app/无界/qiankun' })">
      <template #icon>
        <Icon class="coder-icon" icon="g-goods1" :size="80" />
      </template>
      <template #extra>
        <a-button type="primary" @click="goTemplateDoc">{{$t('views.dashboard.welcome.Geega.1fd872a3', { defaultValue: '项目使用文档' })}}</a-button>
        <a-button type="dashed" @click="goVSGeegaUICom">{{$t('views.dashboard.welcome.Geega.55093dc7', { defaultValue: '组件库文档' })}}</a-button>
        <div style="margin-bottom: 20px;"></div>
        <a-button type="primary" @click="goVSGeegaUIDoc">{{$t('views.dashboard.welcome.Geega.a23c1e13', { defaultValue: 'VS Code GeegaUI 辅助工具' })}}</a-button>
        <a-button type="ghost" @click="goVSGeegaAntDoc">{{$t('views.dashboard.welcome.Geega.4216dcf5', { defaultValue: 'VS Code Geega ant design 辅助工具' })}}</a-button>
        <div style="margin-bottom: 20px;"></div>
        <a-button type="primary" @click="goTable">{{$t('views.dashboard.welcome.Geega.e2af512d', { defaultValue: 'gxe-table(VUE3) 大数据 Table（虚拟表格）' })}}</a-button>
        <a-button type="primary" @click="goAPI">{{$t('views.dashboard.welcome.Geega.aeec874b', { defaultValue: '接口代码生成器' })}}</a-button>
      </template>
    </a-result>
  </div>
</template>
<script lang="ts">
  import type { PropType } from 'vue';
  import { defineComponent } from 'vue';
  import { Result } from '@geega-ui-plus/ant-design-vue';
  import { Icon } from '@geega-ui-plus/geega-ui';
  import { useDesign } from '/@/hooks/web/useDesign';
  export default defineComponent({
    name: 'Geega',
    components: {
      [Result.name]: Result,
      Icon,
    },
    props: {
      size: {
        type: Number as PropType<number>,
        default: 600,
      },
    },
    setup() {
      const { prefixCls } = useDesign('geega-welcome');

      const goTemplateDoc = () => {
        window.open('http://geega-admin-doc.cloud-dev.geega.com/guide/')
      }

      const goVSGeegaUIDoc = () => {
        window.open('https://marketplace.visualstudio.com/items?itemName=geega-bsc.vscode-geega-ui-helper')
      };

      const goVSGeegaAntDoc = () => {
        window.open('https://marketplace.visualstudio.com/items?itemName=geega-bsc.vscode-geega-ant-helper');
      };

      const goTable = () => {
        window.open('https://geega-ui-plus-docs.cloud-dev.geega.com/gxe-table/docs/guide/install.html');
      };

      const goVSGeegaUICom = () => {
        window.open('http://geega-admin-doc.cloud-dev.geega.com/comp/');
      };

      const goAPI = () => {
        window.open('https://npm.geega.com/-/web/detail/@geega-ui-plus/generate-api');
      };

      return {
        prefixCls,
        goTemplateDoc,
        goVSGeegaUIDoc,
        goVSGeegaAntDoc,
        goVSGeegaUICom,
        goTable,
        goAPI
      };
    },
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-geega-welcome';

  .@{prefix-cls} {
    position: relative;
    top: -10%;

    .coder-icon {
      color: @primary-color;
    }
  }
</style>
