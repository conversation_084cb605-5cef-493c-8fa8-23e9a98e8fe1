<template>
  <div class="welcome">
    <Geega />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import Geega from './Geega.vue';
  export default defineComponent({
    name: 'Welcome',
    components: { <PERSON>ga },
  });
</script>
<style lang="less" scoped>
  .welcome {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }
</style>
