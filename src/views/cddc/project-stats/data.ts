import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { V1ManageCommonProjectTypeEnum, V1ManageTranProjectListPost } from '/@/api/cddc.req';

// 表格配置
export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 200,
    align: 'left',
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTranProjectListPost({ projectType: 1 });
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '所属工艺',
    dataIndex: 'countAlgorithmShow',
    width: 160,
    align: 'left',
    search: {
      field: 'countAlgorithm',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageCommonProjectTypeEnum();
          return resp.map((item) => ({ ...item, label: item.desc, value: item.code }));
        },
      },
    },
  },
  {
    title: '要求人数',
    dataIndex: 'configNum',
    width: 120,
    align: 'left',
    customRender: ({ record }) => {
      return record.configNum || '32';
    },
  },
  {
    title: '完成训练人数',
    dataIndex: 'completedNum',
    width: 140,
    align: 'left',
  },
  {
    title: '完成考核人数',
    dataIndex: 'checkedNum',
    width: 140,
    align: 'left',
  },
  {
    title: '累计作业场次',
    dataIndex: 'trainNum',
    width: 140,
    align: 'left',
  },
  {
    title: '累计作业次数',
    dataIndex: 'jobNum',
    width: 140,
    align: 'left',
  },
];
