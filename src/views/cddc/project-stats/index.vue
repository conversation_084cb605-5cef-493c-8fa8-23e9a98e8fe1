<template>
  <div class="project-stats-container">
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'completedNum'">
          <a-button type="link" @click="handleTrainingClick(record)">{{
            record.completedNum || 0
          }}</a-button>
        </template>
        <template v-if="column.dataIndex === 'checkedNum'">
          <a-button type="link" @click="handleAssessmentClick(record)">{{
            record.checkedNum || 0
          }}</a-button>
        </template>
        <template v-if="column.dataIndex === 'totalSessions'">
          {{ record.totalSessions || 0 }}
        </template>
      </template>
    </BasicTablePlus>
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageStatisticalReportProjectPost } from '/@/api/cddc.req';
import { useRouter } from 'vue-router';

const router = useRouter();

const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};
  const data = {
    ...rawQuery,
    startTime: rawQuery.timeRange?.at(0),
    endTime: rawQuery.timeRange?.at(1),
  };

  params.data = data;
  return params;
};

// 点击完成训练人数跳转到训练数据报表页面
const handleTrainingClick = (record: any) => {
  router.push({
    path: '/cddc/trainReport',
    query: {
      projectId: record.projectId,
      projectName: record.projectName,
      origin: 'project-stats',
    },
  });
};

// 点击完成考核人数跳转到考核统计页面
const handleAssessmentClick = (record: any) => {
  router.push({
    path: '/cddc/assessment-stats',
    query: {
      projectId: record.projectId,
      projectName: record.projectName,
      origin: 'project-stats',
    },
  });
};

const [registerTable] = useTable({
  columns,
  showAction: false,
  api: (params) => V1ManageStatisticalReportProjectPost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
});
</script>

<style lang="less" scoped>
.project-stats-container {
  padding: 2.08vw 1.85vw;
  background: #fff;
  min-height: calc(100vh - 120px);

  // 确保表格内容居左显示
  :deep(.ant-table-tbody > tr > td) {
    text-align: left !important;
  }
}

@media (max-width: 1366px) {
  .project-stats-container {
    padding: 16px;
  }
}
</style>
