<template>
  <div class="user-container">
    <!-- 用户列表 -->
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" @click="handleBind(record)">绑定</a-button>
            <a-button v-if="record.cardNoList?.length > 0" type="link" @click="handleUnbind(record)"
              >解绑</a-button
            >
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
    <!-- 删除确认 -->
    <BasicModal centered @register="register" title="确认删除" @ok="confirmDelete">
      <div>确定要删除选中的 {{ selectedRowKeys.length }} 个用户吗？此操作不可恢复。</div>
    </BasicModal>
    <!-- 新增用户抽屉 -->
    <UserForm ref="drawerRef" @success="handleSuccess"></UserForm>
    <!-- 刷卡绑定弹框 -->
    <CardBindModal ref="cardBindModalRef" :userId="currentUser?.id" @success="handleBindSuccess" />
    <!-- 卡片解绑弹框 -->
    <CardUnbindModal
      ref="cardUnbindModalRef"
      :userId="currentUser?.id"
      @success="handleBindSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import UserForm from './components/UserForm.vue';
import CardBindModal from '/@/components/CardBindModal/index.vue';
import CardUnbindModal from '/@/components/CardUnbindModal/index.vue';
import { userQuery, userStatus } from '/@/api/admin/user';
import { V1ManageSysUserDeleteBatchPost, V1ManageSysUserDeleteIdDelete } from '/@/api/cddc.req';

const selectedRowKeys = ref<string[]>([]); // 修改类型为 string[] 以解决类型错误
const { createMessage } = useMessage();
const drawerRef = ref();
const cardBindModalRef = ref();
const cardUnbindModalRef = ref();

const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  api: userQuery,
  columns,
  tableProps: {
    showIndexColumn: false,
  },
});

const [register, { openModal }] = useModal();
const currentUser = ref<any>(null);

// 新增用户
const handleAdd = async () => {
  drawerRef.value?.openDrawer();
  await drawerRef.value?.setFormData({});
  await drawerRef.value?.resetForm();
};

// 提交成功
const handleSuccess = () => {
  reload();
};

// 确认删除
const confirmDelete = async () => {
  try {
    await V1ManageSysUserDeleteBatchPost({ ids: selectedRowKeys.value });
    createMessage.success('删除成功');
    selectedRowKeys.value = [];
    clearSelectedRowKeys();
    reload();
    openModal(false);
  } catch (error) {
    createMessage.error('删除失败：' + (error as Error).message);
  }
};

// 编辑用户
const handleEdit = async (record: any) => {
  drawerRef.value?.openDrawer();
  await drawerRef.value?.setFormData(record);
};

// 删除用户
const handleDelete = async (record: any) => {
  await V1ManageSysUserDeleteIdDelete({ id: record.id });
  reload();
};

// 状态变化
const handleStatusChange = async (record: any) => {
  await userStatus({
    id: record.id,
    status: record.status === 'ENABLE' ? 'DISABLE' : 'ENABLE',
  });
  reload();
};

// 绑定用户
const handleBind = async (record: any) => {
  // 先设置当前用户
  currentUser.value = record;
  // 等待下一个事件循环，确保 currentUser 已经更新
  await nextTick();
  // 检查已绑定卡数量
  if (record.cardNoList?.length >= 2) {
    createMessage.warning('最多允许绑定2张卡号，请前往解绑');
    return;
  }
  // 再打开弹窗
  cardBindModalRef.value?.open();
};

// 解除绑定
const handleUnbind = async (record: any) => {
  currentUser.value = record;

  // 打开解绑弹窗
  await nextTick();
  cardUnbindModalRef.value?.open({
    id: record.id,
    name: record.name,
    cardNoList: record.cardNoList,
  });
};

// 绑定成功
const handleBindSuccess = () => {
  reload();
};
</script>

<style lang="less" scoped>
.user-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
