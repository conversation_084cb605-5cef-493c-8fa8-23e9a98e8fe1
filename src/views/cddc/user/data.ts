import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { V1ManageJobsPagePost } from '/@/api/cddc.req';
export const columns: EnhancedColumn[] = [
  {
    title: '用户名',
    dataIndex: 'name',
    width: 120,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入用户名',
      },
    },
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 180,
  },
  {
    title: '岗位名称',
    dataIndex: 'jobName',
    width: 160,
    search: {
      field: 'jobId',
      component: 'ApiSelect',
      colProps: {
        span: 6,
      },
      componentProps: {
        api: async () => {
          const { records } = await V1ManageJobsPagePost({
            pageSize: 1000,
            currentPage: 1,
            data: {},
          });
          return records?.map((o) => ({ label: o.name, value: o.id }));
        },
        placeholder: '请选择',
      },
    },
  },
  {
    title: '最近登录时间',
    dataIndex: 'lastLoginTime',
    width: 160,
  },
];
