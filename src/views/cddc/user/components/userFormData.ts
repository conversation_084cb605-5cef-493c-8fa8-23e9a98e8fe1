import type { FormSchema } from '@geega-ui-plus/geega-ui';
import {
  V1ManageJobsPagePost,
  V1ManageLearningProjectsPagePost,
  V1ManageSysOrgTree,
} from '/@/api/cddc.req';
import { projectList } from '/@/api/admin/project';

// 表单配置
export const formSchemas: FormSchema[] = [
  {
    field: 'name',
    label: '用户名',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    required: false,
    componentProps: {
      placeholder: '请输入手机号',
      maxLength: 11,
    },
  },
  // 岗位
  {
    field: 'jobId',
    label: '岗位名称',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: async () => {
        const { records } = await V1ManageJobsPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: {},
        });
        return records?.map((o) => ({ label: o.name, value: o.id }));
      },
      placeholder: '请选择岗位名称',
    },
  },
  // 树形组织
  {
    field: 'organizationId',
    label: '所属部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: async () => {
        const res = await V1ManageSysOrgTree({ name: '' });
        return res || [];
      },
      showSearch: true,
      allowClear: true,
      treeNodeFilterProp: 'name',
      treeDefaultExpandAll: true,
      fieldNames: {
        label: 'name',
        value: 'id',
        key: 'id',
        children: 'childNodes',
      },
      placeholder: '请选择组织',
    },
  },

  // 道场项目
  {
    field: 'projectIdList',
    label: '智能技能道场项目',
    component: 'ApiSelect',
    componentProps: {
      api: projectList,
      showSearch: true,
      showArrow: true,
      optionFilterProp: 'label',
      labelField: 'name',
      valueField: 'id',
      mode: 'multiple',
      placeholder: '请选择要求项目',
      maxTagCount: 'responsive',
      params: {
        projectType: 1,
      },
    },
  },
  // 学习项目
  {
    field: 'learningIds',
    label: '智能学习系统项目',
    component: 'ApiSelect',
    componentProps: {
      api: async () => {
        const { records } = await V1ManageLearningProjectsPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: {},
        });
        return records?.map((o) => ({ label: o.name, value: o.id }));
      },
      showSearch: true,
      showArrow: true,
      optionFilterProp: 'label',
      mode: 'multiple',
      placeholder: '请选择要求项目',
      maxTagCount: 'responsive',
    },
  },
  // 考试项目,只读
  {
    field: 'examProjectIdList',
    label: '智能考试系统项目',
    component: 'Input',
    slot: 'examProjectIdList',
    componentProps: {
      disabled: true,
    },
  },
  // 智能评定,只读
  {
    field: 'assessmentProjectIdList',
    label: '智能生产评定',
    component: 'Input',
    slot: 'assessmentProjectIdList',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'idCard',
    label: '身份证号',
    component: 'Input',
    rules: [
      {
        pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        message: '请输入正确的身份证号',
      },
    ],
    componentProps: {
      placeholder: '请输入身份证号',
    },
  },
  {
    field: 'cardNoList',
    label: '绑定状态',
    component: 'Input',
    slot: 'cardNoList',
  },
  {
    field: 'password',
    label: '密码',
    component: 'InputGroup',
    slot: 'passwordGroup',
    colProps: { span: 24 },
  },
  // 人脸上传
  {
    field: 'faceId',
    label: '人脸照片',
    component: 'Upload',
    slot: 'upload',
  },
];
