<template>
  <BasicDrawer
    width="600px"
    @register="registerDrawer"
    :title="title"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #passwordGroup="{ model, field, schema }">
        <a-input-group compact>
          <a-input-password
            v-model:value="model[field]"
            class="password-input"
            placeholder="请输入密码"
            :disabled="schema.componentProps?.disabled"
          />
          <a-button type="primary" @click="resetPassword">重置密码</a-button>
        </a-input-group>
      </template>
      <template #upload="{ model }">
        <a-tag :color="model.faceId ? 'success' : 'default'">
          {{ model.faceId ? '已采集' : '未采集' }}
        </a-tag>
      </template>
      <template #cardNoList="{ model }">
        <a-tag :color="model.cardNoList?.length ? 'success' : 'default'">
          {{ model.cardNoList?.length ? '已绑定' : '未绑定' }}
        </a-tag>
      </template>
      <template #examProjectIdList="{ model }">
        <a-input
          class="disabled-input-with-tooltip"
          disabled
          :value="formatListText(model.examProjectIdList)"
          :title="formatListText(model.examProjectIdList)"
        />
      </template>
      <template #assessmentProjectIdList="{ model }">
        <a-input
          class="disabled-input-with-tooltip"
          disabled
          :value="formatListText(model.assessmentProjectIdList)"
          :title="formatListText(model.assessmentProjectIdList)"
        />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts">
import { defineComponent, nextTick, ref } from 'vue';
import { BasicDrawer, BasicForm, useDrawer, useForm } from '@geega-ui-plus/geega-ui';
import { useMessage } from '/@/hooks/web/useMessage';
import { userUpdate } from '/@/api/admin/user';
import { createFile } from '/@/components/Uploader/utils';
import {
  V1CommonFileUploadDownloadFileIdPost,
  V1ManageSysUserId,
  V1ManageUserLearningsPagePost,
  V1ManageExamPaperPagePost,
  V1ManageExamRecordPagePost,
  V1EvaluateTaskUserOrderUserIdPost
} from '/@/api/cddc.req';
import { formSchemas } from './userFormData';

export default defineComponent({
  name: 'UserForm',
  components: {
    BasicDrawer,
    BasicForm,
  },
  props: {
    faceImg: {
      type: String,
      default: '',
    },
  },
  emits: ['success', 'register'],
  setup(props, { emit }) {
    const { createMessage } = useMessage();
    // 抽屉标题
    const title = ref('新增用户');

    const [
      registerForm,
      { validate, resetFields, setProps, updateSchema, clearValidate, setFieldsValue },
    ] = useForm({
      schemas: formSchemas,
      showActionButtonGroup: false,
      rowProps: {
        gutter: [24, 12],
      },
       baseColProps: { span: 12 },
    });

    // 编辑数据
    const editData = ref<any>({});
    const [registerDrawer, { openDrawer }] = useDrawer();

    // 重置密码
    const resetPassword = () => {
      const randomPassword = Math.random().toString(36).slice(-8);
      setFieldsValue({
        password: randomPassword,
      });
    };
    // 重置表单
    const resetForm = async () => {
      await nextTick();
      resetFields();
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const values = await validate();
        console.log('表单数据:', values);
        await userUpdate({
          faceId: values.faceId,
          idCard: values.idCard,
          workNo: values.workNo,
          jobId: values.jobId,
          projectIdList: values.projectIdList,
          learningIds: values.learningIds,
          orgIds: [values.organizationId],
          id: editData.value.id,
        });
        createMessage.success('编辑成功');
        resetFields();
        emit('success', values);
        openDrawer(false);
      } catch (error) {
        console.error('表单错误:', error);
      }
    };

    const setFaceId = async (faceId: any) => {
      if (!faceId) {
        return [];
      }

      try {
        const faceData = await V1CommonFileUploadDownloadFileIdPost({
          fileId: faceId,
        });

        if (faceData) {
          const file = createFile({
            name: `face_${faceId}.jpg`,
            url: faceData,
            uid: faceId,
            status: 'done',
            id: faceId,
          });
          return [file];
        }
      } catch (error) {
        console.error('Error fetching face data:', error);
      }
      return [];
    };

    // 获取用户学习项目列表
    const getUserLearnings = async (userId: string) => {
      try {
        const { records } = await V1ManageUserLearningsPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: { userId },
        });
        return records?.map(item => item.learningId) || [];
      } catch (error) {
        console.error('获取用户学习项目失败:', error);
        return [];
      }
    };

    // 获取用户考试项目列表
    const getUserExams = async (userId: string) => {
      try {
        const { records } = await V1ManageExamRecordPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: { userId ,type: 'SKILL_INSPECTION'},
        });
        return records?.map(item => item.paperName) || [];
      } catch (error) {
        console.error('获取用户考试项目失败:', error);
        return [];
      }
    };

    // 获取用户评定项目列表
    const getUserAssessments = async (userId: string) => {
      try {
        const result = await V1EvaluateTaskUserOrderUserIdPost({
          userId
        });
        const uniqueResult = [...new Set(result?.map(item => item.evaluateConfigId))];
        return uniqueResult.map(item => result?.find(item2 => item2.evaluateConfigId === item)?.evaluateName);
      } catch (error) {
        console.error('获取用户评定项目失败:', error);
        return [];
      }
    };

    // 设置表单数据
    const setFormData = async (data: any) => {
      await nextTick();
      // 新增用户
      if (!data || !data.id) {
        title.value = '新增用户';
        setProps({
          disabled: false,
        });
        updateSchema({
          field: 'password',
          required: true,
          show: true,
        });
        resetFields();
        return;
      }

      // 编辑用户，先获取用户详细信息
      try {
        const userId = data.id;
        const userDetail = await V1ManageSysUserId({ id: userId });
        if (userDetail) {
          editData.value = userDetail;
          title.value = '编辑用户';

          // 处理项目数据
          if (!userDetail.projectList || !userDetail.projectList.length) {
            userDetail.projectIdList = [];
          } else {
            userDetail.projectIdList = userDetail.projectList?.map((item) => item.projectId) || [];
          }

          // 获取用户学习项目列表
          userDetail.learningIds = await getUserLearnings(userId);

          // 获取用户考试项目列表
          userDetail.examProjectIdList = await getUserExams(userId);

          // 获取用户评定项目列表
          userDetail.assessmentProjectIdList = await getUserAssessments(userId);

          // 处理人脸数据
          // userDetail.files = userDetail.faceId ? await setFaceId(userDetail.faceId) : [];

          // 隐藏密码字段
          updateSchema({
            field: 'password',
            required: false,
            show: false,
          });

          // 禁用基础信息字段
          const disabledFields = ['name', 'phone'];
          updateSchema(
            disabledFields.map((field) => ({
              field,
              componentProps: { disabled: true },
            }))
          );

          // 设置组织ID
          userDetail.organizationId = userDetail?.orgList?.[0]?.id;

          // 设置表单数据
          setFieldsValue(userDetail);
          clearValidate();
        } else {
          createMessage.error('获取用户信息失败');
        }
      } catch (error) {
        console.error('获取用户详情失败:', error);
        createMessage.error('获取用户信息失败');
      }
    };

    // 监听projectIdList字段变化
    updateSchema({
      field: 'projectIdList',
      componentProps: {
        onChange: () => {
          clearValidate('projectIdList');
        },
      },
    });

    // 格式化列表文本
    const formatListText = (value: any): string => {
      if (!value) return '';
      if (Array.isArray(value)) return value.join(', ');
      return String(value);
    };

    return {
      title,
      resetForm,
      resetPassword,
      openDrawer,
      registerForm,
      setFormData,
      registerDrawer,
      handleSubmit,
      formatListText,
    };
  },
});
</script>

<style lang="less" scoped>
.cddc-ant-input-group.cddc-ant-input-group-compact {
  .password-input {
    width: calc(100% - 78px);
    margin-right: 8px;
  }
}
.cddc-ant-tag {
  padding: 2px 8px;
}
.disabled-input-with-tooltip {
  cursor: default;

  :deep(input) {
    cursor: default;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
