<template>
  <div class="progress-report">
    <ScreenTitle> 考核目标 </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="props.loading">
      <ScreenBox class="progress-items h-full">
        <div class="grid grid-cols-2 gap-1.5">
          <ScreenBox1
            class="progress-data-item"
            v-for="(item, idx) in assessmentData"
            :key="idx"
            :item="item"
          />
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import type { AssessmentData } from '../types';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';

const props = defineProps<{
  /**
   * 报表数据
   */
  data?: AssessmentData;
  loading?: boolean;
}>();

const assessmentData = computed(() => {
  const data = props.data;

  return [
    {
      label: '考核作业时间',
      unit: 'min',
      color: 'blue',
      value: data?.requestDuration || 0,
    },
    {
      label: '考核操作次数',
      unit: '次',
      color: 'blue',
      value: data?.requestFrequency || 0,
    },
    {
      label: '合格率目标',
      unit: '%',
      color: 'blue',
      value: data?.requestQualificationRate || 0,
    },
    {
      label: '达标率目标',
      unit: '%',
      color: 'blue',
      value: data?.requestActionRate || 0,
    },
  ];
});
</script>

<style lang="less" scoped>
.progress-report {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;
  min-width: 0;
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  padding: 0.74vw;
  height: 100%;

  .grid {
    height: 100%;
    align-content: flex-start;
  }
}

.progress-data-item {
  padding: 0.74vw;
  border-radius: 4px;

  &:nth-child(1n) {
    background-size: 100% 100%, auto 30%;
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .progress-report {
    gap: 0.6vw;
    min-width: 0;

    .progress-items {
      gap: 0.6vw;
      padding: 0.6vw;

      .grid {
        gap: 0.42vw;
      }
    }

    .progress-data-item {
      padding: 0.6vw;

      &:nth-child(1n) {
        background-size: 100% 100%, auto 30%;
      }
    }

    :deep(.screen-box-1) {
      .label {
        font-size: 0.9vw;
      }

      .item-divider {
        width: 0.84vw;
      }

      .divider-box {
        height: 0.19vw;
        margin: 0.74vw 0;
      }
    }

    :deep(.valid-value) {
      font-size: 1.26vw;
      line-height: inherit;
      align-items: start;

      .unit {
        font-size: 0.74vw;
        margin-left: 2px;
      }
    }
  }
}

:deep(.screen-box-1) {
  .label {
    font-size: 0.92vw;
  }
  .valid-value {
    font-size: 1.26vw;
    line-height: initial;
    align-items: center;
    .unit {
      font-size: 0.84vw;
      margin-left: 2px;
    }
  }
}
</style>
