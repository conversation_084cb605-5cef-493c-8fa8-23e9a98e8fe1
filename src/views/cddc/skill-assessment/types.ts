import type { Ref } from 'vue';

export interface WebSocketClient {
  send: (data: any) => void;
  close: () => void;
}

export interface WebSocketInstance {
  wsClient: Ref<WebSocketClient | null>;
  connect: () => void;
  disconnect: () => void;
  send: (data: any) => void;
}

export interface AssessmentResponse {
  id?: string;
  status?: string;
  accuracy?: number;
  timeSpent?: number;
  effectiveNum?: number;
  qualificationNum?: number;
  operationLog?: {
    id: string;
    result: number;
    operationTime: string;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
}

export interface RealTimeData {
  overallScore: number;
  validOperations: number;
  invalidOperations: number;
  failedOperations: number;
  targetDuration?: number;
  targetOperations?: number;
  targetQualifiedRate?: number;
  targetAchievementRate?: number;
  operationLog?: {
    id: string;
    result: number;
    operationTime: string;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
  [key: string]: any;
}

export interface AssessmentData {
  requestDuration?: number;
  requestFrequency?: number;
  requestQualificationRate?: number;
  requestActionRate?: number;
}

export interface PointDataItem {
  objectFlag: string;
  result: number;
  sort: number;
  status: number;
}

export enum AssessmentStatusEnum {
  NOT_STARTED = 0,
  IN_PROGRESS = 1,
  PAUSED = 2,
  COMPLETED = 3,
}
