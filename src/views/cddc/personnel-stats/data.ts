import { V1ManageTrainStudyRecordsProjects } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 160,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '配置训练人数',
    dataIndex: 'configNum',
    width: 120,
  },
  {
    title: '已完成训练人数',
    dataIndex: 'completedNum',
    width: 120,
  },
  {
    title: '训练中人数',
    dataIndex: 'ingNum',
    width: 120,
  },
  {
    title: '考试通过人数',
    dataIndex: 'checkedNum',
    width: 180,
  },
];
