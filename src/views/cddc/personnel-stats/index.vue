<template>
  <div class="personnel-stats-container">
    <BasicTablePlus @register="registerTable"> </BasicTablePlus>
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageStatisticalReportProjectUserPost } from '/@/api/cddc.req';

const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};
  const data = {
    ...rawQuery,
  };

  params.data = data;
  return params;
};

const [registerTable] = useTable({
  columns,
  showAction: false,
  api: (params) => V1ManageStatisticalReportProjectUserPost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
});
</script>

<style lang="less" scoped>
.personnel-stats-container {
  padding: 0px 8px;
  background: #fff;
}
</style>
