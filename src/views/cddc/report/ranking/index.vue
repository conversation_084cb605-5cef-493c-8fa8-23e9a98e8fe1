<script lang="ts" setup>
import { computed } from 'vue';
import RankingBoard from '/@/components/RankingBoard.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import { useRouter } from 'vue-router';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { useAsyncData } from '/@/composables';
import {
  V1ManageTrainStudyRecordsPagePost,
  V1ManageTrainStudyRecordsRaceRankProjectId,
} from '/@/api/cddc.req';
import { useUrlSearchParams, watchImmediate } from '@vueuse/core';
import StationInfo from '/@/components/ClientPort/StationInfo.vue';
import dayjs from 'dayjs';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { RaceType } from '/@/enums/projectEnum';

const router = useRouter();

interface IRouteParams {
  userId: string;
  projectId?: string;
}

const urlParams = useUrlSearchParams<IRouteParams>('hash');

const rankingDataApi = useAsyncData(V1ManageTrainStudyRecordsRaceRankProjectId, []);

const projectList = useAsyncData(async () => {
  const resp = await V1ManageTrainStudyRecordsPagePost({
    currentPage: 1,
    pageSize: 100,
    data: {
      projectType: 3,
      userId: urlParams.userId,
    },
  });
  return resp.records?.map((item) => ({
    ...item,
    label: item.projectName,
    value: item.projectId,
  }));
}, []);

const currentProject = computed(() => {
  return projectList.data.value?.find((item) => item.value === urlParams.projectId);
});


fetchInitData();

watchImmediate(
  () => urlParams.projectId,
  async (val) => {
    if (!val) {
      return;
    }

    await rankingDataApi.load({
      projectId: val,
    });
  }
);

async function fetchInitData() {
  await projectList.load();

  if (!urlParams.projectId) {
    urlParams.projectId = projectList.data.value?.at(0)?.value;
  }
}

const rankingData = computed(() => {
  const data = rankingDataApi.data.value;

  const isTimingMode = currentProject.value?.raceType?.toString() == RaceType.Timing;

  return data.map((item) => ({
    rank: parseInt(item.ranking!),
    label: item.userName!,
    isHighlight: item.userId === urlParams.userId,
    desc: isTimingMode
      ? `${item.value} 次`
      : `${dayjs.duration(item.value!, 's').format('mm 分 ss 秒')}`,
  }));
});

const goBack = () => {
  router.back();
};

function goToChallengePage() {
  const projectData = currentProject.value;
  if (!projectData?.id) return;

  router.push({
    path: '/cddc/skill-competition',
    query: {
      projectId: projectData.id,
    },
  });
}
</script>

<template>
  <div class="p-20px">
    <!-- 顶部信息 -->
    <div class="header">
      <div class="flex-1 flex items-center gap-2">
        <div class="back-btn">
          <a-button class="float-back-btn" type="link" @click="goBack">
            <span class="relative -top-2px">
              <SvgIcon name="g-ic-line-left" />
            </span>
            <span class="pl-1"> 返回 </span>
          </a-button>
        </div>
        <div class="v-divider mx-2">&nbsp;</div>
        <div class="flex items-center gap-2">
          <span> 项目： </span>
          <a-select
            class="w-140px"
            size="large"
            v-model:value="urlParams.projectId"
            :options="projectList.data.value"
            placeholder="请选择项目"
          />
        </div>
      </div>

      <StationInfo />
    </div>

    <ScreenTitle class="mb-5">
      <span> 比赛榜单 </span>
      <template #suffix>
        <div class="flex items-center justify-end flex-1">
          <a-button
            size="large"
            type="primary"
            v-if="false"
            @click="goToChallengePage"
          >
            去挑战
          </a-button>
        </div>
      </template>
    </ScreenTitle>

    <RankingBoard
      v-if="rankingData.length"
      :data="rankingData"
      :columns="2"
      class="mx-auto w-800px"
    />
    <customizeRenderEmpty v-else />
  </div>
</template>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;

  padding-bottom: 12px;
  gap: 8px;

  .v-divider {
    width: 2px;
    --ignore-dark-bg: rgba(255, 255, 255, 0.2);
    background: var(--ignore-dark-bg);
  }
}

.back-btn {
  position: relative;
  width: 55px;
  height: 0px;

  .float-back-btn {
    position: absolute;
    padding: 10px;
    height: auto;
    color: white;

    top: 0%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
