<script lang="ts" setup>
export interface PercentageBoxProps {
  /**
   * @default 'success'
   */
  type?: 'error' | 'success';
}

defineProps<PercentageBoxProps>();
</script>

<template>
  <div class="percentage-box-bg" :class="`is-${type || 'success'}`">
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.percentage-box-bg {
  max-width: 238px;
  width: 100%;
  aspect-ratio: 238 / 150;
  padding-bottom: 65px;

  display: flex;
  align-items: end;
  justify-content: center;
  position: relative;

  &.is-error {
    --ignore-dark--image: url('@/assets/svg/screen/bg-percentage-red.svg');
    background: no-repeat center / contain var(--ignore-dark--image);
  }

  &.is-success {
    --ignore-dark--image: url('@/assets/svg/screen/bg-percentage-green.svg');
    background: no-repeat center / contain var(--ignore-dark--image);
  }
}
</style>
