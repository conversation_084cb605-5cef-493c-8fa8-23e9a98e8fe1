<script lang="ts" setup>
import { computed } from 'vue';
import FailedRecordReport from './FailedRecordReport.vue';
import PercentageBox from './PercentageBox.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import { useRouter } from 'vue-router';
import { useAsyncData } from '/@/composables';
import {
  V1LocationStudyRaceProjectIdUserId,
  V1ManageTrainStudyRecordsId,
  V1ManageTranProjectId,
} from '/@/api/cddc.req';
import { watchImmediate } from '@vueuse/core';
import { ProjectType, RaceType } from '/@/enums/projectEnum';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

export interface TrainingReportProps {
  /**
   * 记录 ID
   */
  recordId: string;
}

const props = defineProps<TrainingReportProps>();

const router = useRouter();

const raceData = useAsyncData(V1LocationStudyRaceProjectIdUserId, {});

const currentProjectRecord = useAsyncData(V1ManageTrainStudyRecordsId, {});
const currentProject = useAsyncData(V1ManageTranProjectId, {});

watchImmediate(
  () => props.recordId,
  () => fetchInitData()
);

async function fetchInitData() {
  await currentProjectRecord.load({
    id: props.recordId,
  });

  const resp = currentProjectRecord.data.value;

  await currentProject.load({
    id: resp.projectId!,
  });

  await raceData.load({
    userId: resp.userId!,
    projectId: resp.projectId!,
  });
}

const trainData = computed(() => {
  const data = raceData.data.value;

  return [
    {
      label: '用时',
      color: 'green',
      value: formatSecondsToMinute(data.trainDuration || 0),
    },
    {
      label: '有效作业次数',
      value: data.effectiveNum || 0,
      unit: '次',
      color: 'green',
    },
  ];
});

const info = computed(() => {
  const data = raceData.data.value;

  const isTimingMode = data.raceType?.toString() === RaceType.Timing;

  const description = isTimingMode
    ? `在 ${data.requestDuration || 0} 分钟内完成`
    : `完成 ${data.requestFrequency || 0} 次操作`;

  return {
    rank: data.ranking,
    date: data.lastUpdateTime || '-',
    required: description,
  };
});

function seeRanking() {
  const data = currentProjectRecord.data.value;

  router.push({
    path: '/cddc/report/ranking',
    query: {
      userId: data.userId,
      projectId: data.projectId,
    },
  });
}

function goToChallengePage() {
  const projectData = currentProjectRecord.data.value;
  if (!projectData.projectId) return;

  router.push({
    path: '/cddc/skill-competition',
    query: {
      projectId: projectData.projectId,
    },
  });
}
</script>

<template>
  <div class="progress-report">
    <ScreenTitle class="mb-3">
      <span> 比赛信息 </span>
      <template #suffix>
        <div class="flex justify-end flex-1 gap-3">
          <a-button size="large" class="px-4!" @click="seeRanking">查看榜单</a-button>
          <a-button
            type="primary"
            size="large"
            class="px-4!"
            v-if="false"
            @click="goToChallengePage"
            >再次挑战</a-button
          >
        </div>
      </template>
    </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="raceData.loading.value">
      <ScreenBox class="progress-items h-200px">
        <div class="flex gap-5 h-full">
          <div class="flex-item percentage-box flex-inline items-center">
            <PercentageBox type="success">
              <div class="text-big">
                <span class="text-6">第</span>
                {{ info.rank }}
                <span class="text-6">名</span>
              </div>
            </PercentageBox>
          </div>
          <div class="flex-item extra-info flex gap-5" style="flex: 2">
            <div class="info-row">
              <div class="key"> 时间 </div>
              <div class="value">
                {{ info.date }}
              </div>
            </div>
            <div class="info-row">
              <div class="key"> 要求 </div>
              <div class="value">
                {{ info.required }}
              </div>
            </div>
          </div>

          <div class="flex-item flex gap-5" style="flex: 4">
            <ScreenBox1
              class="flex-1 train-item"
              v-for="(item, idx) in trainData"
              :key="idx"
              :item="item"
            />
          </div>
        </div>
      </ScreenBox>
    </a-spin>
  </div>
  <!-- 不合格记录 -->
  <div class="section">
    <FailedRecordReport :record-id="recordId" title="比赛记录" :project-type="ProjectType.Race" />
  </div>
</template>

<style lang="less" scoped>
.section {
  padding: 20px 0;
  border-radius: 4px;
}

.text-big {
  font-size: 4vw;
  font-style: italic;
  font-weight: 700;
  margin-bottom: 4px;
  text-shadow: 0 0 5px rgb(169, 169, 169);
}

.extra-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;

  --ignore-dark-bg: linear-gradient(
    228deg,
    rgba(62, 71, 81, 0.2) 13.78%,
    rgba(62, 71, 81, 0.2) 13.78%,
    // rgba(62, 71, 81, 0) 120.36%
  );

  background: var(--ignore-dark-bg);
  padding: 0 16px;

  .info-row {
    font-size: 1vw;

    .key {
      font-size: 1.1vw;
      color: rgb(62, 234, 179);
    }
  }
}

.flex-item {
  flex: 1;
  width: 0;
}

.train-item {
  flex: 2;
  each(range(4), {
      &:nth-child(@{value}n) {
        --ignore-dark-image: url('@/assets/svg/screen/bg-box-icon-@{value}.svg');

        background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png'),
        no-repeat right top / auto 80% var(--ignore-dark-image);
      }
    });
}
</style>
