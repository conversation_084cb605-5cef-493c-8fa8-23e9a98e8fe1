<script lang="ts" setup>
import StampText from '/@/components/ClientPort/StampText.vue';
import VImage from '/@/components/ImagePreview/VImage.vue';
import { toFixed } from '/@/utils';
import VAsyncImage from '/@/components/ImagePreview/VAsyncImage.vue';
import type {
  AlarmItemElement,
  V1LocationHomeAlarmDetailsStatisticsPostResponseResult,
} from '/@/api/cddc.model';
import { computed } from 'vue';
import { getDictLabel } from '/@/dict';
import { ACTION_TYPES_WRONG } from '/@/dict/modules/station';
import { ProjectType } from '/@/enums/projectEnum';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

export interface FailedRecordReportProps {
  item: V1LocationHomeAlarmDetailsStatisticsPostResponseResult;
  projectType?: ProjectType;
}

const props = defineProps<FailedRecordReportProps>();

const data = computed(() => {
  const item = props.item;
  return {
    ...item,
    items: item.alarmItems?.map((record) => ({
      ...record,
      timestamp: record.timestamp,
      stampLabel: record.result === 0 ? '不合格' : record.standard === 0 ? '不达标' : '--',
      stampType: record.result === 0 ? 'error' : record.standard === 0 ? 'warning' : '--',
      // image: record.frameImage,
      recordDetailAlarmId: record.recordDetailAlarmId,
      content: getDictLabel(ACTION_TYPES_WRONG, record.actionType),
    })),
  };
});

const emit = defineEmits<{
  preview: [data: AlarmItemElement];
}>();

const descriptions = computed(() => {
  const d = data.value;
  const examDescriptions = [
    {
      label: '考核时长',
      value: formatSecondsToMinute(d.trainDuration || 0),
      unit: '',
    },
  ];

  const raceDescriptions = [
    {
      label: '比赛时长',
      value: formatSecondsToMinute(d.trainDuration || 0),
      unit: '',
    },
  ];

  const result = [
    ...(props.projectType === ProjectType.Exam ? examDescriptions : []),
    ...(props.projectType === ProjectType.Race ? raceDescriptions : []),
    {
      label: '作业次数',
      value: d.opNum,
      unit: '次',
    },
    {
      label: '作业合格率',
      value: toFixed((d.opNumOk! / d.opNum!) * 100 || 0),
      unit: '%',
    },
    {
      label: '作业合格次数',
      value: d.opNumOk || 0,
      unit: '次',
    },
    {
      label: '作业不合格次数',
      value: d.opNum! - d.opNumOk! || 0,
      unit: '次',
    },
    {
      label: '动作达标率',
      value: toFixed((d.actionNumOk! / d.actionNum!) * 100 || 0),
      unit: '%',
    },
    {
      label: '动作达标次数',
      value: d.actionNumOk || 0,
      unit: '次',
    },
    {
      label: '动作不达标次数',
      value: d.actionNum! - d.actionNumOk! || 0,
      unit: '次',
    },
  ];

  return result;
});

const isPass = computed(() => data.value.skillStatus === 'PASS_OK');
</script>

<template>
  <div class="failed-record-group">
    <div class="group-title flex gap-2 items-center">
      <span>
        {{ data.createdDate }}
      </span>
      <template v-if="projectType === ProjectType.Exam">
        <a-tag v-if="isPass" color="success">合格</a-tag>
        <a-tag v-else color="error">不合格</a-tag>
      </template>
    </div>
    <div class="group-content">
      <div class="description">
        <template v-for="(desc, idx) in descriptions">
          <span>{{ desc.label }}: {{ desc.value }}{{ desc.unit }}</span>
          <span class="v-divider" v-if="idx !== descriptions.length - 1"></span>
        </template>
      </div>
      <div class="record-grid">
        <div v-for="record in data.items" :key="record.recordDetailAlarmId" class="record-item">
          <div class="record-image">
            <VImage class="dark-ignore-image size-full" @click="emit('preview', record)">
              <VAsyncImage class="object-cover!" :img-id="record.recordDetailAlarmId" />
            </VImage>
          </div>
          <div class="record-info">
            <div class="record-status">{{ record.content }}</div>
            <div class="record-time">{{ record.timestamp }}</div>
          </div>
          <div class="record-stamp">
            <StampText :type="record.stampType">{{ record.stampLabel }}</StampText>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.failed-record-group {
  @dot-size: 32px;
  @pl: @dot-size + 8px;

  padding-left: @pl;

  .group-title {
    position: relative;

    color: #29e8ab;
    text-shadow: 0px 2.27px 7.566px rgba(0, 243, 196, 0.2);
    font-size: 24px;

    &::before {
      position: absolute;
      left: -@pl;

      content: '';
      width: @dot-size;
      height: @dot-size;
      background: no-repeat center / 95% url('../assets/timeline-dot.png');
    }
  }

  .group-content {
    position: relative;
    padding: 8px 0 30px;

    &::before {
      position: absolute;
      top: 0;
      left: -(@dot-size / 2) - 8px;
      content: '';
      width: 1px;
      height: 100%;
      --ignore-dark-bg: rgba(255, 255, 255, 0.6);
      background: var(--ignore-dark-bg);
    }

    .description {
      color: rgba(255, 255, 255, 0.8);
      text-shadow: 0px 2.27px 7.566px rgba(0, 243, 196, 0.2);
      font-size: 16px;

      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 0.5em;

      .v-divider {
        width: 2px;

        &::before {
          position: relative;
          top: -1px;
          content: '|';
        }
      }
    }
  }
}

.record-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
  gap: 20px;

  .loading-text {
    grid-column: 1 / -1;
    text-align: center;
    font-size: 14px;
    --ignore-dark-color: rgba(255, 255, 255, 0.4);
    color: var(--ignore-dark-color);
  }
}

.record-item {
  position: relative;
  --dark-ignore-bg: #171F30;
  background: var(--ignore-dark-bg);

  .record-image {
    opacity: 0.8;

    aspect-ratio: 4/3;
    border-radius: 4px;
    margin-bottom: 8px;
    overflow: hidden;

    &:deep(img) {
      width: 100%;
      height: 100%;
      display: block;
      object-fit: contain;
      object-fit: scale-down;
    }
  }

  .record-info {
    padding: 16px;

    .record-status {
      font-size: 20px;
      line-height: 1em;
      margin-bottom: 4px;
    }

    .record-time {
      --ignore-dark-color: rgba(255, 255, 255, 0.6);
      color: var(--ignore-dark-color);
      font-size: 14px;
    }
  }

  .record-stamp {
    position: absolute;
    right: 5px;
    top: 5px;
  }
}
</style>
