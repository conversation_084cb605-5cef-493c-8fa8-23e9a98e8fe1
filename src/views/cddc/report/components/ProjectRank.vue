<script lang="ts" setup>
import { useAsyncData } from '/@/composables';
import { watchImmediate } from '@vueuse/core';
import { V1LocationHomeRankRecordId } from '/@/api/cddc.req';
import ProjectRank from '/@/components/ClientPort/ProjectRank.vue';

const props = defineProps<{
  recordId?: string;
}>();

const apiData = useAsyncData(async () => {
  if (!props.recordId) return {};

  return V1LocationHomeRankRecordId({
    recordId: props.recordId,
  });
}, {});

watchImmediate(
  () => props.recordId,
  () => apiData.load()
);
</script>

<template>
  <ProjectRank :loading="apiData.loading.value" :data="apiData.data.value" />
</template>

<style lang="less" scoped>
.project-rank {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.latest-data {
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .train-item {
    each(range(3), {
      &:nth-child(@{value}n) {
        --ignore-dark-image: url('@/assets/svg/screen/bg-box-icon-@{value}.svg');

        background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png'),
        no-repeat right top / auto 80% var(--ignore-dark-image);
      }
    });
  }
}
</style>
