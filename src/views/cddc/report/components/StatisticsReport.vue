<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import StatisticsBox from '/@/components/ClientPort/StatisticsBox.vue';
import ValidValue from '/@/components/ClientPort/ValidValue.vue';
import { useAsyncData } from '/@/composables';
import { V1LocationHomeDataStatisticsRecordId } from '/@/api/cddc.req';
import { watchImmediate } from '@vueuse/core';
import PercentageBox from './PercentageBox.vue';

const props = defineProps<{
  recordId?: string;
}>();

const apiData = useAsyncData(async () => {
  if (!props.recordId) return {};

  return V1LocationHomeDataStatisticsRecordId({
    recordId: props.recordId,
  });
}, {});

watchImmediate(
  () => props.recordId,
  () => apiData.load()
);

const statisticsData = computed(() => {
  const data = apiData.data.value;

  return [
    {
      value: data.trainNum,
      color: 'green',
      label: '训练次数',
    },
    {
      value: data.opNum,
      color: 'green',
      label: '累计作业次数',
    },
    {
      value: data.opNum! - data.opNumOk!,
      color: 'red',
      label: '不合格次数',
    },
    {
      value: data.actionNum! - data.actionNumOk!,
      color: 'red',
      label: '动作不达标次数',
    },
  ];
});

const percentage = computed(() => {
  const data = apiData.data.value;

  return {
    op: Math.floor((data.actionNumOk! / data.actionNum!) * 100 || 0),
    all: Math.floor((data.opNumOk! / data.opNum!) * 100 || 0),
  };
});
</script>

<template>
  <div class="statistics-report">
    <ScreenTitle> 数据统计 </ScreenTitle>

    <a-spin wrapperClassName="spin-h-full" :spinning="apiData.loading.value">
      <ScreenBox class="h-full">
        <div class="flex items-center">
          <div class="flex-1 w-0 percentage-box">
            <PercentageBox class="percentage-img" type="error">
              <div class="value">{{ percentage.all }}</div>
              <span class="unit">%</span>
            </PercentageBox>
            <div class="label"> 总体合格率 </div>
          </div>
          <div class="percentage-v-divider"> &nbsp; </div>
          <div class="flex-1 w-0 percentage-box">
            <PercentageBox class="percentage-img" type="success">
              <div class="value">{{ percentage.op }}</div>
              <span class="unit">%</span>
            </PercentageBox>
            <div class="label"> 动作达标率 </div>
          </div>
        </div>

        <div class="statistics-grid">
          <StatisticsBox v-for="item in statisticsData">
            <div class="label">{{ item.label }}</div>
            <ValidValue :value="item.value || 0" :color="item.color" unit="次" />
          </StatisticsBox>
        </div>
        <!-- box -->
      </ScreenBox>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.statistics-report {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.percentage-v-divider {
  width: 1px;
  color: #414549;

  background: currentColor;
  position: relative;
  height: 130px;

  &::before,
  &::after {
    content: '';

    position: absolute;
    left: -1px;

    width: 3px;
    height: 6px;
    background: currentColor;
  }

  &::before {
    top: 0;
  }

  &::after {
    bottom: 0;
  }
}

.percentage-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .value {
    font-size: 42px;
    font-style: italic;
    font-weight: 700;
    margin-right: 4px;
  }

  .unit {
    font-size: 22px;
    font-style: italic;
    font-weight: 500;
    position: relative;
    bottom: 10px;
  }

  .label {
    font-size: 24px;
    font-weight: 400;
  }
}

.statistics-grid {
  display: grid;
  margin-top: 20px;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .label {
    font-size: 24px;
    font-weight: 400;
  }
}
</style>
