<script lang="ts" setup>
import { reactive, ref } from 'vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import StampText from '/@/components/ClientPort/StampText.vue';
import { useInfiniteScroll, watchImmediate } from '@vueuse/core';
import { V1LocationHomeAlarmDetailsStatisticsPost } from '/@/api/cddc.req';
import type {
  AlarmItemElement,
  V1LocationHomeAlarmDetailsStatisticsPostResponseResult,
  V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBodyObject,
} from '/@/api/cddc.model';
import { ACTION_TYPES_WRONG } from '/@/dict/modules/station';
import { useLoadingFn } from '/@/composables';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { convertDateRange } from '/@/utils/dateUtil';
import VImagePreview from '/@/components/ImagePreview/VImagePreview.vue';
import VAsyncImage from '/@/components/ImagePreview/VAsyncImage.vue';
import FailedRecordGroup from './FailedRecordGroup.vue';
import type { ProjectType } from '/@/enums/projectEnum';

type FailedRecordItem = V1LocationHomeAlarmDetailsStatisticsPostResponseResult;

export interface FailedRecordReportProps {
  /**
   * 记录 ID
   */
  recordId: string;
  title?: string;
  projectType?: ProjectType;
}

const props = defineProps<FailedRecordReportProps>();

const infiniteScrollEl = ref();

const infiniteScrollIns = useInfiniteScroll(
  infiniteScrollEl,
  () => {
    const length = 10;
    const startIdx = state.visibleData.length;

    state.visibleData.push(...state.data.slice(startIdx, startIdx + length));
  },
  {
    distance: 50,
    canLoadMore: () => {
      const startIdx = state.visibleData.length;

      return startIdx < state.data.length;
    },
  }
);

const FILTER_OPTIONS = [...ACTION_TYPES_WRONG];

const state = reactive({
  data: [] as FailedRecordItem[],
  visibleData: [] as FailedRecordItem[],
  query: {
    filter: [],
    filterNOK: true,
    filterNOKStandard: true,
    date: [],
  },
});

const previewState = reactive({
  visible: false,
  item: null as null | AlarmItemElement,
});

const fetchData = useLoadingFn(_fetchData);

async function _fetchData() {
  if (!props.recordId) return;

  const queryData: V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBodyObject = {
    recordId: props.recordId,
    ...convertDateRange(state.query.date),
    result: state.query.filterNOK ? 0 : undefined,
    standard: state.query.filterNOKStandard ? 0 : undefined,
    actionTypes: state.query.filter,
  };

  const resp = await V1LocationHomeAlarmDetailsStatisticsPost(queryData);
  state.visibleData = [];
  state.data = resp;

  infiniteScrollIns.reset();
}

watchImmediate(
  () => props.recordId,
  () => refreshImagesData()
);

function refreshImagesData() {
  fetchData();
}

function handlePreview(item: AlarmItemElement) {
  previewState.item = item;
  previewState.visible = true;
}
</script>

<template>
  <div class="failed-record-report">
    <ScreenTitle>
      <span>
        {{ title || '不合格记录' }}
      </span>
      <template #suffix>
        <div class="flex flex-1 items-center ml-4 justify-end">
          <span>操作日期：</span>
          <a-range-picker
            class="w-300px !mr-4"
            size="large"
            :popupStyle="{ width: '700px' }"
            v-model:value="state.query.date"
            allow-clear
            @change="refreshImagesData"
          />
          <a-checkbox
            class="standard-pr-0"
            v-model:checked="state.query.filterNOKStandard"
            @change="refreshImagesData"
          >
            不达标标签：
          </a-checkbox>
          <a-select
            class="!mr-4"
            size="large"
            style="width: 200px"
            v-model:value="state.query.filter"
            allow-clear
            max-tag-count="responsive"
            placeholder="请选择"
            mode="multiple"
            :options="FILTER_OPTIONS"
            show-arrow
            @change="refreshImagesData"
          />
          <a-checkbox v-model:checked="state.query.filterNOK" @change="refreshImagesData">
            不合格标签
          </a-checkbox>
        </div>
      </template>
    </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="fetchData.loading">
      <ScreenBox>
        <div ref="infiniteScrollEl" class="max-h-700px overflow-auto">
          <template v-if="state.visibleData?.length">
            <FailedRecordGroup
              v-for="(item, idx) in state.visibleData"
              :kye="`${item.createdDate}-${idx}`"
              :item="item"
              :projectType="projectType"
              @preview="handlePreview"
            />
          </template>
          <customizeRenderEmpty v-else />
        </div>
      </ScreenBox>
    </a-spin>

    <VImagePreview v-model:visible="previewState.visible">
      <div class="w-80vw h-60vh relative bg-black">
        <VAsyncImage :img-id="previewState.item?.recordDetailAlarmId" />
        <div class="absolute top-0 right-0">
          <StampText :type="previewState.item?.stampType">
            {{ previewState.item?.stampLabel }}
          </StampText>
        </div>
      </div>
    </VImagePreview>
  </div>
</template>

<style lang="less" scoped>
.failed-record-report {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.standard-pr-0 {
  &:deep(span:nth-child(2)) {
    padding-right: 0;
  }
}
</style>
