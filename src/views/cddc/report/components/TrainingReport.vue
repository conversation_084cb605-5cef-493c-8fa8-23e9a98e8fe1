<script lang="ts" setup>
import StatisticsReport from './StatisticsReport.vue';
import ProgressReport from './ProgressReport.vue';
import ProjectRank from './ProjectRank.vue';
import FailedRecordReport from './FailedRecordReport.vue';

export interface TrainingReportProps {
  /**
   * 记录 ID
   */
  recordId: string;
}

defineProps<TrainingReportProps>();
</script>

<template>
  <!-- 数据统计 -->
  <div class="flex gap-5">
    <StatisticsReport class="flex-1 w-0" :record-id="recordId" />
    <ProgressReport class="flex-1 w-0" :record-id="recordId" />
    <ProjectRank class="flex-1 w-0" :record-id="recordId" />
  </div>

  <!-- 不合格记录 -->
  <div class="section">
    <FailedRecordReport :record-id="recordId" />
  </div>
</template>

<style lang="less" scoped>
@import url('/@/views/cddc/home/<USER>/home-components.less');

.section {
  padding: 20px 0;
  border-radius: 4px;
}
</style>
