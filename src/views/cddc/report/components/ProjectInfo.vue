<script lang="ts" setup>
import { useVModel, watchImmediate } from '@vueuse/core';
import { useAsyncData } from '/@/composables';
import { V1ManageTrainStudyRecordsPagePost } from '/@/api/cddc.req';
import { computed, ref } from 'vue';
import { ProjectType } from '/@/enums/projectEnum';
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '/@/api/cddc.model';

export interface IProjectProps {
  recordId?: string;
  userId: string;
  projectType: ProjectType;
}

const props = defineProps<IProjectProps>();
const emit = defineEmits(['update:recordId']);

const vRecordId = useVModel(props, 'recordId', emit);

const selectedRecord = ref<V1LocationStudyCheckProjectIDUserIDGetResponseResult>();

const projectList = useAsyncData(async () => {
  const resp = await V1ManageTrainStudyRecordsPagePost({
    pageSize: 999,
    currentPage: 1,
    data: {
      userIdList: [props.userId],
      projectType: props.projectType as any,
    },
  });

  const options = resp.records?.map((item) => ({
    ...item,
    label: item.projectName,
    value: item.id,
  }));

  return options;
}, []);

watchImmediate(
  () => props.projectType,
  async () => {
    await projectList.load();

    const oldSelectedName = selectedRecord.value?.projectName;
    const options = projectList.data.value;

    // 优先判断 id
    const selected = options?.find(
      (item) => item.id === vRecordId.value || item.projectName === oldSelectedName
    );

    if (selected?.id === vRecordId.value) {
      selectedRecord.value = selected;
    }

    vRecordId.value = selected?.id ?? options?.at(0)?.value;
  }
);

watchImmediate(vRecordId, () => {
  if (vRecordId.value) {
    selectedRecord.value = projectList.data.value?.find((item) => item.value === vRecordId.value);
  } else {
    selectedRecord.value = undefined;
  }
});

const currentItem = computed(() => {
  return projectList.data.value?.find((item) => item.value === vRecordId.value);
});
</script>

<template>
  <div class="project-info inline-flex items-center gap-2">
    <span> 项目： </span>
    <a-select
      class="w-140px"
      size="large"
      v-model:value="vRecordId"
      :options="projectList.data.value"
      placeholder="请选择项目"
    />
    <template v-if="currentItem?.projectType?.toString() === ProjectType.Training">
      <span>合格率要求：{{ currentItem?.requestQualificationRate || 0 }}%</span>
      <span>达标率要求：{{ currentItem?.requestActionRate || 0 }}%</span>
      <span>效率要求：{{ currentItem?.requestEfficiency || 0 }}次/min</span>
    </template>
  </div>
</template>

<style lang="less" scoped>
//
</style>
