<script lang="ts" setup>
import { V1LocationHomeProgressStatisticsRecordId } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { watchImmediate } from '@vueuse/core';
import ProgressReport from '/@/components/ClientPort/ProgressReport.vue';

const props = defineProps<{
  recordId?: string;
}>();

const apiData = useAsyncData(async () => {
  // 如果有外部数据或没有recordId，则不调用接口
  if (!props.recordId) return {};

  return V1LocationHomeProgressStatisticsRecordId({
    recordId: props.recordId,
  });
}, {});

watchImmediate(
  () => props.recordId,
  () => apiData.load()
);
</script>

<template>
  <ProgressReport :data="apiData.data.value" :loading="apiData.loading.value" />
</template>

<style lang="less" scoped></style>
