<script lang="ts" setup>
import { computed } from 'vue';
import FailedRecordReport from './FailedRecordReport.vue';
import PercentageBox from './PercentageBox.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import {
  V1LocationStudyCheckProjectIdUserId,
  V1ManageTrainStudyRecordsId,
  V1ManageTranProjectId,
} from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { watchImmediate } from '@vueuse/core';
import { ProjectStatusEnum, ProjectType } from '/@/enums/projectEnum';
import { useRouter } from 'vue-router';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

export interface TrainingReportProps {
  /**
   * 记录 ID
   */
  recordId: string;
}

const props = defineProps<TrainingReportProps>();

const router = useRouter();

const currentProjectRecord = useAsyncData(V1ManageTrainStudyRecordsId, {});
const currentProject = useAsyncData(V1ManageTranProjectId, {});
const studyData = useAsyncData(V1LocationStudyCheckProjectIdUserId, {});

watchImmediate(
  () => props.recordId,
  () => fetchInitData()
);

async function fetchInitData() {
  await currentProjectRecord.load({ id: props.recordId! });
  const resp = currentProjectRecord.data.value;

  await currentProject.load({
    id: resp.projectId!,
  });

  await studyData.load({
    userId: resp.userId!,
    projectId: resp.projectId!,
  });
}

const trainData = computed(() => {
  const data = studyData.data.value;

  return [
    {
      label: '用时',
      color: 'green',
      value: formatSecondsToMinute(data.trainDuration || 0),
    },
    {
      label: '有效作业次数',
      value: data.effectiveNum || 0,
      unit: '次',
      color: 'green',
    },
    {
      label: '操作合格率',
      value: Math.floor((data.opNumOk! / data.opNum!) * 100 || 0),
      unit: '%',
      color: 'green',
    },
    {
      label: '动作达标率',
      value: Math.floor((data.actionNumOk! / data.actionNum!) * 100 || 0),
      unit: '%',
      color: 'green',
    },
  ];
});

const info = computed(() => {
  const data = studyData.data.value;

  const isPassed = data.status === ProjectStatusEnum.PASSED_CHECK;

  return {
    passed: isPassed,
    date: data.lastUpdateTime || '-',
    required: `${data.requestDuration || 0} min 内完成${
      data.requestFrequency || 0
    }次操作且合格率在 ${data.requestQualificationRate || 0}% 和动作达标率在 ${
      data.requestActionRate
    }% 以上`,
  };
});

function gotoExamPage() {
  const data = currentProjectRecord.data.value;
  if (!data.projectId) return;

  router.push({
    path: '/cddc/skill-assessment',
    query: {
      projectId: data.projectId,
    },
  });
}
</script>

<template>
  <div class="progress-report">
    <ScreenTitle class="mb-3">
      <span> 考核信息 </span>
      <template #suffix>
        <div class="flex justify-end flex-1">
          <a-button type="primary" size="large" class="px-4!" v-if="false" @click="gotoExamPage">
            重考
          </a-button>
        </div>
      </template>
    </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="studyData.loading.value">
      <ScreenBox class="progress-items h-200px">
        <div class="flex gap-5 h-full">
          <div class="flex-item percentage-box flex-inline items-center">
            <PercentageBox :type="info.passed ? 'success' : 'error'">
              <div class="text-big">
                {{ info.passed ? '合格' : '不合格' }}
              </div>
            </PercentageBox>
          </div>
          <div class="flex-item extra-info flex gap-5" style="flex: 2">
            <div class="info-row">
              <div class="key"> 时间 </div>
              <div class="value">
                {{ info.date }}
              </div>
            </div>
            <div class="info-row">
              <div class="key"> 要求 </div>
              <div class="value">
                {{ info.required }}
              </div>
            </div>
          </div>

          <div class="flex-item flex gap-5" style="flex: 4">
            <ScreenBox1
              class="flex-1 train-item"
              v-for="(item, idx) in trainData"
              :key="idx"
              :item="item"
            />
          </div>
        </div>
      </ScreenBox>
    </a-spin>
  </div>
  <!-- 不合格记录 -->
  <div class="section">
    <FailedRecordReport :record-id="recordId" title="考核记录" :project-type="ProjectType.Exam" />
  </div>
</template>

<style lang="less" scoped>
.section {
  padding: 20px 0;
  border-radius: 4px;
}

.text-big {
  font-size: 1.6vw;
  font-style: italic;
  font-weight: 700;
  margin-bottom: 0px;
}

.extra-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;

  --ignore-dark-bg: linear-gradient(
    228deg,
    rgba(62, 71, 81, 0.2) 13.78%,
    rgba(62, 71, 81, 0.2) 13.78%
  );

  background: var(--ignore-dark-bg);
  padding: 0 16px;

  .info-row {
    font-size: 1vw;

    .key {
      font-size: 1.1vw;
      color: rgb(62, 234, 179);
    }
  }
}

.flex-item {
  flex: 1;
  width: 0;
}

.train-item {
  each(range(4), {
      &:nth-child(@{value}n) {
        --ignore-dark-image: url('@/assets/svg/screen/bg-box-icon-@{value}.svg');

        background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png'),
        no-repeat right top / auto 80% var(--ignore-dark-image);
      }
    });
}
</style>
