<script lang="ts" setup>
import { watchImmediate } from '@vueuse/core';
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { V1ManageTrainStudyRecordsId } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import ProjectInfo from './components/ProjectInfo.vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import ScreenPageContainer from '/@/components/ClientPort/ScreenPageContainer.vue';
import TrainingReport from './components/TrainingReport.vue';
import ExamReport from './components/ExamReport.vue';
import RaceReport from './components/RaceReport.vue';
import StationInfo from '/@/components/ClientPort/StationInfo.vue';
import { ProjectType } from '/@/enums/projectEnum';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { useRouteQuery } from '/@/composables/useRouteQuery';

const router = useRouter();

interface IRouteParams {
  origin?: 'data-report';
  /**
   * 记录 ID
   */
  recordId?: string;

  userId?: string;

  /**
   * 报告类型
   */
  type?: ProjectType;
}

const urlParams = useRouteQuery<IRouteParams>();

// 是否为数据报表
const isDataReport = computed(() => urlParams.origin === 'data-report');

const basicInfo = useAsyncData(async () => {
  if (!urlParams.recordId) return {};

  const resp = await V1ManageTrainStudyRecordsId({ id: urlParams.recordId });

  if (resp.userId) {
    urlParams.userId = resp.userId;
  }

  return resp;
}, {});

const projectType = computed(() => basicInfo.data.value.projectType?.toString() as ProjectType);

watchImmediate(() => urlParams.recordId, updateBasicInfo);

fetchInitData();

async function fetchInitData() {
  urlParams.type ||= ProjectType.Training;
}

async function updateBasicInfo() {
  await basicInfo.load();
}

const goBack = () => {
  router.back();
};
</script>

<template>
  <ScreenPageContainer class="report-container home-container">
    <!-- 顶部信息 -->
    <div class="header">
      <div class="flex-1 flex items-center gap-2">
        <div class="back-btn">
          <a-button class="float-back-btn" type="link" @click="goBack">
            <span class="relative -top-2px">
              <SvgIcon name="g-ic-line-left" />
            </span>
            <span class="pl-1"> 返回 </span>
          </a-button>
        </div>
        <div class="v-divider mr-2">&nbsp;</div>
        <div class="switches">
          <a-radio-group v-model:value="urlParams.type" size="large">
            <a-radio-button :value="ProjectType.Training">训练报告</a-radio-button>
            <a-radio-button :value="ProjectType.Exam">考核报告</a-radio-button>
            <a-radio-button :value="ProjectType.Race">比赛报告</a-radio-button>
          </a-radio-group>
        </div>
        <div class="v-divider mx-2">&nbsp;</div>
        <template v-if="isDataReport">
          <span class="truncate max-w-120px" :title="basicInfo.data.value.userName">
            用户：{{ basicInfo.data.value.userName || '--' }}
          </span>
          <span>最新数据时间：{{ basicInfo.data.value.lastUpdateTime || '--' }}</span>
        </template>
        <ProjectInfo
          v-if="urlParams.userId && urlParams.type"
          v-model:record-id="urlParams.recordId"
          :project-type="urlParams.type"
          :user-id="urlParams.userId"
        />
      </div>

      <StationInfo />
    </div>

    <template v-if="urlParams.recordId && urlParams.type === projectType">
      <TrainingReport
        v-if="urlParams.type === ProjectType.Training"
        :record-id="urlParams.recordId"
      />
      <ExamReport v-if="urlParams.type === ProjectType.Exam" :record-id="urlParams.recordId" />
      <RaceReport v-if="urlParams.type === ProjectType.Race" :record-id="urlParams.recordId" />
    </template>
    <customizeRenderEmpty v-else />
  </ScreenPageContainer>
</template>

<style lang="less" scoped>
@import url('/@/views/cddc/home/<USER>/home-components.less');

.report-container {
  .header {
    display: flex;
    align-items: center;

    padding-bottom: 12px;
    gap: 8px;

    .v-divider {
      width: 2px;
      --ignore-dark-bg: rgba(255, 255, 255, 0.2);
      background: var(--ignore-dark-bg);
    }
  }
}

.back-btn {
  position: relative;
  width: 55px;
  height: 0px;

  .float-back-btn {
    position: absolute;
    padding: 10px;
    height: auto;
    color: white;

    top: 0%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
