<template>
  <BaseProjectManagement
    :project-type="3"
    project-name="比赛项目"
    :columns="columns"
    :drawer-component="CompetitionProjectDrawer"
  />
</template>

<script lang="ts" setup>
import { columns } from './data';
import CompetitionProjectDrawer from './components/ProjectDrawer.vue';
import BaseProjectManagement from '/@/components/project/BaseProjectManagement.vue';
</script>

<style lang="less" scoped>
.competition-project-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
