<template>
  <BaseProjectDrawer
    :title="title"
    :project-type="3"
    :record="record"
    :formSchemas="formSchemas"
    @success="$emit('success', $event)"
    @register="$emit('register')"
    ref="drawerRef"
  />
</template>

<script lang="ts" setup>
import BaseProjectDrawer from '/@/components/project/BaseProjectDrawer.vue';
import type { CompetitionProjectFormData } from '../types';
import { formSchemas } from './formData';
import { ref } from 'vue';

const drawerRef = ref();

defineProps<{
  record?: Partial<CompetitionProjectFormData>;
  title?: string;
}>();

defineEmits<{
  (e: 'success', data: any): void;
  (e: 'register'): void;
}>();

defineExpose({
  openDrawer: () => drawerRef.value.openDrawer(),
  setFormData: (data: Partial<CompetitionProjectFormData>) => drawerRef.value.setFormData(data),
  resetForm: () => drawerRef.value.resetForm(),
});
</script>
