export type CompetitionProjectStatus = 'ENABLE' | 'DISABLE';

export interface AlgorithmItem {
  modelCode: string | null;
  modelJson?: string;
  modelJsonObj?: {
    isIndicator: number;
    isQualified: boolean;
    isActionQualified: boolean;
  };
}

export interface CompetitionProjectFormData {
  id?: number;
  name: string;
  locationList?: [] | null;
  locationIds: string[];
  requiredTime: number;
  requiredCount: number;
  qualifiedRate: number;
  status?: CompetitionProjectStatus;
  actionQualifiedRate: number;
  countMethod: string | null;
  effectRequirement: number;
  modelList: AlgorithmItem[];
  raceType: 1 | 2; // 比赛特有字段
  rules: string; // 比赛规则
  competitionDuration: number; // 比赛时长（分钟）
}

export interface CompetitionProjectRecord
  extends Omit<CompetitionProjectFormData, 'countMethod' | 'effectRequirement'> {
  method: string;
  status: CompetitionProjectStatus;
  updateTime: string;
  updater: string;
  currentTeams: number; // 当前报名队伍数
  registrationStatus: 'NOT_STARTED' | 'IN_PROGRESS' | 'CLOSED'; // 报名状态
  competitionStatus: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED'; // 比赛状态
}

export interface CompetitionProjectSubmitData extends CompetitionProjectFormData {
  algorithms: AlgorithmItem[];
}

export type AlgorithmValidateError = {
  type?: string;
  checkbox?: string;
};

export type AlgorithmValidateResult = {
  isValid: boolean;
  errors: AlgorithmValidateError[];
  firstErrorMessage?: string;
};
