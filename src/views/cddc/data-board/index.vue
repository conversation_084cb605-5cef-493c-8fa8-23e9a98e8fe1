<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import { useAsyncData } from '/@/composables';
import {
  V1ManageSysUnitTerminalList,
  V1ManageTrainStudyRecordsMonthlyStatistics,
} from '/@/api/cddc.req';
import { calcPercentage } from '/@/utils';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';

const statisticsData = useAsyncData(V1ManageTrainStudyRecordsMonthlyStatistics, {});
const listData = useAsyncData(V1ManageSysUnitTerminalList, []);

fetchInitData();

function fetchInitData() {
  statisticsData.load();
  listData.load();
}

const trainData = computed(() => {
  const d = statisticsData.data.value;
  return [
    {
      label: '训练次数',
      value: d.trainNum || 0,
      unit: '次',
    },
    {
      label: '累计作业次数',
      value: d.opNum,
      unit: '次',
    },
    {
      label: '总体合格率',
      value: calcPercentage(d.opNumOk, d.opNum),
      unit: '%',
    },
    {
      label: '不合格次数',
      value: d.opNum! - d.opNumOk! || 0,
      unit: '次',
    },
    {
      label: '动作达标率',
      value: calcPercentage(d.actionNumOk, d.actionNum),
      unit: '%',
    },
    {
      label: '动作不达标次数',
      value: d.actionNum! - d.actionNumOk! || 0,
      unit: '次',
    },
  ];
});

const stations = computed(() => {
  const list = listData.data.value;
  const items = list.map((item) => ({
    label: item.status !== 'IN_USE' ? '空闲' : '使用中',
    status: item.status !== 'IN_USE' ? 'idle' : 'busy',
    name: item.unitName,
  }));

  return items;
});
</script>

<template>
  <div class="p-4">
    <ScreenTitle class="mb-3">本月数据</ScreenTitle>
    <a-spin :spinning="statisticsData.loading.value">
      <div class="flex gap-5">
        <ScreenBox1
          class="flex-1 train-item"
          v-for="(item, idx) in trainData"
          :key="idx"
          :item="item"
        />
      </div>
    </a-spin>
    <ScreenTitle class="my-3">工作台</ScreenTitle>
    <ScreenBox>
      <a-spin :spinning="listData.loading.value">
        <div class="stations" v-if="stations.length">
          <div class="station-item" v-for="station in stations" :class="station.status">
            <div class="station-name">
              {{ station.name }}
            </div>
            <div class="station-label">
              {{ station.label }}
            </div>
          </div>
        </div>
        <customizeRenderEmpty v-else />
      </a-spin>
    </ScreenBox>
  </div>
</template>

<style lang="less" scoped>
.stations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.station-item {
  display: flex;
  align-items: center;
  padding: 0 20px;

  gap: 8px;
  font-size: 16px;

  height: 100px;

  --ignore-dark-bg-color: rgba(255, 255, 255, 0.04);
  background: var(--ignore-dark-bg-color);

  border-radius: 4px;

  --ignore-dark-color: #29e8ab;

  &.idle {
    --ignore-dark-color: #29e8ab;
  }

  &.busy {
    --ignore-dark-color: #e97a13;
  }

  .station-name {
    flex: 1;
    word-break: break-all;
  }

  .station-label {
    position: relative;
    padding-left: 18px;
    color: var(--ignore-dark-color);

    &::before {
      content: '';
      @size: 10px;
      display: block;
      position: absolute;
      left: 0px;
      top: 8px;

      width: @size;
      height: @size;

      border-radius: 99px;
      background: var(--ignore-dark-color);
    }
  }
}
</style>
