// 首页专用的组件样式适配
.home-container {
  :deep(.statistics-report) {
    .percentage-box {
      .percentage-box-bg,
      .percentage-green,
      .percentage-red {
        max-width: 11vw;
        padding-bottom: 3.3vw;

        .value {
          font-size: 1.8vw;
          margin-right: 0.2vw;
        }

        .unit {
          font-size: 1.1vw;
          bottom: 0.7vh;
        }
      }

      .label {
        font-size: 1.1vw;
        margin-top: 0.8vh;
      }
    }

    .statistics-grid {
      margin-top: 1.2vh;
      gap: 1vw;

      .label {
        font-size: 1.1vw;
      }
    }
  }

  :deep(.progress-report) {
    .progress-items {
      gap: 1vw;
    }

    .progress-row {
      gap: 0.8vw;
      padding: 0 1vw;

      .progress-label {
        font-size: 1.1vw;
      }

      :deep(.progress-bar) {
        .value {
          font-size: 1vw;
        }
        .bar {
          height: 0.8vh;
        }
      }
    }
  }

  :deep {
    .latest-data {
      gap: 1vw;
    }

    .screen-box-1 {
      padding: 1.2vh 1vw;

      .label {
        font-size: 1.1vw;
      }

      .divider-box {
        height: 0.3vh;
        margin: 1.2vh 0;
      }

      .item-divider {
        width: 0.8vw;
        height: 0.3vh;
      }

      :deep(.valid-value) {
        .value {
          font-size: 2.94vw;
          .color {
            font-size: 1.8vw;
          }
        }

        .unit {
          font-size: 0.9vw;
          top: -0.1vh;
          margin-left: 0.2vw;
        }
      }
    }
  }
  :deep(.valid-value) {
    font-size: clamp(1.6vw, 2.08vw, 2.34vw);
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .home-container {
    :deep(.statistics-report) {
      .percentage-box {
        .percentage-green,
        .percentage-red {
          max-width: 9vw;
          padding-bottom: 3vh;

          .value {
            font-size: 1.6vw;
          }

          .unit {
            font-size: 1vw;
          }
        }

        .label {
          font-size: 1vw;
        }
      }

      .statistics-grid {
        .label {
          font-size: 1vw;
        }
      }
    }

    :deep(.progress-report) {
      .progress-row {
        .progress-label {
          font-size: 1vw;
        }

        :deep(.progress-bar) {
          .value {
            font-size: 0.9vw;
          }
          .bar {
            height: 0.7vh;
          }
        }
      }
    }

    :deep(.latest-train-report) {
      .train-item {
        .label {
          font-size: 1vw;
        }

        :deep(.valid-value) {
          .value {
            .color {
              font-size: 1.6vw;
            }
          }

          .unit {
            font-size: 0.8vw;
          }
        }
      }
    }
    :deep(.valid-value) {
      font-size: clamp(1.6vw, 2.08vw, 2.34vw);
    }
  }
}
