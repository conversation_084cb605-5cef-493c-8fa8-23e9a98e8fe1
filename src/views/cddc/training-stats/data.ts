import { V1ManageTrainStudyRecordsProjects } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 160,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '训练场次',
    dataIndex: 'trainNum',
  },
  {
    title: '累计作业次数',
    dataIndex: 'opNum',
  },
  {
    title: '总体合格率',
    dataIndex: 'passRate',
    customRender: ({ text }) => {
      return text ? `${text}%` : '0%';
    },
  },
  {
    title: '不合格次数',
    dataIndex: 'nokNum',
  },
  {
    title: '动作达标率',
    dataIndex: 'actionPassRate',
    customRender: ({ text }) => {
      return text ? `${text}%` : '0%';
    },
  },
  {
    title: '不达标次数',
    dataIndex: 'actionNokNum',
  },
  {
    title: '训练日期',
    dataIndex: 'trainDate',
    show: false,
    search: {
      field: 'timeRange',
      component: 'RangePicker',
      componentProps: {
        allowClear: true,
        showTime: { format: 'HH:mm' },
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        style: { width: '100%' },
      },
      colProps: { span: 8 },
    },
  },
];
