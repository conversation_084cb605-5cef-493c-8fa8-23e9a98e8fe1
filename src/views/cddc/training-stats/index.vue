<template>
  <div class="training-stats-container">
    <BasicTablePlus @register="registerTable"> </BasicTablePlus>
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageStatisticalReportTrainNumPost } from '/@/api/cddc.req';

const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};

  const data = {
    ...rawQuery,
    startTime: rawQuery.timeRange?.at(0),
    endTime: rawQuery.timeRange?.at(1),
  };

  params.data = data;
  return params;
};

const [registerTable, { reload }] = useTable({
  columns,
  showAction: false,
  api: (params) => V1ManageStatisticalReportTrainNumPost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
});
</script>

<style lang="less" scoped>
.training-stats-container {
  padding: 0px 8px;
  background: #fff;
}
</style>
