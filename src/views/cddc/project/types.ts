export type ProjectStatus = 'ENABLE' | 'DISABLE';
export interface AlgorithmItem {
  modelCode: string | null;
  modelJson?: string;
  modelJsonObj?: {
    isIndicator: number;
    isQualified: boolean;
    isActionQualified: boolean;
  };
}

export interface ProjectFormData {
  id?: number;
  name: string;
  locationList?: [] | null;
  locationIds: string[];
  requiredTime: number;
  requiredCount: number;
  qualifiedRate: number;
  status?: ProjectStatus;
  actionQualifiedRate: number;
  countMethod: string | null;
  effectRequirement: number;
  modelList: AlgorithmItem[];
}

export interface ProjectRecord extends Omit<ProjectFormData, 'countMethod' | 'effectRequirement'> {
  method: string;
  status: ProjectStatus;
  updateTime: string;
  updater: string;
}

export interface ProjectSubmitData extends ProjectFormData {
  algorithms: AlgorithmItem[];
}

// 算法校验错误类型
export type AlgorithmValidateError = {
  type?: string;
  checkbox?: string;
};

// 算法校验结果类型
export type AlgorithmValidateResult = {
  isValid: boolean;
  errors: AlgorithmValidateError[];
  firstErrorMessage?: string;
};
