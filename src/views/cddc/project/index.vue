<template>
  <BaseProjectManagement
    :project-type="1"
    project-name="训练项目"
    :columns="columns"
    :drawer-component="ProjectDrawer"
  />
</template>

<script lang="ts" setup>
import { columns } from './data';
import ProjectDrawer from './components/ProjectDrawer.vue';
import BaseProjectManagement from '/@/components/project/BaseProjectManagement.vue';
</script>

<style lang="less" scoped>
.project-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
