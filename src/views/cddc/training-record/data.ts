import {
  V1ManageTrainStudyRecordsProjects,
  V1ManageTrainStudyRecordsUsers,
} from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { toFixed } from '/@/utils';

// 表格配置
export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 120,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '项目类型',
    dataIndex: 'projectType',
    width: 100,
    customRender: ({ record }) => {
      // 根据项目类型显示对应文本,1:训练、2:考核、3:比赛
      const typeMap = {
        1: '训练',
        2: '考核',
        3: '比赛',
      };
      return typeMap[record.projectType] || '-';
    },
    search: {
      field: 'projectTypeList',
      component: 'Select',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showArrow: true,
        maxTagCount: 'responsive',
        options: [
          { label: '训练', value: 1 },
          { label: '考核', value: 2 },
          { label: '比赛', value: 3 },
        ],
      },
    },
  },
  {
    title: '人员姓名',
    dataIndex: 'userName',
    width: 120,
    search: {
      field: 'userIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsUsers();
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '操作工位',
    dataIndex: 'stationName',
    width: 120,
    customRender: () => {
      // 根据设计图显示默认值，实际应该从API获取工位信息
      return '拧紧操作台1';
    },
    search: {
      field: 'stationIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          // TODO: 实际应该从API获取工位信息
          return [];
        },
      },
    },
  },
  {
    title: '操作时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
    customRender: ({ record }) => {
      if (record.lastUpdateTime) {
        return new Date(record.lastUpdateTime).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        });
      }
      return '-';
    },
    search: {
      field: 'timeRange',
      label: '操作时间',
      component: 'RangePicker',
      componentProps: {
        allowClear: true,
        showTime: { format: 'HH:mm:ss' },
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        style: { width: '100%' },
      },
      colProps: { span: 6 },
    },
  },
  {
    title: '作业次数',
    dataIndex: 'opNum',
    width: 100,
  },
  {
    title: '操作时长',
    dataIndex: 'trainDuration',
    width: 100,
    customRender: ({ record }) => {
      const duration = record.trainDuration || 0;
      if (duration >= 60) {
        const hours = Math.floor(duration / 60);
        const minutes = Math.floor(duration % 60);
        return `${hours}小时${minutes}分钟`;
      } else {
        const minutes = Math.floor(duration);
        const seconds = Math.round((duration - minutes) * 60);
        return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
      }
    },
  },
  {
    title: '合格率',
    dataIndex: 'qualificationRate',
    width: 100,
    customRender: ({ record }) => {
      const rate = record.opNumOk && record.opNum
        ? (record.opNumOk / record.opNum) * 100
        : 0;
      return `${toFixed(rate)}%`;
    },
  },
  {
    title: '达标率',
    dataIndex: 'standardRate',
    width: 100,
    customRender: ({ record }) => {
      const rate = record.actionNumOk && record.actionNum
        ? (record.actionNumOk / record.actionNum) * 100
        : 0;
      return `${toFixed(rate)}%`;
    },
  },
];
