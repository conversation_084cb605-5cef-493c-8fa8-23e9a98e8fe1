<template>
  <div class="training-record-container">
    <BasicTablePlus @register="registerTable" />
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageTrainStudyRecordsPagePost } from '/@/api/cddc.req';
import { ProjectType } from '/@/enums/projectEnum';

const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};
  const data = {
    ...rawQuery,
    startTime: rawQuery.timeRange?.at(0),
    endTime: rawQuery.timeRange?.at(1),
    projectType: ProjectType.Training,
  };

  params.data = data;
  return params;
};

const [registerTable] = useTable({
  columns,
  showAction: false,
  api: (params) => V1ManageTrainStudyRecordsPagePost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
});
</script>

<style lang="less" scoped>
.training-record-container {
  padding: 2.08vw 1.85vw;
  background: #fff;
  min-height: calc(100vh - 120px);

  // 确保表格内容居左显示
  :deep(.ant-table-tbody > tr > td) {
    text-align: left !important;
  }
}

@media (max-width: 1366px) {
  .training-record-container {
    padding: 16px;
  }
}
</style>
