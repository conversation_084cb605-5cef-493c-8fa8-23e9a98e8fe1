import { V1ManageTrainStudyRecordsProjects } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 160,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      colProps: {
        span: 6,
      },
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({ projectType: 3 });
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 120,
  },
  {
    title: '限制时长(min)',
    dataIndex: 'requestDuration',
    width: 120,
  },
  {
    title: '有效作业次数',
    dataIndex: 'effectiveNum',
    width: 120,
  },
  {
    title: '要求次数',
    dataIndex: 'requestFrequency',
    width: 120,
  },
  {
    title: '实际用时',
    dataIndex: 'trainDurationSecond',
    width: 160,
    customRender(opt) {
      return formatSecondsToMinute(opt.value! || 0);
    },
  },
  {
    title: '竞赛排名',
    dataIndex: 'ranking',
    width: 160,
  },
];
