<template>
  <div class="competition-stats-container">
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="handleView(record)">查看详情</a-button>
        </template>
      </template>
    </BasicTablePlus>
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageTrainStudyRecordsPagePost } from '/@/api/cddc.req';
import { useRouter } from 'vue-router';
import { ProjectType } from '/@/enums/projectEnum';

const router = useRouter();
const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};

  const data = {
    ...rawQuery,
    startTime: rawQuery.timeRange?.at(0),
    endTime: rawQuery.timeRange?.at(1),
    projectType: ProjectType.Race,
  };

  params.data = data;
  return params;
};

const handleView = (record: any) => {
  console.log('View details:', record);
  router.push({
    path: '/cddc/report',
    query: { recordId: record.id, type: 3, origin: 'competition-report' },
  });
};

const [registerTable, { reload }] = useTable({
  columns,
  api: (params) => V1ManageTrainStudyRecordsPagePost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
});
</script>

<style lang="less" scoped>
.competition-stats-container {
  padding: 0px 8px;
  background: #fff;
}
</style>
