<template>
  <div class="competition-container">
    <!-- 顶部信息 -->
    <div class="header">
      <div class="competition-select">
        <a-button class="float-back-btn" type="link" @click="goBack">
          <SvgIcon name="g-ic-line-left" />
          <span class="back-text">返回</span>
        </a-button>
        <ProjectSelector
          title="比赛项目"
          @change="handleProjectChange"
          @pList="handleProjectList"
          :showTimeInfo="true"
          :raceType="competitionProgress.raceType"
          :remaining-time="formattedTime"
          :elapsedTime="formattedElapsedTime"
          :is-time-warning="isLastMinute"
          :operations-label="operationsLabel"
          :validOperations="competitionProgress.requestFrequency"
        />
      </div>
    </div>
    <!-- 摄像头画面 -->
    <div class="camera-views">
      <CameraView
        :show-status-tag="true"
        :status-result="realTimeData?.operationLog?.result"
        @connected="handleCameraConnected"
        @disconnected="handleCameraDisconnected"
        @error="handleCameraError"
      />
      <div class="line"></div>
      <TeachingView :point-data="pointData" :diagram-type="projectDiagramType" />
    </div>
    <!-- 比赛实时数据 -->
    <div class="flex gap-5 competition-data">
      <div class="section-grid">
        <OperationLog
          ref="operationLogRef"
          class="flex-1 w-0 skill-log"
          :detailId="competitionId"
          :data="realTimeData"
          style="flex: 3"
        />
        <LiveData class="flex-1 w-0" :data="realTimeData" style="flex: 2" />
        <CompetitionProgress
          class="w-81"
          :data="competitionProgress"
          :formattedTime="formattedTime"
          :isLastMinute="isLastMinute"
          :elapsedTime="formattedElapsedTime"
          :raceType="competitionProgress.raceType"
          style="flex: 1"
        />
      </div>
    </div>
    <!-- 底部信息 -->
    <div class="footer">
      <div class="action-buttons">
        <a-button
          type="primary"
          @click="debounceStartCompetition"
          v-if="!isStart"
          :disabled="!isPlayerConnected"
          :loading="loading"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          {{ isPlayerConnected ? '开始比赛' : '等待摄像头连接...' }}
        </a-button>
        <template v-if="isStart">
          <!-- <a-button
            type="primary"
            @click="submitCompetition(false)"
            v-if="competitionStatus === CompetitionStatusEnum.IN_PROGRESS"
          >
            <template #icon>
              <CheckCircleOutlined />
            </template>
            提交比赛</a-button
          > -->
          <a-button @click="exitCompetition">
            <template #icon>
              <CloseCircleOutlined />
            </template>
            退出比赛</a-button
          >
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from '@geega-ui-plus/ant-design-vue';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@geega-ui-plus/icons-vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { debounce } from 'lodash-es';
import type { CompetitionData, CompetitionResponse } from './types';
import { CompetitionStatusEnum } from './types';
import { useWebSocketHandler } from '/@/composables/useWebSocketHandler';
import ProjectSelector from '/@/components/MyProject/ProjectSelector.vue';
import CameraView from '/@/components/CameraView/index.vue';
import TeachingView from '/@/components/TeachingView/index.vue';
import type { DiagramType } from '/@/components/TeachingView/index.vue';
import type { ProjectInfo } from '/@/types/project';
import OperationLog from '../training/components/OperationLog.vue';
import LiveData from './components/LiveData.vue';
import CompetitionProgress from './components/CompetitionProgress.vue';
import { useGlobSetting } from '/@/hooks/setting';
import { createLocalStorage } from '/@/utils/cache';
import {
  V1LocationStudyExitPost,
  V1LocationStudyInitPost,
  V1LocationStudyOverPost,
  V1LocationStudyStartPost,
  V1LocationStudyVideoCreatePost,
  V1LocationStudyVideoStopPost,
  V1ManageTranProjectWithName,
} from '/@/api/cddc.req';
import { useCountdown } from '/@/composables/useCountdown';
import { ProjectType } from '/@/enums/projectEnum';

// State
const router = useRouter();
const route = useRoute();
const isStart = ref(false);
const loading = ref(false);
const isPlayerConnected = ref(false);
const competitionId = ref('');
const competitionStatus = ref(CompetitionStatusEnum.NOT_STARTED);
const operationLogRef = ref();
const projectList = ref<ProjectInfo[]>([]);
const currentProjectId = ref('');

// 添加计时相关的状态
const elapsedTime = ref(0);
const elapsedTimer = ref<NodeJS.Timer | null>(null);

const realTimeData = ref<CompetitionData>({
  overallScore: 0,
  validOperations: 0,
  invalidOperations: 0,
  failedOperations: 0,
  operationLog: undefined,
});

const pointData = ref([]);
const currProjectName = ref('');
const projectDiagramType = ref<DiagramType>('tighten');
const competitionProgress = ref<CompetitionData>({
  overallScore: 0,
  effectiveNum: 0,
  validOperations: 0,
  invalidOperations: 0,
  failedOperations: 0,
  recordId: '',
  requestDuration: 0,
  requestFrequency: 0,
  requestQualificationRate: 0,
  requestActionRate: 0,
});

// 获取全局配置
const { socketUrl } = useGlobSetting();

const operationsLabel = computed(() => {
  return competitionProgress.value.raceType === 1 ? '有效作业次数' : '要求作业次数';
});

// 使用倒计时 composable
const { isLastMinute, formattedTime, start, reset } = useCountdown({
  onTimeUp: handleTimeUp,
  autoStart: true,
});
const pushTest = () => {
  realTimeData.value = testData;
  realTimeData.value.completedTotal =
    Number(testData.unqualifiedNum + testData.qualificationNum) || 0; //已完成作业次数
  competitionProgress.value.qualificationNum = realTimeData.value.completedTotal;
  // 计时模式
  if (competitionProgress.value.raceType === 1) {
    competitionProgress.value.requestFrequency = testData.qualificationNum || 0;
  }
  if (
    competitionProgress.value.raceType === 2 && // 计次模式
    realTimeData.value.completedTotal >= competitionProgress.value.requestFrequency &&
    competitionStatus.value === CompetitionStatusEnum.IN_PROGRESS
  ) {
    submitCompetition(true); // 调用提交方法并传入 true 表示自动提交
  }
};

// WebSocket handlers
const handleCompetitionDataUpdate = (data: CompetitionResponse) => {
  const currentValidOperations = realTimeData.value.validOperations;
  realTimeData.value = {
    ...realTimeData.value,
    ...data,
    completedTotal: Number(data.unqualifiedNum + data.qualificationNum) || 0, //已完成作业次数
    validOperations: data.operationLog
      ? Math.max(0, currentValidOperations - 1)
      : currentValidOperations,
  };
  // 计时模式
  if (competitionProgress.value.raceType === 1) {
    competitionProgress.value.requestFrequency = data.effectiveNum || 0;
  }
  competitionProgress.value.effectiveNum = data.effectiveNum || 0;

  // 检查是否达到要求次数，如果达到则自动提交比赛
  if (
    competitionProgress.value.raceType === 2 && // 计次模式
    realTimeData.value.effectiveNum >= competitionProgress.value.requestFrequency &&
    competitionStatus.value === CompetitionStatusEnum.IN_PROGRESS
  ) {
    submitCompetition(true); // 调用提交方法并传入 true 表示自动提交
  }
};

// 开始计时函数
const startElapsedTimer = () => {
  elapsedTime.value = 0;
  elapsedTimer.value = setInterval(() => {
    elapsedTime.value++;
  }, 1000);
};

// 停止计时函数
const stopElapsedTimer = () => {
  if (elapsedTimer.value) {
    clearInterval(elapsedTimer.value);
    elapsedTimer.value = null;
  }
};

const exitCompetition = () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出比赛吗？退出后当前比赛进度将丢失。',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      // 提交比赛
      exitPost();
      competitionStatus.value = CompetitionStatusEnum.NOT_STARTED;
      // 停止计时
      stopElapsedTimer();
      router.back();
    },
  });
};

const { initWebSocket, cleanupWebSocket } = useWebSocketHandler({
  type: 'competition',
  onDataUpdate: handleCompetitionDataUpdate,
  onExit: exitCompetition,
  onPointUpdate: (points) => {
    pointData.value = points;
  },
  messages: {
    exitConfirm: true,
    exitTitle: '确认退出',
    exitContent: '比赛正在进行中，确定要退出吗？',
    overMessage: '检测到人员已离开作业，自动结束本次比赛',
  },
});

const handleProjectList = (list: ProjectInfo[]) => {
  projectList.value = list;
};

// Event handlers
const handleProjectChange = async (project: ProjectInfo) => {
  const projectId = route.query.projectId as string;
  const projectName = project.name as string;

  if (!projectId || !projectName) {
    message.error('项目信息不完整');
    return;
  }

  try {
    loading.value = true;
    const ls = createLocalStorage();
    currentProjectId.value = projectId;
    currProjectName.value = projectName;
    // 设置图示类型
    projectDiagramType.value = (project.countAlgorithm as DiagramType) || 'tighten';

    // 获取项目信息和raceType
    const projectInfo = await V1ManageTranProjectWithName({
      name: projectName,
      type: Number(ProjectType.Race),
    });
    // 计时模式
    if (projectInfo.raceType === 1) {
      projectInfo.requestDuration = 0; // 初始为0
      projectInfo.requestFrequency = 0; // 初始为0
    }
    // 设置初始剩余操作次数和比赛时长
    competitionProgress.value = {
      ...competitionProgress.value,
      ...projectInfo,
    };

    // 如果正在比赛中，先结束当前比赛
    if (competitionStatus.value === CompetitionStatusEnum.IN_PROGRESS) {
      cleanupWebSocket();
      isStart.value = false;
    }

    // 重置倒计时
    reset();

    // 创建视频流
    await V1LocationStudyVideoCreatePost({
      projectId,
      userId: ls.get('userId'),
      locationId: ls.get('locationId'),
    });
  } catch (error) {
    message.error('切换项目失败，请重试');
  } finally {
    loading.value = false;
  }
};

// Camera event handlers
const handleCameraConnected = () => {
  isPlayerConnected.value = true;
};

const handleCameraDisconnected = () => {
  isPlayerConnected.value = false;
};

const handleCameraError = () => {
  isPlayerConnected.value = false;
};

// Actions
const debounceStartCompetition = debounce(async () => {
  if (loading.value) return;
  if (!isPlayerConnected.value) {
    message.warning('请等待摄像头连接成功后再开始比赛');
    return;
  }

  loading.value = true;
  const ls = createLocalStorage();
  try {
    const projectInfo = await V1ManageTranProjectWithName({
      name: currProjectName.value,
      type: Number(ProjectType.Race),
    });

    const res = await V1LocationStudyInitPost({
      projectId: projectInfo.id,
      userId: ls.get('userId'),
    });

    if (!res?.id) {
      throw new Error('获取比赛ID失败');
    }

    competitionId.value = res.id;
    // 计时模式
    if (res.raceType === 1) {
      res.requestFrequency = 0; // 初始为0
    }
    // 设置初始剩余操作次数和比赛时长
    competitionProgress.value = {
      ...competitionProgress.value,
      ...res,
    };

    // 初始化WebSocket连接
    if (!socketUrl) {
      throw new Error('缺少WebSocket连接地址');
    }

    initWebSocket({
      socketUrl: socketUrl,
      detailId: res.id,
    });

    const locationId = ls.get('locationId');
    await V1LocationStudyStartPost({ detailId: res.id, locationId });

    isStart.value = true;
    competitionStatus.value = CompetitionStatusEnum.IN_PROGRESS;
    // 开始计时
    startElapsedTimer();
  } catch (error) {
    message.error('开始比赛失败');
  } finally {
    loading.value = false;
  }
}, 300);

const submitCompetition = async (isAutoSubmit = false) => {
  const ls = createLocalStorage();
  if (isAutoSubmit) {
    const msg = competitionProgress.value.raceType === 2 ? '达到要求次数' : '比赛时间已到';
    message.warning(msg);
  }
  try {
    // 提交比赛
    await V1LocationStudyOverPost({
      locationId: ls.get('locationId'),
      detailId: competitionId.value,
    });
    competitionStatus.value = CompetitionStatusEnum.COMPLETED;
    isStart.value = false;
    // 停止计时
    stopElapsedTimer();
    cleanupWebSocket();
    // 跳转到报告页面
    router.push({
      path: '/cddc/report',
      query: {
        recordId: competitionProgress.value?.recordId,
        origin: 'train',
        type: ProjectType.Race,
      },
    });
  } catch (error) {
    message.error('提交比赛失败');
  }
};

// 格式化已用时间
const formattedElapsedTime = computed(() => {
  const minutes = Math.floor(elapsedTime.value / 60);
  const seconds = elapsedTime.value % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
});

const goBack = () => {
  if (isStart.value) {
    Modal.confirm({
      title: '确认退出',
      content: '比赛正在进行中，确定要退出吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        exitPost();
        router.back();
      },
    });
    return;
  }
  router.back();
};

// 处理倒计时结束
function handleTimeUp() {
  message.warning('比赛时间已到！');
  if (competitionStatus.value === CompetitionStatusEnum.IN_PROGRESS) {
    submitCompetition(true); // 使用统一的提交方法，传入 true 表示自动提交
  }
}

// 监听目标时长变化
watch(
  () => competitionProgress.value?.requestDuration,
  (newDuration) => {
    if (newDuration && newDuration > 0) {
      // 将分钟转换为秒
      const durationInSeconds = newDuration * 60;
      start(durationInSeconds);
    } else {
      reset();
    }
  },
  { immediate: true }
);

// 退出比赛
const exitPost = async () => {
  const ls = createLocalStorage();
  if (isStart.value) {
    await V1LocationStudyExitPost({
      locationId: ls.get('locationId'),
      detailId: competitionId.value,
    });
    isStart.value = false;
    cleanupWebSocket();
  }
};

onUnmounted(async () => {
  // 退出比赛
  exitPost();

  // 确保清理计时器
  stopElapsedTimer();

  // 关闭视频流
  if (currentProjectId.value) {
    await V1LocationStudyVideoStopPost({
      projectId: currentProjectId.value,
      userId: ls.get('userId'),
      locationId: ls.get('locationId'),
    });
  }
});
</script>

<style lang="less" scoped>
.competition-container {
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  color: #ffffff;
  background: #040405 url('/@/assets/images/screen/page-bg.png') no-repeat center / 100% 100%;
  padding: 1.5vh 2vw;

  .header {
    margin-bottom: 1.5vh;
    flex-shrink: 0;

    .competition-select {
      margin-bottom: 1.5vh;
      position: relative;
      display: flex;
      align-items: center;

      .time-info {
        display: flex;
        align-items: center;
        gap: 1.5vw;
        margin-left: 2vw;
        color: rgba(255, 255, 255, 0.85);
        font-size: 14px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 0.5vw;

          .info-icon {
            font-size: 16px;
          }

          .highlight {
            color: #29e8ab;
            font-weight: 500;

            &.time {
              font-family: 'Roboto Mono', monospace;
              font-size: 20px;
              font-weight: 600;
              letter-spacing: 1px;
              color: #29e8ab;
            }

            &.text-red {
              color: #f53f3f;
            }
          }
        }

        .info-divider {
          width: 1px;
          height: 14px;
          background: rgba(255, 255, 255, 0.2);
        }
      }
      .float-back-btn {
        width: auto;
        color: #fff;
        height: auto;
        font-size: 16px;
        .back-text {
          margin-left: 4px;
          &::after {
            content: '';
            display: inline-block;
            width: 2px;
            height: 12px;
            --ignore-dark-bg: rgba(255, 255, 255, 0.2);
            background: var(--ignore-dark-bg);
            margin-left: 12px;
          }
        }
      }
      .elapsed-time {
        margin-left: 2vw;
        color: #29e8ab;
        font-size: 16px;
        font-family: 'Roboto Mono', monospace;
      }
    }
  }

  .camera-views {
    display: flex;
    gap: 0.6vw;
    margin-bottom: 1.5vh;
    flex: 1;
    min-height: 0;
    background: url('/@/assets/images/screen/train-camera-bg.png') no-repeat center / 100% 100%;
    padding: 1vh 1.5vw 3vh 1.4vw;
    position: relative;

    .line {
      width: 0.2vw;
      --ignore-dark-img: url('/@/assets/images/screen/train-line.png');
      background: var(--ignore-dark-img) no-repeat center / 100% 100%;
      margin: 9vh 0 3vh 0;
    }

    .btn-switch {
      position: absolute;
      right: 2vw;
      top: 3vh;
    }

    .camera-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      border-radius: 4px;

      .title {
        margin-bottom: 2vh;
        font-size: 1.1vw;
        font-weight: 500;
        flex-shrink: 0;
      }

      .video-container {
        position: relative;
        aspect-ratio: 16/9;
        flex: 1;
        min-height: 0;
        background: #000;
        overflow: hidden;
        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        #camera2 {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          :deep(video) {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }

        .status-tag {
          position: absolute;
          top: 1vh;
          right: 1vw;
          font-size: 0.9vw;
          color: #fff;
          z-index: 99;
          width: 5.6vw;
          height: 5.6vw;

          &.success {
            background: url('@/assets/images/screen/train-icon-qualified.png') no-repeat center /
              100% 100%;
          }

          &.error {
            background: url('@/assets/images/screen/train-icon-unqualified.png') no-repeat center /
              100% 100%;
          }
        }

        .camera-loading {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.6);
          z-index: 10;

          :deep(.ant-spin) {
            color: #fff;

            .ant-spin-text {
              color: #fff;
              font-size: 14px;
              margin-top: 8px;
            }
          }
        }
      }
    }

    .point-box {
      flex: 1;
      padding: 5vh 0 0.5vh 0;
    }
  }

  .competition-data {
    flex-shrink: 0;
    min-height: 300px;

    .section-grid {
      display: flex;
      gap: 1vw;
      width: 100%;
      height: 100%;
      .skill-log {
        :deep(.screen-box) {
          max-height: 31vh;
        }
      }
    }
  }

  .footer {
    display: flex;
    width: 56vw;
    height: 6vh;
    margin: 0 auto;
    flex-shrink: 0;
    background: url('/@/assets/images/screen/train-btn-bg.png') no-repeat center / 100% 100%;
    justify-content: center;
    &.competition {
      padding-left: 7.3vw;
      justify-content: flex-start;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1.1vw;

      .cddc-ant-btn {
        min-width: 12.2vw;
        height: 5.2vh;
        border-radius: 4px;
        font-size: clamp(14px, 1.8vw, 40px);
        font-weight: 500;
      }
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .competition-container {
    padding: 1.2vh 1.6vw;

    .header {
      margin-bottom: 1.2vh;

      .competition-select {
        margin-bottom: 1.2vh;
      }
    }

    .camera-views {
      gap: 0.5vw;
      margin-bottom: 1.2vh;
      padding: 0.8vh 1.2vw 2.5vh 1.2vw;

      .line {
        margin: 8vh 0 2.5vh 0;
      }

      .btn-switch {
        right: 1.6vw;
        top: 2.5vh;
      }

      .camera-box {
        .title {
          margin-bottom: 1.6vh;
          font-size: 1vw;
        }

        .video-container {
          .status-tag {
            font-size: 0.8vw;
            width: 5vw;
            height: 5vw;
          }
        }
      }

      .point-box {
        padding: 4vh 0 0.4vh 0;
      }
    }

    .competition-data {
      margin-bottom: 1.2vh;
      height: 35vh;
      min-height: 280px;

      .section-grid {
        gap: 0.8vw;
      }
    }

    .footer {
      width: 60vw;
      height: 5vh;

      .action-buttons {
        gap: 1vw;

        .cddc-ant-btn {
          min-width: 7vw;
          height: 3.5vh;
          font-size: 1vw;
        }
      }
    }
  }
}
</style>
