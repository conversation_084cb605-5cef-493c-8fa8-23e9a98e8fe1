<template>
  <div class="statistics-report">
    <ScreenTitle> 比赛详情 </ScreenTitle>
    <ScreenBox class="h-full">
      <div class="box-content">
        <!-- 统计数据网格 -->
        <div class="statistics-grid">
          <StatisticsBox v-for="item in statisticsData" :key="item.label">
            <div class="stat-value">{{ item.label }}</div>
            <ValidValue :value="item.value" :color="item.color" :unit="item.unit" />
          </StatisticsBox>
        </div>
      </div>
    </ScreenBox>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import StatisticsBox from '/@/components/ClientPort/StatisticsBox.vue';
import ValidValue from '/@/components/ClientPort/ValidValue.vue';
import type { CompetitionData } from '../types';

const props = defineProps<{
  data: CompetitionData;
}>();

const statisticsData = computed(() => {
  const data = props.data || {};

  return [
    {
      value: data.completedTotal || 0,
      color: 'green',
      label: '已完成作业次数',
      unit: '次',
    },
    {
      value: data.effectiveNum || 0,
      color: 'green',
      label: '有效作业次数',
      unit: '次',
    },
    {
      value: Number(data.qualificationRate) || 0,
      color: 'green',
      label: '合格率',
      unit: '%',
    },
    {
      value: Number(data.actionRate) || 0,
      color: 'green',
      label: '达标率',
      unit: '%',
    },
    {
      value: data.unqualifiedNum || 0,
      color: 'red',
      label: '不合格作业次数',
      unit: '次',
    },
    {
      value: Number(data.nonStandardNum) || 0,
      color: 'red',
      label: '不达标操作次数',
      unit: '次',
    },
  ];
});

const data = computed(() => props.data || {});
</script>

<style lang="less" scoped>
.screen-box {
  padding: 0;
}
.statistics-report {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;
  min-width: 0;

  .box-content {
    height: 100%;
    padding: 0.74vw;
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.74vw;
    height: 100%;
    align-content: center;
  }

  .statistics-box {
    padding: 0.74vw;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 0.37vw;

    .label {
      font-size: 0.84vw;
      color: rgba(255, 255, 255, 0.85);
    }

    .value {
      font-size: 1.4vw;
      color: #fff;
      font-weight: 500;
      display: flex;
      align-items: baseline;
      gap: 0.19vw;

      .unit {
        font-size: 0.74vw;
        color: rgba(255, 255, 255, 0.45);
        font-weight: normal;
      }
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .statistics-report {
    gap: 0.6vw;

    .box-content {
      padding: 0.6vw;
    }

    .statistics-grid {
      gap: 0.6vw;
    }

    .statistics-box {
      padding: 0.6vw;
      gap: 0.3vw;

      .label {
        font-size: 0.9vw;
      }

      .value {
        font-size: 1.6vw;
        gap: 0.15vw;

        .unit {
          font-size: 0.8vw;
        }
      }
    }
  }
}

:deep(.statistics-box) {
  padding: 0.74vw 0.9vw;
  background-size: auto 100%;
  height: 5.42vh;
  min-height: 3.13vw;

  .three-dot {
    @size: 0.4vw;
    width: @size;
    height: @size;

    &::before,
    &::after {
      width: @size;
      height: @size;
    }
  }
}

:deep(.valid-value) {
  font-size: 1.26vw;
  line-height: initial;
  align-items: center;
  .unit {
    font-size: 0.84vw;
    bottom: -0.45vh;
  }
}
</style>
