<template>
  <div class="progress-report">
    <ScreenTitle> 比赛目标 </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="props.loading">
      <ScreenBox class="progress-items h-full">
        <div class="grid grid-cols-2 gap-1.5">
          <ScreenBox1
            class="progress-data-item"
            v-for="(item, idx) in competitionData.slice(0, 2)"
            :key="idx"
            :item="item"
          />
        </div>
        <!-- 剩余时间倒计时 -->
        <div class="grid grid-cols-1">
          <ScreenBox1 :item="competitionData[2]" />
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';
import type { CompetitionData } from '../types';

const props = defineProps<{
  /**
   * 报表数据
   */
  data?: CompetitionData;
  loading?: boolean;
  /**
   * 格式化后的剩余时间
   */
  formattedTime: string;
  /**
   * 是否处于最后一分钟
   */
  isLastMinute: boolean;
  /**
   * 比赛类型 1: 计时模式 2: 计次模式
   */
  raceType: number;
  /**
   * 已用时间（计次模式下使用）
   */
  elapsedTime?: string;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'timeUp'): void;
}>();

// 秒转分钟（用于显示目标时长）
const secondsToMinutes = (seconds: number) => {
  return seconds || 0;
};

const competitionData = computed(() => {
  const data = props.data;
  if (props.raceType === 1) {
    // 计时模式：显示比赛作业时间、有效作业次数、剩余时间
    return [
      {
        label: '比赛作业时间',
        unit: 'min',
        color: 'blue',
        value: data?.requestDuration || 0,
      },
      {
        label: '有效作业次数',
        unit: '次',
        color: 'blue',
        value: data?.effectiveNum || 0,
      },
      {
        label: '剩余时间',
        unit: '',
        color: props.isLastMinute ? 'red' : 'blue',
        value: props.formattedTime,
      },
    ];
  } else {
    // 计次模式：显示要求作业次数、已用时间
    return [
      {
        label: '要求作业次数',
        unit: '次',
        color: 'blue',
        value: data?.requestFrequency || 0,
      },
      {
        label: '已完成次数',
        unit: '次',
        color: 'blue',
        value: data?.qualificationNum || 0,
      },
      {
        label: '已用时间',
        unit: '',
        color: 'blue',
        value: props.elapsedTime || '00:00',
      },
    ];
  }
});
</script>

<style lang="less" scoped>
.progress-report {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;
  min-width: 0;
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  padding: 0.74vw;
  height: 100%;

  .grid {
    height: 100%;
    align-content: flex-start;
  }
}

.progress-data-item {
  padding: 0.74vw;
  border-radius: 4px;

  &:nth-child(1n) {
    background-size: 100% 100%, auto 30%;
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .progress-report {
    gap: 0.6vw;
    min-width: 0;

    .progress-items {
      gap: 0.6vw;
      padding: 0.6vw;

      .grid {
        gap: 0.42vw;
      }
    }

    .progress-data-item {
      padding: 0.6vw;

      &:nth-child(1n) {
        background-size: 100% 100%, auto 30%;
      }
    }

    :deep(.screen-box-1) {
      .label {
        font-size: 0.9vw;
      }

      .item-divider {
        width: 0.84vw;
      }

      .divider-box {
        height: 0.19vw;
        margin: 0.74vw 0;
      }
    }

    :deep(.valid-value) {
      font-size: 1.26vw;
      line-height: inherit;
      align-items: start;

      .unit {
        font-size: 0.74vw;
        margin-left: 2px;
      }
    }
  }
}

:deep(.screen-box-1) {
  .label {
    font-size: 0.92vw;
  }
  .valid-value {
    font-size: 1.26vw;
    line-height: initial;
    align-items: center;
    .unit {
      font-size: 0.84vw;
      margin-left: 2px;
    }
  }
}
</style>
