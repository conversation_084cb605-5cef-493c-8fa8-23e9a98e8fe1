import type { CompetitionData } from './types';

// 比赛状态枚举
export enum CompetitionStatusEnum {
  INIT = 'INIT',
  ING = 'ING',
  STOP = 'STOP',
  OVER = 'OVER',
}

// 测试数据
export const testData: CompetitionData = {
  overallScore: 85,
  skillLevel: 'Advanced',
  completionRate: 75,
  accuracyRate: 90,
  averageTimePerItem: 45,
  totalTimeSpent: 1800,
  completedItems: 15,
  totalItems: 20,
  validOperations: 45,
  invalidOperations: 5,
  failedOperations: 2,
  operationLog: {
    id: 'test-log-1',
    detailId: 'test-detail-1',
    nodeId: 'node-1',
    actionType: 1,
    result: 1,
    operationTime: '2024-02-28 10:00:00',
    actionLabels: [
      {
        actionType: 1,
        color: 1,
        desc: '操作成功',
      },
    ],
  },
};

// 点位数据类型
export interface PointData {
  objectFlag: string; // 对象标识
  result: number; // 结果
  sort: number; // 排序
  status: number; // 状态
}

// 比赛进度数据类型
export interface CompetitionProgress {
  competitionFrequency: number; // 比赛频次
  competitionDuration: number; // 比赛时长
  requestDuration: number; // 要求时长
  requestFrequency: number; // 要求频次
  recordId: string; // 记录ID
  opNum: number; // 操作数
  opNumOk: number; // 成功操作数
  actionNum: number; // 动作数
  actionNumOk: number; // 成功动作数
}
