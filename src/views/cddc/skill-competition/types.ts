import type { OperationLog } from '/@/api/cddc.res';

// 比赛状态枚举
export enum CompetitionStatusEnum {
  NOT_STARTED = 0,
  IN_PROGRESS = 1,
  COMPLETED = 2,
}

// 比赛数据接口
export interface CompetitionData {
  overallScore: number; // 总分
  validOperations: number; // 有效操作数
  invalidOperations: number; // 无效操作数
  failedOperations: number; // 失败操作数
  targetDuration?: number;
  targetOperations?: number;
  targetQualifiedRate?: number;
  targetAchievementRate?: number;
  operationLog?: {
    id: string;
    result: number;
    operationTime: string;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
  [key: string]: any;
}

// 比赛响应接口
export interface CompetitionResponse {
  id?: string;
  status?: string;
  effectiveNum?: number;
  unqualifiedNum: number;
  qualificationNum: number;
  operationLog?: {
    id: string;
    result: number;
    operationTime: string;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
}

// 点位数据接口
export interface PointData {
  objectFlag: string;
  result: number;
  sort: number;
  status: number;
}
