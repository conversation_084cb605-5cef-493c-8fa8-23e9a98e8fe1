// import { V1ManageCommonActionTypeEnum } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

// 表格配置
export const columns: EnhancedColumn[] = [
  {
    title: '工艺名称',
    dataIndex: 'name',
    width: 200,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入名称',
      },
    },
  },
  // {
  //   title: '关联算法',
  //   dataIndex: 'modelList',
  //   customRender(opt) {
  //     return (opt.value || []).map((n) => n.desc).join(',');
  //   },
  //   width: 150,
  //   search: {
  //     field: 'modelCode',
  //     component: 'ApiSelect',
  //     componentProps: {
  //       placeholder: '请选择关联算法',
  //       optionFilterProp: 'desc',
  //       showSearch: true,
  //       api: V1ManageCommonActionTypeEnum,
  //       fieldNames: {
  //         label: 'desc',
  //         value: 'code',
  //         key: 'code',
  //       },
  //     },
  //   },
  // },
  {
    title: '编辑人',
    dataIndex: 'lastUpdateBy',
    width: 120,
  },
  {
    title: '最近编辑时间',
    dataIndex: 'lastUpdateTime',
    width: 120,
  },
];
