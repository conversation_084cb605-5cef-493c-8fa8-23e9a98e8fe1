<template>
  <div class="report-container">
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="showEditModal(record)">编辑</a-button>
            <a-button type="link" @click="removeRecord(record)">删除</a-button>
          </a-space>
        </template>
      </template>
      <template #toolbar>
        <a-button type="primary" ghost @click="showAddModal">+新增</a-button>
      </template>
    </BasicTablePlus>
    <EditDrawer @register="registerDrawer" @success="onSubmit" />
  </div>
</template>

<script lang="ts" setup>
import { useDrawer } from '@geega-ui-plus/geega-ui';
import { columns } from './data';
import {
  V1ManageProcessAlgorithmIdDelete,
  V1ManageProcessAlgorithmPagePost,
} from '/@/api/cddc.req';
import BasicTablePlus from '@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import EditDrawer from './components/EditDrawer.vue';
import type { V1ManageProcessAlgorithmWithCodeGetResponseResult } from '/@/api/cddc.model';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';

type RecordRowItem = V1ManageProcessAlgorithmWithCodeGetResponseResult;

const { createDeteleConfirm } = useMessage();

const [registerTable, tableActions] = useTable({
  columns: columns,
  api: V1ManageProcessAlgorithmPagePost,
  formConfig: {
    baseColProps: { span: 6 },
  },
});

const [registerDrawer, drawerActions] = useDrawer();

// 查看详情
async function showEditModal(record: RecordRowItem) {
  drawerActions.openDrawer(true, record);
}

async function removeRecord(record: RecordRowItem) {
  createDeteleConfirm({
    content: `请确定是否删除?`,
    onOk: async () => {
      await V1ManageProcessAlgorithmIdDelete({ id: record.id! });

      tableActions.reload();

      message.success('删除成功');
    },
  });
}

async function onSubmit() {
  tableActions.reload();
}

async function showAddModal() {
  drawerActions.openDrawer(true, {});
}
</script>

<style lang="less" scoped>
.report-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
