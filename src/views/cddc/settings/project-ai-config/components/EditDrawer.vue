<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import type { V1ManageProcessAlgorithmWithCodeGetResponseResult } from '/@/api/cddc.model';
import VUploaderValue from '/@/components/Uploader/VUploaderValue.vue';
import {
  V1ManageCommonActionTypeEnum,
  V1ManageCommonProjectTypeEnum,
  V1ManageProcessAlgorithmPost,
  V1ManageProcessAlgorithmPut,
} from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';
import { createFile } from '/@/components/Uploader/utils';
import type { VUploadFile } from '/@/components/Uploader';
import { useAsyncData } from '/@/composables';
import UniqueSelectList from '/@/components/UniqueSelectList.vue';
import { cloneDeep } from 'lodash-es';

const emit = defineEmits(['success']);

type RecordItem = V1ManageProcessAlgorithmWithCodeGetResponseResult;

const state = reactive({
  drawerTitle: '编辑工艺算法配置',
});

const currentData = ref<RecordItem>({
  files: [],
  modelList: [],
});

const actionTypeData = useAsyncData(async () =>
  V1ManageCommonActionTypeEnum().then((res) =>
    res.map((item) => ({ ...item, label: item.desc!, value: item.code! }))
  )
);

actionTypeData.load();

const [registerDrawer, drawerActions] = useDrawerInner(async (props: RecordItem) => {
  props = cloneDeep(props);
  state.drawerTitle = props?.id ? '编辑工艺算法配置' : '新增工艺算法配置';

  currentData.value = props || {};

  const files: VUploadFile[] = [];

  if (props.videoId) {
    const file = createFile({
      name: props.videoUrl!,
      url: props.videoUrl,
      uid: props.videoId,
    });
    files.push(file);
  }

  currentData.value.files = files;

  await fromActions.resetFields();
  await fromActions.setFieldsValue({
    ...currentData.value,
    modelList: currentData.value.modelList?.map((n) => n.code),
  });
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'name',
      label: '工艺名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 32,
        // showCount: true,
      }
    },
    {
      field: 'code',
      label: 'Code',
      required: true,
      component: 'ApiSelect',
      componentProps: {
        optionFilterProp: 'label',
        api: V1ManageCommonProjectTypeEnum,
        fieldNames: {
          label: 'desc',
          value: 'code',
          key: 'code',
        },
      },
      // ApiSelect 默认校验有 BUG，需要 blur 触发
      rules: [
        {
          required: true,
          trigger: 'blur',
        },
      ],
    },
    // {
    //   field: 'modelList',
    //   label: '关联算法配置',
    //   required: true,
    //   component: 'Select',
    //   slot: 'modelList',
    // },
    // {
    //   field: 'judgeType',
    //   label: '判断图形',
    //   required: true,
    //   component: 'ApiSelect',
    //   componentProps: {
    //     optionFilterProp: 'label',
    //     api: V1ManageCommonJudgeTypeEnum,
    //     fieldNames: {
    //       label: 'desc',
    //       value: 'code',
    //       key: 'code',
    //     },
    //   },
    //   // ApiSelect 默认校验有 BUG，需要 blur 触发
    //   rules: [
    //     {
    //       required: true,
    //       trigger: 'blur',
    //     },
    //   ],
    // },
    {
      field: 'files',
      label: '标准视频',
      required: true,
      component: 'Upload',
      slot: 'upload',
      rules: [
        {
          required: true,
          trigger: 'change',
          message: '请上传标准视频！',
        },
      ],
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();

    const id = currentData.value.id;

    const videoId = data.files.at(0)?.response?.uid;

    if (!videoId) {
      message.warn('视频正在上传中！');
      throw new Error('视频正在上传中！');
    }

    const requestParams = {
      ...data,
      modelCodes: data.modelList?.join(','),
      videoId: data.files.at(0)?.response.uid,
    };

    if (id) {
      await V1ManageProcessAlgorithmPut({
        ...requestParams,
        id,
      });
      message.success('修改成功');
    } else {
      await V1ManageProcessAlgorithmPost(requestParams);
      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #modelList="{ model }">
        <div class="modelList">
          <UniqueSelectList :options="actionTypeData.data.value" v-model:value="model.modelList" />
        </div>
      </template>
      <template #upload="{ model }">
        <VUploaderValue
          v-model:value="model.files"
          accept=".mp4"
          :maxCount="1"
          :maxSize="500 * 1024"
        />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
