import type { BasicColumn } from '@geega-ui-plus/geega-ui';
import type { V1ManageSysConfigListGetResponseResult } from '/@/api/cddc.model';

// 表格配置
export const columns: BasicColumn[] = [
  {
    title: '配置项名称',
    dataIndex: 'configName',
    width: 160,
  },
  {
    title: '配置设置',
    dataIndex: 'configValue',
    width: 100,
    format(text, record: V1ManageSysConfigListGetResponseResult, index) {
      const unit = record.configUnit;
      return text ? `${text}${unit}` : '--';
    },
  },
  {
    title: '最近更新时间',
    dataIndex: 'lastUpdateTime',
    width: 100,
  },
  {
    title: '最近操作人',
    dataIndex: 'lastUpdateBy',
    width: 100,
    format(text, record: V1ManageSysConfigListGetResponseResult, index) {
      return text || '--';
    },
  },
];
