<template>
  <div class="report-container">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <a-button type="link" @click="showEditModal(record)">编辑</a-button>
      </template>
    </BasicTable>
    <BasicModal title="编辑" @register="registerModal" @ok="onSubmit">
      <BasicForm @register="registerForm"> </BasicForm>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import {
  BasicForm,
  BasicModal,
  BasicTable,
  useForm,
  useModal,
  useTable,
} from '@geega-ui-plus/geega-ui';
import { columns } from './data';
import { V1ManageSysConfigPut, V1OpenApiSysConfigList } from '/@/api/cddc.req';
import { nextTick } from 'vue';

const [registerTable, tableActions] = useTable({
  columns: columns,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
  showIndexColumn: false,
  api: async () => {
    const resp = await V1OpenApiSysConfigList();

    return resp;
  },
});

const [registerForm, formActions] = useForm({
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'id',
      label: '配置项名称',
      component: 'Input',
      show: false,
    },
    {
      field: 'configName',
      label: '配置项名称',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'configValue',
      label: '配置设置',
      component: 'InputNumber',
      required: true,
      componentProps: {
        style: 'width:100%',
        min: 0,
        max: 99,
        precision: 0,
      },
    },
  ],
});

const [registerModal, modalActions] = useModal();

// 查看详情
const showEditModal = async (record: any) => {
  modalActions.openModal();
  await nextTick();
  formActions.setFieldsValue(record);
};

async function onSubmit() {
  modalActions.setModalProps({
    okButtonProps: {
      loading: true,
    },
  });

  try {
    const data = await formActions.validate();

    data.configValue = Math.floor(data.configValue);

    await V1ManageSysConfigPut(data);
    modalActions.setModalProps({
      visible: false,
    });

    tableActions.reload();
  } catch (error) {
    console.error(error);
  }

  modalActions.setModalProps({
    okButtonProps: {
      loading: false,
    },
  });
}
</script>

<style lang="less" scoped>
.report-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
