<script lang="ts" setup>
import { V1ManageTranProjectBindProjectLocationId } from '/@/api/cddc.req';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { useAsyncData } from '/@/composables';
import { watchImmediate } from '@vueuse/core';

const props = defineProps<{
  stationId: string;
}>();

const stationProjectInfo = useAsyncData(V1ManageTranProjectBindProjectLocationId, []);

watchImmediate(
  () => props.stationId,
  () => {
    stationProjectInfo.load(
      {
        projectType: 1,
      },
      {
        locationId: props.stationId,
      }
    );
  }
);
</script>

<template>
  <div class="station-info">
    <div class="block">
      <div class="block-title">训练项目</div>
      <div class="lists" v-if="stationProjectInfo.data.value.length">
        <div class="list-item" v-for="item in stationProjectInfo.data.value">
          {{ item.name }}
        </div>
      </div>
      <customizeRenderEmpty v-else />
    </div>
    <!--  -->
  </div>
</template>

<style lang="less" scoped>
.block-title {
  font-size: large;
  font-weight: bold;
  margin-bottom: 12px;
}

.lists {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.list-item {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  display: block;
}
</style>
