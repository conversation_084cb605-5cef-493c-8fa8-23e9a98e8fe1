<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import StatisticsBox from '/@/components/ClientPort/StatisticsBox.vue';
import ValidValue from '/@/components/ClientPort/ValidValue.vue';

const props = defineProps<{
  data: Record<string, any>;
}>();

const percentageData = computed(() => {
  const data = props.data || {};
  return {
    op: Number(data.totalActionRate || 0),
    all: Number(data.totalQualificationRate || 0),
  };
});

const statisticsData = computed(() => {
  const data = props.data || {};

  return [
    {
      value: Number(data.actionRate) || 0,
      color: 'green',
      label: '实际动作达标率',
      unit: '%',
      target: Number(data.requestActionRate) || 0,
    },
    {
      value: data.standardNum || 0,
      color: 'green',
      label: '动作达标次数',
      unit: '次',
    },
    {
      value: Number(data.nonStandardNum) || 0,
      color: 'red',
      label: '动作不达标次数',
      unit: '次',
    },
    {
      value: Number(data.qualificationRate) || 0,
      color: 'green',
      label: '实际作业合格率',
      unit: '%',
      target: Number(data.requestQualificationRate) || 0,
    },
    {
      value: data.qualificationNum || 0,
      color: 'green',
      label: '作业合格次数',
      unit: '次',
    },
    {
      value: Number(data.unqualifiedNum) || 0,
      color: 'red',
      label: '作业不合格次数',
      unit: '次',
    },
  ];
});

const data = computed(() => props.data || {});
</script>

<template>
  <div class="statistics-report">
    <ScreenTitle> 训练总计 </ScreenTitle>

    <ScreenBox>
      <div class="box-content">
        <!-- 顶部百分比展示 -->
        <div class="flex items-center top-percentage">
          <div class="flex-1 w-0 percentage-item">
            <div class="percentage-up">
              <div class="value">{{ percentageData.op }}</div>
              <div class="label">动作达标率</div>
            </div>
            <div class="percentage-bar">
              <div class="percentage" :style="{ width: `${percentageData.op}%` }"></div>
            </div>
          </div>
          <div class="flex-1 w-0 percentage-item">
            <div class="percentage-up">
              <div class="value">{{ percentageData.all }}</div>
              <div class="label">作业合格率</div>
            </div>
            <div class="percentage-bar">
              <div class="percentage" :style="{ width: `${percentageData.all}%` }"></div>
            </div>
          </div>
        </div>

        <!-- 当前训练次数 -->
        <div class="train-count">
          当前训练统计:<span class="count">{{ data.totalTrainFrequency }}</span
          >次
        </div>

        <!-- 统计数据网格 -->
        <div class="statistics-grid">
          <StatisticsBox v-for="item in statisticsData" :key="item.label">
            <div class="stat-value">{{ item.label }}</div>
            <ValidValue :value="item.value" :color="item.color" :unit="item.unit">
              <template #unit v-if="item.target"> {{ item.unit }} ({{ item.target }}%) </template>
            </ValidValue>
          </StatisticsBox>
        </div>
      </div>
      <!-- box -->
    </ScreenBox>
  </div>
</template>

<style lang="less" scoped>
.statistics-report {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;
  width: 33.33%;
  min-width: 0;
}

.box-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-height: calc(100vh - 200px);
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;

  &::-webkit-scrollbar {
    width: 0.4vw;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 0.2vw;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.top-percentage {
  display: flex;
  justify-content: space-between;
  gap: 1vw;

  .percentage-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    background: var(--ignore-dark-bg);
    width: 100%;

    .percentage-up {
      padding-left: 1vw;
      padding-bottom: 0.8vh;
      --ignore-dark-image: url('@/assets/svg/screen/bg-rate.svg');
      background: var(--ignore-dark-image) no-repeat right top / auto 100%;
      width: 100%;
    }

    .value {
      font-size: 1.26vw;
      font-style: italic;
      font-weight: 700;
      margin-right: 0.3vw;
      color: #29e8ab;

      &.red {
        color: #f53f3f;
      }

      &::after {
        content: '%';
        margin-left: 0.3vw;
        color: rgba(255, 255, 255, 0.6);
        font-size: 1.4vw;
      }
    }

    .label {
      font-size: 0.84vw;
      color: rgba(255, 255, 255, 0.64);
      font-weight: 400;
      display: inline-block;
    }

    .percentage-bar {
      width: 100%;
      height: 0.5vw;
      --ignore-dark-color: #252a2f;
      background-color: var(--ignore-dark-color);
      overflow: hidden;
      position: relative;

      .percentage {
        height: 100%;
        --ignore-dark-image: url('@/assets/svg/screen/bg-percent.png');
        background: var(--ignore-dark-image) repeat left top / auto 100%;
        width: 50%;
      }
    }
  }
}

.train-count {
  font-size: 0.98vw;
  margin-top: 1.5vh;

  .count {
    font-weight: 700;
    color: #29e8ab;
  }
}

.statistics-grid {
  display: grid;
  margin-top: 1vh;
  grid-template-columns: repeat(3, 1fr);
  gap: 1vw;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .stat-value {
    font-size: 0.98vw;
    font-weight: 400;

    &.text-red {
      color: #f53f3f;
    }
  }

  .stat-label {
    font-size: 1.1vw;
    font-weight: 400;
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .statistics-report {
    gap: 0.8vw;
  }

  .box-content {
    &::-webkit-scrollbar {
      width: 0.3vw;
    }
  }

  .top-percentage {
    gap: 0.8vw;

    .percentage-item {
      .percentage-up {
        padding-left: 0.8vw;
        padding-bottom: 0.6vh;
      }

      .value {
        font-size: 2vw;
        margin-right: 0.2vw;

        &::after {
          margin-left: 0.2vw;
          font-size: 1.2vw;
        }
      }

      .label {
        font-size: 0.84vw;
      }

      .percentage-bar {
        height: 0.4vw;
      }
    }
  }

  .train-count {
    font-size: 1vw;
    margin-top: 1.2vh;
  }

  .statistics-grid {
    margin-top: 0.8vh;
    gap: 0.8vw;

    .stat-value {
      font-size: 1.3vw;
    }

    .stat-label {
      font-size: 1vw;
    }
  }
}

:deep(.statistics-box) {
  padding: 4.5px 0.9vw;
  margin-top: 0.8vh;
  height: 5.42vh;
  background-size: auto 100%;

  .three-dot {
    @size: 0.4vw;
    width: @size;
    height: @size;

    &::before,
    &::after {
      width: @size;
      height: @size;
    }
  }
}
:deep(.valid-value) {
  font-size: 1.26vw;
  line-height: initial;
  align-items: center;
  .unit {
    font-size: 0.84vw;
    bottom: -0.45vh;
  }
}
// pad尺寸适配
@media screen and (max-width: 1024px) {
  :deep(.statistics-box) {
    padding: 1vh 1.2vw;
    margin-top: 0.6vh;

    .three-dot {
      @size: 0.3vw;
      width: @size;
      height: @size;

      &::before,
      &::after {
        width: @size;
        height: @size;
      }
    }
  }
}
</style>
