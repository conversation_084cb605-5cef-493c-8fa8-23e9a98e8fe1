// ProgressReport组件在训练页面的样式适配
.training-container {
  :deep(.screen-box) {
    padding: 0.74vw;
  }
  :deep(.progress-report) {
    gap: 0.74vw;
    width: 16.67%; // 1份
    min-width: 0;

    .progress-items {
      gap: 1vw;
      .gap-5 {
        gap: 0.42vw;
      }
    }
    .progress-data-item {
      padding: 0 0.74vw;
      &:nth-child(1n) {
        background-size: 100% 100%, auto 30%;
      }
    }
    .screen-box-1 {
      .label {
        font-size: 0.84vw;
      }
      .item-divider {
        width: 0.84vw;
      }
      .divider-box {
        height: 0.19vw;
        margin: 0.74vw 0;
      }
    }
    .valid-value {
      font-size: 1.26vw;
      line-height: inherit;
      align-items: start;
      .unit {
        font-size: 0.74vw;
        margin-left: 0.1vw;
      }
    }

    .progress-row {
      gap: 0.8vw;
      padding: 0.37vw;

      .progress-label {
        font-size: max(0.6vw, 0.81vw); // 1.1vw * 0.74
      }

      :deep(.progress-bar) {
        margin-top: 0.74vw;
        .value {
          font-size: max(0.6vw, 0.74vw); // 1vw * 0.74
        }
        .bar {
          height: 0.8vh;
        }
      }
    }
  }
  :deep(.progress-bar) {
    margin-top: 0.74vw;
  }
  :deep(.progress-thumb) {
    font-size: 1vw;
    &::before {
      width: 1vw;
      height: 1.1vw;
      top: initial;
      bottom: 0.31vw;
      left: -0.6vw;
    }
    .progress-thumb-text {
      top: initial;
      bottom: 0.05vw;
      left: calc(0.5vw + 0.2vw);
      &.reverse-text {
        left: unset;
        right: calc(0.6vw + 0.2vw);
      }
    }
  }
  :deep(.progress-text) {
    font-size: 0.84vw;
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .training-container {
    :deep(.progress-report) {
      gap: 0.6vw;

      .progress-items {
        gap: 0.8vw;
      }

      .progress-row {
        gap: 0.6vw;
        padding: 0.37vw;

        .progress-label {
          font-size: max(0.6vw, 0.74vw); // 1vw * 0.74
        }

        :deep(.progress-bar) {
          margin-top: 0.74vw;
          .value {
            font-size: max(0.6vw, 0.67vw); // 0.9vw * 0.74
          }
          .bar {
            height: 0.7vh;
          }
        }
      }
    }
  }
}
