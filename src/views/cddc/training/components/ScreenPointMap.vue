<script setup lang="ts">
import { computed } from 'vue';
import type { PointData } from '../data';

const props = defineProps<{
  data: Array<PointData>;
}>();

const gridSize = 8;
const spacing = 8;
const iconSize = 24;

const positions = computed(() => {
  const defaultPositions = Array.from({ length: gridSize * gridSize }, (_, index) => ({
    id: index,
    status: 'default',
  }));

  props.data?.forEach((item) => {
    const positionIndex = Number(item.objectFlag) || 0;
    if (positionIndex >= 0 && positionIndex < gridSize * gridSize) {
      defaultPositions[positionIndex].status = getStatusClass(item.result);
    }
  });

  return defaultPositions;
});

const getStatusClass = (result: number) => {
  switch (result) {
    case 0:
      return 'red';
    case 1:
      return 'green';
    case 2:
      return 'yellow';
    default:
      return 'default';
  }
};
</script>

<template>
  <div
    class="relative grid"
    :style="{
      gridTemplateColumns: `repeat(${gridSize}, 1fr)`,
      gap: `${spacing}px`,
      width: `calc(${gridSize} * ${iconSize}px + ${gridSize - 1} * ${spacing}px)`,
    }"
  >
    <div
      v-for="position in positions"
      :key="position.id"
      class="flex items-center justify-center point-bg"
      :class="position.status"
    ></div>
  </div>
</template>

<style lang="less" scoped>
.point-bg {
  --ignore-dark-bg: #1c2536;
  background: var(--ignore-dark-bg);
  &::after {
    content: '';
    width: 33px;
    height: 29px;
    background: url('@/assets/images/screen/train-point.png') no-repeat center / 100% 100%;
    animation: none;
  }
  &.yellow {
    &::after {
      background-image: url('@/assets/images/screen/train-point-y.png');
      animation: blink 1s ease-in-out 2;
    }
  }
  &.red {
    &::after {
      background-image: url('@/assets/images/screen/train-point-r.png');
      animation: blink 1s ease-in-out 2;
    }
  }
  &.green {
    &::after {
      background-image: url('@/assets/images/screen/train-point-g.png');
    }
  }

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
  }
}
</style>
