import type { Ref } from 'vue';

export interface WebSocketClient {
  send: (data: any) => void;
  close: () => void;
}

export interface WebSocketInstance {
  wsClient: Ref<WebSocketClient | null>;
  connect: () => void;
  disconnect: () => void;
  send: (data: any) => void;
}

export interface TrainingResponse {
  id?: string;
  status?: string;
  actionNum?: number;
  actionNumOk?: number;
  opNum?: number;
  opNumOk?: number;
  standardNum?: number;
  nonStandardNum?: number;
  qualificationNum?: number;
  unqualifiedNum?: number;
  operationLog?: {
    result: number;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
}

export interface TrainingData {
  totalActionRate: number;
  totalQualificationRate: number;
  totalStandardNum: number;
  totalActionNum: number;
  totalQualifiedNum: number;
  totalQualificationNum: number;
  totalTrainFrequency: number;
  opNum: number;
  trainFrequency: number;
  operationLog?: {
    result: number;
    actionLabels?: Array<{
      actionType: number;
      color: number;
      desc: string;
    }>;
  };
  [key: string]: any;
}

export interface PointDataItem {
  objectFlag: string;
  result: number;
  sort: number;
  status: number;
}
