<template>
  <div class="training-container">
    <!-- 顶部信息 -->
    <div class="header">
      <div class="training-select">
        <a-button class="float-back-btn" type="link" @click="goBack">
          <SvgIcon name="g-ic-line-left" />
          <span class="back-text">返回</span>
        </a-button>
        <ProjectSelector title="训练项目" @change="handleProjectChange" @pList="handleProjectList">
          <!-- 右侧训练设置控制插槽 -->
          <template #rightControls>
            <div class="training-controls">
              <!-- 声音控制 -->
              <div class="control-item">
                <SvgIcon :name="soundEnabled ? 'g-icon-sound' : 'g-close-sound'" />
                <a-switch v-model:checked="soundEnabled" @change="handleSoundToggle" />
                <a-select
                  v-model:value="soundType"
                  style="width: 80px"
                  :disabled="!soundEnabled"
                  @change="handleSoundTypeChange"
                >
                  <a-select-option value="rational">理性</a-select-option>
                  <a-select-option value="lively">活泼</a-select-option>
                </a-select>
              </div>
              <!-- 分隔线 -->
              <div class="control-divider"></div>
              <!-- 模式控制 -->
              <div class="control-item">
                <SvgIcon name="g-icon-filesync" />
                <a-select
                  v-model:value="displayMode"
                  style="width: 80px"
                  @change="handleModeChange"
                >
                  <a-select-option value="simple">简约</a-select-option>
                  <a-select-option value="professional">专业</a-select-option>
                </a-select>
              </div>
              <!-- 分隔线 -->
              <div class="control-divider"></div>
            </div>
          </template>
        </ProjectSelector>
        <a-button type="primary" @click="pushTest">训练数据测试</a-button>
      </div>
    </div>
    <!-- 摄像头画面 -->
    <div class="camera-views">
      <CameraView
        :show-status-tag="true"
        :status-result="realTimeData?.operationLog?.result"
        @connected="handleCameraConnected"
        @disconnected="handleCameraDisconnected"
        @error="handleCameraError"
      />
      <div class="line"></div>
      <TeachingView :point-data="pointData" :diagram-type="projectDiagramType" />
    </div>
    <!-- 训练实时数据 -->
    <div class="flex gap-5 training-data">
      <div class="section-grid">
        <OperationLog
          ref="operationLogRef"
          class="flex-1 w-0"
          :detailId="trainId"
          :data="realTimeData"
          style="flex: 3"
        />
        <LiveData class="flex-1 w-0" :data="realTimeData" style="flex: 2" />
        <ProgressReport class="w-81" :data="trainProgress" style="flex: 1" />
      </div>
    </div>
    <!-- 底部信息 -->
    <div :class="['footer', isStart ? 'training' : '']">
      <div class="action-buttons">
        <a-button
          type="primary"
          @click="debounceStartTraining"
          v-if="!isStart"
          :disabled="!isPlayerConnected"
          :loading="loading"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          {{ isPlayerConnected ? '开始训练' : '等待摄像头连接...' }}
        </a-button>
        <template v-if="isStart">
          <a-button type="default" ghost @click="viewReport">
            <template #icon>
              <EyeOutlined />
            </template>
            报告查看</a-button
          >
          <a-button
            type="primary"
            @click="pauseTraining"
            v-if="trainStatus === TrainStatusEnum.ING"
          >
            <template #icon>
              <PauseCircleOutlined />
            </template>
            暂停训练</a-button
          >
          <a-button
            type="primary"
            @click="debounceContinueTraining"
            v-if="trainStatus === TrainStatusEnum.STOP"
          >
            <template #icon>
              <PlayCircleOutlined />
            </template>
            继续训练</a-button
          >
          <a-button
            type="primary"
            danger
            ghost
            @click="debounceExitTraining"
            :disabled="!trainStatus"
          >
            <template #icon>
              <PoweroffOutlined />
            </template>
            退出训练</a-button
          >
        </template>
      </div>
    </div>
    <!-- 暂停训练弹框提醒 -->
    <BasicModal
      @register="register"
      title="暂停训练"
      :minHeight="100"
      :height="100"
      centered
      :showCancelBtn="false"
      :ok-text="'继续训练'"
      @ok="debounceContinueTraining"
      :maskClosable="false"
      :closable="false"
    >
      <div class="dialog-content">
        <p class="tips">暂停训练中</p>
      </div>
      <template #appendFooter>
        <a-button key="submit" type="primary" danger ghost @click="debounceExitTraining"
          >退出训练</a-button
        >
      </template>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  V1LocationStudyOverPost,
  V1LocationStudyStopPost,
  V1LocationStudyInitPost,
  V1LocationStudyStartPost,
  V1LocationStudyTrainRecordProjectIdUserId,
  V1LocationStudyVideoStopPost,
  V1LocationStudyVideoCreatePost,
} from '/@/api/cddc.req';
import { createLocalStorage } from '/@/utils/cache';
import { useGlobSetting } from '/@/hooks/setting';
import { useWebSocketHandler } from '/@/composables/useWebSocketHandler';
import ProjectSelector from '/@/components/MyProject/ProjectSelector.vue';
import LiveData from './components/LiveData.vue';
import OperationLog from './components/OperationLog.vue';
import ProgressReport from '/@/components/ClientPort/ProgressReport.vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  PoweroffOutlined,
  EyeOutlined,
} from '@geega-ui-plus/icons-vue';
import { BasicModal, SvgIcon, useModal } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import TeachingView from '/@/components/TeachingView/index.vue';
import type { DiagramType } from '/@/components/TeachingView/index.vue';
import {  testData, TrainStatusEnum } from './data';
import type { PointData, RealTimeData, TrainProgress } from './data';
import type { TrainingResponse, TrainingData } from './types';
import { useDebounce } from '/@/hooks/core/useDebounce';
import CameraView from '/@/components/CameraView/index.vue';
import { useErrorSound, type ErrorType, type SoundType } from '/@/composables/useErrorSound';

// 项目信息接口定义
interface ProjectInfo {
  id: string;
  name: string;
  recordId: string;
  status: string;
  countAlgorithm?: string;
}

// State
const router = useRouter();
const operationLogRef = ref<any>(null);
const pointData = ref<Array<PointData>>([]);
const loading = ref(false);
const isPlayerConnected = ref(false);
const trainStatus = ref<TrainStatusEnum>(TrainStatusEnum.STOP);
const isStart = ref(false);
const realTimeData = ref<RealTimeData>({
  totalActionRate: 0,
  totalQualificationRate: 0,
  totalStandardNum: 0,
  totalActionNum: 0,
  totalQualifiedNum: 0,
  totalQualificationNum: 0,
  totalTrainFrequency: 0,
  opNum: 0,
  trainFrequency: 0,
});
const trainProgress = ref<TrainProgress>({
  trainFrequency: 0,
  trainDuration: 0,
  requestDuration: 0,
  requestFrequency: 0,
  recordId: '',
  opNum: 0,
  opNumOk: 0,
  actionNum: 0,
  actionNumOk: 0,
});
const projectList = ref<ProjectInfo[]>([]);
const trainId = ref<string>('');
const timerInterval = ref<NodeJS.Timer | null>(null);
const startTime = ref<number>(0);
const pausedDuration = ref<number>(0);
const projectDiagramType = ref<DiagramType>();

// 训练设置相关状态
const soundEnabled = ref<boolean>(true); // 声音开关
const soundType = ref<string>('rational'); // rational=理性, lively=活泼
const displayMode = ref<string>('simple'); // simple=简约, professional=专业

// 声音控制
const { playErrorSound, switchSoundType } = useErrorSound();

const [register, { openModal }] = useModal();

const handleProjectList = (list: ProjectInfo[]) => {
  projectList.value = list;
};

// 声音开关处理
const handleSoundToggle = (checked: boolean) => {
  soundEnabled.value = checked;
  const ls = createLocalStorage();
  ls.set('soundEnabled', checked);
  message.success(checked ? '声音已开启' : '声音已关闭');
};

// 声音类型切换处理
const handleSoundTypeChange = (type: string) => {
  soundType.value = type;
  const ls = createLocalStorage();
  ls.set('soundType', type);

  // 切换声音类型，重新初始化音频元素
  switchSoundType(type as SoundType);

  const typeNames = {
    rational: '理性声音',
    lively: '活泼声音',
  };
  message.success(`已切换到${typeNames[type as keyof typeof typeNames]}`);

  // 播放示例音频让用户听到声音风格的区别
  if (soundEnabled.value) {
    // 使用 wear-gloves (手套识别) 作为示例音频
    playErrorSound([{ actionType: 8 }], type as SoundType);
  }
};

// 模式切换处理
const handleModeChange = (mode: string) => {
  displayMode.value = mode;
  const ls = createLocalStorage();
  ls.set('displayMode', mode);

  const modeNames = {
    simple: '简约版本',
    professional: '专业版本',
  };
  message.success(`已切换到${modeNames[mode as keyof typeof modeNames]}`);
};

// 初始化设置
const initSettings = () => {
  const ls = createLocalStorage();
  soundEnabled.value = ls.get('soundEnabled') ?? true;
  soundType.value = ls.get('soundType') ?? 'rational';
  displayMode.value = ls.get('displayMode') ?? 'simple';

  // 根据恢复的声音类型初始化音频元素
  switchSoundType(soundType.value as SoundType);
};

const pushTest = () => {
  realTimeData.value = testData;
};

const handleCameraConnected = () => {
  isPlayerConnected.value = true;
};

const handleCameraDisconnected = () => {
  isPlayerConnected.value = false;
};

const handleCameraError = () => {
  isPlayerConnected.value = false;
};

const resetOperationLog = () => {
  realTimeData.value = {
    totalActionRate: 0,
    totalQualificationRate: 0,
    totalStandardNum: 0,
    totalActionNum: 0,
    totalQualifiedNum: 0,
    totalQualificationNum: 0,
    totalTrainFrequency: 0,
    opNum: 0,
    trainFrequency: 0,
  };
  trainProgress.value = {
    trainFrequency: 0,
    trainDuration: 0,
    requestDuration: 0,
    requestFrequency: 0,
    recordId: '',
    opNum: 0,
    opNumOk: 0,
    actionNum: 0,
    actionNumOk: 0,
  };
};

const createVideo = async (currProjectId: string) => {
  const ls = createLocalStorage();
  const userId = ls.get('userId');
  await V1LocationStudyVideoCreatePost({
    projectId: currProjectId,
    userId: userId,
    locationId: ls.get('locationId'),
  });
};

const handleProjectChange = async (project: ProjectInfo) => {
  if (!project) return;

  try {
    loading.value = true;
    const ls = createLocalStorage();
    ls.set('currProjectId', project.id);

    // 根据项目类型设置对应的图示类型
    projectDiagramType.value = (project.countAlgorithm as DiagramType) || 'tighten';

    if (trainStatus.value === TrainStatusEnum.ING) {
      const locationId = ls.get('locationId');
      await V1LocationStudyOverPost({ locationId, detailId: trainId.value });
      trainStatus.value = TrainStatusEnum.STOP;
    }

    cleanupWebSocket();
    isStart.value = false;
    resetOperationLog();

    isPlayerConnected.value = false;
    await createVideo(project.id);
    await queryTrainData();
    operationLogRef.value?.clearLocalLog();
    cleanupTimer();
    pointData.value = [];
  } catch (error) {
    console.error('切换项目失败:', error);
    message.error('切换项目失败，请重试');
  } finally {
    loading.value = false;
  }
};

const { socketUrl } = useGlobSetting();

const initWebSocketConnection = () => {
  if (!trainId.value || !socketUrl) {
    console.warn('缺少训练ID或WebSocket URL，无法建立WebSocket连接');
    return;
  }

  initWebSocket({
    socketUrl,
    detailId: trainId.value,
  });
};

const initSign = async () => {
  const ls = createLocalStorage();
  const userId = ls.get('userId');
  const res = await V1LocationStudyInitPost({
    projectId: ls.get('currProjectId'),
    userId: userId,
  });

  if (res) {
    trainId.value = res.id || '';
    trainStatus.value = res.status
      ? TrainStatusEnum[res.status as keyof typeof TrainStatusEnum]
      : TrainStatusEnum.INIT;

    const initialDuration = Number(res.trainDuration) || 0;

    trainProgress.value = {
      trainFrequency: Number(res.opNum) || 0,
      trainDuration: initialDuration,
      requestDuration: Number(res.requestDuration) || 0,
      requestFrequency: Number(res.requestFrequency) || 0,
      recordId: res.recordId || '',
      opNum: Number(res.opNum) || 0,
      opNumOk: Number(res.opNumOk) || 0,
      actionNum: Number(res.actionNum) || 0,
      actionNumOk: Number(res.actionNumOk) || 0,
    };

    await initWebSocketConnection();
  }
};

const startTimer = () => {
  if (timerInterval.value) return;

  const initialSeconds = trainProgress.value.trainDuration || 0;

  startTime.value = Date.now() - initialSeconds * 1000;

  timerInterval.value = setInterval(() => {
    const currentTime = Date.now();
    const totalDurationSeconds = Math.max(
      0,
      Math.floor((currentTime - startTime.value - pausedDuration.value) / 1000)
    );

    trainProgress.value = {
      ...trainProgress.value,
      trainDuration: totalDurationSeconds,
    };
  }, 1000);
};

const pauseTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
    const currentDuration = trainProgress.value.trainDuration || 0;
    pausedDuration.value = Math.max(0, Date.now() - startTime.value - currentDuration * 1000);
  }
};

const cleanupTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
  startTime.value = 0;
  pausedDuration.value = 0;
};

const startTraining = async () => {
  if (!isPlayerConnected.value) {
    message.warning('请等待摄像头连接成功后再开始训练');
    return;
  }

  try {
    loading.value = true;
    await initSign();
    const ls = createLocalStorage();
    const locationId = ls.get('locationId');
    await V1LocationStudyStartPost({ detailId: trainId.value, locationId });
    trainStatus.value = TrainStatusEnum.ING;
    isStart.value = true;
    openModal(false);
    startTimer();
  } catch (error) {
    console.error('开始训练失败:', error);
    message.error('开始训练失败，请重试');
  } finally {
    loading.value = false;
  }
};

const [debounceStartTraining] = useDebounce(startTraining, 500);

const continueTraining = async () => {
  await initWebSocketConnection();
  const ls = createLocalStorage();
  const locationId = ls.get('locationId');
  await V1LocationStudyStartPost({ detailId: trainId.value, locationId });
  trainStatus.value = TrainStatusEnum.ING;
  openModal(false);
  startTimer();
};

const [debounceContinueTraining] = useDebounce(continueTraining, 500);

const viewReport = async () => {
  const ls = createLocalStorage();
  await V1LocationStudyOverPost({ locationId: ls.get('locationId'), detailId: trainId.value });
  isStart.value = false;
  await cleanupWebSocket();
  router.push({
    path: '/cddc/report',
    query: { recordId: trainProgress.value?.recordId, origin: 'train' },
  });
};

const pauseTraining = async () => {
  openModal();
  const ls = createLocalStorage();
  const locationId = ls.get('locationId');
  await V1LocationStudyStopPost({ detailId: trainId.value, locationId });
  trainStatus.value = TrainStatusEnum.STOP;
  wsInstance.value?.disconnect();
  pauseTimer();
};

const exitTraining = async () => {
  const ls = createLocalStorage();
  const locationId = ls.get('locationId');
  await V1LocationStudyOverPost({ locationId, detailId: trainId.value });
  isStart.value = false;
  await cleanupWebSocket();
  cleanupTimer();
  router.push('/cddc/home');
};

const [debounceExitTraining] = useDebounce(exitTraining, 500);

const queryTrainData = async () => {
  const ls = createLocalStorage();
  const res = await V1LocationStudyTrainRecordProjectIdUserId({
    projectId: ls.get('currProjectId'),
    userId: ls.get('userId'),
  });

  if (res) {
    const actionNumOk = Number(res.actionNumOk || 0);
    const actionNum = Number(res.actionNum || 0);
    const opNumOk = Number(res.opNumOk || 0);
    const opNum = Number(res.opNum || 0);

    const actionRate = actionNum > 0 ? Math.floor((actionNumOk / actionNum) * 100) : 0;
    const qualificationRate = opNum > 0 ? Math.floor((opNumOk / opNum) * 100) : 0;

    realTimeData.value = {
      ...realTimeData.value,
      opNum,
      trainFrequency: opNum,
      totalStandardNum: actionNumOk,
      totalActionNum: actionNum,
      totalQualifiedNum: opNumOk,
      totalQualificationNum: opNum,
      totalActionRate: actionRate,
      totalQualificationRate: qualificationRate,
      totalTrainFrequency: 0,
    };

    trainProgress.value = {
      ...trainProgress.value,
      trainFrequency: opNum,
      recordId: res.id || '',
      opNum,
      opNumOk,
      actionNum,
      actionNumOk,
    };
  }
};

const handleTrainingDataUpdate = (data: TrainingResponse) => {
  const standardNum = Number(data.standardNum || 0);
  const nonStandardNum = Number(data.nonStandardNum || 0);
  const qualificationNum = Number(data.qualificationNum || 0);
  const unqualifiedNum = Number(data.unqualifiedNum || 0);

  const newTotalStandardNum = realTimeData.value.totalStandardNum + standardNum;
  const newTotalActionNum = realTimeData.value.totalActionNum + standardNum + nonStandardNum;
  const newTotalQualifiedNum = realTimeData.value.totalQualifiedNum + qualificationNum;
  const newTotalQualificationNum =
    realTimeData.value.totalQualificationNum + qualificationNum + unqualifiedNum;

  const totalActionRate =
    newTotalActionNum > 0 ? Math.floor((newTotalStandardNum / newTotalActionNum) * 100) : 0;
  const totalQualificationRate =
    newTotalQualificationNum > 0
      ? Math.floor((newTotalQualifiedNum / newTotalQualificationNum) * 100)
      : 0;

  const updatedData: TrainingData = {
    ...realTimeData.value,
    ...data,
    totalActionRate,
    totalQualificationRate,
    totalTrainFrequency: qualificationNum + unqualifiedNum || 0,
  };

  realTimeData.value = updatedData;

  trainProgress.value = {
    ...trainProgress.value,
    trainFrequency: trainProgress.value.opNum + (updatedData.totalTrainFrequency || 0),
  };

  // 根据声音设置播放错误提示音
  if (soundEnabled.value && data.operationLog?.actionLabels?.length) {
    // 根据声音类型选择不同的音频文件
    // soundType.value: 'rational'=理性, 'lively'=活泼
    playErrorSound(
      data.operationLog.actionLabels as { actionType: ErrorType }[],
      soundType.value as SoundType
    );
  }
};

const { wsInstance, initWebSocket, cleanupWebSocket } = useWebSocketHandler({
  type: 'training' as const,
  onDataUpdate: handleTrainingDataUpdate,
  onExit: debounceExitTraining,
  onPointUpdate: (points) => {
    pointData.value = points;
  },
  messages: {
    exitConfirm: true,
    exitTitle: '确认退出',
    exitContent: '训练正在进行中，确定要退出吗？',
    overMessage: '检测到人员已离开作业，自动结束本次训练',
  },
});

const goBack = () => {
  router.back();
};

const closeVideo = async () => {
  const ls = createLocalStorage();
  await V1LocationStudyVideoStopPost({
    projectId: ls.get('currProjectId'),
    userId: ls.get('userId'),
    locationId: ls.get('locationId'),
  });
};

// 组件挂载时初始化设置
onMounted(() => {
  initSettings();
});

onUnmounted(async () => {
  if (isStart.value) {
    await debounceExitTraining();
  }
  cleanupTimer();
  await closeVideo();
});
</script>

<style lang="less" scoped>
@import './components/style/progress-report.less';

.training-container {
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  color: #ffffff;
  background: #040405 url('@/assets/images/screen/page-bg.png') no-repeat center / 100% 100%;
  padding: 1.5vh 2vw;

  .header {
    margin-bottom: 1.5vh;
    flex-shrink: 0;

    .training-select {
      margin-bottom: 1.5vh;
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;

      .float-back-btn {
        width: auto;
        color: #fff;
        height: auto;
        font-size: 16px;
        .back-text {
          margin-left: 4px;
          &::after {
            content: '';
            display: inline-block;
            width: 2px;
            height: 12px;
            --ignore-dark-bg: rgba(255, 255, 255, 0.2);
            background: var(--ignore-dark-bg);
            margin-left: 12px;
          }
        }
      }
    }
  }

  // 训练控制组件样式（在ProjectSelector右侧插槽中）
  :deep(.training-controls) {
    display: flex;
    align-items: center;
    gap: 1.1vw;

    .control-item {
      display: flex;
      align-items: center;
      gap: 0.413vw;

      .control-icon {
        color: #fff;
        font-size: 24px;
        flex-shrink: 0;
      }

      .ant-switch {
        background-color: rgba(255, 255, 255, 0.2);

        &.ant-switch-checked {
          background-color: #52c41a;
        }
      }

      .ant-select {
        min-width: 70px;

        .ant-select-selector {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.3);
          color: #fff;
          font-size: 12px;
          height: 28px;

          .ant-select-selection-item {
            color: #fff;
            line-height: 26px;
          }
        }

        .ant-select-arrow {
          color: #fff;
        }

        &.ant-select-disabled {
          .ant-select-selector {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.3);

            .ant-select-selection-item {
              color: rgba(255, 255, 255, 0.3);
            }
          }

          .ant-select-arrow {
            color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    .control-divider {
      width: 1px;
      height: 1.034vw;
      --ignore-dark-bg: rgba(255, 255, 255, 0.5);
      background: var(--ignore-dark-bg);
    }
  }

  .camera-views {
    display: flex;
    gap: 0.6vw;
    margin-bottom: 1.5vh;
    flex: 1;
    min-height: 0;
    background: url('@/assets/images/screen/train-camera-bg.png') no-repeat center / 100% 100%;
    padding: 1vh 1.5vw 3vh 1.4vw;
    position: relative;

    .line {
      width: 0.2vw;
      --ignore-dark-img: url('@/assets/images/screen/train-line.png');
      background: var(--ignore-dark-img) no-repeat center / 100% 100%;
      margin: 9vh 0 3vh 0;
    }
  }

  .training-data {
    flex-shrink: 0;

    .section-grid {
      display: flex;
      gap: 1vw;
      width: 100%;
    }
  }

  .footer {
    display: flex;
    width: 56vw;
    height: 6vh;
    margin: 0 auto;
    flex-shrink: 0;
    background: url('@/assets/images/screen/train-btn-bg.png') no-repeat center / 100% 100%;
    justify-content: center;
    &.training {
      padding-left: 7.3vw;
      justify-content: flex-start;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1.1vw;

      .cddc-ant-btn {
        min-width: 12.2vw;
        height: 5.2vh;
        border-radius: 4px;
        font-size: clamp(14px, 1.8vw, 40px);
        font-weight: 500;
      }
    }
  }
}

.dialog-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;

  .tips {
    text-align: center;
    font-size: 1.3vw;
    font-weight: 500;
    margin-bottom: 0;
  }
}

@media screen and (max-width: 1024px) {
  .training-container {
    padding: 1.2vh 1.6vw;

    .header {
      margin-bottom: 1.2vh;

      .training-select {
        margin-bottom: 1.2vh;
      }

      // 小屏幕下的训练控制样式
      :deep(.training-controls) {
        gap: 12px;

        .control-item {
          .control-label {
            font-size: 12px;
          }
        }

        .control-divider {
          height: 16px;
        }
      }
    }

    .camera-views {
      gap: 0.5vw;
      margin-bottom: 1.2vh;
      padding: 0.8vh 1.2vw 2.5vh 1.2vw;

      .line {
        margin: 8vh 0 2.5vh 0;
      }

      .btn-switch {
        right: 1.6vw;
        top: 2.5vh;
      }

      .camera-box {
        .title {
          margin-bottom: 1.6vh;
          font-size: 1vw;
        }

        .video-container {
          .status-tag {
            font-size: 0.8vw;
            width: 5vw;
            height: 5vw;
          }
        }
      }

      .point-box {
        padding: 4vh 0 0.4vh 0;
      }
    }

    .footer {
      width: 60vw;
      height: 5vh;

      .action-buttons {
        gap: 1vw;

        .cddc-ant-btn {
          min-width: 7vw;
          height: 3.5vh;
          font-size: 1vw;
        }
      }
    }
  }

  .dialog-content {
    .tips {
      font-size: 1.1vw;
    }
  }
}
</style>
