import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '/@/api/cddc.model';
import {
  V1ManageCommonTrainStatusEnum,
  V1ManageTrainStudyRecordsProjects,
  V1ManageTrainStudyRecordsUsers,
} from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { getDictLabel } from '/@/dict';
import { SKILL_STATUS } from '/@/dict/modules/station';
import { toFixed } from '/@/utils';

// 表格配置
export const columns: EnhancedColumn[] = [
  {
    title: '项目',
    dataIndex: 'projectName',
    width: 160,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 200,
    search: {
      field: 'userIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsUsers();
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '训练时长(min)',
    dataIndex: 'trainDuration',
    width: 120,
  },
  {
    title: '要求次数',
    dataIndex: 'requestFrequency',
    width: 100,
  },
  {
    title: '作业次数',
    dataIndex: 'opNum',
    width: 100,
  },
  {
    title: '合格率要求',
    dataIndex: 'requestQualificationRate',
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '0%';
    },
    width: 100,
  },
  {
    title: '训练合格率',
    dataIndex: 'trainingQualifiedRate',
    width: 100,
    customRender({ record }: { record: V1LocationStudyCheckProjectIDUserIDGetResponseResult }) {
      const p = (record.opNumOk! / record.opNum!) * 100 || 0;

      return `${toFixed(p)}%`;
    },
  },
  {
    title: '动作达标率要求',
    dataIndex: 'requestActionRate',
    width: 120,
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '0%';
    },
  },
  {
    title: '动作达标率',
    dataIndex: 'actionRate',
    width: 100,
    customRender({ record }: { record: V1LocationStudyCheckProjectIDUserIDGetResponseResult }) {
      return toFixed((record.actionNumOk! / record.actionNum!) * 100 || 0) + '%';
    },
  },
  {
    title: '效率要求(次/min)',
    dataIndex: 'requestEfficiency',
    width: 140,
  },
  {
    title: '最近效率(次/min)',
    dataIndex: 'lastEfficiency',
    width: 140,
  },
  {
    title: '训练进度',
    dataIndex: 'status',
    // customRender({ record }: { record: V1LocationStudyCheckProjectIDUserIDGetResponseResult }) {
    //   return getDictLabel(REPORT_STATUS, record.status);
    // },
    width: 100,
    search: {
      field: 'statusList',
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showArrow: true,
        placeholder: '请选择',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          const resp = await V1ManageCommonTrainStatusEnum({ projectType: 1 });
          return resp.map((item) => ({ ...item, label: item.desc, value: item.code }));
        },
      },
    },
  },
  {
    title: '技能状态',
    dataIndex: 'skillStatus',
    customRender({ record }: { record: V1LocationStudyCheckProjectIDUserIDGetResponseResult }) {
      return getDictLabel(SKILL_STATUS, record.skillStatus);
    },
    width: 200,
    search: {
      field: 'skillStatusList',
      component: 'Select',
      componentProps: {
        allowClear: true,
        showArrow: true,
        placeholder: '请选择',
        options: SKILL_STATUS,
        mode: 'multiple',
        maxTagCount: 'responsive',
      },
    },
  },
  {
    title: '最近更新时间',
    dataIndex: 'lastUpdateTime',
    width: 260,
    search: {
      field: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
      componentProps: {
        allowClear: true,
        showTime: { format: 'HH:mm:ss' },
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        style: { width: '100%' },
      },
      colProps: { span: 6 },
    },
  },
];
