<template>
  <div class="error-sound-demo">
    <h2>错误音频播放测试</h2>
    
    <div class="controls">
      <div class="control-group">
        <label>声音类型：</label>
        <select v-model="soundType" @change="handleSoundTypeChange">
          <option value="rational">理性</option>
          <option value="lively">活泼</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>播放状态：</label>
        <span :class="{ playing: isPlaying }">
          {{ isPlaying ? '正在播放' : '未播放' }}
        </span>
      </div>
      
      <button @click="stopPlayback" :disabled="!isPlaying">停止播放</button>
    </div>

    <div class="test-buttons">
      <h3>测试按钮</h3>
      
      <div class="button-group">
        <h4>单个错误测试</h4>
        <button @click="playSingleError(1)">双手持枪错误</button>
        <button @click="playSingleError(2)">垂直作业面错误</button>
        <button @click="playSingleError(8)">手套识别错误</button>
      </div>
      
      <div class="button-group">
        <h4>多个错误测试</h4>
        <button @click="playMultipleErrors([1, 2])">2个错误（依次播放）</button>
        <button @click="playMultipleErrors([1, 2, 8])">3个错误（依次播放）</button>
        <button @click="playMultipleErrors([1, 2, 3, 4])">4个错误（依次播放）</button>
      </div>
      
      <div class="button-group">
        <h4>中断测试</h4>
        <button @click="testInterruption">测试中断播放</button>
      </div>
      
      <div class="button-group">
        <h4>混合测试</h4>
        <button @click="playMixedErrors">有声音+无声音错误</button>
      </div>
    </div>

    <div class="log">
      <h3>操作日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useErrorSound, type ErrorType, type SoundType } from '/@/composables/useErrorSound';

const { playErrorSound, switchSoundType, stopCurrentPlayback, isPlaying } = useErrorSound();

const soundType = ref<SoundType>('rational');
const logs = ref<string[]>([]);

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

const handleSoundTypeChange = () => {
  switchSoundType(soundType.value);
  addLog(`切换声音类型到: ${soundType.value}`);
};

const playSingleError = (errorType: ErrorType) => {
  playErrorSound([{ actionType: errorType }], soundType.value);
  addLog(`播放单个错误: ${errorType}`);
};

const playMultipleErrors = (errorTypes: ErrorType[]) => {
  const actionLabels = errorTypes.map(type => ({ actionType: type }));
  playErrorSound(actionLabels, soundType.value);
  addLog(`播放多个错误: [${errorTypes.join(', ')}]`);
};

const testInterruption = () => {
  // 先播放多个错误
  playMultipleErrors([1, 2, 3, 4]);
  addLog('开始播放4个错误...');
  
  // 2秒后中断并播放新错误
  setTimeout(() => {
    playSingleError(8);
    addLog('2秒后中断，播放新错误');
  }, 2000);
};

const playMixedErrors = () => {
  // 包含有声音和无声音的错误
  playErrorSound([
    { actionType: 1 }, // 有声音
    { actionType: 5 }, // 无声音
    { actionType: 2 }, // 有声音
    { actionType: 6 }, // 无声音
    { actionType: 8 }  // 有声音
  ], soundType.value);
  addLog('播放混合错误（有声音+无声音）');
};

const stopPlayback = () => {
  stopCurrentPlayback();
  addLog('手动停止播放');
};

// 监听播放状态变化
watch(isPlaying, (newValue) => {
  addLog(`播放状态变化: ${newValue ? '开始播放' : '停止播放'}`);
});

// 初始化
addLog('错误音频播放测试页面已加载');
</script>

<style scoped>
.error-sound-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 30px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.playing {
  color: #52c41a;
  font-weight: bold;
}

.test-buttons {
  margin-bottom: 30px;
}

.button-group {
  margin-bottom: 20px;
}

.button-group h4 {
  margin-bottom: 10px;
  color: #666;
}

.button-group button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.button-group button:hover {
  background: #40a9ff;
}

.button-group button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.log {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 15px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
