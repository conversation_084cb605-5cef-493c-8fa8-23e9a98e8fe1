<template>
  <div class="learn-project-wrapper">
    <BasicTablePlus @register="registerTable">
      <!-- 表格上方操作栏 -->
      <template #toolbar>
        <a-button type="primary" ghost @click="handleAddProject">
          <template #icon>
            <PlusOutlined />
          </template>
          新增项目
        </a-button>
      </template>

      <!-- 操作列自定义内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a class="action-button edit-button" @click="handleEdit(record)">编辑</a>
            <a class="action-button delete-button" @click="handleDelete(record)">删除</a>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>

    <!-- 新增/编辑项目抽屉 -->
    <BasicDrawer
      v-bind="$attrs"
      :title="drawerTitle"
      :width="600"
      showFooter
      @register="registerDrawer"
      @ok="debounceSubmit"
    >
      <BasicForm @register="registerForm" />
    </BasicDrawer>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { BasicForm, useForm, PageWrapper, BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import { PlusOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { columns, formSchema } from './project.data';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import {
  V1ManageLearningProjectsPagePost,
  V1ManageLearningProjectsPost,
  V1ManageLearningProjectsProjectIdDelete,
  V1ManageLearningProjectsPut,
} from '/@/api/cddc.req';
import { useDebounce } from '/@/hooks/core/useDebounce';
import { formatSecondsToMinuteString } from '/@/utils/dateUtil';

export default defineComponent({
  name: 'LearnProject',
  components: {
    BasicTablePlus,
    PageWrapper,
    BasicDrawer,
    BasicForm,
    PlusOutlined,
  },
  setup() {
    const { createMessage, createConfirm } = useMessage();
    const drawerTitle = ref('新增项目');
    const isUpdate = ref(false);
    const rowId = ref('');
    const handleBeforeFetch = (params: any) => {
      const rawQuery = params.data || {};

      const data = {
        ...rawQuery,
        startTime: rawQuery.timeRange?.at(0),
        endTime: rawQuery.timeRange?.at(1),
      };

      params.data = data;
      return params;
    };
    // 表格注册
    const [registerTable, { reload }] = useTable({
      api: (params) => V1ManageLearningProjectsPagePost(handleBeforeFetch(params)),
      columns,
      tableProps: {
        showIndexColumn: true,
        formConfig: {
          labelWidth: 'auto',
          rowProps: {
            gutter: 16,
          },
          baseColProps: {
            span: 6,
          },
        },
      },
      beforeFetch: handleBeforeFetch,
    });

    // 表单抽屉
    const [registerDrawer, { openDrawer }] = useDrawer();
    const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
      rowProps: {
        gutter: [24, 12],
      },
      schemas: formSchema,
      showActionButtonGroup: false,
    });

    // 新增项目
    function handleAddProject() {
      resetFields();
      isUpdate.value = false;
      drawerTitle.value = '新增项目';
      openDrawer(true);
    }

    // 编辑项目
    function handleEdit(record: Recordable) {
      resetFields();
      isUpdate.value = true;
      drawerTitle.value = '编辑项目';
      rowId.value = record.id;

      openDrawer(true);

      // 延迟一下再设置表单值，避免表单未完全初始化的问题
      setTimeout(() => {
        setFieldsValue({
          ...record,
          duration: formatSecondsToMinuteString(Number(record.duration)),
          materialIdList: record.materialList.map((item: Recordable) => item.id),
        });
      }, 100);
    }

    // 删除项目
    function handleDelete(record: Recordable) {
      createConfirm({
        type: 'warning',
        title: '提示',
        content: '是否确认删除该项目？',
        onOk: async () => {
          await V1ManageLearningProjectsProjectIdDelete({
            projectId: record.id,
          });
          createMessage.success('删除成功');
          reload();
        },
      });
    }

    // 提交表单
    async function handleSubmit() {
      try {
        const values = await validate();
        if (isUpdate.value) {
          await V1ManageLearningProjectsPut({
            ...values,
            id: rowId.value,
          });
          createMessage.success('更新成功');
        } else {
          await V1ManageLearningProjectsPost({
            ...values,
          });
          createMessage.success('新增成功');
        }
        reload();
        openDrawer(false);
      } catch (error) {
        console.error(error);
      }
    }
    //防抖处理
    const [debounceSubmit] = useDebounce(handleSubmit, 500);

    return {
      registerTable,
      registerDrawer,
      registerForm,
      drawerTitle,
      handleAddProject,
      handleEdit,
      handleDelete,
      handleSubmit,
      debounceSubmit
    };
  },
});
</script>

<style lang="less" scoped>
.learn-project-wrapper {
  padding: 0px 8px;
}

.action-button {
  &.delete-button {
    color: #ff4d4f;
  }
}
</style>
