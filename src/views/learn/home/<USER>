import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

// 表格列定义
export const columns: EnhancedColumn[] = [
  {
    title: '学习项目名称',
    dataIndex: 'name',
    width: 180,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入项目名称',
        maxlength: 20,
      },
    },
  },
  {
    title: '学习项目时长',
    dataIndex: 'duration',
    width: 150,
    search: {
      field: 'durationRange',
      label: '学习时长',
      component: 'InputNumber',
      slot: 'durationRange',
      colProps: {
        span: 6,
      },
    },
    customRender: ({ text }) => {
      return formatSecondsToMinute(text);
    },
  },
  {
    title: '学习状态',
    dataIndex: 'status',
    width: 120,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择学习状态',
        options: [
          { label: '待学习', value: 'WAITING' },
          { label: '学习中', value: 'LEARNING' },
          { label: '已完成', value: 'FINISH' },
        ],
      },
    },
  },
  {
    title: '考试状态',
    dataIndex: 'examStatus',
    width: 120,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择考试状态',
        options: [
          { label: '待考试', value: 'TO_BE' },
          { label: '弃考', value: 'QUIT' },
          { label: '合格', value: 'COMPLETED' },
          { label: '不合格', value: 'UNQUALIFIED' },
        ],
      },
    },
  },
  {
    title: '考试分数',
    dataIndex: 'score',
    width: 120,
    search: {
      field: 'scoreRange',
      component: 'Input',
      slot: 'scoreRange',
      colProps: {
        span: 6,
      },
    },
  },
];

// 状态颜色映射
export const statusColorMap = {
  WAITING: 'warning',
  LEARNING: 'processing',
  FINISH: 'success',
  TO_BE: 'warning',
  QUIT: 'error',
  COMPLETED: 'success',
  UNQUALIFIED: 'error',
};

// 状态文本映射
export const statusTextMap = {
  WAITING: '待学习',
  LEARNING: '学习中',
  FINISH: '已完成',
  TO_BE: '待考试',
  QUIT: '弃考',
  COMPLETED: '合格',
  UNQUALIFIED: '不合格',
};
