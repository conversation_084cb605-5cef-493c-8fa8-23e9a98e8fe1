<template>
  <div class="learn-material-wrapper">
    <BasicTablePlus @register="registerTable">
      <!-- 表格上方操作栏 -->
      <template #toolbar>
        <a-button type="primary" ghost @click="handleAddMaterial">
          <template #icon>
            <PlusOutlined />
          </template>
          新增文件
        </a-button>
      </template>

      <!-- 操作列自定义内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a class="action-button edit-button" @click="handleEdit(record)">编辑</a>
            <a class="action-button delete-button" @click="handleDelete(record)">删除</a>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>

    <!-- 新增/编辑文件抽屉 -->
    <BasicDrawer
      v-bind="$attrs"
      :title="drawerTitle"
      :width="600"
      showFooter
      @register="registerDrawer"
      @ok="debounceSubmit"
    >
      <BasicForm @register="registerForm">
        <template #fileContent="{ model }">
          <CommonFileUpload
            v-bind="commonFileUploadProps"
            v-model="model.fileContent"
            @upload-success="uploadSuccess"
          />
        </template>
      </BasicForm>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BasicForm, useForm, BasicDrawer, useDrawer } from '@geega-ui-plus/geega-ui';
import { PlusOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { columns, formSchema } from './material.data';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import {
  V1CommonFileUploadDownloadFileIdPost,
  V1ManageLearningMaterialsMaterialIdDelete,
  V1ManageLearningMaterialsPagePost,
  V1ManageLearningMaterialsPost,
  V1ManageLearningMaterialsPut,
} from '/@/api/cddc.req';
import CommonFileUpload from '/@/components/Upload/CommonFileUpload.vue';
import { createFile } from '/@/components/Uploader/utils';
import type { VUploadFile } from '/@/components/Uploader/types';
import { useDebounce } from '/@/hooks/core/useDebounce';
const { createMessage, createConfirm } = useMessage();
const drawerTitle = ref('新增文件');
const isUpdate = ref(false);
const rowId = ref('');
// 表格注册
const [registerTable, { reload }] = useTable({
  api: V1ManageLearningMaterialsPagePost,
  columns,
});
const commonFileUploadProps = {
  maxSize: 1 * 1024 * 1024,
  maxCount: 1,
  multiple: false,
  accept: '.docx,.doc,.xlsx,.xls,.ppt,.pptx,.pdf,.mp4',
  fileItemActionBtns: [
    {
      btnName: '删除',
      key: 'delete',
    },
  ],
  fileItemClick: (key: string, _file: VUploadFile, actions: any) => {
    if (key === 'delete') {
      actions.remove();
      // 当删除文件时，清空文件名和文件类型字段
      setFieldsValue({
        filename: '',
        fileType: null,
      });
    }
  },
};
// 表单抽屉
const [registerDrawer, { openDrawer }] = useDrawer();
const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  rowProps: {
    gutter: [24, 12],
  },
  schemas: formSchema,
  showActionButtonGroup: false,
});

// 新增文件
function handleAddMaterial() {
  resetFields();
  isUpdate.value = false;
  drawerTitle.value = '新增文件';
  openDrawer(true);

  // 确保文件名和文件类型字段初始为空值
  setFieldsValue({
    filename: '',
    fileType: null,
  });
}

// 编辑文件
async function handleEdit(record: Recordable) {
  resetFields();
  isUpdate.value = true;
  drawerTitle.value = '编辑文件';
  rowId.value = record.id;

  openDrawer(true);
  const files: VUploadFile[] = [];

  if (record.fileId) {
    const fileUrl = await V1CommonFileUploadDownloadFileIdPost({
      fileId: record.fileId,
    });
    const file = createFile({
      name: record.filename!,
      url: fileUrl,
      uid: record.fileId,
    });
    files.push(file);
  }
  // 延迟一下再设置表单值，避免表单未完全初始化的问题
  setTimeout(() => {
    // 设置表单值，确保文件名和文件类型字段正确填充
    setFieldsValue({
      ...record,
      // 如果没有文件内容，确保文件名和文件类型字段为空
      filename: record.filename || '',
      fileType: record.fileType || null,
      fileContent: files,
    });
  }, 100);
}

// 删除文件
function handleDelete(record: Recordable) {
  createConfirm({
    type: 'warning',
    title: '提示',
    content: '是否确认删除该文件？',
    onOk: async () => {
      await V1ManageLearningMaterialsMaterialIdDelete({
        materialId: record.id,
      });
      createMessage.success('删除成功');
      reload();
    },
  });
}
const uploadSuccess = (info) => {
  // 从上传的文件中提取文件名和类型信息
  if (info && info.name) {
    // 设置文件名
    setFieldsValue({
      filename: info.name,
    });

    // 根据文件扩展名设置文件类型
    const fileExt = info.name.split('.').pop()?.toLowerCase();
    let fileType = '';

    if (fileExt === 'docx' || fileExt === 'doc') {
      fileType = 'word';
    } else if (fileExt === 'xlsx' || fileExt === 'xls') {
      fileType = 'excel';
    } else if (fileExt === 'ppt' || fileExt === 'pptx') {
      fileType = 'ppt';
    } else if (fileExt === 'pdf') {
      fileType = 'pdf';
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(fileExt || '')) {
      fileType = 'video';
    }

    setFieldsValue({
      fileType,
    });
  }
};
// 提交表单
async function handleSubmit() {
  try {
    const values = await validate();
    // 处理表单数据，确保符合接口要求
    const processedData = {
      ...values,
      // 确保文件内容格式正确
      fileId: values.fileContent[0]?.response.id,
    };

    if (isUpdate.value) {
      // 执行更新操作
      await V1ManageLearningMaterialsPut({
        ...processedData,
        id: rowId.value,
        fileId: values.fileContent[0]?.response.uid,
      });
      createMessage.success('更新成功');
    } else {
      // 执行添加操作
      await V1ManageLearningMaterialsPost({
        ...processedData,
      });
      createMessage.success('新增成功');
    }
    reload();
    openDrawer(false);
  } catch (error) {
    console.error(error);
  }
}
//防抖处理
const [debounceSubmit] = useDebounce(handleSubmit, 500);
</script>

<style lang="less" scoped>
.learn-material-wrapper {
  padding: 0px 8px;
}

.action-button {
  &.delete-button {
    color: #ff4d4f;
  }
}
</style>
