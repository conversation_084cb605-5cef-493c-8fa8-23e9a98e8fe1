import type { FormSchema } from '@geega-ui-plus/geega-ui';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { convertFileSize } from '/@/components/Uploader/utils';

// 文件类型选项
export const fileTypeOptions = [
  { label: 'Word', value: 'word' },
  { label: 'PPT', value: 'ppt' },
  { label: 'PDF', value: 'pdf' },
  { label: '视频', value: 'video' },
  { label: 'Excel', value: 'excel' },
];

// 表格列定义
export const columns: EnhancedColumn[] = [
  {
    title: '文件名称',
    dataIndex: 'filename',
    width: 250,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入文件名称',
      },
    },
  },
  {
    title: '文件类型',
    dataIndex: 'fileType',
    width: 120,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择文件类型',
        options: fileTypeOptions,
      },
    },
    customRender: ({ text }) => {
      return fileTypeOptions.find((item) => item.value === text)?.label || text;
    },
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 120,
    // 需要进行转换
    customRender: ({ text }) => {
      if (text) {
        const size = Number(parseFloat(text / 1024).toFixed(2));
        return convertFileSize(size);
      }
    },
  },
  {
    title: '编辑人',
    dataIndex: 'lastUpdateBy',
    width: 120,
  },
  {
    title: '编辑时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
];

// 表单配置（新增/编辑）
export const formSchema: FormSchema[] = [
  {
    field: 'filename',
    label: '文件名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入文件名称',
    },
    rules: [{ required: true, message: '请输入文件名称' }],
  },
  {
    field: 'fileType',
    label: '文件类型',
    component: 'Select',
    componentProps: {
      disabled: true,
      options: fileTypeOptions,
      placeholder: '请选择文件类型',
    },
    required: true,
    rules: [{ required: true, message: '请选择文件类型' }],
  },
  {
    field: 'fileContent',
    label: '文件上传',
    component: 'Upload',
    slot: 'fileContent',
    required: true,
    rules: [{ required: true, message: '请上传文件' }],
  },
];

// 模拟数据
export const mockData = [
  {
    id: '1',
    fileName: '装配作业指导书.docx',
    fileType: 'Word',
    fileSize: '2.1MB',
    editor: '张三',
    editTime: '2024-06-01 10:00',
  },
  {
    id: '2',
    fileName: '安全培训PPT.pptx',
    fileType: 'PPT',
    fileSize: '5.3MB',
    editor: '李四',
    editTime: '2024-06-02 09:30',
  },
  {
    id: '3',
    fileName: '焊接工艺流程.pdf',
    fileType: 'PDF',
    fileSize: '1.8MB',
    editor: '王五',
    editTime: '2024-06-03 14:20',
  },
  {
    id: '4',
    fileName: '设备操作视频.mp4',
    fileType: '视频',
    fileSize: '1.2GB',
    editor: '赵六',
    editTime: '2024-06-04 16:10',
  },
  {
    id: '5',
    fileName: '工艺参数表.xlsx',
    fileType: 'Excel',
    fileSize: '800KB',
    editor: '钱七',
    editTime: '2024-06-05 11:45',
  },
];
