<script lang="ts" setup>
import { useRoute } from 'vue-router';
import {
  V1ManageSceneCluesListPost,
  V1ManageSceneQuestionsSceneIdRandom,
  V1ManageScenesSceneId,
} from '/@/api/cddc.req';
import ModelFileTest from '/@/components/ModelRenderer/ModelFileTest.vue';
import ModelRenderer from '/@/components/ModelRenderer/ModelRenderer.vue';
import { useAsyncData } from '/@/composables';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { type Mesh } from 'three';
import { reactive, ref } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import type {
  ClickRecordListElement,
  V1ManageSceneCluesClueIDGetResponseResult,
  V1ManageSceneQuestionsListPostResponseResult,
} from '/@/api/cddc.model';
import { sleep } from '/@/utils';
import SubmitAnswerDrawer from './components/SubmitAnswerDrawer.vue';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import ClueCard from './components/ClueCard.vue';
import { SceneDefaultQuestionID } from './const';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';
import dayjs from 'dayjs';
import type { RaycastEvent } from '/@/components/ThreeJs';

const route = useRoute();

const [registerSubmitDrawer, submitDrawerActions] = useDrawer();

const sceneId = route.query.id as string;

const baseInfo = useAsyncData(() => V1ManageScenesSceneId({ sceneId }), {});

type ProblemItem = V1ManageSceneQuestionsListPostResponseResult;
type ClueItem = V1ManageSceneCluesClueIDGetResponseResult;
type ClickRecordItem = ClickRecordListElement;

const modelLoadingCount = ref(0);

const state = reactive({
  started: false,
  finished: false,
  previousProblem: null as ProblemItem | null,
  problem: null as ProblemItem | null,
  clues: [] as ClueItem[],
  actionTimeline: [] as ClickRecordItem[],
});

const selectedClue = reactive({
  clue: {} as ClueItem,
  mesh: null as Mesh | null,
  pos: {
    x: 240,
    y: 240,
  },
});

fetchInitData();

async function fetchInitData() {
  await baseInfo.load();

  restartGame();
}

async function getRandomProblemAndClues() {
  const problem = await V1ManageSceneQuestionsSceneIdRandom(
    {
      questionId: state.previousProblem?.id,
    },
    { sceneId }
  );

  if (!problem) {
    message.warn('未配置问题！请先配置问题！');
    return;
  }

  const clues = await V1ManageSceneCluesListPost({
    questionId: problem.id,
    sceneId: problem.sceneId,
  });

  const defaultClues = await V1ManageSceneCluesListPost({
    questionId: SceneDefaultQuestionID,
    sceneId: problem.sceneId,
  });

  const extraClues = defaultClues.filter(
    (defaultClue) => !clues.find((item) => item.moduleName === defaultClue.moduleName)
  );

  state.clues = [...clues, ...extraClues];
  state.problem = problem;
}

function resetGameState() {
  state.actionTimeline = [];
  state.previousProblem = state.problem;
  state.problem = null;
  state.clues = [];
}

async function restartGame() {
  if (!baseInfo.data.value.fileId) {
    message.warn('未配置模型！');
    return;
  }

  resetGameState();
  await sleep(1000);

  await getRandomProblemAndClues();

  state.started = true;
  state.finished = false;
}

function handleSelectedModelPart(evt: RaycastEvent) {
  const mesh = evt.mesh;

  if (!mesh) {
    selectedClue.clue = {};
    selectedClue.mesh = null;
    return;
  }

  const clue = state.clues.find((n) => n.moduleName === mesh.name);

  state.actionTimeline.push({
    clueId: clue?.id,
    clickedAt: dayjs().format('YYYY-MM-DD HH:mm:ss') as any,
    moduleName: mesh.name,
    orderNum: state.actionTimeline.length.toString(),
  });

  if (!clue) {
    message.warn('无线索');

    selectedClue.clue = {};
    selectedClue.mesh = null;
    return;
  }

  selectedClue.clue = clue;
  selectedClue.mesh = mesh;

  // todo
  selectedClue.pos = {
    x: evt.pos.x + 10,
    y: evt.pos.y + 10,
  };
}

function showSubmitDrawer() {
  submitDrawerActions.openDrawer(true, {
    ...state.problem,
    clickRecordList: state.actionTimeline,
  });
}

function handleRestart() {
  restartGame();
}
</script>

<template>
  <div class="flex h-full flex-col p-2 gap-4">
    <div>
      <div class="flex items-center mb-2">
        <div class="flex-1">
          <GeegaBasicTitle class="py-0!"> 场景基本信息 </GeegaBasicTitle>
        </div>
        <div>
          <a-button type="primary" @click="$router.push('/virtual/test/train')">退出</a-button>
        </div>
      </div>
      <div class="desc">
        <span class="label">场景名称</span>
        <span class="value">{{ baseInfo.data.value.name }}</span>
      </div>
      <div class="desc">
        <span class="label">场景说明</span>
        <span class="value flex-1 w-0 truncate" :title="baseInfo.data.value.remark">
          {{ baseInfo.data.value.remark }}
        </span>
      </div>
    </div>

    <div class="flex-1 model-wrapper relative">
      <a-spin wrapperClassName="spin-h-full" :spinning="!!modelLoadingCount" tip="模型加载中...">
        <div class="h-full model-render-wrapper">
          <div
            v-if="baseInfo.data.value.fileId"
            class="h-full dark-ignore-style"
            :style="{ background: '#ccc' }"
          >
            <ModelRenderer>
              <ModelFileTest
                :fileId="baseInfo.data.value.fileId"
                @selected="handleSelectedModelPart"
                @beforeLoadingModel="modelLoadingCount++"
                @loaded="modelLoadingCount--"
              />

              <ClueCard
                v-if="selectedClue.clue && selectedClue.mesh"
                :clue="selectedClue.clue"
                :mesh="selectedClue.mesh"
                :pos="selectedClue.pos"
              />
            </ModelRenderer>
          </div>
          <customizeRenderEmpty v-else />
        </div>
      </a-spin>

      <div class="problem-wrapper" v-if="state.started && state.problem">
        <div class="problem flex gap-2 items-center">
          <div class="flex-1 w-0">
            <div class="title">{{ state.problem.name }}</div>
            <div class="description">
              <a-tooltip :title="state.problem.remark" placement="bottom">
                <div class="w-full truncate">
                  {{ state.problem.remark }}
                </div>
              </a-tooltip>
            </div>
          </div>
          <div>
            <a-button type="primary" @click="showSubmitDrawer">开始作答</a-button>
          </div>
        </div>
      </div>
    </div>

    <SubmitAnswerDrawer
      @register="registerSubmitDrawer"
      @restart="handleRestart"
    ></SubmitAnswerDrawer>
  </div>
</template>

<style lang="less" scoped>
.model-wrapper {
  display: flex;
  flex-direction: column;
}

.model-render-wrapper {
  border: 1px solid gray;
}

.problem-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 0 0 10px rgba(137, 137, 137, 0.564);

  .problem {
    width: 600px;
    padding: 12px;

    --ignore-dark-bg: rgba(255, 255, 255, 0.85);
    --ignore-dark-fg: black;
    color: var(--ignore-dark-fg);
    background: var(--ignore-dark-bg);

    border-radius: 0 0 4px 0;

    .title {
      font-size: 16px;
    }

    .description {
      word-wrap: break-word;

      --ignore-dark-fg: #333;
      color: var(--ignore-dark-fg);
    }
  }
}

.desc {
  display: flex;
  gap: 4px;

  .label {
    width: 4em;
    text-align: right;
  }

  .value {
    color: #666;
  }
}
</style>
