<script lang="ts" setup>
import SceneBox from './components/SceneBox.vue';
import { useRouter } from 'vue-router';
import { useMessage } from '/@/hooks/web/useMessage';
import { ref } from 'vue';
import { V1ManageScenesPagePost, V1ManageScenesSceneIdDelete } from '/@/api/cddc.req';
import EditOrCreateScene from './components/EditOrCreateScene.vue';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useInfiniteScrollData } from '/@/composables';
import type { V1ManageScenesCurrentUserListGetResponseResult } from '/@/api/cddc.model';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';

type DataItem = V1ManageScenesCurrentUserListGetResponseResult;

const router = useRouter();

const { createConfirm } = useMessage();

const [registerDrawer, drawerActions] = useDrawer();

const scrollEl = ref<HTMLElement>();

const sceneData = useInfiniteScrollData(scrollEl, V1ManageScenesPagePost);

function handleDetail(data: DataItem) {
  const id = data?.id;

  router.push({
    path: '/virtual/test/detail',
    query: {
      id,
    },
  });
}

function handleDelete(data: DataItem) {
  createConfirm({
    title: '确认删除吗？',
    content: '删除后不可恢复',
    onOk: async () => {
      await V1ManageScenesSceneIdDelete({
        sceneId: data.id!,
      });

      message.success('删除成功');
      reloadData();
    },
  });
}

function handleAdd() {
  drawerActions.openDrawer(true, {});
}

async function reloadData() {
  sceneData.reset();
}
</script>

<template>
  <div class="p-2">
    <div>
      <GeegaBasicTitle class="pt-0!"> 场景配置 </GeegaBasicTitle>
    </div>
    <a-spinning :is-spinning="sceneData.isLoading.value">
      <div ref="scrollEl" class="scene-wrapper">
        <div class="add-box" @click="handleAdd"> + 新增场景 </div>
        <SceneBox
          v-for="item in sceneData.state.data"
          icon=""
          :title="item.name"
          :description="item.remark"
          @detail="handleDetail(item)"
          @delete="handleDelete(item)"
        />
      </div>
    </a-spinning>
    <EditOrCreateScene @register="registerDrawer" @success="reloadData" />
  </div>
</template>

<style lang="less" scoped>
.scene-wrapper {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
}

.add-box {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s;
  min-height: 130px;

  font-size: 16px;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }
}
</style>
