<script lang="ts" setup>
import { useRoute } from 'vue-router';
import {
  V1ManageSceneCluesListPost,
  V1ManageSceneQuestionsListPost,
  V1ManageSceneQuestionsQuestionIdDelete,
  V1ManageScenesPut,
  V1ManageScenesSceneId,
} from '/@/api/cddc.req';
import ModelFileTest from '/@/components/ModelRenderer/ModelFileTest.vue';
import ModelRenderer from '/@/components/ModelRenderer/ModelRenderer.vue';
import { useAsyncData } from '/@/composables';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import EditOrCreateScene from './components/EditOrCreateScene.vue';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import ProblemBox from './components/ProblemBox.vue';
import EditOrCreateClue from './components/EditOrCreateClue.vue';
import EditOrCreateProblem from './components/EditOrCreateProblem.vue';
import type { V1ManageSceneQuestionsListPostResponseResult } from '/@/api/cddc.model';
import { useMessage } from '/@/hooks/web/useMessage';
import { message } from '@geega-ui-plus/ant-design-vue';
import { computed, reactive, ref, shallowRef } from 'vue';
import { chooseFiles } from '/@/components/ImportFile/file';
import { uploadApi } from '/@/components/Uploader/utils';
import { SceneDefaultQuestionID } from './const';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';
import type { RaycastEvent } from '/@/components/ThreeJs';
import ResponsiveTagList from '/@/components/ResponsiveTagList.vue';

const route = useRoute();

const { createConfirm } = useMessage();

const modelAreaLoadingState = reactive({
  count: 0,
  tip: '模型加载中...',
});

const sceneId = route.query.id as string;

const state = reactive({
  selectedProblemId: null as null | string,
});

const fileCache = shallowRef<Record<string, File>>({});

const [registerSceneDrawer, sceneDrawerActions] = useDrawer();
const [registerProblemDrawer, problemDrawerActions] = useDrawer();
const [registerClueDrawer, clueDrawerActions] = useDrawer();

const baseInfo = useAsyncData(() => V1ManageScenesSceneId({ sceneId }), {});

type ProblemItem = V1ManageSceneQuestionsListPostResponseResult;
const problemsApi = useAsyncData(() => V1ManageSceneQuestionsListPost({ sceneId }), []);

const cluesApi = useAsyncData(
  () =>
    V1ManageSceneCluesListPost({
      sceneId,
      questionId: state.selectedProblemId ?? SceneDefaultQuestionID,
    }),
  []
);

const currentClueNames = computed(() =>
  cluesApi.data.value.map((n) => n.moduleName).filter((n) => n != null)
);

fetchInitData();

function fetchInitData() {
  state.selectedProblemId = null

  updateBaseInfData();
  updateProblemsData();

  cluesApi.load();
}

function handleEditBaseInfo() {
  sceneDrawerActions.openDrawer(true, { ...baseInfo.data.value });
}

async function updateBaseInfData() {
  await baseInfo.load();
}

async function updateProblemsData() {
  await problemsApi.load();
}

function updateClueData() {
  cluesApi.load();
  problemsApi.load();
}

async function uploadModel() {
  if (baseInfo.data.value.fileId) {
    const prompt = new Promise<void>((resolve, reject) => {
      createConfirm({
        title: '提示',
        content: '已存在模型，重新上传将会删除所有已存在的问题和线索，请确认是否上传？',
        onOk() {
          resolve();
        },
        onCancel() {
          reject();
        },
      });
    });

    await prompt;
  }

  const files = await chooseFiles({
    accept: '.glb',
    multiple: false,
  });

  const file = files.at(0);
  if (!file?.name.endsWith('.glb')) {
    message.warn('请选择 .glb 类型的模型！');
    return;
  }

  // 最大支持 200M
  if (file.size > 1024 * 1024 * 200) {
    message.warn('模型最大支持 200 M，请优化模型后再上传！');
    return;
  }

  try {
    modelAreaLoadingState.count++;
    modelAreaLoadingState.tip = '模型上传中...';

    const resp = await uploadApi(file);

    const info = { ...baseInfo.data.value };
    info.fileId = resp.id;

    await V1ManageScenesPut({
      ...info,
      id: info.id!,
      name: info.name!,
      userIds: info.userIds!,
    });

    fileCache.value = {
      [info.fileId!]: file,
    };

    fetchInitData()
    message.success('上传成功');
  } catch (error) {
    throw error;
  } finally {
    modelAreaLoadingState.count--;
  }
}

function handleAddProblem() {
  problemDrawerActions.openDrawer(true, {
    sceneId,
  });
}

function handleDeleteProblem(item: ProblemItem) {
  createConfirm({
    title: '确认删除吗？',
    content: '删除后不可恢复',
    onOk: async () => {
      await V1ManageSceneQuestionsQuestionIdDelete({
        questionId: item.id!,
      });

      if (state.selectedProblemId === item.id) {
        handleSelectProblem(item);
      }

      message.success('删除成功');
      updateProblemsData();
    },
  });
}

function handleEditProblem(item: ProblemItem) {
  problemDrawerActions.openDrawer(true, item);
}

function handleSelectProblem(item: ProblemItem) {
  state.selectedProblemId = item.id === state.selectedProblemId ? null : item.id!;

  cluesApi.load();
}

function handleSelectedModelPart(evt: RaycastEvent) {
  const mesh = evt.mesh;
  if (!mesh) {
    return;
  }

  const moduleName = mesh.name;
  const clue = cluesApi.data.value.find((n) => n.moduleName === moduleName);

  clueDrawerActions.openDrawer(
    true,
    clue ?? {
      sceneId,
      questionId: state.selectedProblemId ?? SceneDefaultQuestionID,
      moduleName: mesh.name,
    }
  );
}

function beforeLoadingModel() {
  modelAreaLoadingState.count++;
  modelAreaLoadingState.tip = '模型加载中...';
}
</script>

<template>
  <div class="relative flex h-full flex-col p-2 gap-2">
    <div>
      <div class="flex items-center mb-2">
        <div class="flex-1">
          <GeegaBasicTitle class="py-0!"> 场景基本信息 </GeegaBasicTitle>
        </div>
        <div class="flex gap-2">
          <a-button ghost type="primary" @click="$router.push('/virtual/test/index')">
            返回
          </a-button>
          <a-button type="primary" @click="handleEditBaseInfo">编辑</a-button>
        </div>
      </div>
      <div class="desc">
        <span class="label">场景名称</span>
        <span class="value">{{ baseInfo.data.value.name }}</span>
      </div>
      <div class="desc">
        <span class="label">场景说明</span>
        <span class="value flex-1 w-0 truncate" :title="baseInfo.data.value.remark">
          {{ baseInfo.data.value.remark }}
        </span>
      </div>
      <div class="flex items-start gap-1">
        <span class="label pr-1">授权人员</span>
        <span class="flex-1 w-0">
          <ResponsiveTagList
            class="flex gap-1 flex-wrap"
            :items="baseInfo.data.value.userNames || []"
            :max-count="10"
          >
            <template #default="{ item }">
              <a-tag class="py-0! mr-0!" color="success">
                {{ item }}
              </a-tag>
            </template>
          </ResponsiveTagList>
        </span>
      </div>

      <EditOrCreateScene @register="registerSceneDrawer" @success="updateBaseInfData" />
    </div>

    <div class="flex-1 model-wrapper mt-1">
      <a-spin
        wrapperClassName="spin-h-full"
        :spinning="!!modelAreaLoadingState.count"
        :tip="modelAreaLoadingState.tip"
      >
        <div class="h-full flex flex-col">
          <div class="flex mb-2">
            <div class="flex-1">
              <GeegaBasicTitle class="py-0!"> 模型展示 </GeegaBasicTitle>
            </div>

            <div class="flex gap-2">
              <a-button type="primary" @click="uploadModel">上传模型</a-button>
            </div>
          </div>

          <div class="flex-1 model-render-wrapper">
            <div
              v-if="baseInfo.data.value.fileId"
              class="h-full dark-ignore-style"
              :style="{ background: '#ccc' }"
            >
              <ModelRenderer :inspector="false">
                <ModelFileTest
                  :fileId="baseInfo.data.value.fileId"
                  :marked-parts="currentClueNames"
                  :model-cache="fileCache"
                  @selected="handleSelectedModelPart"
                  @beforeLoadingModel="beforeLoadingModel"
                  @loaded="modelAreaLoadingState.count--"
                />
              </ModelRenderer>
            </div>
            <customizeRenderEmpty v-else />
          </div>
        </div>
      </a-spin>
    </div>

    <div class="problems-wrapper">
      <div class="problems">
        <div class="add-box" @click="handleAddProblem"> +新增问题 </div>
        <ProblemBox
          v-for="item in problemsApi.data.value"
          :key="item.id"
          :count="item.clueNum"
          class="problem-box"
          :class="{ 'is-active': state.selectedProblemId === item.id }"
          :title="item.name"
          :description="item.remark"
          @click="handleSelectProblem(item)"
          @edit="handleEditProblem(item)"
          @delete="handleDeleteProblem(item)"
        />
      </div>
    </div>

    <EditOrCreateClue @register="registerClueDrawer" @success="updateClueData" />
    <EditOrCreateProblem @register="registerProblemDrawer" @success="updateProblemsData" />
  </div>
</template>

<style lang="less" scoped>
.model-render-wrapper {
  border: 1px solid gray;
}

.model-wrapper {
  display: flex;
  flex-direction: column;
}

.problems-wrapper {
  overflow: auto hidden;

  &::-webkit-scrollbar {
    height: 4px;
  }
}

.problems {
  overflow: auto;
  width: fit-content;
  gap: 8px;
  display: flex;
}

.problem-box {
  width: 240px;
  cursor: pointer;

  &.is-active {
    border: 1px solid @primary-color;
  }
}

.add-box {
  width: 240px;
  min-height: 100px;

  border: 1px solid #e8e8e8;
  border-radius: 4px;
  transition: all 0.3s;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.desc {
  display: flex;
  gap: 4px;

  .label {
    width: 4em;
    text-align: right;
  }

  .value {
    color: #666;
  }
}
</style>
