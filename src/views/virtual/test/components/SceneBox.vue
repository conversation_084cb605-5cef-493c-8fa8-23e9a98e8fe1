<script lang="ts" setup>
import ArmIcon from '/@/assets/images/machine-arm.png';

export interface SceneBoxProps {
  icon?: string;
  title?: string;
  description?: string;
  hideActions?: boolean;
}

const props = defineProps<SceneBoxProps>();
const emit = defineEmits(['detail', 'delete']);
</script>

<template>
  <div class="scene-box">
    <div class="scene-box__header">
      <div class="scene-box__icon">
        <img :src="props.icon || ArmIcon" alt="icon" />
      </div>
      <div class="scene-box__title truncate" :title="title">{{ props.title }}</div>
    </div>
    <div class="scene-box__description truncate" :title="description">
      {{ props.description || '&nbsp;' }}
    </div>
    <div class="scene-box__actions" v-if="!hideActions">
      <a-button type="link" @click="emit('detail')">详情</a-button>
      <a-button type="link" @click="emit('delete')">删除</a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.scene-box {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  &__icon {
    width: 36px;
    height: 36px;
    padding: 4px;
    margin-right: 8px;

    border: 1px solid #555;
    border-radius: 100px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  &__title {
    flex: 1;
    width: 0;
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  &__description {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
