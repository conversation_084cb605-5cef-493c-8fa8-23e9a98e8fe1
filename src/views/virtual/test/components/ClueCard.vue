<script lang="ts" setup>
import type { Object3D, Vector2Like } from 'three';
import type { V1ManageSceneCluesClueIDGetResponseResult } from '/@/api/cddc.model';
import { nextTick, ref } from 'vue';
import DraggableObject from '/@/components/DraggableObject.vue';
import { watchImmediate } from '@vueuse/core';
import { clamp } from 'lodash-es';

export interface ClueCardProps {
  clue: V1ManageSceneCluesClueIDGetResponseResult;
  mesh: Object3D;
  pos?: Vector2Like;
}

const props = defineProps<ClueCardProps>();

const cardEl = ref<HTMLElement>();

const pos = ref({
  x: props.pos?.x ?? 240,
  y: props.pos?.y ?? 240,
});

watchImmediate(
  () => props.pos,
  async () => {
    if (props.pos) {
      if (!cardEl.value) {
        await nextTick();
      }

      updatePos(props.pos);
    }
  }
);

function updatePos(newPos: Vector2Like) {
  if (!cardEl.value) {
    return;
  }

  const p = cardEl.value.parentElement?.parentElement!;

  const parentW = p.clientWidth;
  const parentH = p.clientHeight;

  const w = cardEl.value.clientWidth;
  const h = cardEl.value.clientHeight;

  const x = clamp(newPos.x, 0, parentW - w);
  const y = clamp(newPos.y, 0, parentH - h);

  pos.value.x = x;
  pos.value.y = y;
}
</script>

<template>
  <DraggableObject :pos="pos" @update:pos="updatePos">
    <template #default="{ startDrag, style }">
      <div ref="cardEl" class="clue-card" :style="style">
        <div class="title" @pointerdown="startDrag">{{ clue.clueContent }}</div>
        <div class="attachments">
          <a-image-preview-group>
            <a-image v-for="item in clue.attachmentList || []" :src="item.url"></a-image>
          </a-image-preview-group>
        </div>
      </div>
    </template>
  </DraggableObject>
</template>

<style lang="less" scoped>
.clue-card {
  position: absolute;
  width: 300px;
  max-height: 200px;
  overflow: auto;
  padding: 12px 12px;
  border-radius: 4px;

  --ignore-dark-fg: black;
  --ignore-dark-bg: white;
  color: var(--ignore-dark-fg);
  background: var(--ignore-dark-bg);

  &::-webkit-scrollbar {
    width: 4px;
    background: transparent;
  }
}

.title {
  cursor: move;
  user-select: none;
}
</style>
