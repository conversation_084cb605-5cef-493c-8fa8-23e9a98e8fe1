<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import type { V1ManageSceneQuestionsListPostResponseResult } from '/@/api/cddc.model';
import CommonFileUpload from '/@/components/Upload/CommonFileUpload.vue';
import {
  V1ManageDictDataListPost,
  V1ManageSceneQuestionsPost,
  V1ManageSceneQuestionsPut,
  V1ManageSceneQuestionsQuestionId,
} from '/@/api/cddc.req';
import { DictType } from '/@/enums/dictEnum';
import { createFiles, getUploadResponse } from '/@/components/Uploader/utils';
import { downloadFileByUrl } from '/@/utils/file/download';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '',
});

type DataItem = V1ManageSceneQuestionsListPostResponseResult;

const currentData = ref<DataItem>({});

const [registerDrawer, drawerActions] = useDrawerInner(async (props) => {
  state.drawerTitle = props?.id ? '编辑问题' : '新增问题';

  await fromActions.resetFields();

  if (props.id) {
    props = await V1ManageSceneQuestionsQuestionId({
      questionId: props.id,
    });
  }

  currentData.value = props || {};

  await fromActions.setFieldsValue({
    ...currentData.value,
    referenceIdList: createFiles(props.referenceList),
  });
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'name',
      label: '问题名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 20,
      },
    },
    {
      field: 'type',
      label: '问题分类',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: () => V1ManageDictDataListPost({ dictType: DictType.SCENE_QUESTION }),
        labelField: 'dictLabel',
        valueField: 'dictValue',
      },
    },
    {
      field: 'remark',
      label: '问题说明',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'reason',
      label: '根本原因',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'corrective',
      label: '整改措施',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'prevention',
      label: '预防策略',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'referenceIdList',
      label: '参考文件',
      component: 'Upload',
      slot: 'upload',
    },
  ],
  showActionButtonGroup: false,
});

const commonFileUploadProps = {
  accept: '.docx,.pdf,.xlsx,.jpg,.png,.jpeg',
  maxSize: 20 * 1024,
  maxCount: 3,
  fileItemActionBtns: [
    {
      btnName: '下载',
      key: 'download',
    },
    {
      btnName: '删除',
      key: 'delete',
    },
  ],
  fileItemClick: (key: string, file: any, actions: any) => {
    if (key === 'delete') actions.remove();

    if (key === 'download') {
      downloadFileByUrl(file.response.url, file.response.name);
    }
  },
};

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();
    const id = currentData.value.id;

    const postData = {
      ...data,
      id: currentData.value.id,
      sceneId: currentData.value.sceneId,
      referenceIdList: getUploadResponse(data.referenceIdList).map((n) => n.id),
    };

    if (id) {
      await V1ManageSceneQuestionsPut(postData);
      message.success('修改成功');
    } else {
      await V1ManageSceneQuestionsPost(postData);
      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #upload="{ model, field }">
        <CommonFileUpload v-model="model[field]" v-bind="commonFileUploadProps" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
