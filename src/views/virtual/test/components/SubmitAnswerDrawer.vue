<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import type {
  AttachmentListElement,
  ClickRecordListElement,
  V1ManageSceneExamSubmitPostResponseResult,
  V1ManageSceneQuestionsListPostResponseResult,
} from '/@/api/cddc.model';
import { V1ManageSceneExamSubmitPost } from '/@/api/cddc.req';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';
import { downloadFileByUrl } from '/@/utils/file/download';

const emit = defineEmits(['success', 'restart']);

enum ProgressStatus {
  Init,
  Analyzing,
  Submitted,
}

const state = reactive({
  drawerTitle: '问题作答',
  status: ProgressStatus.Init,
  analysis: null as null | V1ManageSceneExamSubmitPostResponseResult,
});

type DataItem = V1ManageSceneQuestionsListPostResponseResult & {
  clickRecordList?: ClickRecordListElement[];
};

const currentData = ref<DataItem>({});

const [registerDrawer, drawerActions] = useDrawerInner(async (props) => {
  currentData.value = props || {};

  state.status = ProgressStatus.Init;
  state.analysis = null;

  await fromActions.resetFields();
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'reason',
      label: '根本原因',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'corrective',
      label: '整改措施',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'prevention',
      label: '预防策略',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxLength: 400,
        showCount: true,
        rows: 4,
      },
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();

    state.status = ProgressStatus.Analyzing;

    const analysis = await V1ManageSceneExamSubmitPost({
      ...data,
      questionId: currentData.value.id!,
      clickRecordList: currentData.value.clickRecordList,
    });

    state.analysis = analysis;
    state.status = ProgressStatus.Submitted;
  } catch (error) {
    state.status = ProgressStatus.Init;
    console.error(error);
  } finally {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });
  }
}

function handleCancel() {
  drawerActions.setDrawerProps({
    visible: false,
    confirmLoading: false,
  });
}

async function handleRestart() {
  emit('restart');
  handleCancel();
}

function handleDownload(fileItem: AttachmentListElement) {
  downloadFileByUrl(fileItem.url!, fileItem.fileName!);
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    :mask-closable="false"
    @register="registerDrawer"
    showFooter
    :keyboard="false"
  >
    <div class="pb-1">
      <div class="title text-base">{{ currentData.name }}</div>
      <div class="description text-gray">{{ currentData.remark }}</div>
    </div>

    <BasicForm @register="registerForm"> </BasicForm>

    <template #footer>
      <div class="flex w-full gap-1 items-center justify-end">
        &nbsp;
        <template v-if="state.status === ProgressStatus.Init">
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="handleSubmit" type="primary">提交</a-button>
        </template>
        <template v-else-if="state.status === ProgressStatus.Submitted">
          <a-button @click="$router.push('/virtual/test/train')">退出</a-button>
          <a-button @click="handleRestart" type="primary">重新开始</a-button>
        </template>
      </div>
    </template>

    <div class="answer-analysis" v-if="state.status === ProgressStatus.Submitted && state.analysis">
      <div>
        <GeegaBasicTitle class="py-0! mb-1"> 答案分析 </GeegaBasicTitle>
      </div>
      <div class="flex flex-col gap-1 p-2 answer-result">
        <div class="row">
          <div class="label">作答结果</div>
          <div class="value">
            <a-tag v-if="state.analysis.qualified" color="success" class="py-0!">正确</a-tag>
            <a-tag v-else color="error" class="py-0!">错误</a-tag>
          </div>
        </div>
        <div class="row">
          <div class="label">答案分析</div>
          <div class="value">{{ state.analysis.analysis }}</div>
        </div>

        <div>
          <div class="font-bold text-green-6">正确答案</div>
        </div>

        <div class="flex flex-col gap-1">
          <div class="row">
            <div class="label">根据原因</div>
            <div class="value">{{ currentData.reason }}</div>
          </div>
          <div class="row">
            <div class="label">改善措施</div>
            <div class="value">{{ currentData.corrective }}</div>
          </div>
          <div class="row">
            <div class="label">预防策略</div>
            <div class="value">{{ currentData.prevention }}</div>
          </div>
          <div class="row">
            <div class="label">附件</div>
            <div class="value" v-if="currentData.referenceList?.length">
              <div class="flex flex-col">
                <a
                  v-for="item in currentData.referenceList"
                  @click="handleDownload(item)"
                  class="truncate"
                >
                  {{ item.fileName }}
                </a>
              </div>
            </div>
            <div v-else class="value text-gray-4">无附件</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="state.status === ProgressStatus.Analyzing"> 评估分析中... </div>
  </BasicDrawer>
</template>

<style lang="less" scoped>
.answer-result {
  border: 1px solid gray;
  border-radius: 4px;
}

.row {
  display: flex;
  gap: 4px;
  align-items: start;

  .label {
    width: 5em;
    text-align: right;

    &::after {
      content: '：';
    }
  }

  .value {
    color: #666;
    flex: 1;
    width: 0;
  }
}
</style>
