<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import type { V1ManageScenesCurrentUserListGetResponseResult } from '/@/api/cddc.model';
import UserTreeSelect from '/@/components/UserTreeSelect.vue';
import { V1ManageScenesPost, V1ManageScenesPut } from '/@/api/cddc.req';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '编辑场景',
});

type DataItem = V1ManageScenesCurrentUserListGetResponseResult;

const currentData = ref<DataItem>({});

const [registerDrawer, drawerActions] = useDrawerInner(async (props) => {
  state.drawerTitle = props?.id ? '编辑场景' : '新增场景';
  currentData.value = props || {};

  await fromActions.resetFields();
  await fromActions.setFieldsValue({
    ...currentData.value,
  });
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'name',
      label: '场景名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 20,
      },
    },
    {
      field: 'remark',
      label: '场景说明',
      component: 'InputTextArea',
      componentProps: {
        maxLength: 200,
        showCount: true,
        rows: 4,
      },
    },
    {
      field: 'userIds',
      label: '授权人员',
      component: 'Input',
      required: true,
      slot: 'userIds',
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();
    const id = currentData.value.id;

    const postData = {
      ...currentData.value,
      ...data,
      id,
    };

    if (id) {
      await V1ManageScenesPut(postData);
      message.success('修改成功');
    } else {
      await V1ManageScenesPost(postData);
      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #userIds="{ model, field }">
        <UserTreeSelect v-model:value="model[field]" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
