<script lang="ts" setup>
export interface ProblemBoxProps {
  icon?: string;
  title?: string;
  count?: string;
  active?: boolean;
  description?: string;
}

const props = defineProps<ProblemBoxProps>();

const emit = defineEmits(['edit', 'delete']);
</script>

<template>
  <div class="scene-box">
    <div class="scene-box__header">
      <div class="scene-box__title flex-1 w-0 truncate" :title="title">{{ props.title }}</div>
      <div class="scene-box__count">线索数量：{{ props.count }}</div>
    </div>
    <div class="scene-box__description truncate" :title="description">
      {{ props.description || '&nbsp;' }}
    </div>
    <div class="scene-box__actions">
      <a-button type="link" @click.stop="emit('edit')">编辑</a-button>
      <a-button type="link" @click.stop="emit('delete')">删除</a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.scene-box {
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  &__count {
    color: rgba(0, 0, 0, 0.45);
  }

  &__description {
    margin-bottom: 4px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
