<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import type { V1ManageSceneCluesClueIDGetResponseResult } from '/@/api/cddc.model';
import CommonFileUpload from '/@/components/Upload/CommonFileUpload.vue';
import {
  V1ManageSceneCluesClueId,
  V1ManageSceneCluesClueIdDelete,
  V1ManageSceneCluesPost,
  V1ManageSceneCluesPut,
} from '/@/api/cddc.req';
import { useMessage } from '/@/hooks/web/useMessage';
import { createFiles, getUploadResponse as getUploadResponses } from '/@/components/Uploader/utils';
import { downloadFileByUrl } from '/@/utils/file/download';

const emit = defineEmits(['success']);

const { createConfirm } = useMessage();

const state = reactive({
  drawerTitle: '',
});

type DataItem = V1ManageSceneCluesClueIDGetResponseResult;

const currentData = ref<DataItem>({});

const [registerDrawer, drawerActions] = useDrawerInner(async (props) => {
  props ||= {};

  state.drawerTitle = props?.id ? '编辑线索' : '新增线索';

  await fromActions.resetFields();

  if (props.id) {
    props = await V1ManageSceneCluesClueId({
      clueId: props.id,
    });
  }

  currentData.value = props || {};

  await fromActions.setFieldsValue({
    ...currentData.value,
    attachmentIdList: createFiles(props.attachmentList),
  });
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'clueContent',
      label: '线索内容',
      component: 'Input',
      componentProps: {
        maxLength: 200,
      },
    },
    {
      field: 'attachmentIdList',
      label: '线索附件',
      component: 'Upload',
      slot: 'upload',
    },
  ],
  showActionButtonGroup: false,
});

const commonFileUploadProps = {
  accept: '.jpg,.png,.jpeg',
  maxSize: 20 * 1024,
  maxCount: 5,
  fileItemActionBtns: [
    {
      btnName: '下载',
      key: 'download',
    },
    {
      btnName: '删除',
      key: 'delete',
    },
  ],
  fileItemClick: (key: string, file: any, actions: any) => {
    if (key === 'delete') actions.remove();

    if (key === 'download') {
      downloadFileByUrl(file.response.url, file.response.name);
    }
  },
};

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();

    if (!data.clueContent && !data.attachmentIdList?.length) {
      message.warn('请至少填写一项内容');
      throw new Error('validate error: 请至少填写一项内容');
    }

    const id = currentData.value.id;

    const postData = {
      ...data,
      id,
      attachmentIdList: getUploadResponses(data.attachmentIdList).map((n) => n.id),
      moduleName: currentData.value.moduleName,
      sceneId: currentData.value.sceneId,
      questionId: currentData.value.questionId,
    };

    if (id) {
      await V1ManageSceneCluesPut(postData);

      message.success('修改成功');
    } else {
      await V1ManageSceneCluesPost(postData);

      message.success('创建成功');
    }

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}

function handleDeleteClueData() {
  createConfirm({
    title: '确认删除吗？',
    content: '删除后不可恢复',
    onOk: async () => {
      await V1ManageSceneCluesClueIdDelete({
        clueId: currentData.value.id!,
      });

      message.success('删除成功');
      handleCancel();
      emit('success');
    },
  });
}

function handleCancel() {
  drawerActions.setDrawerProps({
    visible: false,
    confirmLoading: false,
  });
}
</script>

<template>
  <BasicDrawer width="600" :title="state.drawerTitle" @register="registerDrawer" showFooter>
    <BasicForm @register="registerForm">
      <template #upload="{ model, field }">
        <CommonFileUpload v-model="model[field]" v-bind="commonFileUploadProps" />
      </template>
    </BasicForm>
    <template #footer>
      <div class="flex items-center justify-end gap-2 pt-2">
        <a-button @click="handleCancel">取消</a-button>
        <a-button v-if="!!currentData?.id" type="danger" @click="handleDeleteClueData">
          删除
        </a-button>
        <a-button type="primary" @click="handleSubmit">确认</a-button>
      </div>
    </template>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
