<script lang="ts" setup>
import SceneBox from './components/SceneBox.vue';
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { V1ManageScenesCurrentUserList } from '/@/api/cddc.req';
import type { V1ManageScenesCurrentUserListGetResponseResult } from '/@/api/cddc.model';
import { useAsyncData } from '/@/composables';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';

type DataItem = V1ManageScenesCurrentUserListGetResponseResult;

const router = useRouter();

const scrollEl = ref<HTMLElement>();

const sceneData = useAsyncData(() => V1ManageScenesCurrentUserList({}), []);
sceneData.load();

function handleDetail(data: DataItem) {
  const id = data?.id;

  router.push({
    path: '/virtual/test/train-detail',
    query: {
      id,
    },
  });
}
</script>

<template>
  <div class="p-2">
    <div>
      <GeegaBasicTitle class="pt-0!"> 训练场景选择</GeegaBasicTitle>
    </div>
    <a-spinning :is-spinning="sceneData.loading.value">
      <div ref="scrollEl" class="scene-wrapper">
        <template v-if="sceneData.data.value.length">
          <SceneBox
            v-for="item in sceneData.data.value"
            class="cursor-pointer"
            icon=""
            :title="item.name"
            :description="item.remark"
            hide-actions
            @click="handleDetail(item)"
          />
        </template>
        <customizeRenderEmpty v-else />
      </div>
    </a-spinning>
  </div>
</template>

<style lang="less" scoped>
.scene-wrapper {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
}

.add-box {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s;
  min-height: 130px;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }
}
</style>
