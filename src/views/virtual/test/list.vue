<script lang="ts" setup>
import BasicTablePlus from '@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import {
  V1ManageCommonUserIdName,
  V1ManageDictDataListPost,
  V1ManageSceneExamPagePost,
  V1ManageScenesPagePost,
} from '/@/api/cddc.req';
import { DictType } from '/@/enums/dictEnum';
import { VirtualTestQualifiedOptions } from '/@/enums/virtualTestEnum';
import { convertDateRange } from '/@/utils/dateUtil';
import dayjs from 'dayjs';
import { getDictLabel } from '/@/dict';
import { useAsyncData } from '/@/composables';

const dictOptionsApi = useAsyncData(
  () => V1ManageDictDataListPost({ dictType: DictType.SCENE_QUESTION }),
  []
);

dictOptionsApi.load();

const columns: EnhancedColumn[] = [
  {
    title: '场景',
    dataIndex: 'sceneName',
    width: 200,
    search: {
      component: 'Input',
    },
  },
  {
    title: '问题',
    dataIndex: 'questionName',
    width: 120,
    search: {
      field: 'questionName',
      component: 'Input',
      componentProps: {},
    },
  },
  {
    title: '问题类型',
    dataIndex: 'type',
    width: 120,
    customRender({ text }) {
      return dictOptionsApi.data.value.find((n) => n.dictValue == text)?.dictLabel;
    },
    search: {
      field: 'type',
      component: 'Select',
      componentProps: {
        options: dictOptionsApi.data,
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
        showSearch: true,
        optionFilterProp: 'dictLabel',
      },
    },
  },
  {
    title: '答题人',
    dataIndex: 'answeredBy',
    width: 80,
    search: {
      field: 'userName',
      component: 'Input',
    },
  },
  {
    title: '状态',
    dataIndex: 'qualified',
    width: 160,
    customRender({ text }) {
      return getDictLabel(VirtualTestQualifiedOptions, text ? 1 : 0);
    },
    search: {
      component: 'Select',
      componentProps: {
        options: VirtualTestQualifiedOptions,
      },
    },
  },
  {
    title: '答题时间',
    dataIndex: 'answeredAt',
    width: 160,
    search: {
      component: 'RangePicker',
    },
  },
];

const [registerTable, _tableActions] = useTable({
  columns: columns,
  api: (params) => {
    const data = params.data || {};

    if (data.answeredAt?.length) {
      const d = data.answeredAt.map((date) => dayjs(date));
      const t = convertDateRange(d);

      params.data = {
        ...data,
        ...t,
      };
    }

    return V1ManageSceneExamPagePost(params);
  },
  tableProps: {
    showIndexColumn: true,
    actionColumn: {
      ifShow: false,
    },
  },
  formConfig: {
    labelWidth: '4em',
    labelAlign: 'right',
    baseColProps: { span: 6 },
  },
});
</script>

<template>
  <div class="py-2">
    <BasicTablePlus @register="registerTable"> </BasicTablePlus>
  </div>
</template>

<style lang="less" scoped></style>
