// 大屏测试数据
export const testData = {
  action: 'push',
  actionRate: '100.00', //动作达标率
  detailId: '1868952250275028993', //培训ID
  nonStandardNum: 0, //不达标次数
  operationLog: {
    actionType: 0, //动作类别： 1-双手持枪，2-垂直作业面，3-拧紧贴合， 4-扳手绿灯
    detailId: '1868952250275028993',
    id: '1869316662099808257',
    actionLabels: [
      {
        actionType: 51,
        color: 0,
        desc: '安装完成未回拔',
      },
    ],
    nodeId: '1869316662099808257',
    operationTime: '2024-12-18 17:43:40.578442',
    result: 1, //合格情况： 0-不合格， 1-合格， 2-仅动作不达标
  },
  qualificationNum: 1, //合格次数
  qualificationRate: '100.00', //作业合格率
  requestActionRate: '1', //目标达标率
  requestQualificationRate: '1', //目标合格率
  standardNum: 1, //达标次数
  totalDuration: 9, //当前培训总时长(秒)
  unqualifiedNum: 0, //不合格次数
  userId: 111111, //用户ID
  userName: '张三', //用户名
  locationId: 2222222, //工位ID
  locationName: '工位1', //工位名
  videoUrl: 'rtsp://*************:8554/stream', //视频流地址
};

export const closeData = {
  action: 'locationClose',
  locationId: 2222222,
};

// 工位使用统计测试数据 - 5个工位，3种项目类型（训练、考核、比赛）
export const workstationStatsTestData = [
  // 训练作业次数 (projectType: 1)
  [
    { abscissa: '工位A', ordinate: 45 },
    { abscissa: '工位B', ordinate: 38 },
    { abscissa: '工位C', ordinate: 52 },
    { abscissa: '工位D', ordinate: 41 },
    { abscissa: '工位E', ordinate: 37 },
  ],
  // 考核作业次数 (projectType: 2)
  [
    { abscissa: '工位A', ordinate: 28 },
    { abscissa: '工位B', ordinate: 34 },
    { abscissa: '工位C', ordinate: 31 },
    { abscissa: '工位D', ordinate: 25 },
    { abscissa: '工位E', ordinate: 29 },
  ],
  // 比赛作业次数 (projectType: 3)
  [
    { abscissa: '工位A', ordinate: 15 },
    { abscissa: '工位B', ordinate: 18 },
    { abscissa: '工位C', ordinate: 22 },
    { abscissa: '工位D', ordinate: 16 },
    { abscissa: '工位E', ordinate: 20 },
  ],
];
// 训练人数测试数据
export const trainingTrendTestData = [
  {
    abscissa: '2024-12-01',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '12',
  },
  {
    abscissa: '2024-12-02',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '15',
  },
  {
    abscissa: '2024-12-03',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '8',
  },
  {
    abscissa: '2024-12-04',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '23',
  },
  {
    abscissa: '2024-12-05',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '18',
  },
  {
    abscissa: '2024-12-06',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '31',
  },
  {
    abscissa: '2024-12-07',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '27',
  },
  {
    abscissa: '2024-12-08',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '19',
  },
  {
    abscissa: '2024-12-09',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '42',
  },
  {
    abscissa: '2024-12-10',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '35',
  },
  {
    abscissa: '2024-12-11',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '29',
  },
  {
    abscissa: '2024-12-12',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '46',
  },
  {
    abscissa: '2024-12-13',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '38',
  },
  {
    abscissa: '2024-12-14',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '52',
  },
  {
    abscissa: '2024-12-15',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '44',
  },
  {
    abscissa: '2024-12-16',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '33',
  },
  {
    abscissa: '2024-12-17',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '58',
  },
  {
    abscissa: '2024-12-18',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '49',
  },
  {
    abscissa: '2024-12-19',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '37',
  },
  {
    abscissa: '2024-12-20',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '61',
  },
  {
    abscissa: '2024-12-21',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '45',
  },
  {
    abscissa: '2024-12-22',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '54',
  },
  {
    abscissa: '2024-12-23',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '39',
  },
  {
    abscissa: '2024-12-24',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '66',
  },
  {
    abscissa: '2024-12-25',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '71',
  },
  {
    abscissa: '2024-12-26',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '48',
  },
  {
    abscissa: '2024-12-27',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '55',
  },
  {
    abscissa: '2024-12-28',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '62',
  },
  {
    abscissa: '2024-12-29',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '47',
  },
  {
    abscissa: '2024-12-30',
    ext1: null,
    ext2: null,
    ext3: null,
    ordinate: '69',
  },
];

// 5个工位视频轮播测试数据
export const workstationVideoTestData = [
  {
    id: 'station_001',
    status: '训练中',
    userName: '张三',
    locationName: '工位A',
    imageSrc: '/src/assets/svg/point/pipeline-bg.png',
    duration: '15min32s',
    taskCount: '28次',
    qualifiedRate: '92.86%',
    effectiveRate: '89.29%',
    logInfo: '双手持枪姿势标准',
    result: 1, // 合格
    videoUrl: 'rtsp://*************:8554/stream1',
  },
  {
    id: 'station_002',
    status: '训练中',
    userName: '李四',
    locationName: '工位B',
    imageSrc: '/src/assets/svg/point/pipeline-bg.png',
    duration: '23min18s',
    taskCount: '35次',
    qualifiedRate: '85.71%',
    effectiveRate: '91.43%',
    logInfo: '垂直作业面需调整',
    result: 2, // 仅动作不达标
    videoUrl: 'rtsp://*************:8554/stream2',
  },
  {
    id: 'station_003',
    status: '训练中',
    userName: '王五',
    locationName: '工位C',
    imageSrc: '/src/assets/svg/point/pipeline-bg.png',
    duration: '8min45s',
    taskCount: '12次',
    qualifiedRate: '75.00%',
    effectiveRate: '83.33%',
    logInfo: '拧紧力度不足',
    result: 0, // 不合格
    videoUrl: 'rtsp://*************:8554/stream3',
  },
  {
    id: 'station_004',
    status: '训练中',
    userName: '赵六',
    locationName: '工位D',
    imageSrc: '/src/assets/svg/point/pipeline-bg.png',
    duration: '31min07s',
    taskCount: '42次',
    qualifiedRate: '95.24%',
    effectiveRate: '97.62%',
    logInfo: '操作规范，表现优秀',
    result: 1, // 合格
    videoUrl: 'rtsp://*************:8554/stream4',
  },
  {
    id: 'station_005',
    status: '训练中',
    userName: '孙七',
    locationName: '工位E',
    imageSrc: '/src/assets/svg/point/pipeline-bg.png',
    duration: '19min53s',
    taskCount: '26次',
    qualifiedRate: '88.46%',
    effectiveRate: '92.31%',
    logInfo: '扳手绿灯确认及时',
    result: 1, // 合格
    videoUrl: 'rtsp://*************:8554/stream5',
  },
];
