<template>
  <div class="bottom-panel">
    <div class="chart-container">
      <div class="chart-title">合格率趋势图</div>
      <div class="chart-content">
        <e-charts
          ref="qualifiedRateChart"
          :option="qualifiedRateOptions"
          autoresize
          v-if="qualifiedRateData.length"
        />
        <geega-empty v-if="!qualifiedRateData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">达标率趋势图</div>
      <div class="chart-content">
        <e-charts
          ref="achievementRateChart"
          :option="achievementRateOptions"
          autoresize
          v-if="actionDefectData.length"
        />
        <geega-empty v-if="!actionDefectData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">TOP排名 - {{ currProjectName }}</div>
      <div
        class="ranking-list"
        :class="{
          'no-data': rankingData.length === 0,
          'two-columns': rankingData.length > 5,
        }"
      >
        <div class="ranking-item" v-for="(item, index) in rankingData" :key="index">
          <div class="rank-no" :class="{ trophy: index < 3 }">
            <span v-if="index >= 3">{{ index + 1 }}</span>
          </div>
          <div class="rank-values">
            <div class="rank-name">{{ item.userName }}</div>
            <div class="rank-score" v-if="item.value !== undefined">{{
              formatScore(item.value)
            }}</div>
          </div>
        </div>
        <geega-empty v-if="!rankingData.length" description="暂无数据" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import type { APIModels } from '/@/api/cddc.model';
import {
  V1ManageLargeScreenChartActionPassRatePost,
  V1ManageLargeScreenChartPassRatePost,
  V1ManageLargeScreenProjectPost,
  V1ManageTrainStudyRecordsRaceRankProjectId,
} from '/@/api/cddc.req';
import type { EChartsOption } from 'echarts';
import ECharts from 'vue-echarts';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';

const props = defineProps({
  selectedCraft: {
    type: String,
    default: 'tighten',
  },
  refreshInterval: {
    type: Number,
    default: 5 * 60 * 1000, // 默认5分钟
  },
});

/**
 * 合格率趋势图数据
 */
const qualifiedRateData = ref<APIModels['V1ManageLargeScreenChartPassRatePostResponse']>([]);

/**
 * 动作缺陷图数据
 */
const actionDefectData = ref<APIModels['V1ManageLargeScreenChartActionDefectPostResponse']>([]);
/**
 * TOP排名数据
 */
const rankingData = ref<APIModels['V1ManageTrainStudyRecordsRaceRankProjectIDGetResponse']>([]);

/**
 * 排名更新定时器实例
 */
let rankingRefreshTimer: NodeJS.Timeout | null = null;
/**
 * 当前项目索引
 */
const currentProjectIndex = ref(0);

/**
 * 当前正在显示的项目信息
 */
const currentDisplayProject = ref<
  APIModels['V1ManageCommonActionTypeEnumGetResponseResult'] | null
>(null);
/**
 * 存储的项目列表
 */
const storedProjectList = ref<APIModels['V1ManageCommonActionTypeEnumGetResponseResult'][]>([]);
const currProjectName = ref('');
/**
 * 获取合格率趋势图数据
 */
const getQualifiedRateData = async () => {
  try {
    const res = await V1ManageLargeScreenChartPassRatePost({ type: props.selectedCraft });
    qualifiedRateData.value = res || [];
  } catch (error) {
    console.error('获取合格率趋势图数据失败', error);
  }
};

/**
 * 获取动作缺陷图数据
 */
const getActionDefectData = async () => {
  try {
    const res = await V1ManageLargeScreenChartActionPassRatePost({ type: props.selectedCraft });
    actionDefectData.value = res || [];
  } catch (error) {
    console.error('获取动作缺陷图数据失败', error);
  }
};
let refreshTimer: number | null = null;
/**
 * 刷新所有图表数据
 */
const refreshChartData = () => {
  getQualifiedRateData();
  getActionDefectData();
};
/**
 * 启动刷新定时器
 */
const startRefreshTimer = () => {
  if (refreshTimer) {
    stopRefreshTimer();
  }
  refreshTimer = setInterval(refreshChartData, props.refreshInterval) as unknown as number;
};
/**
 * 停止刷新定时器
 */
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};
onMounted(() => {
  refreshChartData(); // Initial data fetch
  startRefreshTimer();
  startRankingRefreshTimer(); // 启动排名更新定时器
});

onUnmounted(() => {
  stopRefreshTimer();
  stopRankingRefreshTimer(); // 组件卸载时清除排名更新定时器
});

watch(
  () => props.selectedCraft,
  () => {
    refreshChartData();
    stopRankingRefreshTimer();
    // 工艺变更时重新启动排名更新定时器，会重新获取项目列表
    startRankingRefreshTimer();
  }
);

/**
 * 将秒数转换为分秒格式
 * @param seconds 秒数
 * @returns 格式化的时间字符串
 */
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    return `${remainingSeconds}秒`;
  }
};

/**
 * 根据项目类型格式化分数显示
 * @param score 分数
 * @returns 格式化的分数字符串
 */
const formatScore = (value: number): string => {
  if (!currentDisplayProject.value) {
    return `${value}`;
  }

  const raceType = currentDisplayProject.value.raceType;

  if (raceType === 1) {
    return `${value}次`;
  } else if (raceType === 2) {
    return formatTime(value);
  } else {
    return `${value}`;
  }
};
/**
 * 启动排名更新定时器 - 每2分钟更新一次
 */
const startRankingRefreshTimer = async () => {
  if (rankingRefreshTimer) {
    clearInterval(rankingRefreshTimer);
  }

  try {
    // 获取项目列表并存储
    const projectList = await V1ManageLargeScreenProjectPost({
      type: props.selectedCraft,
      projectType: 3,
    });

    // 存储项目列表
    storedProjectList.value = projectList || [];
    // 重置项目索引
    currentProjectIndex.value = 0;

    // 如果有项目列表，立即加载第一个项目的排名数据
    if (storedProjectList.value.length > 0) {
      await refreshRankingData();
    }

    // 设置定时器，每2分钟循环切换项目展示排名
    rankingRefreshTimer = setInterval(
      async () => {
        try {
          await refreshRankingData();
        } catch (error) {
          console.error('定时更新排名数据失败', error);
        }
      },
      2 * 60 * 1000
    ); // 2分钟
  } catch (error) {
    console.error('获取项目列表失败', error);
  }
};

/**
 * 清除排名更新定时器
 */
const stopRankingRefreshTimer = () => {
  if (rankingRefreshTimer) {
    clearInterval(rankingRefreshTimer);
    rankingRefreshTimer = null;
  }
};
/**
 * 刷新排名列表数据
 * 从存储的项目列表中根据当前索引获取项目信息
 */
const refreshRankingData = async () => {
  // 只有当存储的项目列表不为空时才处理
  if (storedProjectList.value && storedProjectList.value.length > 0) {
    // 确保索引在有效范围内
    if (currentProjectIndex.value >= storedProjectList.value.length) {
      currentProjectIndex.value = 0; // 重置为第一个
    }

    // 获取当前索引对应的项目信息
    const currentProject = storedProjectList.value[currentProjectIndex.value];
    const projectId = currentProject.id;
    currProjectName.value = currentProject.name;

    // 保存当前正在显示的项目信息
    currentDisplayProject.value = currentProject;

    try {
      const rankingRes = await V1ManageTrainStudyRecordsRaceRankProjectId({
        projectId,
      });
      // 如果数据超过10条，只取前10条
      rankingData.value = (rankingRes || []).slice(0, 10);

      // 切换到下一个项目索引，实现循环
      currentProjectIndex.value = (currentProjectIndex.value + 1) % storedProjectList.value.length;
    } catch (error) {
      console.error('获取排名数据失败', error);
    }
  }
};
/**
 * 合格率趋势图配置
 */
const qualifiedRateOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    formatter: (params: any) => {
      let res = params[0].name + '<br/>';
      for (let i = 0; i < params.length; i++) {
        res += params[i].marker + '&nbsp;&nbsp;&nbsp;' + params[i].value + '%<br/>';
      }
      return res;
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  grid: {
    left: '5%',
    right: '5%',
    bottom: '8%',
    top: '8%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: qualifiedRateData.value?.map((item) => item.abscissa) || [],
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: string) {
        return value.substring(value.indexOf('-') + 1);
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.1)',
      },
    },
  },
  series: [
    {
      data: qualifiedRateData.value?.map((item) => item.ordinate) || [],
      type: 'line',
      smooth: true,
      symbolSize: 6,
      itemStyle: {
        color: '#14C9C9',
        borderColor: '#fff',
        borderWidth: 2,
      },
      lineStyle: {
        color: '#14C9C9',
        width: 3,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(20, 201, 201, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(20, 201, 201, 0)',
            },
          ],
        },
      },
    },
  ],
}));

/**
 * 达标率趋势图配置
 */
const achievementRateOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    formatter: (params: any) => {
      let res = params[0].name + '<br/>';
      for (let i = 0; i < params.length; i++) {
        res += params[i].marker + '&nbsp;&nbsp;&nbsp;' + params[i].value + '%<br/>';
      }
      return res;
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  grid: {
    left: '5%',
    right: '5%',
    bottom: '8%',
    top: '8%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: actionDefectData.value?.map((item) => item.abscissa) || [],
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: string) {
        return value.substring(value.indexOf('-') + 1);
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.1)',
      },
    },
  },
  series: [
    {
      data: actionDefectData.value?.map((item) => item.ordinate) || [],
      type: 'line',
      smooth: true,
      symbolSize: 6,
      itemStyle: {
        color: '#DBB336',
        borderColor: '#fff',
        borderWidth: 2,
      },
      lineStyle: {
        color: '#DBB336',
        width: 3,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(219, 179, 54, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(219, 179, 54, 0)',
            },
          ],
        },
      },
    },
  ],
}));
</script>

<style lang="less" scoped>
.bottom-panel {
  height: 100%;
  display: flex;
  gap: 17px;
  .chart-container {
    border-radius: 3px;
    flex: 1;
    width: 33.33%;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(64, 158, 255, 0.4);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
    }

    .chart-title {
      font-size: 21px;
      font-weight: bold;
      margin-bottom: 9px;
      color: #fff;
      padding-left: 30px;
    }

    .chart-content {
      height: calc(100% - 45px);
      display: flex;
      justify-content: center;
      // ECharts 样式优化
      :deep(.echarts) {
        .echarts-tooltip {
          background: rgba(1, 22, 39, 0.95) !important;
          border: 1px solid rgba(64, 158, 255, 0.5) !important;
          border-radius: 4px !important;
          color: #fff !important;
        }
      }
    }
    .ranking-list {
      height: calc(100% - 45px);
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px;
      overflow-y: auto;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(64, 158, 255, 0.5);
        border-radius: 2px;

        &:hover {
          background: rgba(64, 158, 255, 0.8);
        }
      }

      &.no-data {
        align-items: center;
        justify-content: center;
      }

      // 两栏布局样式
      &.two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px 10px;
        padding: 8px 10px;

        .ranking-item {
          margin: 0;
          padding: 1px 8px;
          gap: 12px;
          font-size: 13px;

          .rank-no {
            width: 14px;
            height: 14px;
            font-size: 11px;
            margin-left: 2px;

            &.trophy {
              width: 20px;
              height: 20px;
              margin-left: 0;
            }
          }

          .rank-values {
            gap: 6px;

            .rank-name {
              font-size: 13px;
            }

            .rank-score {
              font-size: 13px;
            }
          }
        }
      }

      .ranking-item {
        display: flex;
        align-items: center;
        padding: 1.5px 12px;
        gap: 16px;
        color: #fff;
        font-size: 14px;
        border-radius: 6px;
        transition: all 0.3s ease;
        min-width: 0;

        .rank-no {
          width: 16px;
          height: 16px;
          padding: 0px 5px 0px 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          border-radius: 4px;
          background: #263854;
          color: #fff;
          margin-left: 5px;
          font-size: 12px;

          &.trophy {
            background-size: 100% 100%;
            width: 24px;
            height: 24px;
            margin-left: 0;
            span {
              display: none;
            }
          }
        }

        &:nth-child(1) .rank-no.trophy {
          background-image: url('@/assets/icons/rank-1.png');
        }

        &:nth-child(2) .rank-no.trophy {
          background-image: url('@/assets/icons/rank-2.png');
        }

        &:nth-child(3) .rank-no.trophy {
          background-image: url('@/assets/icons/rank-3.png');
        }

        .rank-values {
          display: flex;
          flex: 1;
          justify-content: space-between;
          align-items: center;
          gap: 8px;
          min-width: 0;

          .rank-name {
            flex: 1;
            min-width: 0;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: rgba(255, 255, 255, 0.9);
          }

          .rank-score {
            font-weight: 600;
            color: #fff;
            border-radius: 10px;
            font-size: 14px;
            white-space: nowrap;
            min-width: fit-content;
            text-align: center;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
</style>
