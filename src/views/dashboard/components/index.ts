/**
 * 导出数字大屏的三个面板组件
 */
export { default as LeftPanel } from './LeftPanel.vue';
export { default as CenterPanel } from './CenterPanel.vue';
export { default as RightPanel } from './RightPanel.vue';
export { default as BottomPanel } from './BottomPanel.vue';

// 技能训练统计渐变色配置：五组渐变色
export const trainingGradientColors = [
  { start: '#93F1FF', end: '#93F1FF' }, // 浅青色（单色，起始和结束相同）
  { start: '#2A3998', end: '#6878EF' }, // 深蓝到浅蓝渐变
  { start: '#E2BE2D', end: '#CEA241' }, // 金黄渐变
  { start: '#FE1827', end: '#FE5F8D' }, // 红到粉红渐变
  { start: '#0935AA', end: '#4A90E2' }, // 深蓝渐变
];
// 技能考核统计渐变色配置：五组渐变色
export const assessmentGradientColors = [
  { start: '#DC6725', end: '#E3B338' }, // 橙黄渐变
  { start: '#225B68', end: '#3EE2E5' }, // 深青到浅青渐变
  { start: '#2276FC', end: '#1358C4' }, // 蓝色渐变
  { start: '#107494', end: '#0990AE' }, // 深青蓝渐变
  { start: '#8B5CF6', end: '#A855F7' }, // 紫色渐变
];
// 工位使用统计渐变色配置：三组渐变色
export const workstationGradientColors = [
  { start: '#79C1FF', end: '#3666E2' }, // 蓝色渐变
  { start: '#FFA66E', end: '#FE8337' }, // 橙色渐变
  { start: '#94F2FF', end: '#08C6E2' }, // 青色渐变
  { start: '#FE1827', end: '#FE5F8D' }, // 红到粉红渐变
  { start: '#0935AA', end: '#4A90E2' }, // 深蓝渐变
];
