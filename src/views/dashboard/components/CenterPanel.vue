<template>
  <div class="center-panel">
    <!-- 2x2网格布局显示4个工位 -->
    <div class="workstations-grid">
      <div class="workstation-item" v-for="index in 4" :key="index">
        <!-- 有数据的工位 -->
        <template v-if="displayedWorkstations[index - 1]">
          <!-- 工位视频区域 -->
          <div class="workstation-video">
            <!-- WebRTC视频播放组件 -->
            <WebRTCPlayer
              v-if="displayedWorkstations[index - 1].videoUrl"
              :key="`${displayedWorkstations[index - 1].id}-${displayedWorkstations[index - 1].videoUrl}-${rotationOffset}`"
              :video-url="displayedWorkstations[index - 1].videoUrl"
              :video-id="`video-${displayedWorkstations[index - 1].id}-${rotationOffset}`"
              class="training-video"
              showStatusTag
              :status-result="displayedWorkstations[index - 1].result"
              @connected="handleVideoConnected(displayedWorkstations[index - 1].id)"
              @disconnected="handleVideoDisconnected(displayedWorkstations[index - 1].id)"
              @error="handleVideoError(displayedWorkstations[index - 1].id)"
            />
            <!-- 备用图片显示 -->
            <img
              v-else
              :src="displayedWorkstations[index - 1].imageSrc"
              alt="训练影像"
              class="training-image"
            />
          </div>

          <!-- 用户信息区域 -->
          <div class="workstation-info">
            <div class="user-info-row">
              <div class="user-details">
                <span class="user-name">{{ displayedWorkstations[index - 1].userName }}</span>
                <span class="user-status">{{ displayedWorkstations[index - 1].status }}</span>
              </div>
              <div class="log-info">
                <div
                  class="status-dot"
                  :class="formatResultClass(displayedWorkstations[index - 1].result)"
                ></div>
                <span class="log-text">{{ displayedWorkstations[index - 1].logInfo }}</span>
              </div>
            </div>
          </div>

          <!-- 底部统计信息 -->
          <div class="workstation-stats">
            <div class="stat-item">
              <span class="stat-label">训练时长</span>
              <span class="stat-value">{{ displayedWorkstations[index - 1].duration }}</span>
            </div>
            <div class="stat-separator">|</div>
            <div class="stat-item">
              <span class="stat-label">作业次数</span>
              <span class="stat-value">{{ displayedWorkstations[index - 1].taskCount }}</span>
            </div>
            <div class="stat-separator">|</div>
            <div class="stat-item">
              <span class="stat-label">合格率</span>
              <span class="stat-value">{{ displayedWorkstations[index - 1].qualifiedRate }}</span>
            </div>
            <div class="stat-separator">|</div>
            <div class="stat-item">
              <span class="stat-label">有效率</span>
              <span class="stat-value">{{ displayedWorkstations[index - 1].effectiveRate }}</span>
            </div>
          </div>
        </template>

        <!-- 没有数据的工位显示暂无数据 -->
        <template v-else>
          <div class="empty-workstation">
            <geega-empty description="暂无数据" />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type PropType, ref, computed, onMounted, onUnmounted, watch } from 'vue';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import WebRTCPlayer from './WebRTCPlayer.vue';
import { useGlobSetting } from '/@/hooks/setting';

interface WorkstationItem {
  id: string | number;
  status: string;
  userName: string;
  imageSrc: string;
  duration: string;
  taskCount: string;
  qualifiedRate: string;
  effectiveRate: string;
  locationName: string; // 工位名
  result: number; // Add result property
  logInfo: string; // Add logInfo property
  videoUrl: string; // Add videoUrl property(rtsp)
}

const props = defineProps({
  /**
   * 工位数据列表
   */
  workstations: {
    type: Array as PropType<WorkstationItem[]>, // 明确指定类型
    default: () => [],
  },
});

// 获取全局配置
const { videoPanelRotationMinutes } = useGlobSetting();

// 轮播相关状态
const rotationOffset = ref(0); // 轮播偏移量
const rotationTimer = ref<NodeJS.Timeout | null>(null); // 轮播定时器

// 固定显示4个工位
const DISPLAY_COUNT = 4;

// 是否需要轮播（工位数量超过4个）
const needRotation = computed(() => {
  return props.workstations.length > DISPLAY_COUNT;
});

// 计算当前显示的工位数据（循环轮播）
const displayedWorkstations = computed(() => {
  if (!needRotation.value) {
    // 如果不需要轮播，直接返回前4个工位
    return props.workstations.slice(0, DISPLAY_COUNT);
  }

  const result: WorkstationItem[] = [];
  const totalWorkstations = props.workstations.length;

  // 从当前偏移量开始，循环取4个工位
  for (let i = 0; i < DISPLAY_COUNT; i++) {
    const index = (rotationOffset.value + i) % totalWorkstations;
    if (props.workstations[index]) {
      result.push(props.workstations[index]);
    }
  }

  return result;
});

// 轮播到下一组
const rotateToNext = () => {
  if (needRotation.value && props.workstations.length > 0) {
    rotationOffset.value = (rotationOffset.value + 1) % props.workstations.length;
  }
};

// 启动轮播定时器
const startRotationTimer = () => {
  stopRotationTimer(); // 先清除现有定时器

  if (needRotation.value && videoPanelRotationMinutes) {
    const rotationInterval = Number(videoPanelRotationMinutes) * 60 * 1000; // 转换为毫秒
    if (rotationInterval > 0) {
      rotationTimer.value = setInterval(rotateToNext, rotationInterval);
    }
  }
};

// 停止轮播定时器
const stopRotationTimer = () => {
  if (rotationTimer.value) {
    clearInterval(rotationTimer.value);
    rotationTimer.value = null;
  }
};

// 监听工位数据变化，重新计算轮播
watch(
  () => props.workstations,
  () => {
    // 重置轮播偏移量
    rotationOffset.value = 0;
    // 重新启动轮播定时器
    startRotationTimer();
  },
  { deep: true }
);

// 组件挂载时启动轮播
onMounted(() => {
  startRotationTimer();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopRotationTimer();
});

/**
 * 定义组件事件
 */
const emit = defineEmits([
  'update:workstations',
  'video-connected',
  'video-disconnected',
  'video-error',
]);

/**
 * @description 根据结果编码返回对应的 CSS 类名
 * @param {number} result - 结果编码
 * @returns {string} 对应的 CSS 类名 (error, success, warning)
 */
const formatResultClass = (result: number): string => {
  switch (result) {
    case 0:
      return 'error'; // 不合格：红色
    case 1:
      return 'success'; // 合格：绿色
    case 2:
      return 'warning'; // 仅动作不达标：橙色
    default:
      return '';
  }
};



/**
 * 视频连接事件处理
 */
const handleVideoConnected = (stationId: string | number) => {
  console.log(`工位 ${stationId} 视频连接成功`);
  emit('video-connected', stationId);
};

const handleVideoDisconnected = (stationId: string | number) => {
  console.log(`工位 ${stationId} 视频连接断开`);
  emit('video-disconnected', stationId);
};

const handleVideoError = (stationId: string | number) => {
  console.error(`工位 ${stationId} 视频连接错误`);
  emit('video-error', stationId);
};
</script>

<style lang="less" scoped>
.center-panel {
  width: 100%;
  height: 100%;
  padding: 4px 4px 4px 5px;
  overflow: hidden;

  .workstations-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    height: 100%;

    .workstation-item {
      border: 1px solid rgba(137, 162, 255, 0.2);
      border-radius: 4px;
      overflow: hidden;
      backdrop-filter: blur(10px);
      box-shadow: none;
      transition: all 0.3s ease;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        border-color: rgba(137, 162, 255, 0.5);
        box-shadow: none;
      }

      .workstation-video {
        flex: 1;
        height: 73%;
        display: flex;
        flex-direction: column;

        .training-image,
        .training-video {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px 4px 0 0;
          border: none;
        }
      }

      .workstation-info {
        padding: 8px 12px;
        background: rgba(13, 21, 51, 0.4);
        border-top: 1px solid rgba(137, 162, 255, 0.1);
        flex-shrink: 0;

        .user-info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 12px;

          .user-details {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;

            .user-name {
              font-size: 14px;
              font-weight: 500;
              color: #fff;
            }

            .user-status {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              padding: 2px 8px;
              background: rgba(45, 153, 255, 0.2);
              border-radius: 2px;
              border: 1px solid rgba(45, 153, 255, 0.3);
            }
          }

          .log-info {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 0; // 允许文本溢出处理

            .status-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              flex-shrink: 0;

              &.success {
                background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);
              }

              &.warning {
                background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
                box-shadow: 0 0 4px rgba(250, 173, 20, 0.6);
              }

              &.error {
                background: linear-gradient(135deg, #f5222d 0%, #cf1322 100%);
                box-shadow: 0 0 4px rgba(245, 34, 45, 0.6);
              }
            }

            .status-text {
              font-size: 12px;
              font-weight: 500;
              flex-shrink: 0;

              &.success {
                color: #00996b;
              }

              &.warning {
                color: #faad14;
              }

              &.error {
                color: #f5222d;
              }
            }

            .log-text {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
              line-height: 1.4;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      .workstation-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0px 8px 0px;
        background: rgba(13, 21, 51, 0.8);
        border-top: 1px solid rgba(137, 162, 255, 0.2);
        gap: 12px;
        flex-shrink: 0;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          flex: 1;

          .stat-label {
            --ignore-color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            color: var(--ignore-color);
            margin-bottom: 2px;
          }

          .stat-value {
            --ignore-color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 600;
            color: var(--ignore-color);
          }
        }

        .stat-separator {
          color: rgba(255, 255, 255, 0.3);
          font-size: 12px;
          margin: 0 4px;
        }
      }

      // 空工位样式
      .empty-workstation {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px dashed rgba(137, 162, 255, 0.3);
        border-radius: 4px;
      }
    }
  }
}

// 响应式调整
@media (max-width: 1600px) {
  .center-panel {
    .workstations-grid {
      .workstation-item {
        .workstation-info {
          padding: 8px 12px;

          .user-info-row {
            gap: 8px;

            .user-details {
              gap: 6px;

              .user-name {
                font-size: 13px;
              }

              .user-status {
                font-size: 11px;
              }
            }

            .log-info {
              gap: 6px;

              .status-text {
                font-size: 11px;
              }

              .log-text {
                font-size: 11px;
              }
            }
          }
        }

        .workstation-stats {
          padding: 8px 12px;
          gap: 8px;

          .stat-item {
            .stat-label {
              font-size: 10px;
            }

            .stat-value {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .center-panel {
    .workstations-grid {
      .workstation-item {
        .workstation-info {
          padding: 6px 8px;

          .user-info-row {
            gap: 6px;

            .user-details {
              gap: 4px;

              .user-name {
                font-size: 12px;
              }

              .user-status {
                font-size: 10px;
                padding: 1px 6px;
              }
            }

            .log-info {
              gap: 4px;

              .status-text {
                font-size: 10px;
              }

              .log-text {
                font-size: 10px;
              }
            }
          }
        }

        .workstation-stats {
          padding: 6px 8px;
          gap: 6px;

          .stat-item {
            .stat-label {
              font-size: 9px;
            }

            .stat-value {
              font-size: 11px;
            }
          }

          .stat-separator {
            font-size: 10px;
          }
        }
      }
    }
  }
}
</style>
