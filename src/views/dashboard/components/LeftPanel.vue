<template>
  <div class="left-panel">
    <div class="chart-container">
      <div class="chart-title">训练人数趋势图</div>
      <div class="chart-content">
        <e-charts
          ref="trainingTrendChart"
          :option="trainingTrendOptions"
          autoresize
          v-if="trainingTrendData.length"
        />
        <geega-empty v-if="!trainingTrendData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">训练场次状态图</div>
      <div class="chart-content">
        <e-charts
          ref="trainingSessionChart"
          :option="trainingSessionOptions"
          autoresize
          v-if="trainingSessionData.length"
        />
        <geega-empty v-if="!trainingSessionData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">作业次数趋势图</div>
      <div class="chart-content">
        <e-charts
          ref="workTrendChart"
          :option="workTrendOptions"
          autoresize
          v-if="jobTrendData.length"
        />
        <geega-empty v-if="!jobTrendData.length" description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import {
  V1ManageLargeScreenChartTrainUserPost,
  V1ManageLargeScreenChartTrainNumPost,
  V1ManageLargeScreenChartJobNumPost,
} from '/@/api/cddc.req';
import type { APIModels } from '/@/api/cddc.model';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import type { EChartsOption } from 'echarts';
import ECharts from 'vue-echarts';

const props = defineProps({
  selectedCraft: {
    type: String,
    default: 'tighten',
  },
  refreshInterval: {
    type: Number,
    default: 5 * 60 * 1000, // 默认5分钟
  },
});

/**
 * 作业次数趋势图数据
 */
const jobTrendData = ref<APIModels['V1ManageLargeScreenChartJobNumPostResponse']>([]);

/**
 * 训练人数趋势图配置
 */
const trainingTrendOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  grid: {
    left: '5%',
    right: '5%',
    bottom: '8%',
    top: '8%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: trainingTrendData.value?.map((item) => item.abscissa) || [],
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: string) {
        return value.substring(value.indexOf('-') + 1);
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: number) {
        return Math.floor(value).toString();
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.1)',
      },
    },
  },
  series: [
    {
      data: trainingTrendData.value?.map((item) => item.ordinate) || [],
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#79C1FF' },
            { offset: 1, color: '#3666E2' },
          ],
        },
      },
    },
  ],
}));

/**
 * 训练人数趋势图数据
 */
const trainingTrendData = ref<APIModels['V1ManageLargeScreenChartTrainUserPostResponse']>([]);

/**
 * 训练场次状态图数据
 */
const trainingSessionData = ref<APIModels['V1ManageLargeScreenChartTrainNumPostResponse']>([]);

/**
 * 获取训练人数趋势图数据
 */
const getTrainingTrendData = async () => {
  try {
    const res = await V1ManageLargeScreenChartTrainUserPost({ type: props.selectedCraft });
    trainingTrendData.value = res || [];
  } catch (error) {
    console.error('获取训练人数趋势图数据失败', error);
  }
};

let refreshTimer: number | null = null;
/**
 * 刷新所有图表数据
 */
const refreshChartData = () => {
  getTrainingTrendData();
  getTrainingSessionData();
  getJobTrendData();
};
/**
 * 启动刷新定时器
 */
const startRefreshTimer = () => {
  if (refreshTimer) {
    stopRefreshTimer();
  }
  refreshTimer = setInterval(refreshChartData, props.refreshInterval) as unknown as number;
};

/**
 * 停止刷新定时器
 */
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

onMounted(() => {
  refreshChartData(); // Initial data fetch
  startRefreshTimer();
});

onUnmounted(() => {
  stopRefreshTimer();
});

watch(
  () => props.selectedCraft,
  () => {
    refreshChartData();
  }
);

/**
 * 获取训练场次状态图数据
 */
const getTrainingSessionData = async () => {
  try {
    const res = await V1ManageLargeScreenChartTrainNumPost({ type: props.selectedCraft });
    trainingSessionData.value = res || [];
  } catch (error) {
    console.error('获取训练场次状态图数据失败', error);
  }
};

/**
 * 获取作业次数趋势图数据
 */
const getJobTrendData = async () => {
  try {
    const res = await V1ManageLargeScreenChartJobNumPost({ type: props.selectedCraft });
    jobTrendData.value = res || [];
  } catch (error) {
    console.error('获取作业次数趋势图数据失败', error);
  }
};

/**
 * 训练场次状态图配置
 */
const trainingSessionOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  grid: {
    left: '5%',
    right: '5%',
    bottom: '8%',
    top: '8%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: trainingSessionData.value?.map((item) => item.abscissa) || [],
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: string) {
        return value.substring(value.indexOf('-') + 1);
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: number) {
        return Math.floor(value).toString();
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.1)',
      },
    },
  },
  series: [
    {
      data: trainingSessionData.value?.map((item) => item.ordinate) || [],
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#94F2FF' },
            { offset: 1, color: '#08C6E2' },
          ],
        },
      },
    },
  ],
}));

/**
 * 作业次数趋势图配置
 */
const workTrendOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  grid: {
    left: '5%',
    right: '5%',
    bottom: '8%',
    top: '8%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: jobTrendData.value?.map((item) => item.abscissa) || [],
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      formatter: function (value: string) {
        return value.substring(value.indexOf('-') + 1);
      },
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.3)',
      },
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(64, 158, 255, 0.1)',
      },
    },
  },
  series: [
    {
      data: jobTrendData.value?.map((item) => item.ordinate) || [],
      type: 'line',
      smooth: true,
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#306FFF',
      },
      itemStyle: {
        color: '#306FFF',
        borderColor: '#fff',
        borderWidth: 2,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(48, 111, 255, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(48, 111, 255, 0)',
            },
          ],
        },
      },
    },
  ],
}));
</script>

<style lang="less" scoped>
.left-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .chart-container {
    border-radius: 3px;
    flex: 1;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(64, 158, 255, 0.4);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
    }

    .chart-title {
      font-size: 21px;
      font-weight: bold;
      margin-bottom: 9px;
      padding-left: 30px;
      color: #fff;
      border-bottom: 1px solid rgba(64, 158, 255, 0.2);
      text-shadow: 0px 1.8px 2.4px #1f60ab;
      font-family: 'PingFang SC';
    }

    .chart-content {
      height: calc(100% - 45px);
      display: flex;
      justify-content: center;
      // ECharts 样式优化
      :deep(.echarts) {
        .echarts-tooltip {
          background: rgba(1, 22, 39, 0.95) !important;
          border: 1px solid rgba(64, 158, 255, 0.5) !important;
          border-radius: 4px !important;
          color: #fff !important;
        }
      }
    }
  }
}

// 图表全局样式优化
:deep(.left-panel) {
  // 坐标轴文字颜色
  .echarts text {
    fill: rgba(255, 255, 255, 0.8) !important;
  }

  // 坐标轴线条颜色
  .echarts .x-axis-line,
  .echarts .y-axis-line {
    stroke: rgba(64, 158, 255, 0.3) !important;
  }

  // 网格线颜色
  .echarts .grid-line {
    stroke: rgba(64, 158, 255, 0.1) !important;
  }
}
</style>
