<template>
  <div class="webrtc-player">
    <div class="video-container" :class="{ connecting: isConnecting }">
      <!-- 连接状态标签 -->
      <div v-if="showStatusTag" class="status-tag" :class="statusClass"></div>
      <!-- 视频元素 -->
      <video :id="videoId" autoplay playsinline muted class="video-element"></video>
      <!-- 连接中的加载状态 -->
      <div v-if="isConnecting" class="video-loading">
        <a-spin tip="视频连接中..." />
      </div>
      <!-- 连接失败提示 -->
      <div v-if="connectionFailed" class="connection-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">视频连接失败</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import WebRtcStreamer from '/@/utils/helper/webrtcstreamer';

interface Props {
  videoUrl: string;
  videoId: string;
  showStatusTag?: boolean;
  statusResult?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showStatusTag: false,
  statusResult: -1,
});

const emit = defineEmits<{
  connected: [];
  disconnected: [];
  error: [];
}>();

// 状态管理
const isConnecting = ref(false);
const connectionFailed = ref(false);
const isTimeout = ref(false);
let webRtcServer: InstanceType<typeof WebRtcStreamer> | null = null;

// 获取全局配置
const { webrtcUrl } = useGlobSetting();

// 状态样式计算
const statusClass = computed(() => {
  switch (props.statusResult) {
    case 1:
      return 'success';
    case 0:
      return 'error';
    case 2:
      return 'warning';
    default:
      return '';
  }
});

/**
 * 连接WebRTC视频流
 */
const connectVideoStream = async () => {
  if (!props.videoUrl) {
    console.warn('缺少视频流地址');
    return;
  }

  try {
    isConnecting.value = true;
    connectionFailed.value = false;
    isTimeout.value = false;

    // 解析视频流地址获取IP
    const ip = props.videoUrl.match(/rtsp:\/\/(.*?):/)?.[1];
    if (!ip) {
      message.error('视频流地址格式错误');
      connectionFailed.value = true;
      emit('error');
      return;
    }

    // 构建WebRTC服务器地址
    const rtcUrl = webrtcUrl || `http://${ip}:33001`;

    // 清理现有连接
    if (webRtcServer) {
      webRtcServer.disconnect();
      webRtcServer = null;
    }

    // 创建新的WebRTC连接
    webRtcServer = new WebRtcStreamer(props.videoId, rtcUrl);

    // 获取视频元素并设置事件监听
    const videoElement = document.getElementById(props.videoId) as HTMLVideoElement;
    if (videoElement) {
      // 视频开始播放事件
      const handlePlaying = () => {
        if (!isTimeout.value) {
          console.log(`工位 ${props.videoId} WebRTC视频流连接成功`);
          emit('connected');
          isConnecting.value = false;
          connectionFailed.value = false;
        }
      };

      // 视频错误事件
      const handleError = () => {
        console.error(`工位 ${props.videoId} WebRTC视频流连接失败`);
        emit('error');
        emit('disconnected');
        isConnecting.value = false;
        connectionFailed.value = true;
      };

      videoElement.addEventListener('playing', handlePlaying);
      videoElement.addEventListener('error', handleError);

      // 组件卸载时清理事件监听
      onUnmounted(() => {
        videoElement.removeEventListener('playing', handlePlaying);
        videoElement.removeEventListener('error', handleError);
      });
    }

    // 设置连接超时
    setTimeout(() => {
      if (isConnecting.value) {
        isTimeout.value = true;
        isConnecting.value = false;
        connectionFailed.value = true;
        emit('disconnected');
        console.warn(`工位 ${props.videoId} 视频连接超时`);

        if (webRtcServer) {
          webRtcServer.disconnect();
          webRtcServer = null;
        }
      }
    }, 15000); // 15秒超时

    // 开始连接
    webRtcServer.connect(props.videoUrl, '', '', '', '');
  } catch (error) {
    console.error(`工位 ${props.videoId} 视频流连接失败:`, error);
    emit('error');
    emit('disconnected');
    isConnecting.value = false;
    connectionFailed.value = true;
  }
};

/**
 * 断开连接
 */
const disconnect = () => {
  console.log(`工位 ${props.videoId} 开始断开视频连接`);

  // 清理WebRTC连接
  if (webRtcServer) {
    try {
      webRtcServer.disconnect();
    } catch (error) {
      console.warn(`工位 ${props.videoId} 断开WebRTC连接时出错:`, error);
    }
    webRtcServer = null;
  }

  // 清理视频元素
  const videoElement = document.getElementById(props.videoId) as HTMLVideoElement;
  if (videoElement && videoElement.srcObject) {
    try {
      const stream = videoElement.srcObject as MediaStream;
      stream.getTracks().forEach(track => {
        track.stop();
      });
      videoElement.srcObject = null;
    } catch (error) {
      console.warn(`工位 ${props.videoId} 清理视频元素时出错:`, error);
    }
  }

  // 重置状态
  isConnecting.value = false;
  connectionFailed.value = false;
  isTimeout.value = false;

  console.log(`工位 ${props.videoId} 视频连接已断开`);
};

// 监听videoUrl变化，重新连接
watch(
  () => props.videoUrl,
  (newUrl, oldUrl) => {
    // 如果URL发生变化，先断开旧连接再建立新连接
    if (newUrl !== oldUrl) {
      console.log(`工位 ${props.videoId} 视频URL变化: ${oldUrl} -> ${newUrl}`);
      disconnect();
      if (newUrl) {
        // 添加短暂延迟，确保旧连接完全断开
        setTimeout(() => {
          connectVideoStream();
        }, 100);
      }
    }
  },
  { immediate: false }
);

// 组件挂载时连接视频流
onMounted(() => {
  if (props.videoUrl) {
    connectVideoStream();
  }
});

// 组件卸载时清理连接
onUnmounted(() => {
  disconnect();
});
</script>

<style lang="less" scoped>
.webrtc-player {
  width: 100%;
  height: 100%;
  position: relative;

  .video-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    &.connecting {
      background: rgba(0, 0, 0, 0.8);
    }

    .status-tag {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 60px;
      height: 60px;
      z-index: 10;
      background-size: 100% 100%;
      &.success {
        background-image: url('/@/assets/images/screen/train-icon-qualified.png');
      }

      &.warning {
        background-image: url('/@/assets/images/screen/train-icon-warning.png');
      }

      &.error {
        background-image: url('/@/assets/images/screen/train-icon-unqualified.png');
      }
    }

    .video-element {
      width: 100%;
      height: 100%;
      object-fit: cover;
      background: #000;
    }

    .video-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 20;
      color: #fff;
      text-align: center;

      :deep(.ant-spin-text) {
        color: #fff;
      }
    }

    .connection-error {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 20;
      color: #fff;
      text-align: center;

      .error-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .error-text {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
}
</style>
