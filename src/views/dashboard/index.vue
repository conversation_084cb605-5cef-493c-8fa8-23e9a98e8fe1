<template>
  <div class="dashboard-wrapper">
    <div class="dashboard-container" ref="dashboardRef">
    <div class="dashboard-header">
      <div class="header-left">
        <a href="/#/home" class="return-home">
          <Icon icon="ant-design:left-outlined" />
        </a>
        <a-divider type="vertical" class="head-divider" />
        <div class="logo-section">
          <img :src="headerImg" alt="logo" class="header-logo" />
        </div>
        <div class="left-controls">
          <div class="time-display">
            <span class="current-time date">{{ currentDate }}</span>
            <a-divider type="vertical" class="time-divider" />
            <span class="current-time">{{ currentTime }}</span>
            <a-divider type="vertical" class="time-divider" />
            <span class="current-time">{{ currentWeek }}</span>
          </div>
        </div>
      </div>

      <div class="header-center">
        <div class="title-section">
          <div class="main-title">智工育匠数据看板</div>
        </div>
      </div>

      <div class="header-right">
        <div class="controls-section">
          <div class="craft-selector">
            <a-select
              v-model:value="selectedCraft"
              placeholder="请选择工艺"
              size="large"
              @change="handleCraftChange"
              :options="craftOptions"
              dropdownClassName="dashboard-select-dropdown"
            />
          </div>
          <div class="full-screen-btn">
            <div class="custom-fullscreen-btn" @click="toggleFullscreen">
              <img
                :src="isFullscreenRef ? dpCollapseImg : dpExpandImg"
                :alt="isFullscreenRef ? '退出全屏' : '全屏'"
                class="fullscreen-icon"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="dashboard-content">
      <div class="panel-wrapper left">
        <LeftPanel :selected-craft="selectedCraft" :refresh-interval="REFRESH_INTERVAL" />
      </div>
      <div class="panel-wrapper center">
        <CenterPanel :workstations="workstations" />
      </div>
      <div class="panel-wrapper right">
        <RightPanel :selected-craft="selectedCraft" :refresh-interval="REFRESH_INTERVAL" />
      </div>
    </div>
    <div class="dashboard-footer">
      <BottomPanel :selected-craft="selectedCraft" :refresh-interval="REFRESH_INTERVAL" />
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { LeftPanel, CenterPanel, RightPanel, BottomPanel } from './components';
import defImg from '@/assets/svg/point/pipeline-bg.png';
import { V1ManageCommonProjectTypeEnum } from '/@/api/cddc.req';
import { useWebSocket } from '/@/utils/websocket';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGlobSetting } from '/@/hooks/setting';
import { useFullscreen } from '/@/hooks/web/useFullScreen';
import { Icon } from '@geega-ui-plus/geega-ui';
import headerImg from '/@/assets/images/logo.svg';
import dpExpandImg from '/@/assets/images/screen/dp-expand.png';
import dpCollapseImg from '/@/assets/images/screen/dp-collapse.png';
import dayjs from 'dayjs';
// import { workstationVideoTestData } from './components/data';

interface WorkstationItem {
  id: string | number;
  status: string;
  userName: string;
  imageSrc: string;
  duration: string;
  taskCount: string;
  qualifiedRate: string;
  effectiveRate: string;
  logInfo: string;
  locationName: string; // 工位名
  result: number; // 拧紧结果
  videoUrl: string; // 视频地址
}

// 定义刷新间隔常量（5分钟，单位毫秒）
const REFRESH_INTERVAL = 5 * 60 * 1000;

// 仪表盘容器引用
const dashboardRef = ref<HTMLElement | null>(null);

// 工艺选择
const selectedCraft = ref<string | undefined>(undefined); // 默认选择拧紧工艺

// 工艺选项
const craftOptions = ref<Array<{ value: string; label: string }>>([]);
const wsInstance = ref<any>(null);
// 工位数据
const workstations = ref<WorkstationItem[]>([]);

// 当前时间
const currentDate = ref('');
const currentTime = ref('');
const currentWeek = ref('');

// 排名数据
const { createMessage } = useMessage();
const { socketUrl } = useGlobSetting();

// 全屏功能
const { toggleFullscreen, isFullscreenRef } = useFullscreen();

/**
 * 更新当前时间
 */
const updateTime = () => {
  const now = new Date();
  const date = dayjs(now);
  const weekMap = {
    Monday: '星期一',
    Tuesday: '星期二',
    Wednesday: '星期三',
    Thursday: '星期四',
    Friday: '星期五',
    Saturday: '星期六',
    Sunday: '星期日',
  };
  // 拆分成日期、时间、星期三个字段
  currentDate.value = date.format('YYYY-MM-DD');
  currentTime.value = date.format('HH:mm:ss');
  currentWeek.value = weekMap[date.format('dddd')];
};

// 定时器引用
let timeInterval: NodeJS.Timeout | null = null;
/**
 * 设置大屏自适应缩放
 * 根据设计尺寸和实际屏幕尺寸计算缩放比例
 */
const setScale = () => {
  if (!dashboardRef.value) return;

  // 设计尺寸（基于1920*1080的设计稿）
  const designWidth = 1920;
  const designHeight = 1080;

  // 计算缩放比例
  const scaleX = window.innerWidth / designWidth;
  const scaleY = window.innerHeight / designHeight;

  // 使用较小的缩放比例，确保内容完全显示
  const scale = Math.min(scaleX, scaleY);

  // 计算缩放后的实际尺寸
  const scaledWidth = designWidth * scale;
  const scaledHeight = designHeight * scale;

  // 计算居中偏移量
  const offsetX = (window.innerWidth - scaledWidth) / 2;
  const offsetY = (window.innerHeight - scaledHeight) / 2;

  // 设置变换原点为左上角，然后通过translate居中
  dashboardRef.value.style.transformOrigin = 'left top';
  dashboardRef.value.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`;

  // 重置容器尺寸为设计稿尺寸
  dashboardRef.value.style.width = `${designWidth}px`;
  dashboardRef.value.style.height = `${designHeight}px`;
};

/**
 * 窗口大小变化时重新计算缩放
 */
const handleResize = () => {
  setScale();
};

/**
 * 获取工艺选项
 */
const getCraftOptions = async () => {
  try {
    const res = await V1ManageCommonProjectTypeEnum();
    if (res) {
      craftOptions.value = res.map((item) => ({
        value: item.code as string,
        label: item.desc as string,
      }));
      // 设置默认选中的工艺
      if (craftOptions.value.length > 0) {
        selectedCraft.value = craftOptions.value[0].value;
      }
    }
  } catch (error) {
    console.error('Failed to fetch craft options:', error);
  }
};

// 清理绑定相关资源
const cleanupBind = () => {
  try {
    if (wsInstance.value) {
      wsInstance.value.disconnect();
      wsInstance.value = null;
    }
  } catch (error) {
    console.error('断开 WebSocket 连接时发生错误:', error);
  }
};

// 初始化绑定WebSocket
const initBindWebSocket = () => {
  cleanupBind();
  // let socketUrl = '10.168.80.170:19090'; //test
  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    onMessage: handleBindMessage,
    onError: (error) => {
      console.error('WebSocket错误:', error);
      cleanupBind();
      createMessage.error('连接失败，请重试');
    },
    onClose: () => {
      console.log('WebSocket连接已关闭');
    },
    onOpen: () => {
      console.log('WebSocket连接已建立');
    },
    createMessage: {
      action: 'largeScreen',
      content: {
        type: selectedCraft.value,
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {
        type: selectedCraft.value,
      },
    },
  });

  wsInstance.value = ws;
  ws.connect();
};

// 切换工艺后重新初始化WebSocket连接
const handleCraftChange = (craft: string) => {
  selectedCraft.value = craft;
  // 清空当前工位数据，确保视频播放组件能够重新连接
  workstations.value = [];
  initBindWebSocket();
  // 重新设置下拉选项样式
  setSelectDropdownStyle();
};

// 处理绑定消息
const handleBindMessage = (data: any) => {
  if (data.status === 'success') return;
  console.log('data', data);
  switch (data.action) {
    case 'push':
      onDataUpdate(data);
      break;
    case 'locationClose':
      createMessage.warning(data.info || '工位结束训练');
      handleClose(data);
      break;
    case 'init':
      onInitCamera(data);
      break;
    default:
      console.warn('未处理的WebSocket消息类型:', data.action);
      break;
  }
};
/**
 * 初始化摄像头
 * 根据推送的数据初始化工位摄像头
 * @param data 从 WebSocket 接收到的初始化数据
 */
const onInitCamera = (data: any) => {
  const existingWorkstationIndex = workstations.value.findIndex((ws) => ws.id === data.locationId);
  if (existingWorkstationIndex !== -1) {
    // 如果工位已存在，则更新其摄像头信息
    Object.assign(workstations.value[existingWorkstationIndex], {
      userName: data.userName,
      locationName: data.locationName,
      videoUrl: data.videoUrl,
    });
  } else {
    // 如果工位不存在，则添加新的工位（仅包含基本信息和摄像头）
    workstations.value.push({
      id: data.locationId,
      status: '待训练',
      userName: data.userName,
      locationName: data.locationName,
      imageSrc: defImg,
      duration: '0min0s',
      taskCount: '0次',
      qualifiedRate: '0.00%',
      effectiveRate: '0.00%',
      logInfo: '-',
      result: -1,
      videoUrl: data.videoUrl,
    });
  }
};

/**
 * 工位训练数据更新
 * 根据推送的数据更新工位状态
 * @param data 从 WebSocket 接收到的工位数据
 */
const onDataUpdate = (data: any) => {
  const existingWorkstationIndex = workstations.value.findIndex((ws) => ws.id === data.locationId);
  if (existingWorkstationIndex !== -1) {
    // 如果工位已存在，则更新其数据
    Object.assign(workstations.value[existingWorkstationIndex], {
      status: '训练中',
      userName: data.userName,
      locationName: data.locationName,
      duration: `${Math.floor(data.totalDuration / 60)}min${data.totalDuration % 60}s`,
      taskCount: `${data.qualificationNum + data.unqualifiedNum}次`,
      qualifiedRate: `${parseFloat(data.qualificationRate).toFixed(2)}%`,
      effectiveRate: `${parseFloat((data.effectiveNum / (data.qualificationNum + data.unqualifiedNum)) * 100).toFixed(2)}%`,
      logInfo: data.operationLog.actionLabels?.length
      ? data.operationLog.actionLabels.map((label: any) => label.desc).join('、')
        : '-',
      result: Number(data.operationLog.result) || 0,
      videoUrl: data.videoUrl,
    });
  } else {
    // 如果工位不存在，则添加新的工位
    workstations.value.push({
      id: data.locationId,
      status: '训练中',
      userName: data.userName,
      locationName: data.locationName,
      imageSrc: defImg,
      duration: `${Math.floor(data.totalDuration / 60)}min${data.totalDuration % 60}s`,
      taskCount: `${data.standardNum + data.nonStandardNum}次`,
      qualifiedRate: `${parseFloat(data.qualificationRate).toFixed(2)}%`,
      effectiveRate: `${parseFloat(data.actionRate).toFixed(2)}%`,
      logInfo: data.operationLog.actionLabels?.length
        ? data.operationLog.actionLabels[0].desc
        : '-',
      result: Number(data.operationLog.result) || 0,
      videoUrl: data.videoUrl,
    });
  }
};
/**
 * 工位结束训练
 * @param data
 * { "action": "locationClose","locationId": "xxx"}
 */
const handleClose = (data: any) => {
  // 先更新状态为训练结束，隔2秒后关闭对应工位
  workstations.value.forEach((item) => {
    if (item.id === data.locationId) {
      item.status = '训练结束';
      setTimeout(() => {
        workstations.value = workstations.value.filter((item) => item.id !== data.locationId);
      }, 2000);
    }
  });
};

/**
 * 设置选择器下拉选项的字体大小
 */
const setSelectDropdownStyle = () => {
  // 延迟执行，确保下拉选项已经渲染
  setTimeout(() => {
    const dropdownElements = document.querySelectorAll(
      '.dashboard-select-dropdown .cddc-ant-select-item'
    );
    const is4K = window.innerWidth >= 3840 && window.innerHeight >= 2160;
    const fontSize = is4K ? '20px' : '16px';
    const lineHeight = is4K ? '44px' : '36px';
    const padding = is4K ? '10px 16px' : '8px 12px';
    const minHeight = is4K ? '44px' : '36px';

    dropdownElements.forEach((element: Element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.fontSize = fontSize;
      htmlElement.style.lineHeight = lineHeight;
      htmlElement.style.padding = padding;
      htmlElement.style.minHeight = minHeight;
    });
  }, 100);
};

// 组件挂载后设置缩放
onMounted(async () => {
  setScale();
  window.addEventListener('resize', handleResize);
  await getCraftOptions();
  initBindWebSocket();

  // 初始化时间显示
  updateTime();
  timeInterval = setInterval(updateTime, 1000);

  // 设置下拉选项样式
  setSelectDropdownStyle();

  // 初始化测试数据（用于测试轮播效果）
  // workstations.value = [...workstationVideoTestData];
});

// 组件卸载前移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (timeInterval) {
    clearInterval(timeInterval);
    timeInterval = null;
  }
  cleanupBind();
});
</script>

<style lang="less" scoped>
.dashboard-wrapper {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #000;
}

.dashboard-container {
  width: 1920px; /* 设计稿宽度 */
  height: 1080px; /* 设计稿高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-image: url('/@/assets/images/screen/dp-bg.jpg');
  background-size: 100% 100%;
  padding: 17px 32px 32px;
  gap: 16px;
  position: absolute;
  top: 0;
  left: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at center,
      rgba(30, 60, 114, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  & > * {
    position: relative;
    z-index: 1;
  }
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    .head-divider {
      height: 20px;
      margin: 0;
      --ignore-color: rgba(255, 255, 255, 0.2);
      background-color: var(--ignore-color);
    }
    .logo-section {
      .header-logo {
        height: 30px;
        width: auto;
      }

      .logo-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 48px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
    }

    .left-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .time-display {
        display: flex;
        align-items: center;
        .current-time {
          font-size: 18px;
          color: #ffffff;
          font-weight: 500;
          padding: 8px 12px;
          &.date {
            padding-left: 0;
          }
        }
        .time-divider {
          height: 18px;
          margin: 0;
          --ignore-color: rgba(255, 255, 255, 0.6);
          background-color: var(--ignore-color);
        }
      }
    }
    .return-home {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      padding: 8px 16px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }
  }

  .header-center {
    display: flex;
    justify-content: center;
    flex: 2;

    .title-section {
      text-align: center;

      .main-title {
        font-size: 32px;
        font-weight: 400;
        color: #ffffff;
        margin: 0;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;

    .controls-section {
      display: flex;
      align-items: center;
      gap: 16px;

      .craft-selector {
        display: flex;
        align-items: center;
        width: 180px;
        height: 40px;
        :deep(.cddc-ant-select) {
          width: 100%;
          height: 100%;
          .cddc-ant-select-selector {
            --ignore-bg-color: #1f3a49;
            background-color: var(--ignore-bg-color) !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            height: 100%;
            color: #ffffff;
            display: flex;
            align-items: center;

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }

          .cddc-ant-select-selection-item {
            color: #ffffff;
            font-size: 16px;
            line-height: 38px;
            padding: 0 8px;
          }

          .cddc-ant-select-arrow {
            color: rgba(255, 255, 255, 0.8);
          }
          .cddc-ant-select-dropdown {
            background-color: #000 !important;
          }
        }
      }

      .full-screen-btn {
        .custom-fullscreen-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.5);
          }

          .fullscreen-icon {
            width: 38px;
            height: 38px;
            object-fit: contain;
          }
        }
      }
    }
  }
}

.dashboard-content {
  flex: 1;
  display: flex;
  gap: 16px;
  overflow: auto;

  .panel-wrapper {
    overflow-y: auto;
    height: 100%;

    &.left {
      width: 26%;
    }

    &.center {
      width: 50%;
    }

    &.right {
      width: 26%;
    }
  }
}
.dashboard-footer {
  height: 22%;
}

/* 装饰元素动画 */
@keyframes decorationPulse {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.title-decoration .decoration-center {
  animation: decorationPulse 2s ease-in-out infinite;
}

/* 适配4K屏幕的媒体查询 */
@media screen and (min-width: 3840px) and (min-height: 2160px) {
  .dashboard-container {
    /* 在JS中动态设置缩放，这里只是备用 */
    transform-origin: left top;

    .dashboard-header {
      .header-right {
        .controls-section {
          .craft-selector {
            width: 240px;
            height: 50px;

            :deep(.cddc-ant-select) {
              .cddc-ant-select-selector {
                min-height: 50px;
                font-size: 20px;
              }

              .cddc-ant-select-selection-item {
                font-size: 20px;
                line-height: 48px;
                padding: 0 12px;
              }

              .cddc-ant-select-arrow {
                font-size: 16px;
              }
            }
          }

          .full-screen-btn .custom-fullscreen-btn {
            width: 50px;
            height: 50px;

            .fullscreen-icon {
              width: 44px;
              height: 44px;
            }
          }
        }
      }
    }
  }
}
</style>
