<script lang="ts" setup>
import { computed, h } from 'vue';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { useAsyncData } from '/@/composables';
import { getDictItem, getDictLabel } from '/@/dict';
import {
  EvaluateStatusOptions,
  ExamStatusOptions,
  LearningStatusOptions,
  PositionLevelOptions,
  ProjectType,
} from '/@/enums/projectEnum';
import {
  V1EvaluateTaskUserOrderUserIdPost,
  V1ManageExamRecordPagePost,
  V1ManageSysUserId,
  V1ManageTrainStudyRecordsPagePost,
  V1ManageUserLearningsPagePost,
} from '/@/api/cddc.req';
import { useRoute } from 'vue-router';
import { watchImmediate } from '@vueuse/core';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { REPORT_STATUS } from '/@/dict/modules/report';
import { formatSecondsToMinute } from '/@/utils/dateUtil';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { PaperTypeEnum } from '/@/enums/paperEnum';

const route = useRoute();

const basicInfoApi = useAsyncData(V1ManageSysUserId, {});

const userId = computed(() => route.query.id as string);

watchImmediate(userId, () => {
  fetchInitData();
});

function fetchInitData() {
  basicInfoApi.load({
    id: userId.value,
  });
}

// 基础信息列表配置
const infoList = computed(() => {
  const d = basicInfoApi.data.value;
  return [
    {
      label: '人员姓名',
      value: d.name || '-',
    },
    {
      label: '组织名称',
      value: d.orgList?.map((i) => i.name).join(', ') || '-',
    },
    {
      label: '岗位',
      value: d.jobName || '-',
    },
    {
      label: '岗级评定',
      value: getDictLabel(PositionLevelOptions, d.level?.toString()),
    },
  ];
});

const trainingColumns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 200,
  },
  {
    title: '要求作业次数',
    dataIndex: 'requestFrequency',
    width: 120,
  },
  {
    title: '合格率要求',
    dataIndex: 'requestQualificationRate',
    width: 120,
    customRender: ({ text }) => `${text}%`,
  },
  {
    title: '达标率要求',
    dataIndex: 'requestActionRate',
    width: 120,
    customRender: ({ text }) => `${text}%`,
  },
  {
    title: '训练时长',
    dataIndex: 'trainDurationSecond',
    customRender: ({ text }) => Math.floor((+text || 0) / 60),
    width: 120,
  },
  {
    title: '实际作业次数',
    dataIndex: 'opNum',
    width: 120,
  },
  {
    title: '实际合格率',
    dataIndex: 'passRate',
    width: 120,
    customRender: ({ text }) => `${Math.floor(+text || 0)}%`,
  },
  {
    title: '实际达标率',
    dataIndex: 'actionPassRate',
    width: 120,
    customRender: ({ text }) => `${Math.floor(+text || 0)}%`,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender({ value }) {
      return getDictLabel(REPORT_STATUS, value);
    },
  },
];

const examColumns: EnhancedColumn[] = [
  {
    title: '考试名称',
    dataIndex: 'paperName',
    width: 200,
  },
  {
    title: '考试时间',
    dataIndex: 'begTime',
    width: 180,
  },
  {
    title: '合格分数',
    dataIndex: 'standardScore',
    width: 120,
  },
  {
    title: '考试得分',
    dataIndex: 'score',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender({ value }) {
      const item = getDictItem(ExamStatusOptions, value?.toString());

      return h(
        Tag,
        {
          color: item?.colorType,
        },
        [item?.label ?? value ?? '-']
      );
    },
  },
];

const learningColumns: EnhancedColumn[] = [
  {
    title: '学习项目名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '项目要求时间',
    dataIndex: 'requireDuration',
    customRender({ value }) {
      return ((+value || 0) / 60).toFixed(0) + '分钟';
    },
    width: 180,
  },
  {
    title: '实际学习时间',
    dataIndex: 'duration',
    customRender({ value }) {
      return value != null ? formatSecondsToMinute(value) : '-';
    },
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender({ value }) {
      const item = getDictItem(LearningStatusOptions, value?.toString());

      return h(
        Tag,
        {
          color: item?.colorType,
        },
        [item?.label ?? value ?? '-']
      );
    },
  },
  {
    title: '考试状态',
    dataIndex: 'examStatus',
    width: 120,
    customRender({ value }) {
      const item = getDictItem(ExamStatusOptions, value?.toString());

      return h(
        Tag,
        {
          color: item?.colorType,
        },
        [item?.label ?? value ?? '-']
      );
    },
  },
  {
    title: '考试分数',
    dataIndex: 'score',
    width: 120,
  },
];

const onlineColumns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'evaluateName',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
  {
    title: '考评人',
    dataIndex: 'assessorName',
    width: 120,
  },
  {
    title: '考评结果',
    dataIndex: 'status',
    width: 120,
    customRender({ value }) {
      const item = getDictItem(EvaluateStatusOptions, value?.toString());

      return h(
        Tag,
        {
          color: item?.colorType,
        },
        [item?.label ?? value ?? '-']
      );
    },
  },
];

// 标签页配置
const tabListConfig = [
  {
    key: 'training',
    tab: '道场技能培训',
    tableConfig: useTable({
      columns: trainingColumns,
      api: (params) =>
        V1ManageTrainStudyRecordsPagePost({
          ...params,
          data: { userId: userId.value, projectType: ProjectType.Training },
        }),
      tableProps: {
        showTableSetting: false,
        useSearchForm: false,
      },
      showAction: false,
    }),
  },
  {
    key: 'exam',
    tab: '智能考试项目',
    tableConfig: useTable({
      columns: examColumns,
      api: (params) =>
        V1ManageExamRecordPagePost({
          ...params,
          data: {
            userId: userId.value,
            type: PaperTypeEnum.SKILL_INSPECTION,
          },
        }),
      tableProps: {
        showTableSetting: false,
        useSearchForm: false,
      },
      showAction: false,
    }),
  },
  {
    key: 'learning',
    tab: '学习项目',
    tableConfig: useTable({
      columns: learningColumns,
      api: (params) =>
        V1ManageUserLearningsPagePost({
          ...params,
          data: {
            userId: userId.value,
          },
        }),
      tableProps: {
        showTableSetting: false,
        useSearchForm: false,
      },
      showAction: false,
    }),
  },
  {
    key: 'onlineExam',
    tab: '在线考评项目',
    tableConfig: useTable({
      columns: onlineColumns,
      api: async (params) => {
        const result = await V1EvaluateTaskUserOrderUserIdPost({
          userId: userId.value,
        });

        return {
          totalPages: 1,
          totalRows: result.length,
          records: result,
        };
      },
      tableProps: {
        showTableSetting: false,
        useSearchForm: false,
      },
      showAction: false,
    }),
  },
];
</script>

<template>
  <div class="personnel-detail-container">
    <!-- 基础信息 -->
    <a-card class="mb-4">
      <h3 class="section-title">基础信息</h3>
      <div class="basic-info">
        <div v-for="item in infoList" :key="item.label" class="info-item">
          <span class="label">{{ item.label }}：</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
    </a-card>

    <!-- 训练记录和考试认证 -->
    <a-card class="detail-card">
      <a-tabs>
        <a-tab-pane v-for="item in tabListConfig" :key="item.key" :tab="item.tab">
          <BasicTablePlus @register="item.tableConfig[0]" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.personnel-detail-container {
  padding: 16px;
  background: #f0f2f5;

  .mb-4 {
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #1f2329;
  }

  .basic-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        color: #4e5969;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        color: #1f2329;
        font-weight: 500;
      }
    }
  }
}

.detail-card {
  min-height: calc(100vh - 290px);
}
</style>
