import { h } from 'vue';
import { V1ManageJobsPagePost } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { PositionLevelOptions } from '/@/enums/projectEnum';
import { getDictLabel } from '/@/dict';

// 表格列定义
export const columns: EnhancedColumn[] = [
  {
    title: '人员名称（账号）',
    dataIndex: 'name',
    width: 160,
    search: {
      field: 'name',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入人员名称',
        allowClear: true,
      },
    },
  },
  {
    title: '所在组织',
    dataIndex: 'orgList',
    width: 160,
    customRender({ value }) {
      return (value || []).map((item) => item.name).join(', ') || '-';
    },
  },
  {
    title: '岗位名称',
    dataIndex: 'jobName',
    width: 120,
    search: {
      field: 'jobId',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择岗位',
        allowClear: true,
        async api() {
          const resp = await V1ManageJobsPagePost({ currentPage: 1, pageSize: 1000, data: {} });

          return resp.records?.map((item) => ({
            label: item.name,
            value: item.id,
          }));
        },
      },
    },
  },
  {
    title: '岗级',
    dataIndex: 'level',
    width: 100,
    customRender({ value }) {
      return getDictLabel(PositionLevelOptions, value?.toString(), '-');
    },
    search: {
      field: 'level',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择岗级',
        allowClear: true,
        options: PositionLevelOptions,
      },
    },
  },
  // 操作列定义已移至 index.vue 中的 useTable 配置
];

// 图表颜色
export const chartColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272'];
