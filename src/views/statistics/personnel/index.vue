<script lang="ts" setup>
import { ref, computed, reactive } from 'vue';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns, chartColors } from './data';
import type { EChartsOption } from 'echarts';
import { useAsyncData } from '/@/composables';
import { V1ManageSysUserJobPagePost, V1ManageSysUserJobStatistics } from '/@/api/cddc.req';
import type { JobListElement } from '/@/api/cddc.model';
import { sum } from 'lodash-es';
import ECharts from 'vue-echarts';
import { getDictLabel } from '/@/dict';
import { PositionLevelOptions } from '/@/enums/projectEnum';
import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';
import { useRouter } from 'vue-router';
import type { ECElementEvent } from 'echarts/core';

const router = useRouter();

const chartIns = ref<InstanceType<typeof ECharts>>();

const query = reactive({
  jobId: '' as string | undefined,
  jobName: '' as string | undefined,
});

const state = reactive({
  currentHighlightIndex: undefined as number | undefined,
});

const pieData = ref<JobListElement[]>([]);

const statisticsDataApi = useAsyncData(V1ManageSysUserJobStatistics, []);

const pieOptions = computed(() => {
  const option: EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      className: 'dark-ignore-style echarts-tooltip-dark',
    },
    legend: {
      show: true,
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      data: pieData.value.map((item) => item.jobName!),
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        center: ['50%', '50%'],
        data: pieData.value.map((item, index) => ({
          name: item.jobName,
          value: item.userNum,
          itemStyle: {
            color: chartColors[index % chartColors.length],
          },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        label: {
          show: false,
        },
      },
    ],
  };

  return option;
});

// 岗级分布数据
const totalPersonnel = computed(() => {
  const d = statisticsDataApi.data.value;

  return sum(d.jobLevelList?.map((n) => n.userNum!));
});

const levelDistribution = computed(() => {
  const d = statisticsDataApi.data.value;

  return d.jobLevelList;
});

// 表格相关
const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};
  const data = {
    ...rawQuery,
  };
  params.data = data;
  return params;
};

const [registerTable, tableActions] = useTable({
  columns,
  api: async (params) => {
    const resp = await V1ManageSysUserJobPagePost(params);
    return resp;
  },
  beforeFetch: handleBeforeFetch,
  showAction: true, // 显示操作列
  tableProps: {
    canResize: false,
  },
});

fetchInitData();

async function fetchInitData() {
  await statisticsDataApi.load({});

  const d = statisticsDataApi.data.value;
  pieData.value = d.jobList || [];
}

async function handleClickPie(arg: ECElementEvent) {
  {
    // update highlight part
    if (state.currentHighlightIndex != null) {
      chartIns.value?.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: state.currentHighlightIndex,
      });
    }

    if (state.currentHighlightIndex === arg.dataIndex) {
      state.currentHighlightIndex = undefined;
    } else {
      state.currentHighlightIndex = arg.dataIndex;

      chartIns.value?.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: arg.dataIndex,
      });
    }
  }

  const item =
    state.currentHighlightIndex != null ? pieData.value.at(state.currentHighlightIndex) : undefined;

  query.jobId = item?.jobId;
  query.jobName = item?.jobName;

  await statisticsDataApi.load({
    jobId: query.jobId,
  });
}

// 显示详情/编辑/新增
function showDetails(record: any) {
  router.push({
    path: '/statistics/personnel/detail',
    query: {
      id: record.id,
    },
  });
}
</script>

<template>
  <div class="personnel-stats-container">
    <a-card class="mb-4">
      <div class="statistics-overview">
        <div class="chart-section">
          <h3 class="section-title">岗位分布</h3>
          <div class="pie-chart-container relative">
            <ECharts
              v-show="pieData.length"
              ref="chartIns"
              :option="pieOptions"
              autoresize
              @click="handleClickPie"
            />
            <customizeRenderEmpty v-if="!pieData.length" />
          </div>
        </div>
        <div class="level-section">
          <h3 class="section-title">岗级分布({{ query.jobName || '全部' }})</h3>
          <div class="level-stats">
            <div class="total-count">
              <div class="count-label">总人数</div>
              <div class="count-value">{{ totalPersonnel }}</div>
            </div>
            <div class="level-list" v-if="levelDistribution?.length">
              <div v-for="(level, index) in levelDistribution" :key="index" class="level-item">
                <div class="level-name">
                  {{ getDictLabel(PositionLevelOptions, level.level?.toString()) }}
                </div>
                <div class="level-count">{{ level.userNum || 0 }}人</div>
              </div>
            </div>
            <div class="relative" v-else>
              <customizeRenderEmpty />
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <a-card>
      <BasicTablePlus @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a class="action-button" @click="showDetails(record)"> 详情 </a>
          </template>
        </template>
      </BasicTablePlus>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.personnel-stats-container {
  padding: 16px;
  background: #f0f2f5;

  .mb-4 {
    margin-bottom: 16px;
  }

  .statistics-overview {
    display: flex;
    gap: 24px;

    .chart-section,
    .level-section {
      flex: 1;
      min-width: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #1f2329;
    }

    .pie-chart-container {
      display: flex;
      height: 240px;

      .pie-chart {
        flex: 3;
        height: 100%;
      }

      .chart-legend {
        flex: 2;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
          }

          .legend-text {
            font-size: 14px;
            color: #4e5969;
          }
        }
      }
    }

    .level-stats {
      height: 240px;
      display: flex;
      flex-direction: column;

      .total-count {
        text-align: center;
        margin-bottom: 24px;

        .count-label {
          font-size: 14px;
          color: #4e5969;
          margin-bottom: 8px;
        }

        .count-value {
          font-size: 32px;
          font-weight: 500;
          color: #1f2329;
        }
      }

      .level-list {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;

        .level-item {
          text-align: center;
          padding: 16px;
          min-width: 80px;

          .level-name {
            font-size: 16px;
            font-weight: 500;
            color: #1f2329;
            margin-bottom: 8px;
          }

          .level-count {
            font-size: 14px;
            color: #4e5969;
          }
        }
      }
    }
  }
}
</style>
