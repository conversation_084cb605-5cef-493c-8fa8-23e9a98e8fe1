<template>
  <PageWrapper>
    <div :class="prefixCls">
      <a-card title="Default size card" style="width: 300px">
        <template #extra><a href="#">more</a></template>
        <p>card content</p>
        <p>card content</p>
        <p>card content</p>
      </a-card>

      <p>tree</p>
      <a-tree
        v-model:selectedKeys="selectedKeys"
        v-model:checkedKeys="checkedKeys"
        default-expand-all
        checkable
        :height="233"
        :tree-data="treeData"
      >
        <template #title="{ title, key }">
          <span v-if="key === '0-0-1-0'" style="color: #1890ff">{{ title }}</span>
          <template v-else>{{ title }}</template>
        </template>
      </a-tree>

      <p>{{$t('views.demo.virtual.index.bd0a7e73', { defaultValue: '虚拟滚动（最大可以支撑 10w 列、30w 行）' })}}</p>
      <a-button type="primary" style="margin: 10px 0 20px 0;" @click="goTable">{{$t('views.demo.virtual.index.9ef7879a', { defaultValue: '大数据表格使用指南' })}}</a-button>

      <gxe-table
      border
      show-overflow
      ref="xTable1"
      height="600"
      :row-config="{ isHover: true }"
    >
      <gxe-column type="seq" width="100"  :title="$t('views.demo.virtual.index.9aedcc25', { defaultValue: '序号' })"></gxe-column>
      <gxe-column field="name" :title="$t('views.demo.virtual.index.196b3c82', { defaultValue: '姓名' })" sortable></gxe-column>
      <gxe-column field="role" :title="$t('views.demo.virtual.index.2f4564cf', { defaultValue: '角色' })"></gxe-column>
      <gxe-column field="sex" :title="$t('views.demo.virtual.index.1a327f1f', { defaultValue: '性别' })"></gxe-column>
      <template #empty>
        <GeegaEmpty></GeegaEmpty>
    </template>
    </gxe-table>
  </div>

  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, nextTick,onMounted,watch } from 'vue';
  import {
    PageWrapper,
  } from '@geega-ui-plus/geega-ui';
  import { PlusOutlined , DisconnectOutlined } from '@geega-ui-plus/icons-vue';
  import { Select } from '@geega-ui-plus/ant-design-vue';
  import type { TreeProps } from '@geega-ui-plus/ant-design-vue';
  import { GxeTableInstance } from "@geega-ui-plus/geega-table";
  import XEUtils from "xe-utils";
  import { useDesign } from '/@/hooks/web/useDesign';
  // 按需加载样式
  import '@geega-ui-plus/ant-design-vue/lib/card/style/';
  import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';

  export default defineComponent({
    name: 'virtual',
    components: { PageWrapper,PlusOutlined , DisconnectOutlined, Select, GeegaEmpty},
    setup() {
        const mockList1: any = [];
        for (let index = 0; index < 100000; index++) {
          mockList1.push({
            name: "Test" + index,
            role: "Developer",
            sex: "男",
          });
        }

        const xTable1 = ref<GxeTableInstance>();

        onMounted(() => {
          nextTick(() => {
            const $table = xTable1.value;
            if ($table) {
              $table
              $table.loadData(XEUtils.clone(mockList1, true));
            }
          });
        });

        const goTable = () =>{
          window.open('https://geega-ui-plus-docs.cloud-dev.geega.com/gxe-table/docs/guide/install.html');
        }

        const { prefixCls } = useDesign('vertical-wrapper');

        function dig(path = '0', level = 3) {
          const list: TreeProps['treeData'] = [];
          for (let i = 0; i < 10; i += 1) {
            const key = `${path}-${i}`;
            const treeNode: TreeProps['treeData'][number] = {
              title: key,
              key,
            };

            if (level > 0) {
              treeNode.children = dig(key, level - 1);
            }

            list.push(treeNode);
          }
          return list;
        }

        const selectedKeys = ref<string[]>(['0-0-0', '0-0-1']);
        const checkedKeys = ref<string[]>(['0-0-0', '0-0-1']);
        watch(selectedKeys, () => {
          console.log('selectedKeys', selectedKeys);
        });
        watch(checkedKeys, () => {
          console.log('checkedKeys', checkedKeys);
        });

        return {
          treeData: dig(),
          selectedKeys,
          checkedKeys,
          xTable1,
          goTable,
          prefixCls,
          GeegaEmpty
        };
      }
  });
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-vertical-wrapper';
.@{prefix-cls} {
  padding: 0 8px;
  background: #fff;
}
</style>
