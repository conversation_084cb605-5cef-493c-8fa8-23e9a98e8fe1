<template>
  <PageWrapper>
    <div :class="prefixCls">
      <section class="header">
        <Form
          layout="inline"
          :model="filter.params"
          :label-col="{
            style: {
              width: '52px',
            },
          }"
        >
          <Row span="24" :gutter=8 class="row">
            <Col span="4">
              <div class="form-item">
                <Item :label="$t('views.demo.anttable.index.db0b5974', { defaultValue: '员工姓名' })" name="name">
                  <Input :placeholder="$t('views.demo.anttable.index.a5c989c7', { defaultValue: '请输入' })" v-model:value="filter.params.name" allow-clear />
                </Item>
              </div>
            </Col>

            <Col span="4">
              <div class="form-item">
                <Item :label="$t('views.demo.anttable.index.b0943821', { defaultValue: '员工类型' })" name="email">
                  <Select
                    allowClear
                    :placeholder="$t('views.demo.anttable.index.981355ab', { defaultValue: '请选择员工类型' })"
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    v-model:value="filter.params.type"
                  >
                    <Option value="0">{{$t('views.demo.anttable.index.64db3919', { defaultValue: '内部' })}}</Option>
                    <Option value="1">{{$t('views.demo.anttable.index.c234a9f6', { defaultValue: '外部' })}}</Option>
                  </Select>
                </Item>
              </div>
            </Col>
            <Col span="16">
              <div class="form-item form-btn">
                <Button>{{$t('views.demo.anttable.index.69187513', { defaultValue: '重置' })}}</Button>
                <Button type="primary" style="margin-left: 4px">{{$t('views.demo.anttable.index.7aa9138d', { defaultValue: '查询' })}}</Button>
              </div>
            </Col>
          </Row>
        </Form>
      </section>
      <section class="container">
        <div class="operation">
          <a-button class="operation-item" @click="successModal"> <DisconnectOutlined />{{$t('views.demo.anttable.index.73672dde', { defaultValue: '新增员工 ' })}}</a-button>
          <a-button class="operation-item"> <PlusOutlined />{{$t('views.demo.anttable.index.ae14ae82', { defaultValue: '导出 ' })}}</a-button>
        </div>
        <a-table
          bordered
          :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
          :columns="peopleTableConfig"
          :dataSource="mockList"
          :pagination="{
            pageSize: 20,
            defaultPageSize: 20,
            current: 1,
            total: 60,
            showQuickJumper: true,
            showTotal: (total) => {
              return h(
                'div',
                { class: 'people-pagination-total', style: { position: 'absolute', left: '0' } },
                [
                  h('span', {}, '共'),
                  h('span', { style: { color: '#00996b' } }, total),
                  h('span', {}, '条数据'),
                ]
              );
            },
          }"
          :scroll="{
            x: 800,
            scrollToFirstRowOnChange: true,
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'operation'">
              <div class="btn">
                <a-button
                  v-if="hasPermission('edit-user')"
                  type="link"
                  :class="record.tenantAdmin ? 'btn-item dis-btn' : 'btn-item primary-btn'"
                  >{{$t('views.demo.anttable.index.dd35e0c0', { defaultValue: '编辑 ' })}}</a-button>
                <a-button
                  type="link"
                  :class="record.tenantAdmin ? 'btn-item dis-btn' : 'btn-item primary-btn'"
                  >{{$t('views.demo.anttable.index.f97b5d9b', { defaultValue: '删除 ' })}}</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </section>
    </div>
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, h, reactive , onMounted } from 'vue';
  import { PageWrapper } from '@geega-ui-plus/geega-ui';
  import { PlusOutlined, DisconnectOutlined } from '@geega-ui-plus/icons-vue';
  import {
    TableColumnProps,
    Form,
    Select,
    Button,
    Input,
    Row,
    Col,
  } from '@geega-ui-plus/ant-design-vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useDesign } from '/@/hooks/web/useDesign';
  import i18nVue from '@geega/i18n-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { Item } = Form;

  export default defineComponent({
    name: 'anttable',
    components: {
      PageWrapper,
      Form,
      Item,
      Select,
      Button,
      Input,
      PlusOutlined,
      DisconnectOutlined,
      Row,
      Col,
    },
    setup() {
      const { hasPermission } = usePermission();
      const { prefixCls } = useDesign('ant-table');
      const mockList: any = [];
      for (let index = 0; index < 60; index++) {
        mockList.push({
          serial: index,
          name: 'Test' + index,
          tenantFullOrg: 'IT',
        });
      }

      const { createSuccessModal } = useMessage();

      const peopleTableConfig: TableColumnProps[] = [
        {
          key: '1',
          dataIndex: 'serial',
          title: i18nVue.t('views.demo.anttable.index.9aedcc25', { defaultValue: '序号' }),
          width: '20px',
          ellipsis: true,
        },
        {
          key: '2',
          dataIndex: 'name',
          title: i18nVue.t('views.demo.anttable.index.db0b5974', { defaultValue: '员工姓名' }),
          width: '50px',
          ellipsis: true,
        },
        {
          key: '3',
          dataIndex: 'tenantFullOrg',
          title: i18nVue.t('views.demo.anttable.index.66a06727', { defaultValue: '所属组织' }),
          width: '100px',
          ellipsis: true,
        },
        {
          key: '11',
          dataIndex: 'operation',
          title: i18nVue.t('views.demo.anttable.index.2535ccbc', { defaultValue: '操作' }),
          fixed: 'right',
          width: '20px',
        },
      ];

      const filter = reactive({
        params: {
          name: '',
          type: '',
        },
      });

      const successModal = ()=>{
        createSuccessModal({
            title:"tile",
            content:'content',
        });
      }

      onMounted(()=>{
        // 主动通知GMOM 离开该页面，需要应用确认
        if(window.__MICRO_APP_ENVIRONMENT__){
          window.microApp.dispatch({ appName:'vuewjdemo',event:'leaveCheck',mainAppName:"mom",data:{path:'/micro/vuewjdemo/demo/list'}})
        }
      })

      return {
        peopleTableConfig,
        mockList,
        h,
        hasPermission,
        prefixCls,
        filter,
        successModal
      };
    },
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-ant-table';

  .@{prefix-cls} {
    :deep(.@{ant-prefix}-form) {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      label::after {
        content: '';
      }
      .form-btn {
        display: flex;
        justify-content: flex-end;
      }
    }

    // :deep(.table-striped) td {
    //   background-color: @gplus-table-row-striped-bg-color;
    // }
    .row {
      flex: 1;
    }

    .header {
      padding: 8px 8px 0 8px;
      background-color: @gplus-white-color;
    }

    .container {
      padding: 0px 8px 0px;
      background-color: @gplus-white-color;
    }

    .operation {
      display: flex;
      align-items: center;
      background: @gplus-white-color;
      padding-bottom: 8px;
      margin-top: 2px;
      padding-top: 8px;
      .operation-item {
        display: flex;
        align-items: center;
        margin-right: 8px;
        cursor: pointer;
        color: @primary-color;
        border-color: @primary-color;
      }
    }
  }
</style>
