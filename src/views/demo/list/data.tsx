import { FormProps, SvgIcon, BasicColumn,DescItem, Copy ,TableCellTagType , TableCellStateType, GTag,Gbadge} from '@geega-ui-plus/geega-ui';
import regex from '/@/utils/regex';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();

import { Tooltip } from '@geega-ui-plus/ant-design-vue';
import GeegaCopy from '/@/components/GeegaCopy/index.vue';


// 基础table列数据源
export function getColumns(): BasicColumn[] {
  return [
    {
      title: t('view.ad.order'),
      dataIndex: 'order',
      align:'left',
      resizable:true,
      width:200,
      helpMessage:'....',
      minWidth:60,
      maxWidth:300,
      customRender: ({ record }) => {
        return (
          <GeegaCopy text={(record.order)} />
        );
      },
    },
    {
      title: t('view.ad.nu'),
      dataIndex: 'nu',
      align:'left',
      ellipsis: true,
      width:200,
      minWidth:60,
      maxWidth:300,
      resizable:true,
      copyable:true,
      customRender: ({ record }) => {
        return (
          <Tooltip title={record.nu}>
             <span>{record.nu}</span>
          </Tooltip>
        );
      },
    },
    {
      title: t('view.ad.other'),
      sorter: true,
      dataIndex: 'other',
      align:'left',
      width:200,
      minWidth:60,
      maxWidth:300,
      resizable:true,
      showSorterTooltip:true,
      copyable:true,
      tooltip:true,
      customTipText: (text) => `${text}+示范customTipText校准`,
    },
    {
      title: t('view.ad.delayStatus'),
      dataIndex: 'delayStatus',
      align:'left',
      width:100,
      minWidth:60,
      maxWidth:300,
      resizable:true,
      tooltip:true,
      customRender: ({ record }) => {
        return (
          // type "success" | "error" | "warning" | "processing" | "disabled" | "normal"
          <GTag type={record.tag==1?'error':'success'}>{record.delayStatus}</GTag>
        );
      },
      // tag: {
      //   // "success" | "error" | "warning" | "processing" | "disabled" | "normal"
      //   type:'success' as TableCellTagType,
      // },
      filters: [
        { text: t('view.ad.yes'), value: '1' },
        { text: t('view.ad.no'), value: '2' },
      ],
    },
    {
      title: t('view.ad.name'),
      dataIndex: 'name',
      align:'left',
      width:200,
      minWidth:60,
      maxWidth:300,
      resizable:true,
    },
    {
      title: t('view.ad.level'),
      dataIndex: 'level',
      align:'left',
      width:200,
      minWidth:60,
      maxWidth:300,
      resizable:true,
      tag: {
        // "success" | "error" | "warning" | "processing" | "disabled" | "normal"
        type:'success' as TableCellTagType, // 优先级低于bgColor color
      },
      customRender: ({ record }) => {
        return (
          <section>
            <span>{record.level}</span>
            {/* <Copy text={(record.level)} /> */}
          </section>
        );
      },
    },
    {
      title: t('view.ad.status'),
      dataIndex: 'status',
      width:200,
      align:'left',
      minWidth:60,
      maxWidth:300,
      customRender: ({ record }) => {
        return (
          // type:"success" | "error" | "warning" | "processing" | "disabled" | "normal"
          <Gbadge status='warning' text={record.status}></Gbadge>
        );
      },
      // badge:{
      //   type:'processing' as TableCellStateType,
      // },
      resizable:true,
    },
    {
      title: t('view.ad.detail'),
      dataIndex: 'detail',
      align:'left',
      width:200,
      minWidth:60,
      maxWidth:300,
      resizable:true,
    },

  ];
}

// maindev/*/list/detail?xx

// 菜单1 maindev/dev-->  MoMApp 容器 setData==>maindev/dev

// 菜单2 maindev/data-->  MoMApp 容器  setData => maindev/data

// ${setData}/list/detail?xx

// 基础table搜索表单数据源
export function getSearchFormConfig(): Partial<FormProps> {
  return {
    // uset
    labelWidth: 'unset',
    baseColProps: {
      span: 4,
    },
    rowProps:{
      gutter:20,
    },
    actionColOptions:{
      span:4,
    },
    autoAdvancedLine: 1,
    alwaysShowLines: 2,
    showAdvancedButton: true,
    schemas: [
      {
        field: 'order',
        label: t('view.ad.order'),
        component: 'Input',
        componentProps:{
          maxlength:6,
          showCount:true,
        }
      },
      {
        field: 'status',
        label: t('view.ad.status'),
        component: 'Select',
        componentProps: {
          options: [
            {
              label: t('view.ad.normal'),
              value: 1,
            },
            {
              label:  t('view.ad.exception'),
              value: 2,
            }
          ],
        },
      },
      {
        field: 'delayStatus',
        label:  t('view.ad.delay'),
        component: 'Select',
        defaultValue: [],
        componentProps: {
          mode: 'multiple',
          options: [
            {
              label: t('view.ad.yes'),
              value: 1,
            },
            {
              label: t('view.ad.no'),
              value: 2,
            }
          ],
        },
        renderComponentContent: () => {
          return {
            // 自定义ICON
            // suffixIcon: () => {
            //   return <SvgIcon name="g-icon_regular_down" />;
            // },
          };
        },
      },
      // {
      //   field: 'level',
      //   label: t('view.ad.level'),
      //   component: 'Select',
      //   componentProps: {
      //     options: [
      //       {
      //         label: level.hight,
      //         value: 1,
      //       },
      //       {
      //         label: level.middle,
      //         value: 2,
      //       },
      //       {
      //         label: level.low,
      //         value: 3,
      //       }
      //     ],
      //   },
      // },
      // {
      //   field: 'nu',
      //   labelWidth:54,
      //   label: t('view.ad.nu'),
      //   component: 'Input',
      // },
      {
        field: 'name',
        label: t('view.ad.name'),
        component: 'Input',
      },
      {
        field: 'beginTime',
        label:  t('view.ad.beginTime'),
        component: 'DatePicker',
        renderComponentContent: () => {
          return {
            suffixIcon: () => {
              return <SvgIcon name="g-icon_regular_time" />;
            },
          };
        },
      }
    ],
  };
}

// 抽屉表单数据源 -双行
export function getFormConfig(): Partial<FormProps> {
  return {
    baseColProps: {
      span: 11,
    },
    schemas: [
      {
        field: 'name',
        label: t('view.ad.name'),
        component: 'Input',
        helpMessage:'.....',
        defaultValue: '',
        dynamicDisabled:()=>{
          return true;
        },
        rules: [{ required: true, max: 20 }, regex.noSpecialSymbol],
      },
      {
        field: 'status',
        label: t('view.ad.status'),
        component: 'Select',
        componentProps: {
          options: [
            {
              label: t('view.ad.normal'),
              value: '0',
            },
            {
              label: t('view.ad.exception'),
              value: '1',
            },
            {
              label: t('view.ad.noStatus'),
              value: '2',
            }
          ],
        },
        colProps: {

          offset: 2,
        },
        renderComponentContent: () => {
          return {
            // 自定义ICON
            // suffixIcon: () => {
            //   return <SvgIcon name="g-icon_regular_down" />;
            // },
          };
        },
      },
      {
        field: 'note',
        label: t('view.ad.note'),
        component: 'InputTextArea',
        componentProps: {
          maxlength: 150,
          showCount: true,
          rows: 6,
        },
        colProps: {
          span: 24,
        },
      },
    ],
    showActionButtonGroup: false,
    // isSubmit:true,
    // actionColOptions: {
    //   span: 24,
    // },
  };
}

// 详情
export function getDescConfig(): DescItem[] {
  return [
    {
      field: 'name',
      label: t('view.ad.name'),
    },
    {
      field: 'status',
      label: t('view.ad.status'),
      render: (curVal) => {
        return `${curVal==1?t('view.ad.normal'):curVal==2 ? t('view.ad.exception'):t('view.ad.noStatus')}`;
      },
    },
    {
      field: 'other',
      span:12,
      label: t('view.ad.other'),
    }
  ];
}
