<template>
  <PageWrapper>
    <BasicTable bordered @register="registerTable" @resizeColumn="handleResizeColumn">
      <!-- 表格上方总操作栏 -->
      <template #toolbar>
        <div>
            <a-button type="primary" @click="handle(null, ActionTypeEnum.CREATE)">
              <template #icon>
              <disconnect-outlined />
            </template>
              {{ ActionTypeEnum.CREATE }}
            </a-button
          >
          <a-button type="primary" ghost  @click="handle(null, ActionTypeEnum.EXPORT)">
            <template #icon>
              <plus-outlined />
            </template>
            {{ ActionTypeEnum.EXPORT }}</a-button>
        </div>
      </template>
      <!-- 行操作栏 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction  :divider="false"  :actions="createActions(record)" :dropDownActions="[{label:'xxx'},{label: 'yyy'}]">
          </TableAction>
        </template>
      </template>

    </BasicTable>

    <BasicDrawer v-bind="$attrs"  class="add-drawer-dialog" showFooter :cancelText="t('view.ad.cancel')" :okText="t('view.ad.ok')" :showCancelBtn="true" :showOkBtn="true" :title="actionType" @register="registerFormDrawer" @close="resetFormFields" @ok="onSubmitForCreate">
      <BasicForm layout="vertical" @register="registerForm" />
    </BasicDrawer>

    <BasicDrawer :title="t('view.ad.desc')" @register="registerDescDrawer">
      <Description @register="registerDesc" class="mt-4" />
      <template #footer></template>
    </BasicDrawer>

    <GeegaIconModal @register="registerIconModal" @success="chooseSuccess" />

  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, toRaw, nextTick, onMounted } from 'vue';
  import {
    BasicTable,
    useTable,
    TableAction,
    EditRecordRow,
    ActionItem,
    PageWrapper,
    BasicDrawer,
    useDrawer,
    BasicForm,
    useForm,
    Description,
    useDescription,
    useModal
  } from '@geega-ui-plus/geega-ui';
  import { PlusOutlined , DisconnectOutlined } from '@geega-ui-plus/icons-vue';
  import { getColumns, getSearchFormConfig, getFormConfig,getDescConfig } from './data';
  import { demoListApi,unreadCount } from '/@/api/demo/demo';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import GeegaIconModal from '/@/components/GeegaIconModal/index.vue';
  import { Select } from '@geega-ui-plus/ant-design-vue';
  import i18nVue from '@geega/i18n-vue';

  export default defineComponent({
    name: 'Ad',
    components: { BasicTable, PageWrapper, BasicDrawer, BasicForm, TableAction, PlusOutlined , DisconnectOutlined, Description, GeegaIconModal, Select},
    setup() {

    const [registerIconModal, { openModal: openIconModal }] = useModal();

    const { t } = useI18n();

    const ActionTypeEnum = {
      CREATE:t('view.ad.add'),
      EDIT:t('view.ad.edit'),
      DELETE:t('view.ad.delete'),
      EXPORT:t('view.ad.export'),
      DESC:t('view.ad.desc'),
    };

    enum ConfirmTypeEnum {
      WARNINFG = 'warning',
      ERROR = 'error',
      SUCCESS = 'success',
      INFO = 'info',
    }

      const { createMessage, createDeteleConfirm,createErrorModal } = useMessage();
      // 操作类型
      const actionType = ref(ActionTypeEnum.CREATE);
      // 表格
      const [registerTable, { reload }] = useTable({
        // canResize:false,
        api: demoListApi,
        showTableSetting: true,
        fetchSetting:{

        },
        tableSetting: {
          redo: false,
          size: false,
          setting: true,
          fullScreen: false,
        },
        afterFetch: (t) => {
          // console.log('afterFetch data:', t);
        },
        indexColumnProps:{
          width:80
        },
        columns: getColumns(),
        rowKey: 'order',
        // 表格-搜索栏属性
        formConfig: getSearchFormConfig(),
        useSearchForm: true,
        rowSelection: {
          type: 'checkbox',
        },
        // 表格上方tabs选择器
        // tableTabs: {
        //   filed: 'status',
        //   activeKey: '',
        //   options: [
        //     { label: '全部', value: '' },
        //     { label: '异常', value: '1' },
        //     { label: '正常', value: '2' },
        //     { label: '未知状态', value: '3' },
        //   ],
        // },
        // 表格-操作栏属性
        actionColumn: {
          width: 180,
          title:  t('view.ad.option'),
          dataIndex: 'action',
          align:"center",
          fixed: 'right',
        },
        // isEnableLocalColumnsStorage: true,
        // columnsStorageId: '1676448853558' + 'geega-template-demo-ad-table',
      });
      // 表格-操作栏实例化
      const createActions = (record: EditRecordRow): ActionItem[] => {
        return [
          {
            label: ActionTypeEnum.EDIT,
            onClick: () => handle(record, ActionTypeEnum.EDIT),
          },
          {
            label: ActionTypeEnum.DELETE,
            // danger:true,
            onClick: () => handle(record, ActionTypeEnum.DELETE),
          },
          {
            label: ActionTypeEnum.DESC,
            onClick: () => handle(record, ActionTypeEnum.DESC),
          },
        ];
      };

      // 表格-操作-删除
      const handleDelete = (record) => {
        // createErrorModal({
        //   title:'....',
        //   content:'content',
        //   okText:'我知道了'
        // })
        createDeteleConfirm({
          type: ConfirmTypeEnum.WARNINFG,
          content: `${t('view.ad.confirm')} ${actionType.value + record.name}？`,
          onOk: async () => {
            // 删除逻辑
            //xxxx

            // 重载刷新列表
            reload();
          },
        });
      };

      // form表单
      const [
        registerFormDrawer,
        { openDrawer: openFormDrawer, changeLoading: changeFormLoading },
      ] = useDrawer();

      const [
        registerDescDrawer,
        { openDrawer: openDescDrawer },
      ] = useDrawer();

      const [
        registerForm,
        { resetFields: resetFormFields, setFieldsValue: setFormFieldsValue,validate, getFieldsValue },
      ] = useForm(getFormConfig());
      // form表单-创建操作
      const handleCreate = () => {
        openFormDrawer();
      };
      // form表单-编辑操作
      const handleEdit = (record) => {
        openFormDrawer();
        nextTick(() => {
          setFormFieldsValue(record);
        });
      };


      const descData = ref({});
      const [registerDesc,{setDescProps}] = useDescription({
        data: descData.value,
        schema: getDescConfig(),
      });

      const handleDesc = (record) => {
        openDescDrawer();
        nextTick(() => {
          setDescProps({
            data:record
          })
        });
      };

      const handleEx = (record) => {
        openIconModal(true, { record });
      };

      const chooseSuccess = (e) => {
        console.log(i18nVue.t('views.demo.list.index.36cd28e8', { defaultValue: '回传参数:' }),e)
      };

      // form表单-提交操作
      const onSubmitForCreate = async () => {
        const checkRes = await validate();
        console.log('validate',checkRes)
        const values = getFieldsValue();
        console.log('getFieldsValue',values)
        if (values) {
          try {
            if (actionType.value === ActionTypeEnum.CREATE) {
              // await demoPostApi(values);
            } else {
              // await demoPostApi(values);
            }
            changeFormLoading(true);
            setTimeout(() => {
              changeFormLoading(false);
              createMessage.success(actionType.value + t('view.ad.success'));
              reload();
              resetFormFields();
              openFormDrawer(false);
            }, 3000);

          } finally {
            // changeFormLoading(false);
          }
        }
      };

      // 操作总调度
      const handle = (record, action) => {
        // 在入口处解构，方便在操作函数内部中直接使用
        const curRecord = toRaw(record);
        const productName = curRecord?.name ? curRecord?.name : " ";
        actionType.value = action +" "+ productName
        switch (action) {
          case ActionTypeEnum.CREATE:
            handleCreate();
            break;
          case ActionTypeEnum.EDIT:
            handleEdit(curRecord);
            break;
          case ActionTypeEnum.DELETE:
            handleDelete(curRecord);
            break;
          case ActionTypeEnum.DESC:
            handleDesc(curRecord);
            break;
          case ActionTypeEnum.EXPORT:
            handleEx(curRecord);
            break;
        }
      };

      const handleResizeColumn = (w, col) => {
        col.width = w;
      };

      onMounted(()=>{
        // 主动通知GMOM 离开该页面，需要应用自身控制
        if(window.__MICRO_APP_ENVIRONMENT__){
          window.microApp.dispatch({ appName:'vuewjdemo',event:'leaveCheck',mainAppName:"mom",data:{path:'/micro/vuewjdemo/demo/list'}})
        }
        // unreadCount({}).then((data)=>{
        //   console.log(data)
        // })
      })

      return {
        handle,
        ActionTypeEnum,
        registerTable,
        registerFormDrawer,
        openFormDrawer,
        registerForm,
        onSubmitForCreate,
        actionType,
        createActions,
        resetFormFields,
        registerDescDrawer,
        registerDesc,
        handleResizeColumn,
        t,
        registerIconModal,
        chooseSuccess
      };
    },
  });
</script>
