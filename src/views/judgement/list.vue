<script lang="ts" setup>
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { Button, message } from '@geega-ui-plus/ant-design-vue';
import { PlusOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useDrawer } from '@geega-ui-plus/geega-ui';
import CreateDrawer from './components/CreateDrawer.vue';
import { V1EvaluateTaskPagePost, V1EvaluateTaskDeleteIdDelete } from '/@/api/cddc.req';
import type { PurpleAPIModel } from '/@/api/cddc.model';
import { EvaluateStatusEnum, EvaluateStatusOptions } from '/@/enums/projectEnum';
import { getDictItem, getDictLabel } from '/@/dict';
import ReviewDrawer from './components/ReviewDrawer.vue';
import { getUserId } from '/@/logics/userUtils';

const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'evaluateName',
    width: 200,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入项目名称',
      },
    },
  },
  {
    title: '人员名称',
    dataIndex: 'assessorName',
    width: 150,
    search: {
      field: 'userName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入人员名称',
      },
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: EvaluateStatusOptions,
      },
    },
  },
  {
    title: '评审人员',
    dataIndex: 'reviewerList',
    width: 150,
    tooltip: true,
    customRender({ text }) {
      return text?.map((item) => item.userName).join(',');
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '更新时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
  },
];

const [registerTable, tableActions] = useTable({
  columns,
  api: V1EvaluateTaskPagePost,
  tableProps: {
    actionColumn: {
      width: 180,
    },
  },
});

const { createDeteleConfirm } = useMessage();

const [registerCreateDrawer, createDrawerActions] = useDrawer();

const [registerReviewDrawer, reviewDrawerActions] = useDrawer();

function handleReview(record: PurpleAPIModel) {
  reviewDrawerActions.openDrawer(true, record);
}

function handleDetail(record: PurpleAPIModel) {
  reviewDrawerActions.openDrawer(true, record);
}

function handleDelete(record: PurpleAPIModel) {
  createDeteleConfirm({
    content: `请确定是否删除评审记录?`,
    onOk: async () => {
      await V1EvaluateTaskDeleteIdDelete({ id: record.id! });
      message.success('删除成功');
      tableActions.reload();
    },
  });
}

function getStatusText(status?: number) {
  return getDictLabel(EvaluateStatusOptions, status?.toString());
}

function getStatusTagColor(status?: number) {
  return getDictItem(EvaluateStatusOptions, status?.toString())?.colorType;
}

function handleAdd() {
  createDrawerActions.openDrawer(true, {});
}

function handleSuccess() {
  tableActions.reload();
}

const showReviewButton = (record: PurpleAPIModel) => {
  if (record.status?.toString() !== EvaluateStatusEnum.PENDING) {
    return false;
  }

  return record.reviewerList?.find((item) => {
    return item.userId === getUserId();
  });
};
</script>

<template>
  <div class="judgement-list-page">
    <BasicTablePlus @register="registerTable">
      <template #toolbar>
        <Button type="primary" ghost @click="handleAdd" style="margin-bottom: 12px">
          新增评审任务
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button v-if="showReviewButton(record)" type="link" @click="handleReview(record)">
              进行评审
            </a-button>
            <a-button type="link" v-else @click="handleDetail(record)">详情</a-button>
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusTagColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
      </template>
    </BasicTablePlus>

    <CreateDrawer @register="registerCreateDrawer" @success="handleSuccess" />
    <ReviewDrawer @register="registerReviewDrawer" @success="handleSuccess" />
  </div>
</template>

<style lang="less" scoped>
.judgement-list-page {
  padding: 0 8px;
}
</style>
