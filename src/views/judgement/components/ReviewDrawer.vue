<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner, type FormSchema } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import type {
  PurpleAPIModel,
  V1EvaluateTaskOrderInfoIDPostResponseResult,
} from '/@/api/cddc.model';
import { EvaluateStatusEnum } from '/@/enums/projectEnum';
import { V1EvaluateTaskOrderInfoIdPost, V1EvaluateTaskSubmitPost } from '/@/api/cddc.req';
import { getUserId } from '/@/logics/userUtils';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '评定任务详情',
});

const currentData = ref<V1EvaluateTaskOrderInfoIDPostResponseResult>({});

const PassOptions = [
  { label: '通过', value: '1' },
  { label: '不通过', value: '0' },
];

const [registerDrawer, drawerActions] = useDrawerInner(async (props: PurpleAPIModel) => {
  state.drawerTitle = '评定任务详情';
  await formActions.resetFields();

  const infoData = await V1EvaluateTaskOrderInfoIdPost({
    id: props.id!,
  });

  const isPending = infoData.status?.toString() === EvaluateStatusEnum.PENDING;

  const resultList: any = isPending
    ? props.evaluateProjectList?.map((item) => ({
      evaluateProject: item,
    }))
    : infoData.resultList?.map((item) => ({ ...item, result: item.result.toString() }));

  currentData.value = {
    ...props,
    ...(infoData || {}),
    result: isPending ? undefined : (infoData.result?.toString() as any),
    duration: isPending ? undefined : infoData.duration,
    resultList: resultList || [],
    reviewUserNames: infoData.reviewerList?.map((item) => item.userName).join(','),
  };

  await updateFormSchemas({
    showResultList: !!currentData.value.resultList?.length,
  })

  await formActions.setFieldsValue({
    ...currentData.value,
  });

  await formActions.clearValidate();
});

const isDisabled = computed(
  () =>
    currentData.value.status?.toString() !== EvaluateStatusEnum.PENDING ||
    !currentData.value.reviewerList?.find((item) => item.userId === getUserId())
);

const [registerForm, formActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: getFormSchema({ showResultList: false }),
  showActionButtonGroup: false,
});

async function updateFormSchemas(opt: { showResultList?: boolean }) {
  const schemas: FormSchema[] = getFormSchema(opt);
  await formActions.updateSchema(schemas);
}

function getFormSchema(opt: { showResultList?: boolean }) {
  const schemas: FormSchema[] = [
    {
      field: 'assessorName',
      label: '评定对象',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'reviewUserNames',
      label: '评审人',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'resultList',
      label: '评审项目',
      component: 'Input',
      required: true,
      ifShow: opt.showResultList,
      slot: 'skill-group',
      rules: [
        {
          async validator(rule, value) {
            const invalid = value?.some((n) => n.result == null);

            if (invalid) {
              throw new Error('请选择评审结果');
            }
          },
        },
      ],
    },
    {
      field: 'duration',
      label: '所用时长（分钟）',
      component: 'InputNumber',
      required: true,
      componentProps: {
        disabled: isDisabled,
        min: 1,
        precision: 0,
        max: 999,
        style: 'width: 100%',
      },
    },
    {
      field: 'result',
      label: '评定结果',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        disabled: isDisabled,
        options: PassOptions,
      },
    },
  ];

  return schemas;
}


async function handleSubmit() {
  if (isDisabled.value) {
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
    return;
  }

  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await formActions.validate();

    await V1EvaluateTaskSubmitPost({
      ...data,
      id: currentData.value.id,
    });

    message.success('评审成功');
    emit('success');

    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });
    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #skill-group="{ field }">
        <a-form-item-rest>
          <div class="flex flex-col gap-2">
            <div class="skill-item" v-for="item in currentData.resultList">
              <label>{{ item.evaluateProject }}</label>
              <a-radio-group
                :disabled="isDisabled"
                v-model:value="item.result"
                :options="PassOptions"
                @change="formActions.validateFields([field])"
              />
            </div>
          </div>
        </a-form-item-rest>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped>
.skill-item {
  display: flex;
  flex-direction: column;
  border: 1px dashed gray;
  padding: 8px;
  border-radius: 4px;
}
</style>
