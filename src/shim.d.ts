declare interface Window {
  __POWERED_BY_QIANKUN__: string;
  __webpack_public_path__: string;
  __INJECTED_PUBLIC_PATH_BY_QIANKUN__: string;
  __MICRO_APP_BASE_ROUTE__: string;
  __MICRO_APP_ENVIRONMENT__: string;
  __MICRO_APP_PUBLIC_PATH__: string;
  __KEYCLOAK__: any;
  __GEEGAGUC__: any;
  microApp: any;
  __POWERED_BY_QIANKUN__:any
  // 是否存在无界
  __POWERED_BY_WUJIE__?: boolean;
  // 子应用mount函数
  __WUJIE_MOUNT: () => void;
  // 子应用unmount函数
  __WUJIE_UNMOUNT: () => void;
  // 子应用无界实例
  __WUJIE: { mount: () => void };
  $wujie:any;
  __MICRO_MONITOR__:any;
  __ClientTemplateMonitor__:any;
  Ping:any;
}

declare let __webpack_public_path__: string;

declare let process: any;
