import Guc from '@geega-components/geega-guc-js-next-sdk';
import { useGlobSetting } from '/@/hooks/setting';

import { useLocale } from '/@/locales/useLocale';
const { getCurrentLocale } = useLocale();

const globSetting = useGlobSetting();

const { appId, gucApiUrl,env } = globSetting;
let geegaguc: any;


const mapGUCLang = {
    "zh_CN":"zh-CN",
    "en":"en-US",
}

// 此处注意：keycloak 仅为融合老项目保留 ，主应用建议全局暴露  __GEEGAGUC__
if (window.__KEYCLOAK__ || window.__GEEGAGUC__ ) {
  geegaguc = window.__KEYCLOAK__ || window.__GEEGAGUC__  ;
} else {
  if(window.__POWERED_BY_WUJIE__ && window.parent.__GEEGAGUC__)
  {
    geegaguc = window.parent.__GEEGAGUC__ ;
  } else {
    geegaguc =new Guc({
      server: gucApiUrl,
      env,
      appId,
      axiosRequestConfig: {
        headers: {
          'Accept-Language': mapGUCLang[getCurrentLocale()] || getCurrentLocale(),
        },
      },
    });
  }
}

export default geegaguc;
