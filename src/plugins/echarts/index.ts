import * as echarts from 'echarts/core';
import darkTheme from './theme';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ctorialBar<PERSON>hart } from 'echarts/charts';

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  LegendComponent,
} from 'echarts/components';

import { Canvas<PERSON>enderer, SVGRenderer } from 'echarts/renderers';

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  Map<PERSON>hart,
  SVGRenderer,
  CanvasRenderer,
  PictorialBarChart,
]);

echarts.registerTheme('dark', darkTheme);

export default echarts;
