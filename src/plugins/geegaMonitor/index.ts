import ClientMonitor from '@tce/online-monitoring';

import { useGlobSetting } from '/@/hooks/setting';

import { isRunMicroEnv } from '/@/utils/micro'

const globSetting = useGlobSetting();

const { cmAppId, cmApiUrl, env} = globSetting;

const  CMonitor = {

     init: ()=> {
      if(cmApiUrl && cmAppId){
        window.__ClientTemplateMonitor__ = ClientMonitor;
        if(isRunMicroEnv() && window.__MICRO_MONITOR__){
          window.__ClientTemplateMonitor__ = window.__MICRO_MONITOR__;
        } else {
          window.__ClientTemplateMonitor__.register({
            appID: cmAppId, // 应用ID，必填，由监控平台分配
            collector: cmApiUrl, // 监控平台地址，必填
            service: `geega-admin-template-${env}`, // 业务系统名，必须英文。
            pagePath: window.location.hash, // 路由地址，注意：哈希路由用window.location.hash
            serviceVersion: 'v1.0.0', // 业务系统版本号
            feedback: false,
            noTraceOrigins: [], // 白名单，如有用到第三方接口，必填
          })
        }
      }

      // window.__ClientTemplateMonitor?.customOptions?.onTraceOrigins?.push('http://xxx.com')
     }
}



export default CMonitor;
