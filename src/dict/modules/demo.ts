import { DictItem, DictColorTypeEnum } from '../model';
  import i18nVue from '@geega/i18n-vue';

const STATUS: DictItem[] = [
  { value: '0', label: i18nVue.t('dict.modules.demo.ec5f4145', { defaultValue: '待提交' }), colorType: DictColorTypeEnum.DISABLED },
  { value: '1', label: i18nVue.t('dict.modules.demo.5605ce3b', { defaultValue: '待审核' }), colorType: DictColorTypeEnum.PRIMARY },
  { value: '2', label: i18nVue.t('dict.modules.demo.68d4b919', { defaultValue: '被驳回' }), colorType: DictColorTypeEnum.ERROR },
  { value: '3', label: i18nVue.t('dict.modules.demo.cb95accf', { defaultValue: '已发布' }), colorType: DictColorTypeEnum.SUCCESS },
];

const STATE: DictItem[] = [
  { value: '0', label: i18nVue.t('dict.modules.demo.ec5f4145', { defaultValue: '待提交' }), colorType: DictColorTypeEnum.DISABLED },
  { value: '1', label: i18nVue.t('dict.modules.demo.5605ce3b', { defaultValue: '待审核' }), colorType: DictColorTypeEnum.PRIMARY },
  { value: '2', label: i18nVue.t('dict.modules.demo.68d4b919', { defaultValue: '被驳回' }), colorType: DictColorTypeEnum.ERROR },
  { value: '3', label: i18nVue.t('dict.modules.demo.cb95accf', { defaultValue: '已发布' }), colorType: DictColorTypeEnum.SUCCESS },
];

export default {
  STATUS,
  STATE,
};
