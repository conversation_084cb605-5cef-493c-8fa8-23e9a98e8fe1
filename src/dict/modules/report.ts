import type { DictItem } from '../model';

/**
 *
    TO_BE("TO_BE", "待训练"),
    ING("ING", "训练中"),
    COMPLETED("COMPLETED", "训练完成"),
    NOT_ALLOWED("NOT_ALLOWED", "不允许"),
    TO_BE_CHECK("TO_BE_CHECK", "待考核"),
    CHECKED("CHECKED", "考核完成"),
 */
export const REPORT_STATUS: DictItem[] = [
  {
    label: '待训练',
    value: 'TO_BE',
    colorType: 'warning',
  },
  {
    label: '训练中',
    value: 'ING',
    colorType: 'processing',
  },
  {
    label: '训练完成',
    value: 'COMPLETED',
    colorType: 'success',
  },
  {
    label: '待考核',
    value: 'TO_BE_CHECK',
    colorType: 'warning',
  },
  {
    label: '考核完成',
    value: 'CHECKED',
    colorType: 'success',
  },
  {
    label: '待考试',
    value: 'TO_BE_TEST',
    colorType: 'warning',
  },
  {
    label: '考试未通过',
    value: 'FAILED_TEST',
    colorType: 'error',
  },
  {
    label: '考试通过',
    value: 'PASSED_TEST',
    colorType: 'success',
  },
  // {
  //   label: '不允许',
  //   value: 'NOT_ALLOWED',
  //   colorType: 'error',
  // },
];
