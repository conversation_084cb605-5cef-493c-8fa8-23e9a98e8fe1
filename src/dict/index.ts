import { cloneDeep } from 'lodash-es';
import { DictItem } from './model';
import i18nVue from '@geega/i18n-vue';

// @ts-ignore
const dicts = import.meta.globEager('./modules/*.ts');
const dictModule: any = {};

Object.keys(dicts).forEach((key) => {
  const name = key.split('/')[2].split('.')[0].toUpperCase();
  dictModule[name] = dicts[key].default;
});

dictModule.get = function (scope: string, value: string | number, dictItemKey = 'value') {
  if (!scope) {
    return '--';
  }

  try {
    const [area_1, area_2, area_3] = scope.split('.');

    const dict = (this[area_1]?.[area_2] || []) as DictItem[];

    const item = dict.find((item) => value.toString() === item.value.toString()) || {};

    if (!dict.length) {
      console.warn('无字典数据');
    }

    if (area_3) {
      const errorText = i18nVue.t('dict.index.378ef0ff', {
        defaultValue: '数据字典域只支持2级！想使用多级，自己来改！！',
      });
      alert(errorText);
      console.error(new Error(errorText));
      return undefined;
    }

    if (value === undefined) {
      // 不传code的时候返字典数组
      return cloneDeep(dict);
    }

    return dictItemKey == null ? item : item[dictItemKey];
  } catch (error) {
    console.error(error);
  }
};

export default dictModule;

export function getDictItem(options: DictItem[], value?: string | number) {
  return options.find((item) => item.value === value);
}

export function getDictLabel(options: DictItem[], value?: string | number, defaultLabel = '-') {
  return getDictItem(options, value)?.label || defaultLabel;
}
