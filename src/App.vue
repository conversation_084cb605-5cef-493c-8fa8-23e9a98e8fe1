<template>
  <!-- :autoInsertSpaceInButton="false" -->
  <ConfigProvider
    v-bind="lockEvent"
    :locale="getAntdLocale"
    :renderEmpty="customizeRenderEmpty"
    :prefixCls="pkg.gplusPreFixCls"
  >
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts">
import { defineComponent, onUnmounted, provide } from 'vue';
import { ConfigProvider } from '@geega-ui-plus/ant-design-vue';
import { AppProvider } from '@geega-ui-plus/geega-ui';
import pkg from '../package.json';

import { initAppConfigStore } from '/@/logics/initAppConfig';

import { useLockPage } from '/@/hooks/web/useLockPage';
import { useLocale } from '/@/locales/useLocale';

import 'dayjs/locale/zh-cn';

import { customizeRenderEmpty } from '/@/components/GeegaEmpty/empty';

import { appStore } from '/@/store/modules/app';

import { isRunMicroEnv } from '/@/utils/micro';
import { tokenRefresher } from '/@/utils/auth/tokenRefresher';
import { THEME_KEY } from 'vue-echarts';

export default defineComponent({
  name: 'App',
  components: { ConfigProvider, AppProvider },
  setup() {
    // setup echarts theme
    provide(THEME_KEY, 'dark')

    // support Multi-language
    const { getAntdLocale } = useLocale();

    // Initialize vuex internal system configuration
    let projectConfig = {};

    // micro / qiankun / wujie
    // micro的时候 隐藏导航
    if (isRunMicroEnv()) {
      // 融合mom的设置隐藏 左侧菜单 与 顶部
      projectConfig = {
        showLogo: false, // 不显logo
        menuSetting: {
          show: false, // 不显菜单
        },
        headerSetting: {
          show: false, // 不限时头部时
        },
      };
      appStore.commitProjectConfigState(projectConfig);
    }

    initAppConfigStore();

    // Create a lock screen monitor
    const lockEvent = useLockPage();

    // 在组件卸载时停止token刷新服务
    onUnmounted(() => {
      tokenRefresher.stopRefreshTimer();
    });

    return {
      getAntdLocale,
      lockEvent,
      pkg,
      customizeRenderEmpty,
    };
  },
});
</script>
