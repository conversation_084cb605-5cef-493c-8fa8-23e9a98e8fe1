import type { FormSchema, FormProps } from '@geega-ui-plus/geega-ui';
import {
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  V1ManageQuestionBankColumnMappingListPost,
  V1ManageQuestionBankTypeGetQuestionBankTypeById,
  V1ManageQuestionBankColumnGetQuestionBankColumnById,
} from '/@/api/cddc.req';
import type { QuestionBankColumnDTOElement } from '/@/api/cddc.model';
import { buildUUID } from '/@/utils/uuid';
import { clamp } from 'lodash-es';

export const dynamicFieldMark = '__dynamicFieldMark__';

const splitMarkStr = '__$##$__'

type QuestionBankDynamicFieldFormSchemaProps = {
  formSchema: FormSchema[];
  fields: QuestionBankColumnDTOElement[];
};

export async function createQuestionBankDynamicFieldFormSchema(
  questionBankId?: string,
  isHasPropertyList?: boolean
): Promise<QuestionBankDynamicFieldFormSchemaProps> {
  const V1ManageQuestionBankTypeGetQuestionBankTypeByIdApi = async (_opt) => {
    const res = await V1ManageQuestionBankTypeGetQuestionBankTypeById({ typeId: questionBankId });

    const _list = (res?.questionBankTypeColumns || []).filter((o) => !!o.questionBankColumnDTO);

    return _list.map((o) => {
      const { columnKey, columnName, id } = o.questionBankColumnDTO || {};
      return {
        columnKey,
        columnName,
        id,
      };
    });
  };

  const api = questionBankId
    ? V1ManageQuestionBankTypeGetQuestionBankTypeByIdApi
    : V1ManageQuestionBankColumnGetQuestionBankColumnListPost;

  const resp = await api({});

  const formSchema = (resp || []).map((item) => {
    const commonProps = {
      field: `${dynamicFieldMark}${item.columnKey!}`,
      label: item.columnName!,
    };
    return isHasPropertyList
      ? {
          ...commonProps,
          component: 'ApiTreeSelect',
          componentProps: {
            allowClear: true,
            maxTagCount: 'responsive',
            multiple: true,
            treeCheckable: true,
            treeDefaultExpandAll: true,
            virtual: false,
            showArrow: true,
            api: loadQuestionBankColumnMappingTreeList(item.id!),
            fieldNames: {
              label: 'label',
              value: 'value',
              key: 'value',
              children: 'childNodes',
            },
            treeNodeFilterProp: 'label',
          },
        }
      : {
          ...commonProps,
          component: 'ApiSelect',
          componentProps: {
            api: loadQuestionBankColumnMappingList(item.id!),
            showSearch: true,
            optionFilterProp: 'label',
            mode: 'multiple',
            showArrow: true,
            maxTagCount: 'responsive',
          },
        };
  });
  return {
    formSchema,
    fields: resp || [],
  } as unknown as QuestionBankDynamicFieldFormSchemaProps;
}

type ChildNode = {
  label: string;
  value?: string;
  _value?: string;
};

function loadQuestionBankColumnMappingTreeList(id: string) {
  return async () => {
    const res = await V1ManageQuestionBankColumnGetQuestionBankColumnById({
      columnId: id,
    });

    const tree = [
      {
        label: res.columnName!,
        value: `${res.columnKey}${splitMarkStr}${buildUUID()}`,
        _value: res.columnKey!,
        childNodes: [] as ChildNode[],
        disabled: true,
        checkable: false
      },
      ...(res.questionBankColumnPropertyList || []).map((item) => ({
        label: item.propertyName!,
        value: `${item.propertyKey}${splitMarkStr}${buildUUID()}`,
        _value: item.propertyKey!,
        childNodes: [] as ChildNode[],
        disabled: true,
        checkable: false
      })),
    ];

    const resp = await V1ManageQuestionBankColumnMappingListPost({
      columnId: id,
    });

    const [self, ...other] = tree;

    resp?.forEach((o) => {
      const _exist = self.childNodes.some((opt) => opt._value === o.columnValue);
      if (!_exist) {
        self.childNodes.push({
          label: o.columnValue!,
          value: o.columnValue ? `${o.columnValue}${splitMarkStr}${buildUUID()}`: o.columnValue,
          _value: o.columnValue
        });
      }

      const col = tree.find((opt) => opt._value === o.propertyKey);

      if (col) {
        const _exist = col.childNodes.some((opt) => opt._value === o.propertyValue);

        if (!_exist) {
          col.childNodes.push({
            label: o.propertyValue!,
            value: o.propertyValue ? `${o.propertyValue}${splitMarkStr}${buildUUID()}`: o.propertyValue,
            _value: o.propertyValue
          });
        }
      }
    });

    return [self, ...other].filter(o => !!o.childNodes.length);

  };
}

function loadQuestionBankColumnMappingList(id: string) {
  return async () => {
    const resp = await V1ManageQuestionBankColumnMappingListPost({
      columnId: id,
    });

    const opts = (resp || []).map((o) => o.columnValue).filter(Boolean);

    const _opts = [...new Set(opts)];

    return _opts.map((o) => ({
      label: o,
      value: o,
    }));
  };
}

export function creatSearchConfig(schemas: FormSchema[] = []): Partial<FormProps> {
  return {
    labelWidth: 'auto',
    baseColProps: {
      span: 6,
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    schemas,
    actionColOptions: {
      span: 24,
    },
    rowProps: {
      gutter: 16,
    },
  };
}

export function formatDynamicParams(params: Record<string, any> = {}, dynamicFieldKey: string) {
  const _keys = Object.keys(params);

  return _keys.reduce(
    (iter, item) => {
      const _is = item.startsWith(dynamicFieldMark);

      if (_is) {
        const _list = (params[item] || []) as string[];

        const columnKey = item.replace(dynamicFieldMark, '');

        _list.forEach((_columnValue) => {
          let columnValue = _columnValue

          if (columnValue && columnValue.includes(splitMarkStr)) {

            columnValue = columnValue.split(splitMarkStr)[0]

          }

          iter[dynamicFieldKey].push({
            columnKey,
            columnValue,
          });
        });
      } else {
        iter[item] = params[item];
      }
      return iter;
    },
    { [dynamicFieldKey]: [] } as Record<string, any>
  );
}
