interface WebSocketOptions {
  url: string;
  userId?: string | number;
  detailId?: string | number;
  locationId?: string | number;
  headers?: Record<string, string>;
  onMessage?: (data: any) => void;
  onError?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onOpen?: (event: Event) => void;
  reconnectLimit?: number;
  reconnectInterval?: number;
  createMessage?: {
    action: string;
    content?: Record<string, any>;
  };
  heartbeatMessage?: {
    action: string;
    content?: Record<string, any>;
  };
}

/**
 * WebSocket客户端
 */
export class WebSocketClient {
  private ws: WebSocket | null = null;
  private readonly options: WebSocketOptions;
  private reconnectCount = 0;
  private reconnecting = false;
  private pingTimer: number | null = null;
  private pongTimer: number | null = null;
  private isManualClosed = false;

  constructor(options: WebSocketOptions) {
    this.options = {
      reconnectLimit: 3,
      reconnectInterval: 3000,
      createMessage: options.createMessage || {
        action: 'create',
        content: {
          userId: options.userId,
          detailId: options.detailId,
          locationId: options.locationId,
        },
      },
      heartbeatMessage: options.heartbeatMessage || {
        action: 'heartbeat',
        content: {
          userId: options.userId,
          detailId: options.detailId,
          locationId: options.locationId,
        },
      },
      ...options,
    };
    this.connect();
  }

  private connect() {
    if (this.ws) {
      this.ws.close();
    }

    try {
      let wsUrl = this.options.url;

      this.ws = new WebSocket(wsUrl);

      if (this.options.headers && Object.keys(this.options.headers).length > 0) {
        console.log('WebSocket headers will be sent in the initial message:', this.options.headers);
      }

      this.ws.onopen = this.onOpen.bind(this);
      this.ws.onclose = this.onClose.bind(this);
      this.ws.onerror = this.onError.bind(this);
      this.ws.onmessage = this.onMessage.bind(this);
    } catch (error) {
      console.error('WebSocket连接错误:', error);
      if (!this.isManualClosed) {
        this.reconnect();
      }
    }
  }

  private onOpen(event: Event) {
    console.log('WebSocket已连接');
    this.reconnectCount = 0;
    this.reconnecting = false;
    this.isManualClosed = false;

    if (this.options.headers && Object.keys(this.options.headers).length > 0) {
      const createMessageWithHeaders = {
        ...this.options.createMessage,
        headers: this.options.headers,
      };
      this.send(createMessageWithHeaders);
    } else {
      this.send(this.options.createMessage);
    }

    this.startHeartbeat();
    this.options.onOpen?.(event);
  }

  private onClose(event: CloseEvent) {
    console.log('WebSocket已关闭', this.isManualClosed ? '(手动关闭)' : '(异常关闭)');
    this.stopHeartbeat();
    if (!this.isManualClosed) {
      this.reconnect();
    }
    this.options.onClose?.(event);
  }

  private onError(event: Event) {
    console.error('WebSocket错误:', event);
    this.options.onError?.(event);
  }

  private onMessage(event: MessageEvent) {
    try {
      const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
      this.options.onMessage?.(data);
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  private reconnect() {
    if (this.reconnecting || this.reconnectCount >= (this.options.reconnectLimit || 3)) {
      return;
    }

    this.reconnecting = true;
    this.reconnectCount++;

    console.log(`正在重连... 第${this.reconnectCount}次尝试`);
    setTimeout(() => {
      this.connect();
      this.reconnecting = false;
    }, this.options.reconnectInterval);
  }

  private startHeartbeat() {
    this.pingTimer = window.setInterval(() => {
      this.send(this.options.heartbeatMessage);
    }, 30000); // 每30秒发送一次心跳
  }

  private stopHeartbeat() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    if (this.pongTimer) {
      clearTimeout(this.pongTimer);
      this.pongTimer = null;
    }
  }

  public send(data: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket未连接');
    }
  }

  public close() {
    console.log('手动关闭WebSocket连接');
    this.isManualClosed = true;
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Vue组件的Hook
import { ref, onUnmounted } from 'vue';

export function useWebSocket(options: WebSocketOptions) {
  const wsClient = ref<WebSocketClient | null>(null);

  const connect = () => {
    wsClient.value = new WebSocketClient(options);
  };

  const disconnect = () => {
    if (wsClient.value) {
      wsClient.value.close();
      wsClient.value = null;
    }
  };

  const send = (data: any) => {
    wsClient.value?.send(data);
  };

  onUnmounted(() => {
    disconnect();
  });

  return {
    wsClient,
    connect,
    disconnect,
    send,
  };
}
