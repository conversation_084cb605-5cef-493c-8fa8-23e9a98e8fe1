export class Ping {
  private opt = {
    favicon:String,
    timeout: Number,
    logError:<PERSON><PERSON><PERSON>,

  };
  private start = new Date();
  private timer;
  private img = new Image();
  private wasSuccess = false;


  private defalutOpt = {
    favicon:"/favicon.ico",
    timeout:0,
    logError:false,
  };


  constructor(options={}) {
    this.opt = {
      ...this.defalutOpt,
      ...options
    }
  }

  pingCheck(promise,callback,resolve,reject) {
    if (this.timer) { clearTimeout(this.timer); }
    var pong = new Date() - this.start;

    if (!callback) {
        if (promise) {
            return this.wasSuccess ? resolve(pong) : reject(pong);
        } else {
            throw new Error("Promise is not supported by your browser. Use callback instead.");
        }
    } else if (typeof callback === "function") {
        // When operating in timeout mode, the timeout callback doesn't pass [event] as e.
        // Notice [this] instead of [self], since .call() was used with context
        if (!this.wasSuccess) {
            if (this.opt.logError) { console.error("error loading resource"); }
            if (promise) { reject(pong); }
            return callback("error", pong);
        }
        if (promise) { resolve(pong); }
        return callback(null, pong);
    } else {
        throw new Error("Callback is not a function.");
    }
  }

  ping(source, callback){
    let promise, resolve, reject;
    if (typeof Promise !== "undefined") {
        promise = new Promise(function(_resolve, _reject) {
            resolve = _resolve;
            reject = _reject;
        });
    }

    const self = this;

    this.start = new Date();

    const onload=()=> {
      self.wasSuccess = true;
      self.pingCheck(promise,callback,resolve,reject);
    }

    const onerror = ()=> {
      self.wasSuccess = false;
      self.pingCheck(promise,callback,resolve,reject);
    }

    this.wasSuccess = false;
    this.img = new Image();
    this.img.onload = onload;
    this.img.onerror = onerror;

    if (this.opt.timeout) {
      this.timer = setTimeout(()=>{
        this.pingCheck(promise,callback,resolve,reject);
      }, this.opt.timeout);
    }



    this.img.src = source + this.opt.favicon + "?" + (+new Date()); // Trigger image load with cache buster
    return promise;
  }


}
