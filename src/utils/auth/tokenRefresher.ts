import { userStore } from '/@/store/modules/user';
import geegaguc from '/@/plugins/geegaguc';

export class TokenRefresher {
  private static instance: TokenRefresher;
  private refreshInterval: number = 4 * 60 * 60 * 1000; // 4小时刷新一次
  private timer: NodeJS.Timer | null = null;
  private isRefreshing: boolean = false;

  private constructor() {}

  public static getInstance(): TokenRefresher {
    if (!TokenRefresher.instance) {
      TokenRefresher.instance = new TokenRefresher();
    }
    return TokenRefresher.instance;
  }

  private async refreshToken() {
    if (this.isRefreshing) return;

    try {
      this.isRefreshing = true;
      const gucData = await geegaguc.refreshToken();
      const { token } = gucData.data;
      if (token) {
        userStore.commitKeycloakTokenState(token);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      // 如果刷新失败，可能需要重新登录
      userStore.clearData();
    } finally {
      this.isRefreshing = false;
    }
  }

  public startRefreshTimer() {
    if (this.timer) {
      this.stopRefreshTimer();
    }

    // 设置定时器
    this.timer = setInterval(() => {
      this.refreshToken();
    }, this.refreshInterval);
  }

  public stopRefreshTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  public isRunning(): boolean {
    return this.timer !== null;
  }
}

// 导出单例实例
export const tokenRefresher = TokenRefresher.getInstance();
