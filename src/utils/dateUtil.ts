/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import dayjs, { Dayjs } from 'dayjs';

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
const DATE_FORMAT = 'YYYY-MM-DD';

export function formatToDateTime(
  date: dayjs.Dayjs | undefined = undefined,
  format = DATE_TIME_FORMAT
): string {
  return dayjs(date).format(format);
}

export function formatToDate(
  date: dayjs.Dayjs | undefined = undefined,
  format = DATE_FORMAT
): string {
  return dayjs(date).format(format);
}

export const dateUtil = dayjs;

/**
 *
 * 转换成日期范围，转换成：【YYYY-MM-DD 00:00:00, YYYY-MM-DD 23:59:59】
 * @param date
 */
export function convertDateRange(date?: Dayjs[], opt?: { names?: string[] }) {
  if (date?.length !== 2) {
    return;
  }

  const { names = ['startTime', 'endTime'] } = opt || {};

  const [start, end] = date;

  return {
    [names[0]]: start.startOf('d').format(DATE_TIME_FORMAT) as any,
    [names[1]]: end.endOf('d').format(DATE_TIME_FORMAT) as any,
  };
}


// 根据秒转分秒显示
export function formatSecondsToMinute(seconds: number) {
  const duration = dayjs.duration(seconds, 'seconds');
  const hours = duration.hours();
  const minutes = duration.minutes();
  const secondsLeft = duration.seconds();

  if (seconds < 60) {
    return `${secondsLeft}s`;
  }

  let result = '';
  if (hours > 0) {
    result += `${hours}h`;
  }
  if (minutes > 0) {
    result += `${minutes}min`;
  }
  if (secondsLeft > 0) {
    result += `${secondsLeft}s`;
  }
  return result;
}

// 根据秒转分，保留两位小数,返回字符串
export function formatSecondsToMinuteString(seconds: number) {
  if (seconds % 60 === 0) {
    return seconds / 60;
  } else {
    return (seconds / 60).toFixed(2);
  }
}
