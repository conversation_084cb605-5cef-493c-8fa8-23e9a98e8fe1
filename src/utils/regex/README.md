# 通用正则表达式库

用于项目中表单字段的验证

## 引入方法

```ts
import regex from '/@/utils/regex';
```

## 使用方法

```ts
const schemas = [
  {
    field: 'field3',
    label: '电话输入框',
    component: 'Input',
    defaultValue: '',

    // 按照rule的规范引入，建议一个规则占用一个校验item
    rules: [{ required: true }, regex.mobile, regex.account],
  },
];
```

## 扩展指导

### 先扩展 RegexName

```ts
type RegexName =
  | 'enEmail'
  | 'cnEmail'

  ...

  | '你想要扩展的校验 Key';
```

### 再写入 regexMap

```ts
const regexMap: Record<RegexName, RegexItem> = {
  enEmail: {
    message: '只允许英文字母、数字、下划线、英文句号、以及中划线组成',
    pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
  },

  ...

  '你想要扩展的校验 Key': {
    message: '报错文案',
    pattern: '正则表达式',
  }
}
```
