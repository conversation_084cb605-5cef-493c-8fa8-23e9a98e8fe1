
  import i18nVue from '@geega/i18n-vue';interface RegexItem {
  message: string;
  pattern: RegExp;
}

type RegexName =
  | 'enEmail'
  | 'cnEmail'
  | 'mobile'
  | 'landline'
  | 'domain'
  | 'IP'
  | 'account'
  | 'chinese'
  | 'english'
  | 'upCaseEnglish'
  | 'lowerCaseEnglish'
  | 'englishAndNumber'
  | 'int'
  | 'positiveInt'
  | 'negativeInt'
  | 'unNegativeInt'
  | 'unPositiveInt'
  | 'englishNumberAndUnderscore'
  | 'chineseEnglishNumberAndUnderscore'
  | 'chineseEnglishNumberAndNoUnderscore'
  | 'noSpecialSymbol'
  | 'noTilde';

const regexMap: Record<RegexName, RegexItem> = {
  enEmail: {
    message: i18nVue.t('utils.regex.index.44db8ec8', { defaultValue: '请输入正确的邮箱地址，只允许英文字母、数字、下划线、英文句号、以及中划线' }),
    pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
  },

  cnEmail: {
    message: i18nVue.t('utils.regex.index.b679f1a3', { defaultValue: '请输入正确的邮箱地址，名称允许汉字、字母、数字，域名只允许英文域名' }),
    pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
  },

  mobile: {
    message: i18nVue.t('utils.regex.index.a4b559a8', { defaultValue: '请输入正确的手机号' }),
    pattern: /^1(3|4|5|6|7|8|9)\d{9}$/,
  },

  landline: {
    message: i18nVue.t('utils.regex.index.6ad84202', { defaultValue: '请输入正确的固定电话' }),
    pattern: /(\(\d{3,4}\)|\d{3,4}-|\s)?\d{8}/,
  },

  domain: {
    message: i18nVue.t('utils.regex.index.87f83d07', { defaultValue: '请输入正确的域名' }),
    pattern: /^((http:\/\/)|(https:\/\/))?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(\/)/,
  },

  IP: {
    message: i18nVue.t('utils.regex.index.5f9de4b0', { defaultValue: '请输入正确的IP' }),
    pattern: /((?:(?:25[0-5]|2[0-4]\d|[01]?\d?\d)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d?\d))/,
  },

  account: {
    message: i18nVue.t('utils.regex.index.d3cdeeab', { defaultValue: '只允许字母开头，允许5-16字节，允许字母数字下划线' }),
    pattern: /^[a-zA-Z][a-zA-Z0-9_]{4,15}$/,
  },

  chinese: {
    message: i18nVue.t('utils.regex.index.195a0dbb', { defaultValue: '只允许输入汉字' }),
    pattern: /^[\u4e00-\u9fa5]{0,}$/,
  },

  english: {
    message: i18nVue.t('utils.regex.index.1fb75c84', { defaultValue: '只允许输入英文' }),
    pattern: /^[A-Za-z]+$/,
  },

  upCaseEnglish: {
    message: i18nVue.t('utils.regex.index.dce8e585', { defaultValue: '只允许输入大写英文' }),
    pattern: /^[A-Z]+$/,
  },

  lowerCaseEnglish: {
    message: i18nVue.t('utils.regex.index.d66f7814', { defaultValue: '只允许输入小写英文' }),
    pattern: /^[a-z]+$/,
  },

  englishAndNumber: {
    message: i18nVue.t('utils.regex.index.e901d53e', { defaultValue: '只允许输入英文和数字' }),
    pattern: /^[A-Za-z0-9]+$/,
  },

  englishNumberAndUnderscore: {
    message: i18nVue.t('utils.regex.index.47a79df2', { defaultValue: '只允许输入由数字、26个英文字母或者下划线组成的字符串' }),
    pattern: /^\w+$/,
  },
  chineseEnglishNumberAndUnderscore: {
    message: i18nVue.t('utils.regex.index.745d89db', { defaultValue: '只允许输入中文、英文、数字包括下划线' }),
    pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
  },
  chineseEnglishNumberAndNoUnderscore: {
    message: i18nVue.t('utils.regex.index.4fa39997', { defaultValue: '只允许输入中文、英文、数字但不包括下划线等符号' }),
    pattern: /^[\u4E00-\u9FA5A-Za-z0-9]+$/,
  },
  noSpecialSymbol: {
    message: i18nVue.t('utils.regex.index.152753cb', { defaultValue: '禁止输入含有%&\',;=?$"等字符' }),
    pattern: /[^%&',;=?$\x22]+/,
  },
  noTilde: {
    message: i18nVue.t('utils.regex.index.4a18f346', { defaultValue: '禁止输入含有~的字符' }),
    pattern: /[^~\x22]+/,
  },

  int: {
    message: i18nVue.t('utils.regex.index.6454d7f5', { defaultValue: '只允许输入整数' }),
    pattern: /^-?[1-9]\d*$/,
  },

  positiveInt: {
    message: i18nVue.t('utils.regex.index.32ad9603', { defaultValue: '只允许输入正整数' }),
    pattern: /^[1-9]\d*$/,
  },

  negativeInt: {
    message: i18nVue.t('utils.regex.index.ddfa4c47', { defaultValue: '只允许输入负整数' }),
    pattern: /^-[1-9]\d*$/,
  },

  unNegativeInt: {
    message: i18nVue.t('utils.regex.index.e0304a66', { defaultValue: '只允许输入非负整数' }),
    pattern: /^[1-9]\d*|0$/,
  },

  unPositiveInt: {
    message: i18nVue.t('utils.regex.index.9bc5a65a', { defaultValue: '只允许输入非正整数' }),
    pattern: /^-[1-9]\d*|0$/,
  },
};

export default regexMap;
