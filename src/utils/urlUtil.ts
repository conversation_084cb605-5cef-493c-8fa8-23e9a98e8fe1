import type { LocaleType } from '/#/config';
import { localeStore } from '/@/store/modules/locale';

/**、
 * @param isOnlyUrl = true 仅从URL上获取
 * 如果设置false 优先级URL>local>Navigator>'zh-CN'，
 */
export function getLangByHrefParams(isOnlyUrl = true): string {

  let params;

  const href = window.location.href;
  if(window.location.hash){
    let hash = window.location.hash;
    const searchParams =hash.substring(hash.indexOf('?')+1)
    params = new URLSearchParams(searchParams);
  } else {
    const url = new URL(href);
    params = url.searchParams;
  }
  if(isOnlyUrl){
    return params.get('lang')
  } else {
    return params.get('lang') || localeStore.getLocale || navigator.language || 'zh-CN';
  }
}

export function changeNavUrl(locale:LocaleType){
    // 如果 url 存在code 修改URL
    const urlLang = getLangByHrefParams(true)
    const mapLang = {
      "zh_CN":"zh-CN",
      "en":"en-US",
    }
    let  newUrl = window.location.href;
    const newUrlLang = mapLang[locale];
    if(urlLang && newUrlLang){
      newUrl = window.location.href.replace(`lang=${urlLang}`,`lang=${newUrlLang}`)
    } else {
      const navUrl = window.location.href;
      if(newUrlLang){
        if(navUrl && navUrl.indexOf('?') > -1){
          newUrl = `${navUrl}&lang=${newUrlLang}`
        } else {
          newUrl = `${navUrl}?lang=${newUrlLang}`
        }
     }
    }
    window.history.replaceState({}, '', newUrl);
}
