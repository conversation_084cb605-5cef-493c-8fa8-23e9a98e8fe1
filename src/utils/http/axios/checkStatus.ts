import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { userStore } from '/@/store/modules/user';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();
const { createMessage } = useMessage();

const error = createMessage.error!;


const cmError = (msg)=>{
  if(globSetting.cmApiUrl && globSetting.cmAppId){
    window.__ClientTemplateMonitor__.reportFrameErrors(
      {
        pagePath: window.location.hash,
      },
      new Error(`${msg}`) // 此处为 Error 对象，如：new Error('error.msg')
    )
  }
}

export function checkStatus(status: number, msg: string): void {
  const { t } = useI18n();
  switch (status) {
    case 400:
      error(`${msg}`);
      cmError(msg);
      break;
    // 401: Not logged in
    // Jump to the login page if not logged in, and carry the path of the current page
    // Return to the current page after successful login. This step needs to be operated on the login page.
    case 401:
      cmError(msg||t('sys.api.errMsg401'));
      if (globSetting.enableGuc) {
        userStore.gucLogout(t('sys.api.errMsg401'));
      }
      break;
    case 403:
      cmError(msg||t('sys.api.errMsg403'));
      if (globSetting.enableGuc) {
        userStore.gucLogout(t('sys.api.errMsg403'));
      }
      break;
    // 404请求不存在
    case 404:
      cmError(msg||t('sys.api.errMsg404'));
      error(t('sys.api.errMsg404'));
      break;
    case 405:
      cmError(msg||t('sys.api.errMsg405'));
      error(t('sys.api.errMsg405'));
      break;
    case 408:
      cmError(msg||t('sys.api.errMsg408'));
      error(t('sys.api.errMsg408'));
      break;
    case 500:
      cmError(msg||t('sys.api.errMsg500'));
      error(t('sys.api.errMsg500'));
      break;
    case 501:
      cmError(msg||t('sys.api.errMsg501'));
      error(t('sys.api.errMsg501'));
      break;
    case 502:
      cmError(msg||t('sys.api.errMsg502'));
      error(t('sys.api.errMsg502'));
      break;
    case 503:
      cmError(msg||t('sys.api.errMsg503'));
      error(t('sys.api.errMsg503'));
      break;
    case 504:
      cmError(msg||t('sys.api.errMsg504'));
      error(t('sys.api.errMsg504'));
      break;
    case 505:
      cmError(msg||t('sys.api.errMsg505'));
      error(t('sys.api.errMsg505'));
      break;
    default:
  }
}
