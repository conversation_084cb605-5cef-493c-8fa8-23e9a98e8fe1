/**
 *
 * @param nodes
 * @param fn return true to stop traverse the rest tree nodes.
 */
function _traverseTree(
  nodes: any[],
  parentNode: any,
  fn: (node: any, idx: number, arr: any[], parentNode?: any) => any
) {
  nodes.forEach((node, idx, arr) => {
    if (fn(node, idx, arr, parentNode)) {
      return;
    }

    if (node.children?.length) {
      _traverseTree(node.children, node, fn);
    }
  });
}

/**
 *
 * DFS
 *
 * @param nodes
 * @param fn
 * @returns
 */
export function traverseTree<T>(
  nodes: T | T[],
  fn: (node: T, idx: number, arr: T[], parentNode?: T) => any
) {
  const _nodes = Array.isArray(nodes) ? nodes : [nodes];

  _traverseTree(_nodes, null, (node, idx, arr, parentNode) => {
    fn(node, idx, arr, parentNode);
  });

  return _nodes;
}

export function filterTree<T>(nodes: T | T[], predicate: (node: T) => boolean) {
  nodes = Array.isArray(nodes) ? nodes : [nodes];
  return _filterTree(nodes, predicate);
}

function _filterTree(nodes: any[], predicate: (node: any) => boolean) {
  const newTree: any[] = [];

  for (let node of nodes) {
    node = { ...node };

    const pass = predicate(node);

    const children = node.children || [];

    node.children = _filterTree(children, predicate);

    if (node.children.length || pass) {
      newTree.push(node);
    }
  }

  return newTree;
}

export function findNodeInTree<T>(
  nodes: T | T[],
  predicate: (node: T, idx: number, arr: T[]) => boolean
) {
  const _nodes = Array.isArray(nodes) ? nodes : [nodes];

  let _hit: T | undefined;

  _traverseTree(_nodes, null, (node, idx, arr) => {
    if (predicate(node, idx, arr)) {
      _hit = node;

      return true;
    }
  });

  return _hit;
}

export function filterNodesInTree<T>(nodes: T | T[], predicate: (node: T) => boolean) {
  const _nodes = Array.isArray(nodes) ? nodes : [nodes];

  const findNodes: T[] = [];

  _traverseTree(_nodes, null, (node) => {
    if (predicate(node)) {
      findNodes.push(node);
    }
  });

  return findNodes;
}
