import type { GlobEnvConfig } from '/#/config';

import { warn } from '/@/utils/log';
import pkg from '../../package.json';

export function getCommonStoragePrefix() {
  const { VUE_APP_GLOB_APP_SHORT_NAME } = getAppEnvConfig();
  return `${VUE_APP_GLOB_APP_SHORT_NAME}__${getEnv()}`.toUpperCase();
}

// Generate cache key according to version
export function getStorageShortName() {
  return `${`${pkg.name}__${pkg.version}`}__`.toUpperCase();
}

export function getAppEnvConfig() {
  const ENV = process.env as unknown as GlobEnvConfig;

  const {
    VUE_APP_GLOB_APP_TITLE,
    VUE_APP_GLOB_API_URL,
    VUE_APP_GLOB_APP_SHORT_NAME,
    VUE_APP_GLOB_API_URL_PREFIX,
    VUE_APP_GLOB_UPLOAD_URL,
    VUE_APP_GLOB_GUC_APP_ID,
    VUE_APP_GUC_API_URL,
    VUE_APP_GLOB_CM_URL,
    VUE_APP_GLOB_CM_APP_ID,
    VUE_APP_ENV,
    VUE_APP_GLOB_SOCKET_URL,
    VUE_APP_GLOB_TRAINING_URL,
    VUE_APP_GLOB_PREVIEW_URL,
    VUE_APP_GLOB_WEBRTC_URL,
    VUE_APP_VIDEO_PANEL_ROTATION_MINUTES,
  } = ENV;

  const VUE_APP_ENABLE_GUC = JSON.parse(ENV.VUE_APP_ENABLE_GUC);

  if (!/[a-zA-Z\_]*/.test(VUE_APP_GLOB_APP_SHORT_NAME)) {
    warn(
      `VUE_APP_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`
    );
  }
  return {
    VUE_APP_GLOB_APP_TITLE,
    VUE_APP_GLOB_API_URL,
    VUE_APP_GLOB_APP_SHORT_NAME,
    VUE_APP_GLOB_API_URL_PREFIX,
    VUE_APP_GLOB_UPLOAD_URL,
    VUE_APP_GLOB_GUC_APP_ID,
    VUE_APP_ENABLE_GUC,
    VUE_APP_GUC_API_URL,
    VUE_APP_GLOB_CM_URL,
    VUE_APP_GLOB_CM_APP_ID,
    VUE_APP_ENV,
    VUE_APP_GLOB_SOCKET_URL,
    VUE_APP_GLOB_TRAINING_URL,
    VUE_APP_GLOB_PREVIEW_URL,
    VUE_APP_GLOB_WEBRTC_URL,
    VUE_APP_VIDEO_PANEL_ROTATION_MINUTES,
  };
}

/**
 * @description: Development model
 */
export const devMode = 'development';

/**
 * @description: Production mode
 */
export const prodMode = 'production';

/**
 * @description: Get environment variables
 * @returns:
 * @example:
 */
export function getEnv(): string {
  return import.meta.env.MODE;
}

/**
 * @description: Is it a development mode
 * @returns:
 * @example:
 */
export function isDevMode(): boolean {
  return import.meta.env.DEV;
}

/**
 * @description: Is it a production mode
 * @returns:
 * @example:
 */
export function isProdMode(): boolean {
  return import.meta.env.PROD;
}

/**
 * @description: Whether to open mock
 * @returns:
 * @example:
 */
export function isUseMock(): boolean {
  return JSON.stringify(import.meta.env.VUE_APP_USE_MOCK) === 'true';
}

export function getOrigin(): string {
  const origin = isDevMode() ? 'http://question-bank.caas-cloud-dev.geega.com' : location.origin;
  return origin;
}
