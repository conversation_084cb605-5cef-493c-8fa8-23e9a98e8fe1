// 算法类型和对应的算法选项枚举
export const AlgorithmTypeEnum = {
  TIGHTENING: 'tighten', // 拧紧类
  PLUG: 2, // 堵盖类
  SEAL: 3, // 密封胶条类
  PIPELINE: 4, // 管线安装类
  CONDUIT: 5, // 线束插接类
} as const;

// 算法类型对应的算法选项
export const AlgorithmOptionsMap = {
  [AlgorithmTypeEnum.TIGHTENING]: [
    { label: '拧紧计数', value: 'TIGHTENING_COUNT' },
    { label: '拧紧贴合判定', value: 'TIGHTEN_FIT' },
    { label: '双手作业判定', value: 'BOTH_HANDS' },
    { label: '垂直作业判定', value: 'VERTICAL_WORKING_SURFACE' },
    { label: '拧紧枪红绿灯判定', value: 'GREEN_LIGHT' },
  ],
  [AlgorithmTypeEnum.PLUG]: [
    { label: '堵盖计数', value: 'PLUG_COUNT11' },
    { label: '堵盖大小判定', value: 'PLUG_COUNT' },
    { label: '堵盖贴合判定', value: 'PLUG_FIT' },
    { label: '堵盖手部按压判定', value: 'PLUG_POSITION' },
  ],
  [AlgorithmTypeEnum.SEAL]: [
    { label: '胶条安装判定', value: 'SEAL_COUNT' },
    { label: '手部按压判定', value: 'SEAL_FIT' },
    { label: '胶条结果判定', value: 'SEAL_FLATNESS' },
  ],
  [AlgorithmTypeEnum.PIPELINE]: [
    { label: '油管距离判定', value: 'PIPELINE_COUNT' },
    { label: '手部回拔判定', value: 'PIPELINE_CONNECTION' },
    { label: '管路距离判定', value: 'PIPELINE_FIXATION' },
  ],
  [AlgorithmTypeEnum.CONDUIT]: [
    { label: '手部拿取判定', value: 'CONDUIT_COUNT' },
    { label: '手部回拔判定', value: 'CONDUIT_CONNECTION' },
    { label: '线束状态判定', value: 'CONDUIT_FIXATION' },
  ],
} as const;

// 获取算法类型对应的选项
export const getAlgorithmOptions = (type: number) => {
  return AlgorithmOptionsMap[type as keyof typeof AlgorithmOptionsMap] || [];
};
