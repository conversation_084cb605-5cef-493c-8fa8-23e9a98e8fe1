// token key
export const TOKEN_KEY = 'APP__NAME__TOKEN__';

export const LOCALE_KEY = 'APP__NAME__LOCALE__';

// token key
export const GEEGAGUC_TOKEN_KEY = 'APP__NAME__GEEGAGUC_TOKEN';

export const GEEGAGUC_REFRESH_TOKEN_KEY = 'APP__NAME__GEEGAGUC_REFRESH_TOKEN';

// user info key
export const USER_INFO_KEY = 'APP__NAME__USER__INFO__';

// role info key
export const ROLES_KEY = 'APP__NAME__ROLES__KEY__';

// project config key
export const PROJ_CFG_KEY = 'APP__NAME__PROJ__CFG__KEY__';

// lock info
export const LOCK_INFO_KEY = 'APP__NAME__LOCK__INFO__KEY__';

// base global local key
export const APP_LOCAL_CACHE_KEY = 'APP__NAME__COMMON__LOCAL__KEY__';

// base global session key
export const APP_SESSION_CACHE_KEY = 'APP__NAME__COMMON__SESSION__KEY__';

export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}
