export enum ProjectStatusEnum {
  NOT_ALLOWED = 'NOT_ALLOWED',
  TO_BE = 'TO_BE',
  ING = 'ING',
  COMPLETED = 'COMPLETED',
  TO_BE_CHECK = 'TO_BE_CHECK',
  CHECKED = 'CHECKED',
  FAILED_CHECK = 'FAILED_CHECK',
  PASSED_CHECK = 'PASSED_CHECK',
}

export const ProjectStatusMap = {
  [ProjectStatusEnum.NOT_ALLOWED]: {
    text: '不允许',
    color: 'default',
  },
  [ProjectStatusEnum.TO_BE]: {
    text: '待训练',
    color: 'warning',
  },
  [ProjectStatusEnum.ING]: {
    text: '训练中',
    color: 'processing',
  },
  [ProjectStatusEnum.COMPLETED]: {
    text: '训练完成',
    color: 'success',
  },
  [ProjectStatusEnum.TO_BE_CHECK]: {
    text: '待考核',
    color: 'warning',
  },
  [ProjectStatusEnum.FAILED_CHECK]: {
    text: '考核未通过',
    color: 'error',
  },
  [ProjectStatusEnum.CHECKED]: {
    text: '考核完成',
    color: 'success',
  },
  [ProjectStatusEnum.PASSED_CHECK]: {
    text: '考核通过',
    color: 'success',
  },
};

export enum SkillStatusEnum {
  PASS_RATE_NO = 'PASS_RATE_NO',
  EFFICIENCY_NOT = 'EFFICIENCY_NOT',
  TRAIN_FREQUENCY_NOT = 'TRAIN_FREQUENCY_NOT',
  TRAIN_DURATION_NOT = 'TRAIN_DURATION_NOT',
  PASS_OK = 'PASS_OK',
}

export const SkillStatusMap = {
  [SkillStatusEnum.PASS_RATE_NO]: {
    text: '合格率不达标',
    color: 'error',
  },
  [SkillStatusEnum.EFFICIENCY_NOT]: {
    text: '效率不达标',
    color: 'error',
  },
  [SkillStatusEnum.TRAIN_FREQUENCY_NOT]: {
    text: '训练次数不达标',
    color: 'error',
  },
  [SkillStatusEnum.TRAIN_DURATION_NOT]: {
    text: '时长不达标',
    color: 'error',
  },
  [SkillStatusEnum.PASS_OK]: {
    text: '合格',
    color: 'success',
  },
};

// 训练状态
export enum TrainStatusEnum {
  INIT = 'INIT',
  ING = 'ING',
  STOP = 'STOP',
  COMPLETE = 'COMPLETE',
}

/**
 * 项目类型(1:训练、2:考核、3:比赛)
 */
export enum ProjectType {
  Training = '1',
  Exam = '2',
  Race = '3',
}

/**
 * 比赛类型(1、时间模式 2、次数模式)
 */
export enum RaceType {
  Timing = '1',
  Count = '2',
}

/**
 * 岗级类型
 */
export enum PositionLevelEnum {
  L0 = '0',
  L1 = '1',
  L2 = '2',
  L3 = '3',
}

export const PositionLevelOptions = [
  {
    label: 'L0',
    value: PositionLevelEnum.L0,
  },
  {
    label: 'L1',
    value: PositionLevelEnum.L1,
  },
  {
    label: 'L2',
    value: PositionLevelEnum.L2,
  },
  {
    label: 'L3',
    value: PositionLevelEnum.L3,
  },
];

export enum PositionLevelTypeEnum {
  /**
   * 训练项目
   */
  TRAIN = 'TRAIN',
  /**
   * 学习项目
   */
  LEARNING = 'LEARNING',
  /**
   * 考试项目
   */
  EXAM = 'EXAM',
  /**
   * 在线考评项目
   */
  ONLINE = 'ONLINE',
}

export enum TrainItemStatusEnum {
  /**
   * 训练完成
   */
  TRAINED = 'TRAINED',
  /**
   * 考核完成
   */
  PASSED = 'PASSED',
}

export const TrainItemStatusOptions = [
  {
    label: '训练完成',
    value: TrainItemStatusEnum.TRAINED,
  },
  {
    label: '考核完成',
    value: TrainItemStatusEnum.PASSED,
  },
];

export enum EvaluateStatusEnum {
  PENDING = '0',
  PASSED = '1',
  REJECTED = '2',
}

export const EvaluateStatusOptions = [
  {
    label: '待评审',
    value: EvaluateStatusEnum.PENDING,
    colorType: 'warning',
  },
  {
    label: '合格',
    value: EvaluateStatusEnum.PASSED,
    colorType: 'success',
  },
  {
    label: '不合格',
    value: EvaluateStatusEnum.REJECTED,
    colorType: 'error',
  },
];

/**
  WAITING: '待学习',
  LEARNING: '学习中',
  FINISH: '已完成',
 */
export enum LearningStatusEnum {
  /**
   * 待学习
   */
  WAITING = 'WAITING',

  /**
   * 学习中
   */
  LEARNING = 'LEARNING',

  /**
   * 已完成
   */
  FINISH = 'FINISH',
}

export const LearningStatusOptions = [
  { label: '待学习', value: LearningStatusEnum.WAITING, colorType: 'warning' },
  { label: '学习中', value: LearningStatusEnum.LEARNING, colorType: 'processing' },
  { label: '已完成', value: LearningStatusEnum.FINISH, colorType: 'success' },
];

export enum ExamStatusEnum {
  /**
   * 待考试
   */
  TO_BE = 'TO_BE',

  /**
   * 弃考
   */
  QUIT = 'QUIT',

  /**
   * 合格
   */
  COMPLETED = 'COMPLETED',

  /**
   * 不合格
   */
  UNQUALIFIED = 'UNQUALIFIED',
}

export const ExamStatusOptions = [
  { label: '待考试', value: ExamStatusEnum.TO_BE, colorType: 'warning' },
  { label: '弃考', value: ExamStatusEnum.QUIT, colorType: 'error' },
  { label: '合格', value: ExamStatusEnum.COMPLETED, colorType: 'success' },
  { label: '不合格', value: ExamStatusEnum.UNQUALIFIED, colorType: 'error' },
];
