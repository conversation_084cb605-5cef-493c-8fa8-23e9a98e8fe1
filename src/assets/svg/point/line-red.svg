<svg width="85" height="580" viewBox="0 0 85 580" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28.5 323.5C31.5 325.392 37.5 331.159 37.5 339.088C37.5 347.018 37.5 355.333 37.5 358.5" stroke="#2F6FB6"/>
<path d="M31.5 323.5C34.5 325.392 40.5 331.159 40.5 339.088C40.5 347.018 40.5 355.333 40.5 358.5" stroke="#00996B"/>
<path d="M57.5 323.5C54.5 325.392 48.5 331.159 48.5 339.088C48.5 347.018 48.5 355.333 48.5 358.5" stroke="#3FE1FF"/>
<path d="M54.5 323.5C51.5 325.392 45.5 331.159 45.5 339.088C45.5 347.018 45.5 355.333 45.5 358.5" stroke="#E97A13"/>
<path d="M43 358V324" stroke="#F53F3F"/>
<g filter="url(#filter0_d_1423_2631)">
<path d="M37 357L37 573L49 573L49 357L37 357Z" fill="url(#paint0_linear_1423_2631)"/>
<path d="M36.7002 573.3L49.2998 573.3L49.2998 356.7L36.7002 356.7L36.7002 573.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<path d="M28.5 267.5C31.5 265.608 37.5 259.841 37.5 251.912C37.5 243.982 37.5 235.667 37.5 232.5" stroke="#2F6FB6"/>
<path d="M31.5 267.5C34.5 265.608 40.5 259.841 40.5 251.912C40.5 243.982 40.5 235.667 40.5 232.5" stroke="#00996B"/>
<path d="M57.5 267.5C54.5 265.608 48.5 259.841 48.5 251.912C48.5 243.982 48.5 235.667 48.5 232.5" stroke="#3FE1FF"/>
<path d="M54.5 267.5C51.5 265.608 45.5 259.841 45.5 251.912C45.5 243.982 45.5 235.667 45.5 232.5" stroke="#E97A13"/>
<path d="M43 232L43 266" stroke="#F53F3F"/>
<g filter="url(#filter1_d_1423_2631)">
<path d="M37 17L37 233L49 233L49 17L37 17Z" fill="url(#paint1_linear_1423_2631)"/>
<path d="M36.7002 233.3L49.2998 233.3L49.2998 16.7002L36.7002 16.7002L36.7002 233.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<g filter="url(#filter2_d_1423_2631)">
<path d="M69.3 265.72C70.6253 265.72 71.7004 266.795 71.7004 268.12V294.04C71.7002 294.835 71.055 295.481 70.26 295.481H14.5803C13.7852 295.481 13.1401 294.835 13.1399 294.04V268.12C13.1399 266.795 14.2149 265.72 15.5403 265.72H69.3ZM34.7405 289.24C33.6801 289.24 32.8196 290.1 32.8196 291.16C32.8198 292.221 33.6802 293.08 34.7405 293.08H50.0999C51.1601 293.08 52.0196 292.221 52.0198 291.16C52.0198 290.1 51.1602 289.24 50.0999 289.24H34.7405Z" fill="url(#paint2_linear_1423_2631)"/>
</g>
<g filter="url(#filter3_d_1423_2631)">
<path d="M9.2998 297.4C9.2998 296.34 10.1594 295.48 11.2198 295.48H74.5798C75.6402 295.48 76.4998 296.34 76.4998 297.4V322.36C76.4998 323.42 75.6402 324.28 74.5798 324.28H54.8998H30.8998H11.2198C10.1594 324.28 9.2998 323.42 9.2998 322.36V297.4Z" fill="url(#paint3_linear_1423_2631)"/>
<path d="M74.5801 295.24C75.7729 295.24 76.7402 296.207 76.7402 297.4V322.36C76.7402 323.553 75.7729 324.52 74.5801 324.52H11.2197C10.0268 324.52 9.05957 323.553 9.05957 322.36V297.4C9.05961 296.207 10.0269 295.24 11.2197 295.24H74.5801Z" stroke="#FDCDC5" stroke-width="0.48"/>
</g>
<g filter="url(#filter4_d_1423_2631)">
<path d="M9.2998 297.4C9.2998 296.34 10.1594 295.48 11.2198 295.48H74.5798C75.6402 295.48 76.4998 296.34 76.4998 297.4V322.36C76.4998 323.42 75.6402 324.28 74.5798 324.28H56.8198C55.7594 324.28 54.8998 323.42 54.8998 322.36V322.36C54.8998 321.3 54.0402 320.44 52.9798 320.44H42.8998H32.8198C31.7594 320.44 30.8998 321.3 30.8998 322.36V322.36C30.8998 323.42 30.0402 324.28 28.9798 324.28H11.2198C10.1594 324.28 9.2998 323.42 9.2998 322.36V297.4Z" fill="url(#paint4_linear_1423_2631)"/>
<path d="M74.5801 295.24C75.7729 295.24 76.7402 296.207 76.7402 297.4V322.36C76.7402 323.553 75.7729 324.52 74.5801 324.52H56.8193C55.6266 324.52 54.6602 323.553 54.6602 322.36C54.6601 321.432 53.9073 320.68 52.9795 320.68H32.8193C31.8918 320.68 31.1397 321.432 31.1396 322.36C31.1396 323.553 30.1724 324.52 28.9795 324.52H11.2197C10.0268 324.52 9.05957 323.553 9.05957 322.36V297.4C9.05961 296.207 10.0269 295.24 11.2197 295.24H74.5801Z" stroke="#FDCDC5" stroke-width="0.48"/>
</g>
<path d="M15.0598 265.96H69.7795C70.7074 265.96 71.4602 266.712 71.4602 267.64V295.24H13.3801V267.64L13.3889 267.468C13.475 266.621 14.1901 265.96 15.0598 265.96Z" stroke="#FDCDC5" stroke-width="0.48"/>
<g filter="url(#filter5_d_1423_2631)">
<rect x="22.74" y="266.2" width="7.68" height="28.8" fill="url(#paint5_linear_1423_2631)"/>
<rect x="22.5" y="265.96" width="8.16" height="29.28" stroke="#FDCDC5" stroke-width="0.48"/>
</g>
<g filter="url(#filter6_d_1423_2631)">
<rect x="54.4199" y="266.2" width="7.68" height="28.8" fill="url(#paint6_linear_1423_2631)"/>
<rect x="54.1799" y="265.96" width="8.16" height="29.28" stroke="#FDCDC5" stroke-width="0.48"/>
</g>
<defs>
<filter id="filter0_d_1423_2631" x="25.4799" y="340.68" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter1_d_1423_2631" x="25.4799" y="0.680023" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter2_d_1423_2631" x="9.29989" y="265.72" width="66.2405" height="37.4407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter3_d_1423_2631" x="4.97982" y="295" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter4_d_1423_2631" x="4.97982" y="295" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter5_d_1423_2631" x="20.34" y="261.88" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<filter id="filter6_d_1423_2631" x="52.0199" y="261.88" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2631"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2631" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1423_2631" x1="37" y1="465.014" x2="49" y2="465.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint1_linear_1423_2631" x1="37" y1="125.014" x2="49" y2="125.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint2_linear_1423_2631" x1="43.0239" y1="265.72" x2="42.9822" y2="293.497" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5473A"/>
<stop offset="0.15" stop-color="#F57A70"/>
<stop offset="0.560665" stop-color="#F6685D"/>
<stop offset="0.827305" stop-color="#E03F32"/>
<stop offset="0.99" stop-color="#E2372A"/>
</linearGradient>
<linearGradient id="paint3_linear_1423_2631" x1="43.5926" y1="295.48" x2="43.5586" y2="322.361" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5473A"/>
<stop offset="0.15" stop-color="#F57A70"/>
<stop offset="0.560665" stop-color="#F6685D"/>
<stop offset="0.827305" stop-color="#E03F32"/>
<stop offset="0.99" stop-color="#E2372A"/>
</linearGradient>
<linearGradient id="paint4_linear_1423_2631" x1="43.5926" y1="295.48" x2="43.5586" y2="322.361" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5473A"/>
<stop offset="0.15" stop-color="#F57A70"/>
<stop offset="0.560665" stop-color="#F6685D"/>
<stop offset="0.827305" stop-color="#E03F32"/>
<stop offset="0.99" stop-color="#E2372A"/>
</linearGradient>
<linearGradient id="paint5_linear_1423_2631" x1="26.6592" y1="266.2" x2="26.362" y2="293.078" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5473A"/>
<stop offset="0.15" stop-color="#F57A70"/>
<stop offset="0.560665" stop-color="#F6685D"/>
<stop offset="0.827305" stop-color="#E03F32"/>
<stop offset="0.99" stop-color="#E2372A"/>
</linearGradient>
<linearGradient id="paint6_linear_1423_2631" x1="58.3391" y1="266.2" x2="58.0419" y2="293.078" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5473A"/>
<stop offset="0.15" stop-color="#F57A70"/>
<stop offset="0.560665" stop-color="#F6685D"/>
<stop offset="0.827305" stop-color="#E03F32"/>
<stop offset="0.99" stop-color="#E2372A"/>
</linearGradient>
</defs>
</svg>
