<svg width="90" height="70" viewBox="0 0 90 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Property 1=&#231;&#187;&#191;, &#230;&#150;&#185;&#229;&#144;&#145;=&#229;&#143;&#179;&#232;&#190;&#185;">
<g id="Rectangle 34624538" filter="url(#filter0_di_1000_1724)">
<path d="M35.8604 2.99519C35.8578 1.8925 34.9631 1 33.8604 1L7.00014 1C5.89552 1 5.00006 1.89552 5.00014 3.00015L5.00418 59.0001C5.00426 60.1047 5.89966 61 7.00418 61L33.9952 61C35.1016 61 35.9978 60.1016 35.9952 58.9952L35.8604 2.99519Z" fill="url(#paint0_linear_1000_1724)"/>
<path d="M33.8604 0.750001C35.1009 0.750001 36.1074 1.75406 36.1104 2.99459L36.2452 58.9946C36.2482 60.2393 35.2399 61.25 33.9952 61.25L7.00418 61.25C5.7616 61.25 4.75427 60.2427 4.75418 59.0002L4.75014 3.00016C4.75005 1.75746 5.75744 0.750003 7.00014 0.750003L33.8604 0.750001Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<path id="Rectangle 34624540" d="M41.6207 10.5361L41.75 10.6074L41.75 10.7551L41.75 50.2449L41.75 50.3925L41.6208 50.4638L36.6293 53.2189L36.2585 53.4235L36.2585 53L36.25 8.00005L36.2499 7.57676L36.6207 7.78104L41.6207 10.5361Z" fill="url(#paint1_linear_1000_1724)" stroke="#ABD6C9" stroke-width="0.5"/>
<g id="Rectangle 34624541" filter="url(#filter1_di_1000_1724)">
<path d="M73.9475 11L41.9497 11L41.9497 50L73.9475 50L77.6001 46.5L77.6001 14.5L73.9475 11Z" fill="url(#paint2_linear_1000_1724)"/>
<path d="M73.9475 10.75L74.048 10.75L74.1205 10.8195L77.7731 14.3195L77.8501 14.3933L77.8501 14.5L77.8501 46.5L77.8501 46.6067L77.7731 46.6805L74.1205 50.1805L74.048 50.25L73.9475 50.25L41.9497 50.25L41.6997 50.25L41.6997 50L41.6997 11L41.6997 10.75L41.9497 10.75L73.9475 10.75Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<rect id="Rectangle 34624553" width="13" height="39" transform="matrix(-1 8.74228e-08 8.74228e-08 1 64 11)" fill="url(#paint3_linear_1000_1724)"/>
<path id="Line 197" d="M73.5 11L73.5 50" stroke="#00996B"/>
<g id="Rectangle 34624544" filter="url(#filter2_di_1000_1724)">
<path d="M77.5 14L85.5 14L85.499 47L77.5 47L77.5 14Z" fill="url(#paint4_linear_1000_1724)"/>
<path d="M77.75 14.25L85.25 14.25L85.2491 46.75L77.75 46.75L77.75 14.25Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<path id="Rectangle 34624554" d="M77.5 14.5L74 11L74 50L77.5 46.5L77.5 14.5Z" fill="#016E4D" fill-opacity="0.51"/>
</g>
<defs>
<filter id="filter0_di_1000_1724" x="0.500244" y="0.5" width="39.9949" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1000_1724"/>
</filter>
<filter id="filter1_di_1000_1724" x="37.4497" y="10.5" width="44.6504" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1000_1724"/>
</filter>
<filter id="filter2_di_1000_1724" x="73.5" y="14" width="16" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1000_1724"/>
</filter>
<linearGradient id="paint0_linear_1000_1724" x1="20.1804" y1="1" x2="20.5" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint1_linear_1000_1724" x1="28.6526" y1="32" x2="41.6526" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#025642"/>
<stop offset="1" stop-color="#01986A"/>
</linearGradient>
<linearGradient id="paint2_linear_1000_1724" x1="58.1001" y1="11" x2="57.9475" y2="47.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.215495" stop-color="#02C289"/>
<stop offset="0.63203" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint3_linear_1000_1724" x1="6.5" y1="1.95" x2="7" y2="39" gradientUnits="userSpaceOnUse">
<stop stop-color="#019065"/>
<stop offset="0.15" stop-color="#00A573"/>
<stop offset="0.560665" stop-color="#01805A"/>
<stop offset="0.755388" stop-color="#017553"/>
<stop offset="0.99" stop-color="#017C57"/>
</linearGradient>
<linearGradient id="paint4_linear_1000_1724" x1="82.5" y1="13.4" x2="82.5" y2="46.9667" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
</defs>
</svg>
