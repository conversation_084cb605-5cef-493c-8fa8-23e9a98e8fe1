<svg width="85" height="580" viewBox="0 0 85 580" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M27.5 323.5C30.5 325.392 36.5 331.159 36.5 339.088C36.5 347.018 36.5 355.333 36.5 358.5" stroke="#2F6FB6"/>
<path d="M30.5 323.5C33.5 325.392 39.5 331.159 39.5 339.088C39.5 347.018 39.5 355.333 39.5 358.5" stroke="#00996B"/>
<path d="M56.5 323.5C53.5 325.392 47.5 331.159 47.5 339.088C47.5 347.018 47.5 355.333 47.5 358.5" stroke="#3FE1FF"/>
<path d="M53.5 323.5C50.5 325.392 44.5 331.159 44.5 339.088C44.5 347.018 44.5 355.333 44.5 358.5" stroke="#E97A13"/>
<path d="M42 358V324" stroke="#F53F3F"/>
<g filter="url(#filter0_d_1423_2632)">
<path d="M36 357L36 573L48 573L48 357L36 357Z" fill="url(#paint0_linear_1423_2632)"/>
<path d="M35.7002 573.3L48.2998 573.3L48.2998 356.7L35.7002 356.7L35.7002 573.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<path d="M28.5 267.5C31.5 265.608 37.5 259.841 37.5 251.912C37.5 243.982 37.5 235.667 37.5 232.5" stroke="#2F6FB6"/>
<path d="M31.5 267.5C34.5 265.608 40.5 259.841 40.5 251.912C40.5 243.982 40.5 235.667 40.5 232.5" stroke="#00996B"/>
<path d="M57.5 267.5C54.5 265.608 48.5 259.841 48.5 251.912C48.5 243.982 48.5 235.667 48.5 232.5" stroke="#3FE1FF"/>
<path d="M54.5 267.5C51.5 265.608 45.5 259.841 45.5 251.912C45.5 243.982 45.5 235.667 45.5 232.5" stroke="#E97A13"/>
<path d="M43 232L43 266" stroke="#F53F3F"/>
<g filter="url(#filter1_d_1423_2632)">
<path d="M37 17L37 233L49 233L49 17L37 17Z" fill="url(#paint1_linear_1423_2632)"/>
<path d="M36.7002 233.3L49.2998 233.3L49.2998 16.7002L36.7002 16.7002L36.7002 233.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<g filter="url(#filter2_d_1423_2632)">
<path d="M70.2996 294.14C70.2993 294.935 69.6543 295.581 68.8591 295.581H13.1794C12.4342 295.58 11.8208 295.013 11.7468 294.288L11.739 294.14V267.74C11.739 266.68 12.5986 265.82 13.659 265.82H68.3796C69.4399 265.82 70.2996 266.68 70.2996 267.74V294.14ZM12.2244 294.239C12.2251 294.246 12.2274 294.253 12.2283 294.259C12.2234 294.22 12.2195 294.181 12.2195 294.14L12.2244 294.239ZM33.3396 289.34C32.2794 289.341 31.4197 290.2 31.4197 291.26C31.4199 292.32 32.2795 293.18 33.3396 293.18H48.7C49.7602 293.18 50.6197 292.321 50.6199 291.26C50.6199 290.2 49.7603 289.34 48.7 289.34H33.3396Z" fill="url(#paint2_linear_1423_2632)"/>
</g>
<g filter="url(#filter3_d_1423_2632)">
<path d="M7.8999 297.5C7.8999 296.44 8.75952 295.58 9.8199 295.58H73.1799C74.2403 295.58 75.0999 296.44 75.0999 297.5V322.46C75.0999 323.52 74.2403 324.38 73.1799 324.38H53.4999H29.4999H9.8199C8.75952 324.38 7.8999 323.52 7.8999 322.46V297.5Z" fill="url(#paint3_linear_1423_2632)"/>
<path d="M73.1802 295.34C74.373 295.34 75.3403 296.307 75.3403 297.5V322.46C75.3403 323.653 74.373 324.62 73.1802 324.62H9.81982C8.62693 324.62 7.65967 323.653 7.65967 322.46V297.5C7.65971 296.307 8.62695 295.34 9.81982 295.34H73.1802Z" stroke="#FEEECC" stroke-width="0.48"/>
</g>
<g filter="url(#filter4_d_1423_2632)">
<path d="M7.8999 297.5C7.8999 296.44 8.75952 295.58 9.8199 295.58H73.1799C74.2403 295.58 75.0999 296.44 75.0999 297.5V322.46C75.0999 323.52 74.2403 324.38 73.1799 324.38H55.4199C54.3595 324.38 53.4999 323.52 53.4999 322.46V322.46C53.4999 321.4 52.6403 320.54 51.5799 320.54H41.4999H31.4199C30.3595 320.54 29.4999 321.4 29.4999 322.46V322.46C29.4999 323.52 28.6403 324.38 27.5799 324.38H9.8199C8.75952 324.38 7.8999 323.52 7.8999 322.46V297.5Z" fill="url(#paint4_linear_1423_2632)"/>
<path d="M73.1802 295.34C74.373 295.34 75.3403 296.307 75.3403 297.5V322.46C75.3403 323.653 74.373 324.62 73.1802 324.62H55.4194C54.2267 324.62 53.2603 323.653 53.2603 322.46C53.2602 321.532 52.5074 320.78 51.5796 320.78H31.4194C30.4918 320.78 29.7398 321.532 29.7397 322.46C29.7397 323.653 28.7725 324.62 27.5796 324.62H9.81982C8.62693 324.62 7.65967 323.653 7.65967 322.46V297.5C7.65971 296.307 8.62695 295.34 9.81982 295.34H73.1802Z" stroke="#FEEECC" stroke-width="0.48"/>
</g>
<path d="M13.6599 266.06H68.3796C69.3075 266.06 70.0603 266.812 70.0603 267.74V295.339H11.9802V267.74L11.989 267.568C12.0751 266.721 12.7902 266.06 13.6599 266.06Z" stroke="#FDCDC5" stroke-width="0.48"/>
<g filter="url(#filter5_d_1423_2632)">
<rect x="21.3401" y="266.3" width="7.68" height="28.8" fill="url(#paint5_linear_1423_2632)"/>
<rect x="21.1001" y="266.06" width="8.16" height="29.28" stroke="#FEEECC" stroke-width="0.48"/>
</g>
<g filter="url(#filter6_d_1423_2632)">
<rect x="53.02" y="266.3" width="7.68" height="28.8" fill="url(#paint6_linear_1423_2632)"/>
<rect x="52.78" y="266.06" width="8.16" height="29.28" stroke="#FEEECC" stroke-width="0.48"/>
</g>
<defs>
<filter id="filter0_d_1423_2632" x="24.4799" y="340.68" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter1_d_1423_2632" x="25.4799" y="0.680023" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter2_d_1423_2632" x="7.89901" y="265.82" width="66.2405" height="37.4407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter3_d_1423_2632" x="3.57992" y="295.1" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter4_d_1423_2632" x="3.57992" y="295.1" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter5_d_1423_2632" x="18.9401" y="261.98" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<filter id="filter6_d_1423_2632" x="50.62" y="261.98" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2632"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2632" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1423_2632" x1="36" y1="465.014" x2="48" y2="465.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint1_linear_1423_2632" x1="37" y1="125.014" x2="49" y2="125.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint2_linear_1423_2632" x1="41.623" y1="265.82" x2="41.5814" y2="293.597" gradientUnits="userSpaceOnUse">
<stop stop-color="#C25D04"/>
<stop offset="0.15" stop-color="#FC8800"/>
<stop offset="0.560665" stop-color="#E97604"/>
<stop offset="0.789998" stop-color="#C26100"/>
<stop offset="0.99" stop-color="#D27302"/>
</linearGradient>
<linearGradient id="paint3_linear_1423_2632" x1="42.1927" y1="295.58" x2="42.1587" y2="322.461" gradientUnits="userSpaceOnUse">
<stop stop-color="#C25D04"/>
<stop offset="0.15" stop-color="#FC8800"/>
<stop offset="0.560665" stop-color="#E97604"/>
<stop offset="0.789998" stop-color="#C26100"/>
<stop offset="0.99" stop-color="#D27302"/>
</linearGradient>
<linearGradient id="paint4_linear_1423_2632" x1="42.1927" y1="295.58" x2="42.1587" y2="322.461" gradientUnits="userSpaceOnUse">
<stop stop-color="#C25D04"/>
<stop offset="0.15" stop-color="#FC8800"/>
<stop offset="0.560665" stop-color="#E97604"/>
<stop offset="0.789998" stop-color="#C26100"/>
<stop offset="0.99" stop-color="#D27302"/>
</linearGradient>
<linearGradient id="paint5_linear_1423_2632" x1="25.2593" y1="266.3" x2="24.9621" y2="293.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#C25D04"/>
<stop offset="0.15" stop-color="#FC8800"/>
<stop offset="0.560665" stop-color="#E97604"/>
<stop offset="0.789998" stop-color="#C26100"/>
<stop offset="0.99" stop-color="#D27302"/>
</linearGradient>
<linearGradient id="paint6_linear_1423_2632" x1="56.9392" y1="266.3" x2="56.642" y2="293.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#C25D04"/>
<stop offset="0.15" stop-color="#FC8800"/>
<stop offset="0.560665" stop-color="#E97604"/>
<stop offset="0.789998" stop-color="#C26100"/>
<stop offset="0.99" stop-color="#D27302"/>
</linearGradient>
</defs>
</svg>
