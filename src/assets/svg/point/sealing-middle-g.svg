<svg width="717" height="59" viewBox="0 0 717 59" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 0.834961C1.75736 0.834961 0.75 1.84232 0.75 3.08496L0.75 56.085C0.75 57.3276 1.75735 58.335 2.99999 58.335L714 58.335C715.243 58.335 716.25 57.3276 716.25 56.085V3.08496C716.25 1.84232 715.243 0.834961 714 0.834961L3 0.834961Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<circle cx="706.112" cy="17.6079" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="706.112" cy="47" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="10.4153" cy="15.6079" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="10.4153" cy="45" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter0_d_967_8455)">
<path d="M687 30.1684L27 30.1685" stroke="#9197A3" stroke-width="5"/>
</g>
<g filter="url(#filter1_di_967_8455)">
<path d="M26.8279 14.666C26.8279 12.4569 28.6187 10.666 30.8279 10.666L683.415 10.666C685.624 10.666 687.415 12.4569 687.415 14.666V45.666C687.415 47.8752 685.624 49.666 683.415 49.666L30.8279 49.666C28.6188 49.666 26.8279 47.8752 26.8279 45.666L26.8279 14.666Z" fill="#007354"/>
<path d="M30.8279 10.166C28.3426 10.166 26.3279 12.1807 26.3279 14.666L26.3279 45.666C26.3279 48.1513 28.3426 50.166 30.8279 50.166L683.415 50.166C685.901 50.166 687.915 48.1513 687.915 45.666V14.666C687.915 12.1807 685.901 10.166 683.415 10.166L30.8279 10.166Z" stroke="#ABD6C9"/>
</g>
<mask id="mask0_967_8455" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="25" y="9" width="664" height="42">
<path d="M30.8279 10.166C28.3426 10.166 26.3279 12.1807 26.3279 14.666L26.3279 45.666C26.3279 48.1513 28.3426 50.166 30.8279 50.166L683.415 50.166C685.901 50.166 687.915 48.1513 687.915 45.666V14.666C687.915 12.1807 685.901 10.166 683.415 10.166L30.8279 10.166Z" fill="#954500" stroke="#FC8800"/>
</mask>
<g mask="url(#mask0_967_8455)">
<g filter="url(#filter2_f_967_8455)">
<path d="M-19.2463 14.8694C-19.2463 13.1021 -17.8136 11.6694 -16.0463 11.6694L744.837 11.6694C746.604 11.6694 748.037 13.1021 748.037 14.8694C748.037 16.4158 746.784 17.6694 745.237 17.6694L-16.4463 17.6694C-17.9927 17.6694 -19.2463 16.4158 -19.2463 14.8694Z" fill="#ABFFD9"/>
</g>
<g filter="url(#filter3_f_967_8455)">
<path d="M-27.5967 42.3361C-27.5967 40.8633 -26.4028 39.6694 -24.93 39.6694L737.02 39.6694C738.493 39.6694 739.687 40.8633 739.687 42.3361C739.687 43.6248 738.642 44.6694 737.353 44.6694L-25.2633 44.6694C-26.552 44.6694 -27.5967 43.6248 -27.5967 42.3361Z" fill="#00372A"/>
</g>
</g>
<defs>
<filter id="filter0_d_967_8455" x="23" y="24.6685" width="668" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_967_8455"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_967_8455" result="shape"/>
</filter>
<filter id="filter1_di_967_8455" x="21.8279" y="9.66602" width="670.588" height="49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_967_8455"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_967_8455" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_967_8455"/>
</filter>
<filter id="filter2_f_967_8455" x="-39.2463" y="-8.33057" width="807.283" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_967_8455"/>
</filter>
<filter id="filter3_f_967_8455" x="-39.5967" y="27.6694" width="791.283" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_967_8455"/>
</filter>
</defs>
</svg>
