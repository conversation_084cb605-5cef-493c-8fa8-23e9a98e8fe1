<svg width="723" height="233" viewBox="0 0 723 233" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M91 3.68164C43.9177 3.68164 5.75 41.8493 5.75 88.9316L5.75 222.068C5.75 224.415 7.65279 226.318 10 226.318H61.5117C63.8589 226.318 65.7617 224.415 65.7617 222.068L65.7617 103.661C65.7617 78.9467 85.7969 58.9115 110.512 58.9115L616.094 58.9115C640.808 58.9115 660.844 78.9468 660.844 103.662V222.068C660.844 224.416 662.746 226.318 665.094 226.318L718 226.318C720.347 226.318 722.25 224.415 722.25 222.068V88.9316C722.25 41.8494 684.082 3.68164 637 3.68164L91 3.68164Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<g filter="url(#filter0_d_967_9935)">
<path d="M691.945 202.024V127.212C691.945 71.9834 647.173 27.2119 591.945 27.2119L137.371 27.2119C82.1426 27.2119 37.3711 71.9834 37.3711 127.212L37.3711 202.024" stroke="#9197A3" stroke-width="5"/>
</g>
<path d="M88.9408 15.6025C88.9408 18.2306 86.8102 20.3612 84.1821 20.3612C81.5539 20.3612 79.4233 18.2306 79.4233 15.6025C79.4233 12.9743 81.5539 10.8438 84.1821 10.8438C86.8102 10.8438 88.9408 12.9743 88.9408 15.6025Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M40.7005 215.284C40.7005 217.912 38.57 220.042 35.9418 220.042C33.3137 220.042 31.1831 217.912 31.1831 215.284C31.1831 212.655 33.3137 210.525 35.9418 210.525C38.57 210.525 40.7005 212.655 40.7005 215.284Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M644.657 13.6025C644.657 16.2306 642.526 18.3612 639.898 18.3612C637.27 18.3612 635.139 16.2306 635.139 13.6025C635.139 10.9743 637.27 8.84375 639.898 8.84375C642.526 8.84375 644.657 10.9743 644.657 13.6025Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M696.485 215.602C696.485 218.231 694.355 220.361 691.726 220.361C689.098 220.361 686.968 218.231 686.968 215.602C686.968 212.974 689.098 210.844 691.726 210.844C694.355 210.844 696.485 212.974 696.485 215.602Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter1_i_967_9935)">
<path d="M16.5977 95.5899C16.5977 50.3025 53.3103 13.5898 98.5977 13.5898L631.322 13.5898C676.609 13.5898 713.322 50.3025 713.322 95.5898V199.855C713.322 202.064 711.531 203.855 709.322 203.855L676.168 203.855C673.959 203.855 672.168 202.064 672.168 199.855V102.969C672.168 73.6981 648.439 49.9693 619.168 49.9693L109.355 49.9693C80.0843 49.9693 56.3553 73.6982 56.3553 102.969L56.3553 199.855C56.3553 202.064 54.5645 203.855 52.3553 203.855H20.5977C18.3885 203.855 16.5977 202.064 16.5977 199.855L16.5977 95.5899Z" fill="#FC8800"/>
</g>
<path d="M98.5977 13.0898C53.0342 13.0898 16.0977 50.0264 16.0977 95.5899L16.0977 199.855C16.0977 202.34 18.1124 204.355 20.5977 204.355H52.3553C54.8406 204.355 56.8553 202.34 56.8553 199.855L56.8553 102.969C56.8553 73.9743 80.3604 50.4693 109.355 50.4693L619.168 50.4693C648.163 50.4693 671.668 73.9743 671.668 102.969V199.855C671.668 202.34 673.683 204.355 676.168 204.355L709.322 204.355C711.807 204.355 713.822 202.34 713.822 199.855V95.5898C713.822 50.0264 676.885 13.0898 631.322 13.0898L98.5977 13.0898Z" stroke="#FC8800"/>
<g filter="url(#filter2_f_967_9935)">
<path d="M30.9541 102.009C30.9541 62.2443 63.1896 30.0088 102.954 30.0088L620.246 30.0088C660.011 30.0088 692.246 62.2443 692.246 102.009V198.889C692.246 201.098 690.456 202.889 688.246 202.889L685.625 202.889C683.416 202.889 681.625 201.098 681.625 198.889V104.277C681.625 69.4832 653.419 41.2771 618.625 41.2771L105.961 41.2771C71.1669 41.2771 42.9608 69.4832 42.9608 104.277L42.9608 198.889C42.9608 201.098 41.1699 202.889 38.9608 202.889H34.9541C32.745 202.889 30.9541 201.098 30.9541 198.889L30.9541 102.009Z" fill="#FA9550"/>
</g>
<defs>
<filter id="filter0_d_967_9935" x="30.8711" y="21.7119" width="667.574" height="185.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_967_9935"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_967_9935" result="shape"/>
</filter>
<filter id="filter1_i_967_9935" x="15.5977" y="12.5898" width="698.724" height="192.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.964706 0 0 0 0 0.407843 0 0 0 0 0.364706 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_967_9935"/>
</filter>
<filter id="filter2_f_967_9935" x="0.954102" y="0.00878906" width="721.292" height="232.88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_967_9935"/>
</filter>
</defs>
</svg>
