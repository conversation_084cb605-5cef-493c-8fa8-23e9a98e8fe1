<svg width="722" height="238" viewBox="0 0 722 238" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M632 231.25C679.082 231.25 717.25 193.082 717.25 146L717.25 12.8635C717.25 10.5163 715.347 8.61348 713 8.61348L661.488 8.61348C659.141 8.61348 657.238 10.5163 657.238 12.8635L657.238 132.27C657.238 156.433 637.651 176.02 613.488 176.02L105.906 176.02C81.744 176.02 62.1565 156.433 62.1565 132.27L62.1565 12.8633C62.1565 10.5161 60.2536 8.61331 57.9065 8.61332L4.99996 8.61352C2.65273 8.61354 0.749962 10.5163 0.749963 12.8635L0.749974 146C0.749978 193.082 38.9178 231.25 86 231.25L632 231.25Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<g filter="url(#filter0_d_1000_1682)">
<path d="M31.0551 30.9762L31.0551 105.788C31.0551 161.017 75.8266 205.788 131.055 205.788L585.629 205.788C640.857 205.788 685.629 161.017 685.629 105.788L685.629 30.9761" stroke="#9197A3" stroke-width="5"/>
</g>
<path d="M634.059 217.398C634.059 214.769 636.19 212.639 638.818 212.639C641.446 212.639 643.577 214.769 643.577 217.398C643.577 220.026 641.446 222.156 638.818 222.156C636.19 222.156 634.059 220.026 634.059 217.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M682.299 20.7164C682.299 18.0882 684.43 15.9577 687.058 15.9577C689.686 15.9577 691.817 18.0882 691.817 20.7164C691.817 23.3445 689.686 25.4751 687.058 25.4751C684.43 25.4751 682.299 23.3446 682.299 20.7164Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M78.3434 219.398C78.3434 216.769 80.474 214.639 83.1021 214.639C85.7303 214.639 87.8608 216.769 87.8608 219.398C87.8608 222.026 85.7303 224.156 83.1021 224.156C80.474 224.156 78.3434 222.026 78.3434 219.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M26.5148 20.3975C26.5148 17.7694 28.6453 15.6388 31.2735 15.6388C33.9017 15.6388 36.0322 17.7694 36.0322 20.3975C36.0322 23.0257 33.9017 25.1563 31.2735 25.1563C28.6453 25.1563 26.5148 23.0257 26.5148 20.3975Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter1_i_1000_1682)">
<path d="M706.402 140.308C706.402 185.595 669.69 222.308 624.402 222.308L91.6784 222.308C46.391 222.308 9.67844 185.595 9.67844 140.308L9.67843 36.0429C9.67843 33.8338 11.4693 32.0429 13.6784 32.0429L46.8319 32.0427C49.041 32.0427 50.8319 33.8336 50.8319 36.0427L50.8319 132.928C50.8319 162.199 74.5608 185.928 103.832 185.928L613.645 185.928C642.916 185.928 666.645 162.199 666.645 132.928L666.645 36.0429C666.645 33.8338 668.436 32.0429 670.645 32.0429L702.402 32.0429C704.611 32.0429 706.402 33.8338 706.402 36.0429L706.402 140.308Z" fill="#F53F3F"/>
</g>
<path d="M624.402 222.808C669.966 222.808 706.902 185.871 706.902 140.308L706.902 36.0429C706.902 33.5576 704.888 31.5429 702.402 31.5429L670.645 31.5429C668.159 31.5429 666.145 33.5576 666.145 36.0429L666.145 132.928C666.145 161.923 642.64 185.428 613.645 185.428L103.832 185.428C74.8369 185.428 51.3319 161.923 51.3319 132.928L51.3319 36.0427C51.3319 33.5575 49.3172 31.5427 46.8319 31.5427L13.6784 31.5429C11.1931 31.5429 9.17843 33.5577 9.17843 36.0429L9.17844 140.308C9.17844 185.871 46.1149 222.808 91.6784 222.808L624.402 222.808Z" stroke="#FF9285"/>
<g filter="url(#filter2_f_1000_1682)">
<path d="M691.086 135.82C691.086 175.585 658.85 207.82 619.086 207.82L102.753 207.82C62.989 207.82 30.7535 175.585 30.7535 135.82L30.7534 34.1111C30.7534 31.902 32.5442 30.1112 34.7533 30.1111L37.374 30.111C39.5832 30.111 41.3742 31.9019 41.3742 34.111L41.3742 133.552C41.3742 168.346 69.5802 196.552 104.374 196.552L616.08 196.552C650.874 196.552 679.08 168.346 679.08 133.552L679.08 34.1111C679.08 31.902 680.871 30.1111 683.08 30.1111L687.086 30.1111C689.295 30.1111 691.086 31.902 691.086 34.1111L691.086 135.82Z" fill="#FF9285"/>
</g>
<defs>
<filter id="filter0_d_1000_1682" x="24.5552" y="27.9761" width="667.574" height="185.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1682"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1682" result="shape"/>
</filter>
<filter id="filter1_i_1000_1682" x="8.67822" y="31.043" width="698.724" height="192.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.964706 0 0 0 0 0.407843 0 0 0 0 0.364706 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1000_1682"/>
</filter>
<filter id="filter2_f_1000_1682" x="0.753418" y="0.11084" width="720.333" height="237.709" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1000_1682"/>
</filter>
</defs>
</svg>
