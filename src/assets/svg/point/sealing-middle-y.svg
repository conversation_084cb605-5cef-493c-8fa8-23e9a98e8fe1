<svg width="717" height="59" viewBox="0 0 717 59" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 0.501953C1.75736 0.501953 0.75 1.50931 0.75 2.75195L0.75 55.7519C0.75 56.9946 1.75735 58.0019 2.99999 58.0019L714 58.002C715.243 58.002 716.25 56.9946 716.25 55.752V2.75195C716.25 1.50931 715.243 0.501953 714 0.501953L3 0.501953Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<circle cx="706.112" cy="17.2749" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="706.112" cy="46.6672" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="10.4155" cy="15.2749" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<circle cx="10.4155" cy="44.6672" r="5" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter0_d_1073_5272)">
<path d="M687 29.8354L27 29.8354" stroke="#9197A3" stroke-width="5"/>
</g>
<g filter="url(#filter1_di_1073_5272)">
<path d="M26 14.8337C26 12.6246 27.7909 10.8337 30 10.8337L682.937 10.8337C685.147 10.8337 686.937 12.6246 686.937 14.8337V45.8337C686.937 48.0429 685.147 49.8337 682.937 49.8337L30 49.8337C27.7909 49.8337 26 48.0429 26 45.8337L26 14.8337Z" fill="#954500"/>
<path d="M30 10.3337C27.5147 10.3337 25.5 12.3485 25.5 14.8337L25.5 45.8337C25.5 48.319 27.5147 50.3337 30 50.3337L682.937 50.3337C685.423 50.3337 687.437 48.319 687.437 45.8337V14.8337C687.437 12.3485 685.423 10.3337 682.937 10.3337L30 10.3337Z" stroke="#FC8800"/>
</g>
<mask id="mask0_1073_5272" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="25" y="9" width="663" height="42">
<path d="M30 10.3337C27.5147 10.3337 25.5 12.3485 25.5 14.8337L25.5 45.8337C25.5 48.319 27.5147 50.3337 30 50.3337L682.937 50.3337C685.423 50.3337 687.437 48.319 687.437 45.8337V14.8337C687.437 12.3485 685.423 10.3337 682.937 10.3337L30 10.3337Z" fill="#D26700" stroke="#FC8800"/>
</mask>
<g mask="url(#mask0_1073_5272)">
<g filter="url(#filter2_f_1073_5272)">
<path d="M-21.3423 21.0667C-21.3423 16.0593 -17.283 12 -12.2756 12L737.281 12C742.288 12 746.348 16.0593 746.348 21.0667C746.348 25.4481 742.796 29 738.414 29L-13.4089 29C-17.7904 29 -21.3423 25.4481 -21.3423 21.0667Z" fill="#FCC166"/>
</g>
</g>
<defs>
<filter id="filter0_d_1073_5272" x="23" y="24.3354" width="668" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1073_5272"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1073_5272" result="shape"/>
</filter>
<filter id="filter1_di_1073_5272" x="21" y="9.83374" width="670.938" height="49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1073_5272"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1073_5272" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10.55"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.533333 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1073_5272"/>
</filter>
<filter id="filter2_f_1073_5272" x="-51.3423" y="-18" width="827.69" height="77" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1073_5272"/>
</filter>
</defs>
</svg>
