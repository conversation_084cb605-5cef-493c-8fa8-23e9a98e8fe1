<svg width="722" height="238" viewBox="0 0 722 238" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M90 6.75C42.9177 6.75 4.75 44.9176 4.75 91.9999L4.75 225.137C4.75 227.484 6.65279 229.387 9 229.387H60.5117C62.8589 229.387 64.7617 227.484 64.7617 225.137L64.7617 105.73C64.7617 81.5674 84.3492 61.9798 108.512 61.9798L616.094 61.9798C640.256 61.9798 659.844 81.5674 659.844 105.73V225.137C659.844 227.484 661.746 229.387 664.094 229.387L717 229.387C719.347 229.387 721.25 227.484 721.25 225.137V92C721.25 44.9177 683.082 6.75 636 6.75L90 6.75Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<g filter="url(#filter0_d_967_9934)">
<path d="M690.945 207.024V132.212C690.945 76.9834 646.173 32.2119 590.945 32.2119L136.371 32.2119C81.1426 32.2119 36.3711 76.9834 36.3711 132.212L36.3711 207.024" stroke="#9197A3" stroke-width="5"/>
</g>
<path d="M87.9408 20.6025C87.9408 23.2306 85.8102 25.3612 83.1821 25.3612C80.5539 25.3612 78.4233 23.2306 78.4233 20.6025C78.4233 17.9743 80.5539 15.8438 83.1821 15.8438C85.8102 15.8438 87.9408 17.9743 87.9408 20.6025Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M39.7005 217.284C39.7005 219.912 37.57 222.042 34.9418 222.042C32.3137 222.042 30.1831 219.912 30.1831 217.284C30.1831 214.655 32.3137 212.525 34.9418 212.525C37.57 212.525 39.7005 214.655 39.7005 217.284Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M643.657 18.6025C643.657 21.2306 641.526 23.3612 638.898 23.3612C636.27 23.3612 634.139 21.2306 634.139 18.6025C634.139 15.9743 636.27 13.8438 638.898 13.8438C641.526 13.8438 643.657 15.9743 643.657 18.6025Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M695.485 217.602C695.485 220.231 693.355 222.361 690.726 222.361C688.098 222.361 685.968 220.231 685.968 217.602C685.968 214.974 688.098 212.844 690.726 212.844C693.355 212.844 695.485 214.974 695.485 217.602Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter1_i_967_9934)">
<path d="M15.5977 97.6924C15.5977 52.4051 52.3103 15.6924 97.5977 15.6924L630.322 15.6924C675.609 15.6924 712.322 52.405 712.322 97.6924V201.957C712.322 204.166 710.531 205.957 708.322 205.957L675.168 205.957C672.959 205.957 671.168 204.166 671.168 201.957V105.072C671.168 75.8007 647.439 52.0718 618.168 52.0718L108.355 52.0718C79.0843 52.0718 55.3553 75.8007 55.3553 105.072L55.3553 201.957C55.3553 204.166 53.5645 205.957 51.3553 205.957H19.5977C17.3885 205.957 15.5977 204.166 15.5977 201.957L15.5977 97.6924Z" fill="#F53F3F"/>
</g>
<path d="M97.5977 15.1924C52.0342 15.1924 15.0977 52.1289 15.0977 97.6924L15.0977 201.957C15.0977 204.442 17.1124 206.457 19.5977 206.457H51.3553C53.8406 206.457 55.8553 204.442 55.8553 201.957L55.8553 105.072C55.8553 76.0768 79.3604 52.5718 108.355 52.5718L618.168 52.5718C647.163 52.5718 670.668 76.0768 670.668 105.072V201.957C670.668 204.443 672.683 206.457 675.168 206.457L708.322 206.457C710.807 206.457 712.822 204.442 712.822 201.957V97.6924C712.822 52.1289 675.885 15.1924 630.322 15.1924L97.5977 15.1924Z" stroke="#FF9285"/>
<g filter="url(#filter2_f_967_9934)">
<path d="M30.9141 102.18C30.9141 62.4152 63.1496 30.1797 102.914 30.1797L619.247 30.1797C659.011 30.1797 691.247 62.4152 691.247 102.18V203.889C691.247 206.098 689.456 207.889 687.247 207.889L684.626 207.889C682.417 207.889 680.626 206.098 680.626 203.889V104.448C680.626 69.6541 652.42 41.448 617.626 41.448L105.92 41.448C71.1262 41.448 42.9201 69.6541 42.9201 104.448L42.9201 203.889C42.9201 206.098 41.1292 207.889 38.9201 207.889H34.9141C32.7049 207.889 30.9141 206.098 30.9141 203.889L30.9141 102.18Z" fill="#FF9285"/>
</g>
<defs>
<filter id="filter0_d_967_9934" x="29.8711" y="26.7119" width="667.574" height="185.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_967_9934"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_967_9934" result="shape"/>
</filter>
<filter id="filter1_i_967_9934" x="14.5977" y="14.6924" width="698.724" height="192.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.964706 0 0 0 0 0.407843 0 0 0 0 0.364706 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_967_9934"/>
</filter>
<filter id="filter2_f_967_9934" x="0.914062" y="0.179688" width="720.333" height="237.709" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_967_9934"/>
</filter>
</defs>
</svg>
