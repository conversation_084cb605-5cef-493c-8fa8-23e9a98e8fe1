<svg width="723" height="233" viewBox="0 0 723 233" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M632 229.318C679.082 229.318 717.25 191.151 717.25 144.068L717.25 10.9318C717.25 8.58462 715.347 6.68184 713 6.68184L661.488 6.68184C659.141 6.68184 657.238 8.58463 657.238 10.9318L657.238 129.339C657.238 154.053 637.203 174.089 612.488 174.089L106.906 174.089C82.1917 174.089 62.1565 154.053 62.1565 129.339L62.1565 10.9317C62.1565 8.58445 60.2536 6.68167 57.9065 6.68168L4.99996 6.68188C2.65273 6.6819 0.749962 8.58467 0.749963 10.9319L0.749974 144.068C0.749978 191.151 38.9178 229.318 86 229.318L632 229.318Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<g filter="url(#filter0_d_1000_1696)">
<path d="M31.0551 30.9762L31.0551 105.788C31.0551 161.017 75.8266 205.788 131.055 205.788L585.629 205.788C640.857 205.788 685.629 161.017 685.629 105.788L685.629 30.9761" stroke="#9197A3" stroke-width="5"/>
</g>
<path d="M634.059 217.398C634.059 214.769 636.19 212.639 638.818 212.639C641.446 212.639 643.577 214.769 643.577 217.398C643.577 220.026 641.446 222.156 638.818 222.156C636.19 222.156 634.059 220.026 634.059 217.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M682.299 17.7164C682.299 15.0882 684.43 12.9577 687.058 12.9577C689.686 12.9577 691.817 15.0882 691.817 17.7164C691.817 20.3445 689.686 22.4751 687.058 22.4751C684.43 22.4751 682.299 20.3446 682.299 17.7164Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M78.3434 219.398C78.3434 216.769 80.474 214.639 83.1021 214.639C85.7303 214.639 87.8608 216.769 87.8608 219.398C87.8608 222.026 85.7303 224.156 83.1021 224.156C80.474 224.156 78.3434 222.026 78.3434 219.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M26.5148 17.3975C26.5148 14.7694 28.6453 12.6388 31.2735 12.6388C33.9017 12.6388 36.0322 14.7694 36.0322 17.3975C36.0322 20.0257 33.9017 22.1563 31.2735 22.1563C28.6453 22.1563 26.5148 20.0257 26.5148 17.3975Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter1_i_1000_1696)">
<path d="M706.402 137.41C706.402 182.697 669.69 219.41 624.402 219.41L91.6784 219.41C46.391 219.41 9.67844 182.698 9.67844 137.41L9.67843 33.1455C9.67843 30.9364 11.4693 29.1455 13.6784 29.1455L46.8319 29.1453C49.041 29.1453 50.8319 30.9361 50.8319 33.1453L50.8319 130.031C50.8319 159.302 74.5608 183.031 103.832 183.031L613.645 183.031C642.916 183.031 666.645 159.302 666.645 130.031L666.645 33.1454C666.645 30.9363 668.436 29.1454 670.645 29.1454L702.402 29.1454C704.611 29.1454 706.402 30.9363 706.402 33.1454L706.402 137.41Z" fill="#FC8800"/>
</g>
<path d="M624.402 219.91C669.966 219.91 706.902 182.974 706.902 137.41L706.902 33.1454C706.902 30.6602 704.888 28.6454 702.402 28.6454L670.645 28.6454C668.159 28.6454 666.145 30.6602 666.145 33.1454L666.145 130.031C666.145 159.026 642.64 182.531 613.645 182.531L103.832 182.531C74.8369 182.531 51.3319 159.026 51.3319 130.031L51.3319 33.1453C51.3319 30.66 49.3172 28.6453 46.8319 28.6453L13.6784 28.6455C11.1931 28.6455 9.17843 30.6602 9.17843 33.1455L9.17844 137.41C9.17844 182.974 46.1149 219.91 91.6784 219.91L624.402 219.91Z" stroke="#FC8800"/>
<g filter="url(#filter2_f_1000_1696)">
<path d="M692.046 130.991C692.046 170.756 659.81 202.991 620.046 202.991L102.754 202.991C62.9891 202.991 30.7536 170.756 30.7536 130.991L30.7536 34.1111C30.7536 31.902 32.5445 30.1111 34.7536 30.1111L37.3748 30.111C39.5841 30.111 41.375 31.9018 41.375 34.111L41.375 128.723C41.375 163.517 69.581 191.723 104.375 191.723L617.039 191.723C651.833 191.723 680.039 163.517 680.039 128.723L680.039 34.1111C680.039 31.902 681.83 30.1111 684.039 30.1111L688.046 30.1111C690.255 30.1111 692.046 31.902 692.046 34.1111L692.046 130.991Z" fill="#FA9550"/>
</g>
<defs>
<filter id="filter0_d_1000_1696" x="24.5552" y="27.9761" width="667.574" height="185.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1696"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1696" result="shape"/>
</filter>
<filter id="filter1_i_1000_1696" x="8.67822" y="28.1455" width="698.724" height="192.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.964706 0 0 0 0 0.407843 0 0 0 0 0.364706 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1000_1696"/>
</filter>
<filter id="filter2_f_1000_1696" x="0.753418" y="0.11084" width="721.292" height="232.88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1000_1696"/>
</filter>
</defs>
</svg>
