<svg width="36" height="90" viewBox="0 0 36 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Property 1=&#231;&#187;&#191;">
<g id="&#229;&#141;&#161;&#230;&#137;&#163;" filter="url(#filter0_d_920_958)">
<mask id="path-1-inside-1_920_958" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 2C4 1.44771 4.44772 1 5 1H29C29.5523 1 30 1.44772 30 2V81H22V75.3519C22 74.7996 21.5523 74.3519 21 74.3519H13C12.4477 74.3519 12 74.7996 12 75.3519V81H4V2ZM13 8.35193C12.4477 8.35193 12 8.79964 12 9.35193V63.3519C12 63.9042 12.4477 64.3519 13 64.3519H21C21.5523 64.3519 22 63.9042 22 63.3519V9.35193C22 8.79964 21.5523 8.35193 21 8.35193H13Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 2C4 1.44771 4.44772 1 5 1H29C29.5523 1 30 1.44772 30 2V81H22V75.3519C22 74.7996 21.5523 74.3519 21 74.3519H13C12.4477 74.3519 12 74.7996 12 75.3519V81H4V2ZM13 8.35193C12.4477 8.35193 12 8.79964 12 9.35193V63.3519C12 63.9042 12.4477 64.3519 13 64.3519H21C21.5523 64.3519 22 63.9042 22 63.3519V9.35193C22 8.79964 21.5523 8.35193 21 8.35193H13Z" fill="url(#paint0_linear_920_958)"/>
<path d="M30 81V82H31V81H30ZM22 81H21V82H22V81ZM12 81V82H13V81H12ZM4 81H3V82H4V81ZM5 0C3.89543 0 3 0.895428 3 2H5V2V0ZM29 0H5V2H29V0ZM31 2C31 0.89543 30.1046 0 29 0V2H31ZM31 81V2H29V81H31ZM22 82H30V80H22V82ZM21 75.3519V81H23V75.3519H21ZM21 75.3519H23C23 74.2474 22.1046 73.3519 21 73.3519V75.3519ZM13 75.3519H21V73.3519H13V75.3519ZM13 75.3519V73.3519C11.8954 73.3519 11 74.2474 11 75.3519H13ZM13 81V75.3519H11V81H13ZM4 82H12V80H4V82ZM3 2V81H5V2H3ZM13 9.35193V7.35193C11.8954 7.35193 11 8.24736 11 9.35193H13ZM13 63.3519V9.35193H11V63.3519H13ZM13 63.3519H11C11 64.4565 11.8954 65.3519 13 65.3519V63.3519ZM21 63.3519H13V65.3519H21V63.3519ZM21 63.3519V65.3519C22.1046 65.3519 23 64.4565 23 63.3519H21ZM21 9.35193V63.3519H23V9.35193H21ZM21 9.35193H23C23 8.24736 22.1046 7.35193 21 7.35193V9.35193ZM13 9.35193H21V7.35193H13V9.35193Z" fill="#00996B" mask="url(#path-1-inside-1_920_958)"/>
</g>
</g>
<defs>
<filter id="filter0_d_920_958" x="0.8" y="0.8" width="34.4" height="88.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="4"/>
<feGaussianBlur stdDeviation="2.1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.146859 0 0 0 0 0.180803 0 0 0 0 0.24869 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_920_958"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_920_958" result="shape"/>
</filter>
<linearGradient id="paint0_linear_920_958" x1="15" y1="1" x2="15" y2="81.359" gradientUnits="userSpaceOnUse">
<stop offset="0.025" stop-color="#007354"/>
<stop offset="0.373574" stop-color="#007354"/>
<stop offset="0.394487" stop-color="#03A076"/>
<stop offset="0.526771" stop-color="#05BB8A"/>
<stop offset="0.641849" stop-color="#008A65"/>
<stop offset="0.975" stop-color="#007354"/>
</linearGradient>
</defs>
</svg>
