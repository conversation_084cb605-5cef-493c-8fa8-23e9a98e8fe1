<svg width="85" height="580" viewBox="0 0 85 580" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M27.5 323.5C30.5 325.392 36.5 331.159 36.5 339.088C36.5 347.018 36.5 355.333 36.5 358.5" stroke="#2F6FB6"/>
<path d="M30.5 323.5C33.5 325.392 39.5 331.159 39.5 339.088C39.5 347.018 39.5 355.333 39.5 358.5" stroke="#00996B"/>
<path d="M56.5 323.5C53.5 325.392 47.5 331.159 47.5 339.088C47.5 347.018 47.5 355.333 47.5 358.5" stroke="#3FE1FF"/>
<path d="M53.5 323.5C50.5 325.392 44.5 331.159 44.5 339.088C44.5 347.018 44.5 355.333 44.5 358.5" stroke="#E97A13"/>
<path d="M42 358V324" stroke="#F53F3F"/>
<g filter="url(#filter0_d_1423_2536)">
<path d="M36 357L36 573L48 573L48 357L36 357Z" fill="url(#paint0_linear_1423_2536)"/>
<path d="M35.7002 573.3L48.2998 573.3L48.2998 356.7L35.7002 356.7L35.7002 573.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<path d="M27.5 267.5C30.5 265.608 36.5 259.841 36.5 251.912C36.5 243.982 36.5 235.667 36.5 232.5" stroke="#2F6FB6"/>
<path d="M30.5 267.5C33.5 265.608 39.5 259.841 39.5 251.912C39.5 243.982 39.5 235.667 39.5 232.5" stroke="#00996B"/>
<path d="M56.5 267.5C53.5 265.608 47.5 259.841 47.5 251.912C47.5 243.982 47.5 235.667 47.5 232.5" stroke="#3FE1FF"/>
<path d="M53.5 267.5C50.5 265.608 44.5 259.841 44.5 251.912C44.5 243.982 44.5 235.667 44.5 232.5" stroke="#E97A13"/>
<path d="M42 232L42 266" stroke="#F53F3F"/>
<g filter="url(#filter1_d_1423_2536)">
<path d="M36 17L36 233L48 233L48 17L36 17Z" fill="url(#paint1_linear_1423_2536)"/>
<path d="M35.7002 233.3L48.2998 233.3L48.2998 16.7002L35.7002 16.7002L35.7002 233.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<g filter="url(#filter2_di_1423_2536)">
<path d="M68.8992 265.72C70.2246 265.72 71.2995 266.795 71.2996 268.12V295.481H12.739V268.12C12.7391 266.795 13.8142 265.72 15.1394 265.72H68.8992ZM34.3396 289.24C33.2794 289.241 32.4197 290.1 32.4197 291.16C32.4199 292.22 33.2795 293.08 34.3396 293.08H49.7C50.7602 293.08 51.6197 292.221 51.6199 291.16C51.6199 290.1 50.7603 289.24 49.7 289.24H34.3396ZM14.9431 266.21C14.9281 266.212 14.9131 266.214 14.8982 266.216C14.9231 266.213 14.9482 266.21 14.9734 266.208L14.9431 266.21Z" fill="url(#paint2_linear_1423_2536)"/>
</g>
<g filter="url(#filter3_di_1423_2536)">
<path d="M8.8999 301.24C8.8999 298.059 11.4787 295.48 14.6599 295.48H70.3399C73.5211 295.48 76.0999 298.059 76.0999 301.24V322.36C76.0999 323.42 75.2403 324.28 74.1799 324.28H54.4999H30.4999H10.8199C9.75952 324.28 8.8999 323.42 8.8999 322.36V301.24Z" fill="url(#paint3_linear_1423_2536)"/>
<path d="M70.3403 295.24L70.6489 295.248C73.8189 295.408 76.3402 298.03 76.3403 301.24V322.36C76.3403 323.553 75.373 324.52 74.1802 324.52H10.8198C9.62693 324.52 8.65967 323.553 8.65967 322.36V301.24L8.66748 300.931C8.82821 297.761 11.4496 295.24 14.6597 295.24H70.3403Z" stroke="#ABD6C9" stroke-width="0.48"/>
</g>
<g filter="url(#filter4_di_1423_2536)">
<path d="M8.8999 297.4C8.8999 296.34 9.75952 295.48 10.8199 295.48H74.1799C75.2403 295.48 76.0999 296.34 76.0999 297.4V322.36C76.0999 323.42 75.2403 324.28 74.1799 324.28H56.4199C55.3595 324.28 54.4999 323.42 54.4999 322.36V322.36C54.4999 321.3 53.6403 320.44 52.5799 320.44H42.4999H32.4199C31.3595 320.44 30.4999 321.3 30.4999 322.36V322.36C30.4999 323.42 29.6403 324.28 28.5799 324.28H10.8199C9.75952 324.28 8.8999 323.42 8.8999 322.36V297.4Z" fill="url(#paint4_linear_1423_2536)"/>
<path d="M74.1802 295.24C75.373 295.24 76.3403 296.207 76.3403 297.4V322.36C76.3403 323.553 75.373 324.52 74.1802 324.52H56.4194C55.2267 324.52 54.2603 323.553 54.2603 322.36C54.2602 321.432 53.5074 320.68 52.5796 320.68H32.4194C31.4918 320.68 30.7398 321.432 30.7397 322.36C30.7397 323.553 29.7725 324.52 28.5796 324.52H10.8198C9.62693 324.52 8.65967 323.553 8.65967 322.36V297.4C8.65971 296.207 9.62695 295.24 10.8198 295.24H74.1802Z" stroke="#ABD6C9" stroke-width="0.48"/>
</g>
<path d="M14.6599 265.96H69.3796C70.3075 265.96 71.0603 266.712 71.0603 267.64V295.24H12.9802V267.64L12.989 267.468C13.0751 266.621 13.7902 265.96 14.6599 265.96Z" stroke="#ABD6C9" stroke-width="0.48"/>
<g filter="url(#filter5_ddi_1423_2536)">
<rect x="22.3401" y="266.2" width="7.68" height="28.8" fill="url(#paint5_linear_1423_2536)"/>
<rect x="22.1001" y="265.96" width="8.16" height="29.28" stroke="#ABD6C9" stroke-width="0.48"/>
</g>
<g filter="url(#filter6_ddi_1423_2536)">
<rect x="54.02" y="266.2" width="7.68" height="28.8" fill="url(#paint6_linear_1423_2536)"/>
<rect x="53.78" y="265.96" width="8.16" height="29.28" stroke="#ABD6C9" stroke-width="0.48"/>
</g>
<defs>
<filter id="filter0_d_1423_2536" x="24.4799" y="340.68" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2536" result="shape"/>
</filter>
<filter id="filter1_d_1423_2536" x="24.4799" y="0.680023" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2536" result="shape"/>
</filter>
<filter id="filter2_di_1423_2536" x="8.89901" y="265.72" width="66.2405" height="37.4407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2536" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.064"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1423_2536"/>
</filter>
<filter id="filter3_di_1423_2536" x="4.57992" y="295" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2536" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.064"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1423_2536"/>
</filter>
<filter id="filter4_di_1423_2536" x="4.57992" y="295" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2536" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.064"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1423_2536"/>
</filter>
<filter id="filter5_ddi_1423_2536" x="16.1001" y="261.88" width="18.2399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1423_2536" result="effect2_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1423_2536" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.064"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1423_2536"/>
</filter>
<filter id="filter6_ddi_1423_2536" x="47.78" y="261.88" width="18.2399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2536"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1423_2536" result="effect2_dropShadow_1423_2536"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1423_2536" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.064"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1423_2536"/>
</filter>
<linearGradient id="paint0_linear_1423_2536" x1="36" y1="465.014" x2="48" y2="465.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint1_linear_1423_2536" x1="36" y1="125.014" x2="48" y2="125.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint2_linear_1423_2536" x1="42.1446" y1="265.691" x2="42.1102" y2="290.133" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint3_linear_1423_2536" x1="43.1927" y1="295.48" x2="43.1587" y2="322.361" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint4_linear_1423_2536" x1="43.1927" y1="295.48" x2="43.1587" y2="322.361" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint5_linear_1423_2536" x1="26.2593" y1="266.2" x2="25.9621" y2="293.078" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint6_linear_1423_2536" x1="57.9392" y1="266.2" x2="57.642" y2="293.078" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
</defs>
</svg>
