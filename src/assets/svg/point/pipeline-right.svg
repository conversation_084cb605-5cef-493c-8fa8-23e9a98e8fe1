<svg width="90" height="70" viewBox="0 0 90 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Property 1=&#231;&#129;&#176;, &#230;&#150;&#185;&#229;&#144;&#145;=&#229;&#143;&#179;&#232;&#190;&#185;">
<g id="Rectangle 34624538" filter="url(#filter0_d_1066_5116)">
<path d="M35.8604 59.0048C35.8578 60.1075 34.9631 61 33.8604 61L7.00014 61C5.89552 61 5.00006 60.1045 5.00014 58.9999L5.00417 2.99986C5.00425 1.89534 5.89965 1 7.00417 1L33.9952 1C35.1016 1 35.9978 1.89836 35.9952 3.00481L35.8604 59.0048Z" fill="url(#paint0_linear_1066_5116)"/>
<path d="M33.8604 61.25C35.1009 61.25 36.1074 60.2459 36.1104 59.0054L36.2452 3.00542C36.2482 1.76066 35.2399 0.75 33.9952 0.75L7.00417 0.750002C5.76159 0.750002 4.75426 1.75726 4.75417 2.99984L4.75014 58.9998C4.75005 60.2425 5.75744 61.25 7.00014 61.25L33.8604 61.25Z" stroke="#DCDCDC" stroke-width="0.5"/>
</g>
<path id="Rectangle 34624540" d="M41.6207 51.4639L41.75 51.3926L41.75 51.2449L41.75 11.7551L41.75 11.6075L41.6208 11.5362L36.6293 8.78113L36.2585 8.5765L36.2584 8.99995L36.25 54L36.2499 54.4232L36.6207 54.219L41.6207 51.4639Z" fill="url(#paint1_linear_1066_5116)" stroke="#DCDCDC" stroke-width="0.5"/>
<g id="Rectangle 34624541" filter="url(#filter1_d_1066_5116)">
<path d="M73.9475 51L41.9497 51L41.9497 12L73.9475 12L77.6001 15.5L77.6001 47.5L73.9475 51Z" fill="url(#paint2_linear_1066_5116)"/>
<path d="M73.9475 51.25L74.048 51.25L74.1205 51.1805L77.7731 47.6805L77.8501 47.6067L77.8501 47.5L77.8501 15.5L77.8501 15.3933L77.7731 15.3195L74.1205 11.8195L74.0479 11.75L73.9475 11.75L41.9497 11.75L41.6997 11.75L41.6997 12L41.6997 51L41.6997 51.25L41.9497 51.25L73.9475 51.25Z" stroke="#DCDCDC" stroke-width="0.5"/>
</g>
<rect id="Rectangle 34624553" x="64" y="51" width="13" height="39" transform="rotate(180 64 51)" fill="url(#paint3_linear_1066_5116)" fill-opacity="0.8"/>
<path id="Rectangle 34624554" d="M77.5 47.5L74 51L74 12L77.5 15.5L77.5 47.5Z" fill="#7E7E7E"/>
<path id="Line 197" d="M73.5 51L73.5 12" stroke="#DCDCDC" stroke-width="0.5"/>
<g id="Rectangle 34624544" filter="url(#filter2_d_1066_5116)">
<path d="M77.5 48L85.5 48L85.499 15L77.5 15L77.5 48Z" fill="url(#paint4_linear_1066_5116)"/>
<path d="M77.5 48.25L77.25 48.25L77.25 48L77.25 15L77.25 14.75L77.5 14.75L85.499 14.75L85.749 14.75L85.749 15L85.75 48L85.75 48.25L85.5 48.25L77.5 48.25Z" stroke="#DCDCDC" stroke-width="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_d_1066_5116" x="0.500244" y="0.5" width="39.9949" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1066_5116"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1066_5116" result="shape"/>
</filter>
<filter id="filter1_d_1066_5116" x="37.4497" y="11.5" width="44.6504" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1066_5116"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1066_5116" result="shape"/>
</filter>
<filter id="filter2_d_1066_5116" x="73" y="14.5" width="17" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1066_5116"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1066_5116" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1066_5116" x1="20.1804" y1="61" x2="20.5" y2="5.00002" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint1_linear_1066_5116" x1="28.6526" y1="30" x2="41.6526" y2="30" gradientUnits="userSpaceOnUse">
<stop stop-color="#7B7B7B"/>
<stop offset="1" stop-color="#969696"/>
</linearGradient>
<linearGradient id="paint2_linear_1066_5116" x1="59.4074" y1="51" x2="59.5248" y2="14.5992" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint3_linear_1066_5116" x1="70.634" y1="51" x2="70.3121" y2="87.3983" gradientUnits="userSpaceOnUse">
<stop stop-color="#686868"/>
<stop offset="0.15" stop-color="#8F8E8E"/>
<stop offset="0.560665" stop-color="#838382"/>
<stop offset="0.789998" stop-color="#5F5E5E"/>
<stop offset="0.99" stop-color="#898989"/>
</linearGradient>
<linearGradient id="paint4_linear_1066_5116" x1="81.5825" y1="48" x2="81.2079" y2="17.2036" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
</defs>
</svg>
