<svg width="90" height="70" viewBox="0 0 90 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Property 1=&#231;&#187;&#191;, &#230;&#150;&#185;&#229;&#144;&#145;=&#229;&#183;&#166;&#232;&#190;&#185;">
<g id="Rectangle 34624538" filter="url(#filter0_di_920_985)">
<path d="M54.1396 2.99519C54.1422 1.8925 55.0369 1 56.1396 1L82.9999 1C84.1045 1 84.9999 1.89552 84.9999 3.00014L84.9958 59.0001C84.9957 60.1047 84.1003 61 82.9958 61H56.0048C54.8984 61 54.0022 60.1016 54.0048 58.9952L54.1396 2.99519Z" fill="url(#paint0_linear_920_985)"/>
<path d="M56.1396 0.750001C54.8991 0.750001 53.8926 1.75406 53.8896 2.99459L53.7548 58.9946C53.7518 60.2393 54.7601 61.25 56.0048 61.25H82.9958C84.2384 61.25 85.2457 60.2427 85.2458 59.0002L85.2499 3.00016C85.2499 1.75746 84.2426 0.75 82.9999 0.75L56.1396 0.750001Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<path id="Rectangle 34624540" d="M48.3793 10.5361L48.25 10.6074V10.7551L48.25 50.2449V50.3925L48.3792 50.4638L53.3707 53.2189L53.7415 53.4235L53.7416 53L53.75 8.00005L53.7501 7.57676L53.3793 7.78104L48.3793 10.5361Z" fill="url(#paint1_linear_920_985)" stroke="#ABD6C9" stroke-width="0.5"/>
<g id="Rectangle 34624541" filter="url(#filter1_di_920_985)">
<path d="M16.0525 11L48.0503 11L48.0503 50L16.0525 50L12.3999 46.5L12.3999 14.5L16.0525 11Z" fill="url(#paint2_linear_920_985)"/>
<path d="M16.0525 10.75H15.952L15.8795 10.8195L12.2269 14.3195L12.1499 14.3933V14.5L12.1499 46.5V46.6067L12.2269 46.6805L15.8795 50.1805L15.952 50.25H16.0525L48.0503 50.25H48.3003V50L48.3003 11V10.75H48.0503L16.0525 10.75Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<rect id="Rectangle 34624553" x="26" y="11" width="13" height="39" fill="url(#paint3_linear_920_985)"/>
<path id="Line 197" d="M16.5 11L16.5 50" stroke="#00996B"/>
<g id="Rectangle 34624544" filter="url(#filter2_di_920_985)">
<path d="M12.5 14L4.5 14L4.50096 47H12.5L12.5 14Z" fill="url(#paint4_linear_920_985)"/>
<path d="M12.25 14.25L4.75001 14.25L4.75095 46.75H12.25L12.25 14.25Z" stroke="#ABD6C9" stroke-width="0.5"/>
</g>
<path id="Rectangle 34624554" d="M12.5 14.5L16 11V50L12.5 46.5V14.5Z" fill="#016E4D" fill-opacity="0.51"/>
</g>
<defs>
<filter id="filter0_di_920_985" x="49.5049" y="0.5" width="39.9949" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_920_985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_920_985" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_920_985"/>
</filter>
<filter id="filter1_di_920_985" x="7.8999" y="10.5" width="44.6504" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_920_985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_920_985" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_920_985"/>
</filter>
<filter id="filter2_di_920_985" x="0.5" y="14" width="16" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_920_985"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_920_985" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.6 0 0 0 0 0.419608 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_920_985"/>
</filter>
<linearGradient id="paint0_linear_920_985" x1="69.8196" y1="1" x2="69.5" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint1_linear_920_985" x1="61.3474" y1="32" x2="48.3474" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#025642"/>
<stop offset="1" stop-color="#01986A"/>
</linearGradient>
<linearGradient id="paint2_linear_920_985" x1="31.8999" y1="11" x2="32.0525" y2="47.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.215495" stop-color="#02C289"/>
<stop offset="0.63203" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
<linearGradient id="paint3_linear_920_985" x1="32.5" y1="12.95" x2="33" y2="50" gradientUnits="userSpaceOnUse">
<stop stop-color="#019065"/>
<stop offset="0.15" stop-color="#00A573"/>
<stop offset="0.560665" stop-color="#01805A"/>
<stop offset="0.755388" stop-color="#017553"/>
<stop offset="0.99" stop-color="#017C57"/>
</linearGradient>
<linearGradient id="paint4_linear_920_985" x1="7.5" y1="13.4" x2="7.5" y2="46.9667" gradientUnits="userSpaceOnUse">
<stop stop-color="#00875F"/>
<stop offset="0.15" stop-color="#02C289"/>
<stop offset="0.560665" stop-color="#028E64"/>
<stop offset="0.827305" stop-color="#036648"/>
<stop offset="0.99" stop-color="#016043"/>
</linearGradient>
</defs>
</svg>
