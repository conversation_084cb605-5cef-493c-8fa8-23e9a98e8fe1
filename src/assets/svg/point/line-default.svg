<svg width="85" height="580" viewBox="0 0 85 580" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28.5 323.5C31.5 325.392 37.5 331.159 37.5 339.088C37.5 347.018 37.5 355.333 37.5 358.5" stroke="#2F6FB6"/>
<path d="M31.5 323.5C34.5 325.392 40.5 331.159 40.5 339.088C40.5 347.018 40.5 355.333 40.5 358.5" stroke="#00996B"/>
<path d="M57.5 323.5C54.5 325.392 48.5 331.159 48.5 339.088C48.5 347.018 48.5 355.333 48.5 358.5" stroke="#3FE1FF"/>
<path d="M54.5 323.5C51.5 325.392 45.5 331.159 45.5 339.088C45.5 347.018 45.5 355.333 45.5 358.5" stroke="#E97A13"/>
<path d="M43 358V324" stroke="#F53F3F"/>
<g filter="url(#filter0_d_1423_2633)">
<path d="M37 357L37 573L49 573L49 357L37 357Z" fill="url(#paint0_linear_1423_2633)"/>
<path d="M36.7002 573.3L49.2998 573.3L49.2998 356.7L36.7002 356.7L36.7002 573.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<path d="M28.5 267.5C31.5 265.608 37.5 259.841 37.5 251.912C37.5 243.982 37.5 235.667 37.5 232.5" stroke="#2F6FB6"/>
<path d="M31.5 267.5C34.5 265.608 40.5 259.841 40.5 251.912C40.5 243.982 40.5 235.667 40.5 232.5" stroke="#00996B"/>
<path d="M57.5 267.5C54.5 265.608 48.5 259.841 48.5 251.912C48.5 243.982 48.5 235.667 48.5 232.5" stroke="#3FE1FF"/>
<path d="M54.5 267.5C51.5 265.608 45.5 259.841 45.5 251.912C45.5 243.982 45.5 235.667 45.5 232.5" stroke="#E97A13"/>
<path d="M43 232L43 266" stroke="#F53F3F"/>
<g filter="url(#filter1_d_1423_2633)">
<path d="M37 17L37 233L49 233L49 17L37 17Z" fill="url(#paint1_linear_1423_2633)"/>
<path d="M36.7002 233.3L49.2998 233.3L49.2998 16.7002L36.7002 16.7002L36.7002 233.3Z" stroke="#485773" stroke-width="0.6"/>
</g>
<g filter="url(#filter2_d_1423_2633)">
<path d="M71.0996 294.14C71.0994 294.935 70.4543 295.581 69.6592 295.581H13.9795C13.2343 295.58 12.6208 295.013 12.5469 294.288L12.5391 294.14V267.74C12.5391 266.68 13.3987 265.82 14.4591 265.82H69.1796C70.24 265.82 71.0996 266.68 71.0996 267.74V294.14ZM13.0244 294.239C13.0251 294.246 13.0275 294.253 13.0283 294.259C13.0235 294.22 13.0195 294.181 13.0195 294.14L13.0244 294.239ZM34.1396 289.34C33.0794 289.341 32.2197 290.2 32.2197 291.26C32.2199 292.32 33.0795 293.18 34.1396 293.18H49.5C50.5603 293.18 51.4197 292.321 51.4199 291.26C51.4199 290.2 50.5604 289.34 49.5 289.34H34.1396Z" fill="url(#paint2_linear_1423_2633)"/>
</g>
<g filter="url(#filter3_d_1423_2633)">
<path d="M8.69995 297.5C8.69995 296.44 9.55956 295.58 10.62 295.58H73.9799C75.0403 295.58 75.8999 296.44 75.8999 297.5V322.46C75.8999 323.52 75.0403 324.38 73.9799 324.38H54.3H30.3H10.62C9.55957 324.38 8.69995 323.52 8.69995 322.46V297.5Z" fill="url(#paint3_linear_1423_2633)"/>
<path d="M73.9802 295.34C75.173 295.34 76.1403 296.307 76.1404 297.5V322.46C76.1404 323.653 75.173 324.62 73.9802 324.62H10.6199C9.42698 324.62 8.45972 323.653 8.45972 322.46V297.5C8.45976 296.307 9.427 295.34 10.6199 295.34H73.9802Z" stroke="#DCDCDC" stroke-width="0.48"/>
</g>
<g filter="url(#filter4_d_1423_2633)">
<path d="M8.69995 297.5C8.69995 296.44 9.55956 295.58 10.62 295.58H73.9799C75.0403 295.58 75.8999 296.44 75.8999 297.5V322.46C75.8999 323.52 75.0403 324.38 73.9799 324.38H56.22C55.1596 324.38 54.3 323.52 54.3 322.46V322.46C54.3 321.4 53.4403 320.54 52.38 320.54H42.3H32.2199C31.1596 320.54 30.3 321.4 30.3 322.46V322.46C30.3 323.52 29.4403 324.38 28.38 324.38H10.62C9.55957 324.38 8.69995 323.52 8.69995 322.46V297.5Z" fill="url(#paint4_linear_1423_2633)"/>
<path d="M73.9802 295.34C75.173 295.34 76.1403 296.307 76.1404 297.5V322.46C76.1404 323.653 75.173 324.62 73.9802 324.62H56.2195C55.0268 324.62 54.0603 323.653 54.0603 322.46C54.0602 321.532 53.3074 320.78 52.3796 320.78H32.2195C31.2919 320.78 30.5399 321.532 30.5398 322.46C30.5398 323.653 29.5726 324.62 28.3796 324.62H10.6199C9.42698 324.62 8.45972 323.653 8.45972 322.46V297.5C8.45976 296.307 9.427 295.34 10.6199 295.34H73.9802Z" stroke="#DCDCDC" stroke-width="0.48"/>
</g>
<path d="M14.46 266.06H69.1797C70.1075 266.06 70.8603 266.812 70.8604 267.74V295.339H12.7803V267.74L12.7891 267.568C12.8752 266.721 13.5902 266.06 14.46 266.06Z" stroke="#DCDCDC" stroke-width="0.48"/>
<g filter="url(#filter5_d_1423_2633)">
<rect x="22.1401" y="266.3" width="7.68" height="28.8" fill="url(#paint5_linear_1423_2633)"/>
<rect x="21.9001" y="266.06" width="8.16" height="29.28" stroke="#DCDCDC" stroke-width="0.48"/>
</g>
<g filter="url(#filter6_d_1423_2633)">
<rect x="53.8201" y="266.3" width="7.68" height="28.8" fill="url(#paint6_linear_1423_2633)"/>
<rect x="53.5801" y="266.06" width="8.16" height="29.28" stroke="#DCDCDC" stroke-width="0.48"/>
</g>
<defs>
<filter id="filter0_d_1423_2633" x="25.4799" y="340.68" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter1_d_1423_2633" x="25.4799" y="0.680023" width="35.0402" height="239.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.8"/>
<feGaussianBlur stdDeviation="5.46"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.12133 0 0 0 0 0.159083 0 0 0 0 0.234587 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter2_d_1423_2633" x="8.69906" y="265.82" width="66.2405" height="37.4407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter3_d_1423_2633" x="4.37997" y="295.1" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter4_d_1423_2633" x="4.37997" y="295.1" width="75.8399" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.84"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter5_d_1423_2633" x="19.7402" y="261.98" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<filter id="filter6_d_1423_2633" x="51.4201" y="261.98" width="16.3199" height="37.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92"/>
<feGaussianBlur stdDeviation="1.92"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1423_2633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1423_2633" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1423_2633" x1="37" y1="465.014" x2="49" y2="465.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint1_linear_1423_2633" x1="37" y1="125.014" x2="49" y2="125.014" gradientUnits="userSpaceOnUse">
<stop stop-color="#323F58"/>
<stop offset="0.255" stop-color="#42516D"/>
<stop offset="0.763582" stop-color="#2E384B"/>
<stop offset="1" stop-color="#323D53"/>
</linearGradient>
<linearGradient id="paint2_linear_1423_2633" x1="42.423" y1="265.82" x2="42.3814" y2="293.597" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint3_linear_1423_2633" x1="42.9927" y1="295.58" x2="42.9587" y2="322.461" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint4_linear_1423_2633" x1="42.9927" y1="295.58" x2="42.9587" y2="322.461" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint5_linear_1423_2633" x1="26.0593" y1="266.3" x2="25.7621" y2="293.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint6_linear_1423_2633" x1="57.7392" y1="266.3" x2="57.4421" y2="293.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.15" stop-color="#9E9D9D"/>
<stop offset="0.560665" stop-color="#8A8A8A"/>
<stop offset="0.789998" stop-color="#757575"/>
<stop offset="0.99" stop-color="#999999"/>
</linearGradient>
</defs>
</svg>
