<svg width="720" height="243" viewBox="0 0 720 243" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M634 231.25C681.082 231.25 719.25 193.082 719.25 146L719.25 9.00024C719.25 6.65303 717.347 4.75024 715 4.75024L663.488 4.75025C661.141 4.75025 659.238 6.65303 659.238 9.00025L659.238 130.27C659.238 155.537 638.755 176.02 613.488 176.02L109.906 176.02C84.6394 176.02 64.1565 155.537 64.1565 130.27L64.1565 9.00007C64.1565 6.65286 62.2536 4.75006 59.9065 4.75007L6.99996 4.75029C4.65273 4.7503 2.74996 6.65308 2.74996 9.00029L2.74997 146C2.74998 193.082 40.9178 231.25 88 231.25L634 231.25Z" fill="#2A354B" stroke="#485773" stroke-width="0.5"/>
<g filter="url(#filter0_d_1000_1668)">
<path d="M33.0551 30.9762L33.0551 105.788C33.0551 161.017 77.8266 205.788 133.055 205.788L587.629 205.788C642.857 205.788 687.629 161.017 687.629 105.788L687.629 30.9761" stroke="#9197A3" stroke-width="5"/>
</g>
<path d="M636.059 217.398C636.059 214.769 638.19 212.639 640.818 212.639C643.446 212.639 645.577 214.769 645.577 217.398C645.577 220.026 643.446 222.156 640.818 222.156C638.19 222.156 636.059 220.026 636.059 217.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M684.299 17.7164C684.299 15.0882 686.43 12.9577 689.058 12.9577C691.686 12.9577 693.817 15.0882 693.817 17.7164C693.817 20.3445 691.686 22.4751 689.058 22.4751C686.43 22.4751 684.299 20.3446 684.299 17.7164Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M80.3434 219.398C80.3434 216.769 82.474 214.639 85.1021 214.639C87.7303 214.639 89.8608 216.769 89.8608 219.398C89.8608 222.026 87.7303 224.156 85.1021 224.156C82.474 224.156 80.3434 222.026 80.3434 219.398Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<path d="M28.5148 17.3975C28.5148 14.7694 30.6453 12.6388 33.2735 12.6388C35.9017 12.6388 38.0322 14.7694 38.0322 17.3975C38.0322 20.0257 35.9017 22.1563 33.2735 22.1563C30.6453 22.1563 28.5148 20.0257 28.5148 17.3975Z" fill="#364460" stroke="#29303C" stroke-width="2"/>
<g filter="url(#filter1_i_1000_1668)">
<path d="M708.402 139.342C708.402 184.629 671.69 221.342 626.402 221.342L93.6784 221.342C48.391 221.342 11.6784 184.629 11.6784 139.342L11.6784 35.0771C11.6784 32.868 13.4693 31.0771 15.6784 31.0771L48.8319 31.0769C51.041 31.0769 52.8319 32.8678 52.8319 35.0769L52.8319 131.962C52.8319 161.234 76.5608 184.962 105.832 184.962L615.645 184.962C644.916 184.962 668.645 161.233 668.645 131.962L668.645 35.0771C668.645 32.8679 670.436 31.0771 672.645 31.0771L704.402 31.0771C706.611 31.0771 708.402 32.8679 708.402 35.0771L708.402 139.342Z" fill="#007354"/>
</g>
<path d="M626.402 221.842C671.966 221.842 708.902 184.905 708.902 139.342L708.902 35.0771C708.902 32.5918 706.888 30.5771 704.402 30.5771L672.645 30.5771C670.159 30.5771 668.145 32.5918 668.145 35.0771L668.145 131.962C668.145 160.957 644.64 184.462 615.645 184.462L105.832 184.462C76.8369 184.462 53.3319 160.957 53.3319 131.962L53.3319 35.0769C53.3319 32.5916 51.3172 30.5769 48.8319 30.5769L15.6784 30.5771C13.1931 30.5771 11.1784 32.5919 11.1784 35.0771L11.1784 139.342C11.1784 184.905 48.1149 221.842 93.6784 221.842L626.402 221.842Z" stroke="#ABD6C9"/>
<g filter="url(#filter2_f_1000_1668)">
<path d="M689.247 140.649C689.247 180.414 657.012 212.649 617.247 212.649L102.834 212.649C63.0698 212.649 30.8342 180.414 30.8342 140.649L30.8342 34.1112C30.8342 31.902 32.625 30.1112 34.8341 30.1112L37.4536 30.1111C39.6628 30.111 41.4538 31.9019 41.4538 34.1111L41.4538 138.381C41.4538 173.175 69.6598 201.381 104.454 201.381L614.242 201.381C649.036 201.381 677.242 173.175 677.242 138.381L677.242 34.1112C677.242 31.902 679.033 30.1112 681.242 30.1112L685.247 30.1112C687.456 30.1112 689.247 31.902 689.247 34.1112L689.247 140.649Z" fill="#0BD296"/>
</g>
<defs>
<filter id="filter0_d_1000_1668" x="26.5552" y="27.9761" width="667.574" height="185.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.138731 0 0 0 0 0.179991 0 0 0 0 0.262511 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1000_1668"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1000_1668" result="shape"/>
</filter>
<filter id="filter1_i_1000_1668" x="10.6782" y="30.0771" width="698.724" height="192.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 3.85115e-05 0 0 0 0 0.647112 0 0 0 0 0.452567 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1000_1668"/>
</filter>
<filter id="filter2_f_1000_1668" x="0.833984" y="0.111328" width="718.413" height="242.538" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1000_1668"/>
</filter>
</defs>
</svg>
