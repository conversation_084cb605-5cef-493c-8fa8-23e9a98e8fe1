import '@geega-ui-plus/admin-design-less/src/design/index.less';
import '@geega-ui-plus/ant-design-vue/dist/antd.less';
import '@geega-ui-plus/geega-table/lib/style.css';
import './design/index.less';
import './design/uno.css';

import { createApp } from 'vue';
import Antd from '@geega-ui-plus/ant-design-vue/es';
import App from './App.vue';

import router, { setupRouter, history } from '/@/router';
import { setupStore } from '/@/store';
import { setupErrorHandle } from '/@/logics/error-handle';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
// import GxeTable from '@geega-ui-plus/geega-table';
import CMonitor from '/@/plugins/geegaMonitor/index';
import BaseConfirm from '/@/components/BaseConfirm/index.vue';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';

// router-guard
import '/@/router/guard';
import { isDevMode } from '/@/utils/env';
import './public-path';
import '/@/assets/icons';
import i18nVue from '@geega/i18n-vue';
import i18nGeega from '/@/locales/i18n/index';
import { useMessage } from '/@/hooks/web/useMessage';
import dayjs from 'dayjs';
import durationPlugin from 'dayjs/plugin/duration'
import '/@/plugins/echarts';

dayjs.extend(durationPlugin)

let app: any;

async function render(props = {}) {
  const { container } = props as any;

  app = createApp(App);

  // Global register antd
  app.use(Antd);

  // Global register gxetable
  // app.use(GxeTable);

  app.use(registerGlobComp);

  app.use(i18nGeega);

  app.component('BasicTablePlus', BasicTablePlus);
  app.component('BaseConfirm', BaseConfirm);

  // Multilingual configuration
  setupI18n(app);

  // Configure routing
  setupRouter(app);

  // Configure vuex store
  setupStore(app);

  // Register global directive
  setupGlobDirectives(app);

  // Configure global error handling
  setupErrorHandle(app);

  //monitoring
  CMonitor.init();

  // 与基座进行数据交互
  function handleMicroData(router) {
    // 是否是微前端环境
    if (window.__MICRO_APP_ENVIRONMENT__) {
      // 主动获取基座下发的数据
      //控制基座跳转页面，并渲染子应用
      const globalData = window.microApp.getGlobalData();
      console.log(
        i18nVue.t('i18n_main_67a4ef8e4a9b828fa631db57a39f485b', {
          defaultValue: '基座下发全局数据:',
        }),
        globalData
      );
      // setTimeout(()=>{
      //   //激活GMOM的Menu
      //   globalData?.activeMenuByMenuPathOrMenuID('/micro/vuewjdemo/demo/list')

      //    打开EAM的页面
      //    openPageByMenuIDOrMenuPath(menuId, path = '', isUpdateTopMenuId = false, isUpdateHistory = true)
      //    globalData?.openPageByMenuIDOrMenuPath(null,'/micro/eam/shop-calendar')
      // },4000)
      // 获取下发数据
      // const data = window.microApp.getData()
      const { createDeteleConfirm } = useMessage();
      // 监听基座下发的数据变化
      window.microApp.clearDataListener();
      window.microApp.removeDataListener((data: Record<string, unknown>) => {});
      window.microApp.addDataListener((data: Record<string, unknown>) => {
        console.log(
          i18nVue.t('i18n_main_c815143f8cf26e47c9fa196f96c685be', {
            defaultValue: '监听到基座下发的数据:',
          }),
          data
        );

        // 当基座下发path时进行跳转
        if (data.path && data.path !== router.currentRoute.value.path) {
          router.push(data.path as string);
        }

        if (data.event === 'confirm') {
          const momGlobalData = window.microApp.getGlobalData();
          createDeteleConfirm({
            type: 'warning',
            title: '确认离开',
            content: '确认离开，当前正在编辑的数据会失效',
            onOk: () => {
              // 告知
              window.microApp.dispatch({
                appName: 'vuewjdemo',
                event: 'confirm-ok',
                mainAppName: 'mom',
                data: {
                  callback: () => {
                    console.log('confirm-ok');
                    momGlobalData?.openPageByMenuIDOrMenuPath(null, data?.data?.path);
                  },
                },
              });
            },
            onCancel: () => {
              // 停留当前页面
              window.microApp.dispatch({
                appName: 'vuewjdemo',
                event: 'confirm-cancel',
                mainAppName: 'mom',
                data: {
                  callback: () => {
                    console.log('confirm-cancel');
                    momGlobalData?.activeMenuByMenuPathOrMenuID(window.location.pathname);
                  },
                },
              });
            },
          });
        }
      });

      // 解绑监听函数 window.microApp.removeGlobalDataListener(dataListener: Function)
      window.microApp.addGlobalDataListener((data) => {
        console.log(
          i18nVue.t('i18n_main_c051d08719df5696eeffe772087ef4d6', {
            defaultValue: '监听到基座下发全局数据',
          }),
          data
        );
      });

      // 向基座发送数据
      // window.microApp.dispatch({ pay: 'app' })
    }
  }

  if (window.__MICRO_APP_ENVIRONMENT__) {
    handleMicroData(router);
  }

  // 与基座进行数据交互
  function handleWujieData(router) {
    // 主动获取基座下发的数据
    window.$wujie?.bus.$on('micro-path-change', function (info) {
      console.log(
        i18nVue.t('i18n_main_7bb59b2f03e028990240a12aff5b3a3b', {
          defaultValue: '监听到WJ基座下发的数据[micro-path-change]:',
        }),
        info
      );
      const path = info?.data?.path;
      if (path) {
        if (path && path !== router.currentRoute.value.path) {
          router.push(path as string);
        }
      }
    });

    window.$wujie?.bus.$on('micro-theme-change', function (info) {
      console.log(
        i18nVue.t('i18n_main_ca7fc5d80b5b4390d0cf1772b7eacc41', {
          defaultValue: '监听到WJ基座下发的数据[micro-theme-change]:',
        }),
        info
      );
    });

    setTimeout(() => {
      //激活GMOM的Menu
      const props = window.$wujie?.props;
      console.log(props);
      // 着色菜单
      //props?.activeMenuByMenuPathOrMenuID('/gwj/vuewjdemo/demo/list')

      // 打开EAM的页面
      //openPageByMenuIDOrMenuPath(menuId, path = '', isUpdateTopMenuId = false, isUpdateHistory = true)
      // props?.openPageByMenuIDOrMenuPath(null,'/gwj/eam/shop-calendar')
    }, 4000);
  }

  if (window.__POWERED_BY_WUJIE__) {
    handleWujieData(router);
  }

  // Mount when the route is ready
  router.isReady().then(() => {
    app.mount(container ? container.querySelector('#app') : '#app', true);
    // vue-router4
    if (window.__MICRO_APP_ENVIRONMENT__) {
      router.beforeEach(() => {
        if (window.history.state?.current) {
          window.history.state.current = window.history.state.current.replace(
            new RegExp(window.__MICRO_APP_BASE_ROUTE__, 'g'),
            ''
          );
        }
      });

      router.afterEach(() => {
        if (typeof window.history.state === 'object') {
          window.history.state.current =
            window.__MICRO_APP_BASE_ROUTE__ + (window.history.state.current || '');
        }
      });
    }

    if (window.__POWERED_BY_WUJIE__) {
      //主应用发布消息-是否要求主应用发送路径
      window.$wujie?.bus.$emit('gwj-app-mounted', true);
    }
  });

  // The development environment takes effect
  if (isDevMode()) {
    // app.config.performance = true;
    window.__APP__ = app;
  }
}

//wujie
if (window.__POWERED_BY_WUJIE__) {
  window.__WUJIE_MOUNT = () => {
    render();
  };
  window.__WUJIE_UNMOUNT = () => {
    app.unmount();
  };
} else {
  // 独立运行时
  if (
    window.__MICRO_APP_ENVIRONMENT__ ||
    window.__POWERED_BY_WUJIE__ ||
    !window.__POWERED_BY_QIANKUN__
  ) {
    render();
  }
}

// qiankun 钩子
export async function bootstrap() {
  console.log('[vue] vue app bootstraped');
}
export async function mount(props) {
  render(props);
}
export async function unmount() {
  app.unmount();
  app._container.innerHTML = '';
  app = null;
  history.destroy();
}

window.addEventListener('unmount', function () {
  app.unmount();
  app._container.innerHTML = '';
  app = null;
  history.destroy();
});
