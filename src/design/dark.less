// 暗黑忽略转换色
:root {
  --ignore-dark-16: #161616;
  --ignore-dark-1e: #1e1e1e;
  --ignore-dark-thead: #202a27;
  --ignore-dark-28: #282828;
  --ignore-menu-arrow-dark: #3f3f3f;
  --ignore-menu-3d: #3d3d3d;
  --ignore-dark-33: #333;
  --ignore-dark: #000;
  --ignore-dark-66: #666;
  --ignore-dark-a4: #a4a4a4;
  --ignore-dark-aa: #aaa;
  --ignore-dark-a5: #a5a7a9;
  --ignore-dark-99: #999;
  --ignore-dark-ff: #fff;
  --ignore-dark-4f: #4f4f4f;
  --ignore-dark-a3: #a3a3a3;
  --ignore-dark-0a3327: #0a3327;
}

html[data-darkreader-scheme='dark'] {
  .@{ant-prefix}-empty {
    img {
      filter: brightness(45%);
    }
  }

  .@{ant-prefix}-empty-description {
    filter: brightness(45%);
  }

  // ----- 基础全局样式 --------------------------------------------------
  background-color: var(--ignore-dark-16) !important;

  .toggle-dark-mode {
    // 全局通用隐藏/显示
    display: block;
  }

  .toggle-light-mode {
    // 全局通用隐藏/显示
    display: none;
  }

  // ----- 基础组件样式 --------------------------------------------------
  // 表单
  .@{ant-prefix}-input,
  .@{ant-prefix}-input-number,
  .@{ant-prefix}-select,
  .@{ant-prefix}-select-dropdown,
  .debug-content {
    background-color: var(--ignore-dark-16) !important;
    // border-color: var(--ignore-dark-33) !important;
    color: #fff !important;

    &:disabled {
      color: rgba(255, 255, 255, 0.6) !important;
    }
  }

  .@{ant-prefix}-picker-suffix {
    color: var(--ignore-dark-66) !important;
  }

  // .@{ant-prefix}-form-item-control-input-content > :first-child {
  //   background-color: var(--ignore-dark-66) !important;
  //   color: var(--ignore-dark-99) !important;
  // }

  .@{ant-prefix}-input {
    // border-color: var(--ignore-dark-33) !important;
    background: transparent !important;

    &:placeholder {
      color: var(--ignore-dark-66) !important;
    }
  }
  .@{ant-prefix}-switch {
    background-color: var(--ignore-dark-66) !important;
    // border-color: var(--ignore-dark-33) !important;

    &::after {
      background-color: var(--ignore-dark-66) !important;
      box-shadow: none !important;
    }

    &.@{ant-prefix}-switch-checked {
      background-color: @primary-color !important;

      &::after {
        background-color: rgb(5 80 57) !important;
      }
    }
  }
  .@{ant-prefix}-switch-handle::before {
    background-color: var(--ignore-dark-ff) !important;
  }

  .@{ant-prefix}-calendar-picker-container-content
    .@{ant-prefix}-calendar-panel
    .@{ant-prefix}-calendar-body
    .@{ant-prefix}-calendar-cell
    .@{ant-prefix}-calendar-date:hover {
    background-color: var(--ignore-dark-33) !important;
  }

  // table
  .@{ant-prefix}-table-fixed-header .@{ant-prefix}-table-scroll .@{ant-prefix}-table-header,
  .@{ant-prefix}-table-thead > tr > th {
    background-color: var(--ignore-dark-thead) !important;
  }

  .event-subscription-box .subscript-content .header-title,
  .action-wrap .action-well .item-label .label,
  .trigger-wrap .trigger-well .item-label .label,
  .group-menu-popup-x,
  .@{ant-prefix}-dropdown,
  .@{ant-prefix}-select-dropdown,
  .@{ant-prefix}-drawer-content-wrapper,
  .@{ant-prefix}-modal,
  .@{ant-prefix}-tooltip,
  .@{ant-prefix}-popover,
  .@{ant-prefix}-message-notice-content,
  .@{ant-prefix}-menu-submenu-popup,
  .@{ant-prefix}-calendar-picker-container,
  .@{ant-prefix}-modal,
  .@{ant-prefix}-message-notice-content,
  .@{ant-prefix}-drawer-content-wrapper,
  .@{ant-prefix}-modal-content,
  .@{ant-prefix}-picker-dropdown,
  .@{ant-prefix}-menu-submenu-popup {
    filter: drop-shadow(0 4px 6px #000) brightness(100%) !important;
  }

  .@{ant-prefix}-select-tree-dropdown
    .@{ant-prefix}-select-dropdown-content
    .@{ant-prefix}-select-tree
    li
    .@{ant-prefix}-select-tree-node-content-wrapper
    .@{ant-prefix}-select-tree-title:hover,
  .@{ant-prefix}-dropdown-menu-item-active,
  .@{ant-prefix}-select-dropdown-menu-item-active {
    background-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-select-arrow {
    color: var(--ignore-dark-a4) !important;
  }

  .@{ant-prefix}-popover-arrow {
    border-color: var(--ignore-dark-16) transparent transparent var(--ignore-dark-16);
    box-shadow: var(--ignore-dark-33) -2px -2px 1px;
  }

  .@{ant-prefix}-select-tree-dropdown
    .@{ant-prefix}-select-dropdown-search
    .@{ant-prefix}-select-search__field {
    background-color: var(--ignore-dark-16) !important;
    // border-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-tree li .@{ant-prefix}-tree-node-content-wrapper:hover {
    background-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-select:not(.@{ant-prefix}-select-customize-input) .@{ant-prefix}-select-selector {
    background-color: var(--ignore-dark-16) !important;
    // border: 1px solid var(--ignore-dark-33) !important;
  }
  .@{ant-prefix}-input-affix-wrapper,
  .@{ant-prefix}-picker {
    background-color: var(--ignore-dark-16) !important;
    // border-color: var(--ignore-dark-33) !important;

    &.@{ant-prefix}-input-affix-wrapper-disabled {
      background-color: var(--ignore-dark-33) !important;
      border-color: var(--ignore-dark-66) !important;
    }
  }

  .@{ant-prefix}-table-filter-trigger,
  .@{ant-prefix}-table-column-sorter {
    color: var(--ignore-dark-66) !important;
  }

  .@{namespace}-basic-table-header__toolbar .table-settings svg {
    fill: var(--ignore-dark-66) !important;
  }

  .@{ant-prefix}-table-tbody > tr.@{ant-prefix}-table-row-selected > td {
    background-color: var(--ignore-dark-28) !important;
  }

  .@{ant-prefix}-table-tbody > tr.@{ant-prefix}-table-row:hover > td {
    background-color: var(--ignore-dark-28) !important;
  }

  .@{ant-prefix}-select-item-empty {
    img {
      filter: brightness(45%);
    }
    .@{ant-prefix}-empty-description {
      color: var(--ignore-dark-a3);
    }
  }

  .empty-container img {
    filter: brightness(45%);
  }

  .@{ant-prefix}-checkbox-inner {
    border: 1px solid var(--ignore-dark-66);
  }

  .@{ant-prefix}-checkbox-wrapper-checked {
    .@{ant-prefix}-checkbox-inner {
      border: 1px solid transparent !important;
    }
  }

  .@{namespace}-g-tag span {
    border: 0 !important;
  }

  .@{ant-prefix}-table.@{ant-prefix}-table-bordered
    > .@{ant-prefix}-table-container
    > .@{ant-prefix}-table-header
    > table
    > thead
    > tr
    > th:nth-last-child(2) {
    border-right-color: var(--ignore-dark-thead) !important;
  }

  .@{namespace}-setting-menu-type-picker__item--sidebar {
    &:after {
      background-color: @primary-color;
    }
  }
  .@{namespace}-setting-menu-type-picker__item--top-menu {
    &:after {
      background-color: @primary-color;
    }
  }

  .@{ant-prefix}.@{ant-prefix}-table-bordered
    > .@{ant-prefix}-table-container
    > .@{ant-prefix}-table-body
    > table
    > tbody
    > tr
    > .@{ant-prefix}-table-cell-fix-right-first::after {
    border-right-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-table.@{ant-prefix}-table-bordered
    > .@{ant-prefix}-table-container
    > .@{ant-prefix}-table-body
    > table
    > tbody
    > tr
    > td {
    border-right-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-table.@{ant-prefix}-table-bordered
    > .@{ant-prefix}-table-container
    > .@{ant-prefix}-table-body
    > table
    > tbody
    > tr
    > td {
    border-right: 1px solid var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-table-tbody > tr > td {
    border-right: 1px solid var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-tree .@{ant-prefix}-tree-node-content-wrapper:hover {
    background-color: var(--ignore-dark-33) !important;
  }

  .@{ant-prefix}-tree .@{ant-prefix}-tree-node-content-wrapper.@{ant-prefix}-tree-node-selected {
    background-color: var(--ignore-dark-0a3327);
  }

  .@{ant-prefix}-picker-cell-disabled {
    .@{ant-prefix}-picker-cell-inner {
      background: #555;
    }
  }

  .@{ant-prefix}-select {
    &.@{ant-prefix}-select-disabled {
      .@{ant-prefix}-select-selector {
        background-color: var(--ignore-dark-33) !important;
        border-color: var(--ignore-dark-66) !important;
      }
    }
  }

  // .@{ant-prefix}-pagination-item {
  //   color: var(--ignore-dark-99);

  //   a {
  //     color: var(--ignore-dark-99);
  //   }
  // }
  // .@{namespace}-basic-table .@{ant-prefix}-table-wrapper .@{ant-prefix}-table-pagination {
  //   color: var(--ignore-dark-99);
  //   .@{ant-prefix}-select:not(.@{ant-prefix}-select-customize-input) .@{ant-prefix}-select-selector {
  //     color: var(--ignore-dark-99);
  //   }
  // }

  // .@{ant-prefix}-pagination-jump-next .@{ant-prefix}-pagination-item-container .@{ant-prefix}-pagination-item-ellipsis {
  //   color: var(--ignore-dark-99);;
  // }
}
