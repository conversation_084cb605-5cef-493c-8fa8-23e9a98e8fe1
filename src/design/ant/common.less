.@{ant-prefix}-picker {
  padding: @input-padding-vertical-base @input-padding-horizontal-base !important;
}

// .@{ant-prefix}-pagination .@{ant-prefix}-pagination-item-active a {
//   color: @primary-color;
//   &:hover{
//     color: @primary-color;
//   }
// }


.@{ant-prefix}-select-show-search.@{ant-prefix}-select:not(.@{ant-prefix}-select-customize-input) .@{ant-prefix}-select-selector {
  height: @input-height-base;
  box-sizing: border-box;
}

body .@{ant-prefix}-pagination-options-size-changer.@{ant-prefix}-select .@{ant-prefix}-select-selector {
  background-color: #fff !important;
  border-color: @border-color-base !important
}

body .@{ant-prefix}-pagination-options-quick-jumper input{
  background-color: #fff !important;
  border-color: @border-color-base !important
}

body .@{ant-prefix}-table-pagination.@{ant-prefix}-pagination {
  margin: 8px 0;
}

.@{ant-prefix}-table-tbody > tr > td {
   min-height: 35px;
}

.@{ant-prefix}-btn{
  padding-left: 12px;
  padding-right: 12px;
}

.@{ant-prefix}-table .@{ant-prefix}-table-thead > tr > th {
  border-bottom: 1px solid @primary-color;
}

.@{namespace}-basic-table-action .@{ant-prefix}-btn {
  padding: 0px 5px;
  height: 20px;
}

// .@{namespace}-basic-table .@{ant-prefix}-table {
//   line-height: 1.616 !important;
// }

.@{namespace}-basic-column-setting__cloumn-list .@{ant-prefix}-popover-title {
  display: none;
}

.@{ant-prefix}-modal-confirm-title {
    display: inline-block !important;
    vertical-align: middle;
    padding: 0 2px;
    position: relative;
    top: 1px;
}

span.@{ant-prefix}-input-clear-icon{
  svg[data-icon] {
    vertical-align: inherit;
  }
}
.@{namespace}-svg-icon {
  vertical-align: middle;
}
.@{ant-prefix}-select-arrow .anticon {
  vertical-align: top !important;
}

.@{ant-prefix}-table.gplus-ant-table-bordered > .@{ant-prefix}-table-container > .@{ant-prefix}-table-header > table > thead > tr > th:nth-last-child(2) {
  // border-right: 1px solid #202a27;
  border-right-color: #e9f6f2;
}

.@{ant-prefix}-input-number {
  min-width: 0;
}

.@{ant-prefix}-input-wrapper {
  .@{ant-prefix}-input-search-button {
    height: 24px;
    padding: 0;
  }
}

.@{ant-prefix}-checkbox-wrapper-checked.@{ant-prefix}-checkbox-wrapper-disabled{
  .@{ant-prefix}-checkbox-inner {
    background-color: rgba(0, 153, 107)!important;
  }
}

.@{ant-prefix}-radio-wrapper-checked.@{ant-prefix}-radio-wrapper-disabled{
  .@{ant-prefix}-radio-inner {
    background-color: rgba(0, 153, 107)!important;
    &::after{
      background-color:rgba(0, 0, 0, 0.5)!important
    }
  }
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.@{ant-prefix}-popover-content .@{ant-prefix}-popover-inner-content{
  padding:10px 0!important;
}

.@{namespace}-menu-menu-popover .@{namespace}-menu-item, .@{namespace}-menu-menu-popover .@{namespace}-menu-submenu-title{
  padding: 8px 0px!important;
}

.@{namespace}-menu .@{namespace}-simple-menu__children:hover{
  background-color: rgba(0, 0, 0, 0.4) !important;
  color:rgba(0, 153, 107);
}
