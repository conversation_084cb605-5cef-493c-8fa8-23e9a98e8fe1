.@{ant-prefix}-table-thead > tr > th {
  padding: @geega-ui-table-thead-row-padding !important;
  border-bottom: 1px solid @primary-color;
}

.@{ant-prefix}-table.gplus-ant-table-bordered > .@{ant-prefix}-table-container {
  border-right: 1px solid @table-border-base-color;
}

.@{ant-prefix}-table-tbody > tr.@{ant-prefix}-table-row > td {
  padding: @geega-ui-table-tbody-row-padding !important;
}

.@{ant-prefix}-table-wrapper {
  background-color: #fff;
}

.@{ant-prefix}-form-item-control .anticon {
  line-height: unset;
}

// fix a-select width when it is in a a-form-item
.@{ant-prefix}-form-item {
  .@{ant-prefix}-form-item-control-input-content {
    > div > div {
      flex: 1;
      width: 0;
    }
  }
}

.@{ant-prefix}-table-cell-fix-right-first {
  .@{ant-prefix}-btn {
    padding: 0;
    height: 19px;
  }
}

.@{ant-prefix}-table {
  line-height: 1.6925 !important;

  // 表格内容全局左对齐
  .@{ant-prefix}-table-tbody > tr > td {
    text-align: left !important;
  }

  .@{ant-prefix}-table-thead > tr > th {
    text-align: left !important;
  }
}
