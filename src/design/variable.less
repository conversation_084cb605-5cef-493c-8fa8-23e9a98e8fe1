// 此处声明的变量会覆盖框架已有变量--此文件不可删除 -- 此文件只申明变量
// 用来覆盖
// @geega-plus-ui/ant-design-vue  https://git-front.geega.com/bsc/geega-ui-plus/geega-ui-plus-component-center/-/tree/develop/packages/ant-design-vue/components/style
// @geega-plus-ui/geega-admin-less  https://git.geega.com/geega/front-end/geega-components/-/tree/geega-plus-ui/packages/geega-ui

// ========= header =========
@logo-width: 28px;

@side-drag-z-index: 200;

@page-loading-z-index: 10000;

@lock-page-z-index: 3000;

@layout-header-fixed-z-index: 500;

@multiple-tab-fixed-z-index: 505;

@layout-sider-fixed-z-index: 510;

@layout-mix-sider-fixed-z-index: 550;

@preview-comp-z-index: 1000;

@page-footer-z-index: 99;

@layout-header-height: 50px;

@layout-header-bg: #2c2c2c;

@header-content-height: 50px;

@header-light-bg-hover-color: #f6f6f6;
@header-light-desc-color: #7c8087;
@header-light-bottom-border-color: @primary-color;

// ========= layout content =========
@layout-content-bg-color: #ffffff;

// =================================
// ==============breadcrumb=========
// =================================
@breadcrumb-item-normal-color: #999;

// =================================
// ==============tab=========
// =================================
@tab-multiple-height: 36px;
@tab-multiple-bg-color: #f5f5f5;
