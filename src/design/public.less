// =================================
// ==============app container==========
// =================================

html {
  overflow: hidden;
  -webkit-text-size-adjust: 100%;

  // 表格-操作栏按钮颜色变量
  --gad-primary-color: @primary-color;
  --gad-primary-color-hover: fade(@primary-color, 40%);

  // 表格-操作栏取消按钮颜色变量
  --gxe-cancel-color: #999999;
  --gxe-cancel-color-hover: rgb(153 153 153 / 62%);

  // 表格-主色【图标颜色、checkbox、radio颜色等】
  --gxe-primary-color: var(--gad-primary-color);
  // 表格-icon hover颜色
  --gxe-table-icon-hover-color: var(--gad-primary-color-hover);
  // 表格-loading字体颜色
  --gxe-table-loading-text-color: @text-color;
  // 表格-展开行背景颜色
  --gxe-table-body-expanded-background-color: #f0f0f0;
  // 表格-边框颜色
  --gxe-table-border-color: #e6e6e6;
  // 表格-header字体颜色
  --gxe-table-header-font-color: @table-header-font-color;
  // 表格-header背景颜色
  --gxe-table-header-background-color: @table-header-background-color;
  // 表格-body字体颜色
  --gxe-table-font-color: #363636;
  // 表格-body背景颜色
  --gxe-table-body-background-color: #ffffff;
  // 表格-行背景颜色【斑马线】
  --gxe-table-row-striped-background-color: #ffffff;
  // 表格-行背景颜色【选中当前行】
  --gxe-table-row-current-background-color: #f5f5f5;
  // 表格-行背景颜色【编辑当前行】
  --gxe-table-row-edit-actived-background-color: #efefef;
  // 表格-列背景颜色【选中当前列】
  --gxe-table-column-current-background-color: #f5f5f5;
  // 表格-header hover背景颜色
  --gxe-table-column-hover-background-color: #f5f5f5;
  // 表格-行hover背景颜色【选中checkbox】
  --gxe-table-row-hover-checkbox-checked-background-color: #efefef;
  // 表格-行hover背景颜色【选中radio】
  --gxe-table-row-hover-radio-checked-background-color: #efefef;
  // 表格-行hover背景颜色【选中行】
  --gxe-table-row-hover-current-background-color: #efefef;
  // 表格-行hover背景颜色【斑马线】
  --gxe-table-row-hover-striped-background-color: #efefef;
  // 表格-行hover背景颜色
  --gxe-table-row-hover-background-color: #efefef;
  // 表格-单元格占位符 placeholder 字体颜色
  --gxe-table-cell-placeholder-color: #aaaaaa;
  // 表格固定列-右-阴影
  --gxe-table-fixed-right-scrolling-box-shadow: -8px 0 10px -5px rgb(0 0 0 / 12%);
  // 表格固定列-左-阴影
  --gxe-table-fixed-left-scrolling-box-shadow: 8px 0 10px -5px rgb(0 0 0 / 12%);

  // 表格-不同尺寸行高（padding）调节
  // default -舒适型(默认)：行高48px
  --gxe-table-column-padding-default: 3px 0;
  --gxe-table-row-height-default: 35px;
  // medium -标准型：行高40px
  --gxe-table-column-padding-medium: 9px 0;
  --gxe-table-row-height-medium: 40px;
  // small -紧凑型：行高36px
  --gxe-table-column-padding-small: 7px 0;
  --gxe-table-row-height-small: 36px;
  // mini -mini型：行高32px 【此类型未满足ui规范，但是因为table支持固多次类型的设置】
  --gxe-table-column-padding-mini: 5px 0;
  --gxe-table-row-height-mini: 32px;

  // 表格-列-左边距
  --gxe-table-cell-padding-left: 16px;
  // 表格-列-右边距
  --gxe-table-cell-padding-right: 16px;

  // 表格-header分割线颜色
  --gxe-table-resizable-line-color: #d8d8d8;
  // 表格-header字体font-weight
  --gxe-table-header-font-weight: 600;

  .gxe-table--render-default {
    .gxe-table--header-wrapper {
      border-bottom: 1px solid @primary-color;
    }
  }
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible;
  overflow-x: hidden;

  // 色弱
  &.color-weak {
    filter: invert(80%);
  }

  // 灰色
  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }

  // =================================
  // ==============app container==========
  // =================================

  #app {
    width: 100%;
    height: 100%;
  }

  #i18nChangedButton {
    display: none;
  }

  // =================================
  // ==============wind.css 补充==========
  // =================================
  .ml-3 {
    margin-left: 0.3rem;
  }

  .ml-6 {
    margin-left: 0.6rem;
  }

  .ml-2 {
    margin-left: 4px;
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}

// =================================
// ==============webkit-autofill==========
// =================================

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
}

// =================================
// ==============input webkit-autofill==========
// =================================

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #f3f3f3 inset;
}

input:-internal-autofill-selected {
  background-color: #f3f3f3;
}
// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar-corner {
  background-color: #f2f2f2;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  // background: rgba(0, 0, 0, 0.03);
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 6px;
}
// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

.rc-virtual-list-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
}

img,
svg {
  vertical-align: unset;
}
