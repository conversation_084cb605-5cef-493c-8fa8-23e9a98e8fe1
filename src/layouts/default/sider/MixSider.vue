<template>
  <div :class="`${prefixCls}-dom`" :style="getDomStyle"></div>
  <div v-click-outside="handleClickOutside" :style="getWrapStyle" :class="[
    prefixCls,
    {
      open: openMenu,
      mini: getCollapsed,
    },
  ]" v-bind="getMenuEvents">
    <AppLogo :showTitle="false" :class="`${prefixCls}-logo`" />

    <!-- <Trigger :class="`${prefixCls}-trigger`" /> -->

    <ScrollContainer>
      <ul :class="`${prefixCls}-module`">
        <li :class="[
          `${prefixCls}-module__item `,
          {
            [`${prefixCls}-module__item--active`]: item.path === activePath,
          },
        ]" v-bind="getItemEvents(item)" v-for="item in menuModules" :key="item.path">
          <SimpleMenuTag :item="item" collapseParent dot />
          <Icon :class="`${prefixCls}-module__icon`" :size="getCollapsed ? 16 : 20"
            :icon="item.icon || (item.meta && item.meta.icon)" />
          <p :class="`${prefixCls}-module__name`">
            {{ t(item.name) }}
          </p>
        </li>
      </ul>
    </ScrollContainer>

    <div :class="`${prefixCls}-menu-list`" ref="sideRef" :style="getMenuStyle">
      <div v-show="openMenu" :class="[
        `${prefixCls}-menu-list__title`,
        {
          show: openMenu,
        },
      ]">
        <span class="text"> {{ title }}</span>
        <Icon :size="16" :icon="getMixSideFixed ? 'ri:pushpin-2-fill' : 'ri:pushpin-2-line'" class="pushpin"
          @click="handleFixedMenu" />
      </div>
      <ScrollContainer :class="`${prefixCls}-menu-list__content`">
        <SimpleMenu :items="chilrenMenus" mixSider @menuClick="handleMenuClick" />
      </ScrollContainer>
      <div v-show="getShowDragBar && openMenu" :class="`${prefixCls}-drag-bar`" ref="dragBarRef"></div>
    </div>
  </div>
</template>
<script lang="ts">
import type { Menu } from '/@/router/types';
import type { CSSProperties } from 'vue';
import type { RouteLocationNormalized } from 'vue-router';

import { defineComponent, onMounted, ref, computed, unref } from 'vue';

import { ScrollContainer, SimpleMenuTag, Icon, AppLogo } from '@geega-ui-plus/geega-ui';

import Trigger from '../trigger/HeaderTrigger.vue';

import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useDragLine } from './useLayoutSider';
import { useGlobSetting } from '/@/hooks/setting';
import { useDesign } from '/@/hooks/web/useDesign';
import { useI18n } from '/@/hooks/web/useI18n';
import { useGo } from '/@/hooks/web/usePage';

import { SIDE_BAR_SHOW_TIT_MINI_WIDTH, SIDE_BAR_MINI_WIDTH } from '/@/enums/appEnum';

import clickOutside from '/@/directives/clickOutside';
import { getShallowMenus, getChildrenMenus, getCurrentParentPath } from '/@/router/menus';
import { listenerLastChangeTab } from '/@/logics/mitt/tabChange';
import { SimpleMenu } from '@geega-ui-plus/geega-ui';

export default defineComponent({
  name: 'LayoutMixSider',
  components: {
    ScrollContainer,
    AppLogo,
    SimpleMenu,
    Icon,
    Trigger,
    SimpleMenuTag,
  },
  directives: {
    clickOutside,
  },
  setup() {
    let menuModules = ref<Menu[]>([]);
    const activePath = ref('');
    const chilrenMenus = ref<Menu[]>([]);
    const openMenu = ref(false);
    const dragBarRef = ref<ElRef>(null);
    const sideRef = ref<ElRef>(null);
    const currentRoute = ref<Nullable<RouteLocationNormalized>>(null);

    const { prefixCls } = useDesign('layout-mix-sider');
    const go = useGo();
    const { t } = useI18n();
    const {
      getMenuWidth,
      getCanDrag,
      getCloseMixSidebarOnChange,
      getMixSideTrigger,
      getRealWidth,
      getMixSideFixed,
      mixSideHasChildren,
      setMenuSetting,
      getIsMixSidebar,
      getCollapsed,
    } = useMenuSetting();

    const { title } = useGlobSetting();

    useDragLine(sideRef, dragBarRef, true);

    const getMenuStyle = computed(
      (): CSSProperties => {
        return {
          width: unref(openMenu) ? `${unref(getMenuWidth)}px` : 0,
          left: `${unref(getMixSideWidth)}px`,
        };
      }
    );

    const getIsFixed = computed(() => {
      /* eslint-disable-next-line */
      mixSideHasChildren.value = unref(chilrenMenus).length > 0;
      const isFixed = unref(getMixSideFixed) && unref(mixSideHasChildren);
      if (isFixed) {
        /* eslint-disable-next-line */
        openMenu.value = true;
      }
      return isFixed;
    });

    const getMixSideWidth = computed(() => {
      return unref(getCollapsed) ? SIDE_BAR_MINI_WIDTH : SIDE_BAR_SHOW_TIT_MINI_WIDTH;
    });

    const getDomStyle = computed(
      (): CSSProperties => {
        const fixedWidth = unref(getIsFixed) ? unref(getRealWidth) : 0;
        const width = `${unref(getMixSideWidth) + fixedWidth}px`;
        return getWrapCommonStyle(width);
      }
    );

    const getWrapStyle = computed(
      (): CSSProperties => {
        const width = `${unref(getMixSideWidth)}px`;
        return getWrapCommonStyle(width);
      }
    );

    const getMenuEvents = computed(() => {
      return !unref(getMixSideFixed)
        ? {
          onMouseleave: () => {
            closeMenu();
          },
        }
        : {};
    });

    const getShowDragBar = computed(() => unref(getCanDrag));

    onMounted(async () => {
      menuModules.value = await getShallowMenus();
    });

    listenerLastChangeTab((route) => {
      currentRoute.value = route;
      setActive(true);
      if (unref(getCloseMixSidebarOnChange)) {
        closeMenu();
      }
    });

    function getWrapCommonStyle(width: string): CSSProperties {
      return {
        width,
        maxWidth: width,
        minWidth: width,
        flex: `0 0 ${width}`,
      };
    }

    // Process module menu click
    async function hanldeModuleClick(path: string, hover = false) {
      const children = await getChildrenMenus(path);

      if (unref(activePath) === path) {
        if (!hover) {
          if (!unref(openMenu)) {
            openMenu.value = true;
          } else {
            closeMenu();
          }
        }
        if (!unref(openMenu)) {
          setActive();
        }
      } else {
        openMenu.value = true;
        activePath.value = path;
      }

      if (!children || children.length === 0) {
        go(path);
        chilrenMenus.value = [];
        closeMenu();
        return;
      }
      chilrenMenus.value = children;
    }

    // Set the currently active menu and submenu
    async function setActive(setChildren = false) {
      const path = currentRoute.value?.path;
      if (!path) return;
      const parentPath = await getCurrentParentPath(path);
      activePath.value = parentPath;
      // hanldeModuleClick(parentPath);
      if (unref(getIsMixSidebar)) {
        const activeMenu = unref(menuModules).find((item) => item.path === unref(activePath));
        const p = activeMenu?.path;
        if (p) {
          const children = await getChildrenMenus(p);
          if (setChildren) {
            chilrenMenus.value = children;

            if (unref(getMixSideFixed)) {
              openMenu.value = children.length > 0;
            }
          }
          if (children.length === 0) {
            chilrenMenus.value = [];
          }
        }
      }
    }

    function handleMenuClick(path: string) {
      go(path);
    }

    function handleClickOutside() {
      setActive(true);
      closeMenu();
    }

    function getItemEvents(item: Menu) {
      if (unref(getMixSideTrigger) === 'hover') {
        return {
          onMouseenter: () => hanldeModuleClick(item.path, true),
        };
      }
      return {
        onClick: () => hanldeModuleClick(item.path),
      };
    }

    function handleFixedMenu() {
      setMenuSetting({
        mixSideFixed: !unref(getIsFixed),
      });
    }

    // Close menu
    function closeMenu() {
      if (!unref(getIsFixed)) {
        openMenu.value = false;
      }
    }

    return {
      t,
      prefixCls,
      menuModules,
      hanldeModuleClick,
      activePath,
      chilrenMenus,
      getShowDragBar,
      handleMenuClick,
      getMenuStyle,
      handleClickOutside,
      sideRef,
      dragBarRef,
      title,
      openMenu,
      getItemEvents,
      getMenuEvents,
      getDomStyle,
      handleFixedMenu,
      getMixSideFixed,
      getWrapStyle,
      getCollapsed,
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-layout-mix-sider';
@width: 64px;

.@{prefix-cls} {
  position: fixed;
  top: 0;
  left: 0;
  z-index: @layout-mix-sider-fixed-z-index;
  height: 100%;
  overflow: hidden;
  transition: all 0.2s ease 0s;

  &-dom {
    height: 100%;
    overflow: hidden;
    transition: all 0.2s ease 0s;
  }

  &-logo {
    display: flex;
    padding-left: 0;
    justify-content: center;

    img {
      width: @logo-width;
      height: @logo-width;
    }
  }

  &.light {
    .@{prefix-cls}-logo {
      border-bottom: 1px solid #f0f0f0;
    }

    &.open {
      >.scrollbar {
        border-right: 1px solid #f0f0f0;
      }
    }

    .@{prefix-cls}-module {
      &__item {
        font-weight: normal;

        &--active {
          color: @primary-color;
          background: unset;
        }
      }
    }

    .@{prefix-cls}-menu-list {
      &__content {
        box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
      }

      &__title {
        .pushpin {
          color: rgba(0, 0, 0, 0.35);

          &:hover {
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
  }

  &.mini &-module {
    &__name {
      display: none;
    }

    &__icon {
      margin-bottom: 0;
    }
  }

  &-module {
    position: relative;
    padding-top: 1px;

    &__item {
      position: relative;
      padding: 12px 0;
      color: rgba(255, 255, 255, 0.65);
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: @gplus-white-color;
      }

      // &:hover,
      &--active {
        font-weight: 700;
        color: @gplus-white-color;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background: @primary-color;
          content: '';
        }
      }
    }

    &__icon {
      margin-bottom: 8px;
      font-size: 24px;
      transition: all 0.2s;
    }

    &__name {
      margin-bottom: 0;
      font-size: 12px;
      transition: all 0.2s;
    }
  }

  &-trigger {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 6px;
    padding-left: 12px;
    font-size: 18px;
    color: rgba(255, 255, 255, 0.65);
    cursor: pointer;
  }

  &.light &-trigger {
    background: #fff;
  }

  &-menu-list {
    position: fixed;
    top: 0;
    width: 0;
    width: 200px;
    height: calc(100%);
    background: #fff;
    transition: all 0.2s;

    &__title {
      display: flex;
      font-size: 18px;
      color: @primary-color;
      border-bottom: 1px solid rgb(238, 238, 238);
      opacity: 0;
      transition: unset;
      align-items: center;
      justify-content: space-between;

      &.show {
        min-width: 130px;
        opacity: 1;
        transition: all 0.5s ease;
      }

      .pushpin {
        margin-right: 6px;
        color: rgba(255, 255, 255, 0.65);
        cursor: pointer;

        &:hover {
          color: #fff;
        }
      }
    }

    &__content {
      .scrollbar__wrap {
        height: 100%;
        overflow-x: hidden;
      }

      .scrollbar__bar.is-horizontal {
        display: none;
      }

      .@{ant-prefix}-menu {
        height: 100%;
      }

      .@{ant-prefix}-menu-inline,
      .@{ant-prefix}-menu-vertical,
      .@{ant-prefix}-menu-vertical-left {
        border-right: 1px solid transparent;
      }
    }
  }

  &-drag-bar {
    position: absolute;
    top: 50px;
    right: -1px;
    width: 1px;
    height: calc(100% - 50px);
    cursor: ew-resize;
    background: #f8f8f9;
    border-top: none;
    border-bottom: none;
    box-shadow: 0 0 4px 0 rgba(28, 36, 56, 0.15);
  }
}
</style>
