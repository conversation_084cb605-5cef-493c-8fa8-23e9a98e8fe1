<template>
  <div v-if="getMenuFixed && !getIsMobile" :style="getHiddenDomStyle" v-show="showClassSideBarRef"></div>
  <Sider v-show="showClassSideBarRef" ref="sideRef" breakpoint="lg" collapsible theme="light" :class="getSiderClass"
    :width="getMenuWidth" :collapsed="getCollapsed" :collapsedWidth="getCollapsedWidth" @breakpoint="onBreakpointChange"
    v-bind="getTriggerAttr">
    <template #trigger v-if="getShowTrigger">
      <LayoutTrigger />
    </template>
    <LayoutMenu :menuMode="getMode" :splitType="getSplitType" />
    <DragBar ref="dragBarRef" />
  </Sider>
</template>
<script lang="ts">
import { computed, defineComponent, ref, unref, CSSProperties } from 'vue';

import { Layout } from '@geega-ui-plus/ant-design-vue';
import LayoutMenu from '../menu/index.vue';
import LayoutTrigger from '/@/layouts/default/trigger/index.vue';

import { MenuModeEnum, MenuSplitTyeEnum } from '/@/enums/menuEnum';

import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useTrigger, useDragLine, useSiderEvent } from './useLayoutSider';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';

import DragBar from './DragBar.vue';
export default defineComponent({
  name: 'LayoutSideBar',
  components: { Sider: Layout.Sider, LayoutMenu, DragBar, LayoutTrigger },
  setup() {
    const dragBarRef = ref<ElRef>(null);
    const sideRef = ref<ElRef>(null);

    const {
      getCollapsed,
      getMenuWidth,
      getSplit,
      getRealWidth,
      getMenuHidden,
      getMenuFixed,
      getIsMixMode,
    } = useMenuSetting();

    const { prefixCls } = useDesign('layout-sideBar');

    const { getIsMobile } = useAppInject();

    const { getTriggerAttr, getShowTrigger } = useTrigger(getIsMobile);

    useDragLine(sideRef, dragBarRef);

    const { getCollapsedWidth, onBreakpointChange } = useSiderEvent();

    const getMode = computed(() => {
      return unref(getSplit) ? MenuModeEnum.INLINE : null;
    });

    const getSplitType = computed(() => {
      return unref(getSplit) ? MenuSplitTyeEnum.LEFT : MenuSplitTyeEnum.NONE;
    });

    const showClassSideBarRef = computed(() => {
      return unref(getSplit) ? !unref(getMenuHidden) : true;
    });

    const getSiderClass = computed(() => {
      return [
        prefixCls,
        {
          [`${prefixCls}--fixed`]: unref(getMenuFixed),
          [`${prefixCls}--mix`]: unref(getIsMixMode) && !unref(getIsMobile),
        },
      ];
    });

    const getHiddenDomStyle = computed(
      (): CSSProperties => {
        const width = `${unref(getRealWidth)}px`;
        return {
          width: width,
          overflow: 'hidden',
          flex: `0 0 ${width}`,
          maxWidth: width,
          minWidth: width,
          transition: 'all 0.2s',
        };
      }
    );

    return {
      prefixCls,
      sideRef,
      dragBarRef,
      getIsMobile,
      getHiddenDomStyle,
      getSiderClass,
      getTriggerAttr,
      getCollapsedWidth,
      getMenuFixed,
      showClassSideBarRef,
      getMenuWidth,
      getCollapsed,
      onBreakpointChange,
      getMode,
      getSplitType,
      getShowTrigger,
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-layout-sideBar';

.@{prefix-cls} {
  z-index: @layout-sider-fixed-z-index;

  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
  }

  .@{ant-prefix}-layout-sider-zero-width-trigger {
    top: 40%;
    z-index: 10;
  }

  & .@{ant-prefix}-layout-sider-trigger {
    height: 36px;
    line-height: 36px;
  }
}
</style>
