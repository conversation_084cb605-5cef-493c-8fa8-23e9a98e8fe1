<script lang="ts">
  import { defineComponent, computed, unref } from 'vue';
  import { BackTop } from '@geega-ui-plus/ant-design-vue';

  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useDesign } from '/@/hooks/web/useDesign';

  import { SettingButtonPositionEnum } from '/@/enums/appEnum';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

  import { isRunMicroEnv } from '/@/utils/micro'

  export default defineComponent({
    name: 'LayoutFeatures',
    components: {
      BackTop,
      SettingDrawer: createAsyncComponent(() => import('/@/layouts/default/setting/index.vue')),
    },
    setup() {
      const {
        getUseOpenBackTop,
        getShowSettingButton,
        getSettingButtonPosition,
        getFullContent,
      } = useRootSetting();

      const { prefixCls } = useDesign('setting-drawer-fearure');
      const { getShowHeader } = useHeaderSetting();

      const getIsFixedSettingDrawer = computed(() => {
        if(isRunMicroEnv()){
             return false;
        }

        if (!unref(getShowSettingButton)) {
          return false;
        }
        const settingButtonPosition = unref(getSettingButtonPosition);

        if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
          return !unref(getShowHeader) || unref(getFullContent);
        }
        return settingButtonPosition === SettingButtonPositionEnum.FIXED;
      });

      return {
        getTarget: () => document.body,
        getUseOpenBackTop,
        getIsFixedSettingDrawer,
        prefixCls,
      };
    },
  });
</script>

<template>
  <BackTop v-if="getUseOpenBackTop" :target="getTarget" />
  <SettingDrawer v-if="getIsFixedSettingDrawer" :class="prefixCls" />
</template>

<style lang="less">
  @prefix-cls: ~'@{namespace}-setting-drawer-fearure';

  .@{prefix-cls} {
    position: absolute;
    top: 45%;
    right: 0;
    z-index: 10;
    display: flex;
    padding: 10px;
    color: @gplus-white-color;
    cursor: pointer;
    background: @primary-color;
    border-radius: 6px 0 0 6px;
    justify-content: center;
    align-items: center;

    svg {
      width: 1em;
      height: 1em;
    }
  }
</style>
