@prefix-cls: ~'@{namespace}-multiple-tabs';


.@{prefix-cls} {
  z-index: 10;
  height: @tab-multiple-height;
  line-height: @tab-multiple-height;
  background-color: @gplus-white-color;

  .title {
    position: relative;
  }

  .@{ant-prefix}-tabs-tab-active {
    background-color: @tab-multiple-bg-color !important;
  }

  .@{ant-prefix}-tabs-tab.@{ant-prefix}-tabs-tab-active
  .@{ant-prefix}-tabs-tab-btn {
      color: @primary-color;
      font-weight: 600;
  }

  .@{ant-prefix}-tabs.@{ant-prefix}-tabs-card {
    .@{ant-prefix}-tabs-nav {
      height: @tab-multiple-height;
      margin: 0 !important;
      box-shadow: none;
      border: 0;
      .@{ant-prefix}-tabs-nav-container {
        height: @tab-multiple-height;
      }

      .@{ant-prefix}-tabs-tab {
        height: @tab-multiple-height;
        line-height: @tab-multiple-height;
        color: @text-color;
        background-color: @gplus-white-color;
        &:hover {
          color: @primary-color;
          background-color: @tab-multiple-bg-color;
          .@{ant-prefix}-tabs-tab-remove {
            opacity: 1;
          }
        }

        .@{ant-prefix}-tabs-tab-remove {
          width: 8px;
          height: 36px;
          font-size: 16px;
          color: inherit;
          position: relative;
          top: -1px;
          margin-right: 0px;
          margin-left: 0px;
          &:hover {
            svg {
              width: 0.8em;
            }
          }
        }

        svg {
          fill: @text-color;
        }
      }

      .@{ant-prefix}-tabs-tab:not(.@{ant-prefix}-tabs-tab-active) {
        &:hover {
          color: @primary-color;
        }
      }

      .@{ant-prefix}-tabs-tab-active {
        position: relative;
        // background: @glus-ui-layout-main-bg-color;
        transition: none;
        .@{ant-prefix}-tabs-tab-remove {
          opacity: 1;
        }

        svg {
          width: 0.7em;
        }
      }
    }

    .@{ant-prefix}-tabs-nav > div:nth-child(1) {
      .@{ant-prefix}-tabs-tab {
        border-bottom: 0;
        margin-left: 0 !important;
      }
    }
  }

  .@{ant-prefix}-tabs-tab:not(.@{ant-prefix}-tabs-tab-active) {
    .anticon-close {
      font-size: 14px;
      svg {
        width: 0.8em;
      }
    }
  }

  .@{ant-prefix}-dropdown-trigger {
    display: inline-flex;
  }

  &-content {
    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 36px;
      height: @tab-multiple-height;
      line-height: @tab-multiple-height;
      color: @text-color-secondary;
      text-align: center;
      cursor: pointer;
      // border-left: 1px solid @border-color-base;

      &:hover {
        color: @text-color;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }

    &__extra-fold {
      span.app-iconify {
        vertical-align: -0.125em;
      }
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }
  }
}



.@{ant-prefix}-tabs-dropdown-menu {
  &-title-content {
    display: flex;
    align-items: center;

    .@{prefix-cls} {
      &-content__info {
        width: auto;
        margin-left: 0;
        line-height: 28px;
      }
    }
  }

  &-item-remove {
    margin-left: auto;
  }
}

.multiple-tabs__dropdown {
  .@{ant-prefix}-dropdown-content {
    width: 172px;
  }
}
