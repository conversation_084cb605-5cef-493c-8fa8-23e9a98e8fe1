<script lang="tsx">
import type { PropType, CSSProperties } from 'vue';

import { computed, defineComponent, unref, toRef, ref, watch } from 'vue';
import { BasicMenu, SimpleMenu, AppLogo, ScrollContainer } from '@geega-ui-plus/geega-ui';

import { MenuModeEnum, MenuSplitTyeEnum } from '/@/enums/menuEnum';

import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useGo } from '/@/hooks/web/usePage';
import { useSplitMenu } from './useLayoutMenu';
import { openWindow } from '/@/utils';
import { propTypes } from '/@/utils/propTypes';
import { isUrl } from '/@/utils/is';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';
import LayoutTrigger from '/@/layouts/default/trigger/index.vue';

export default defineComponent({
  name: 'LayoutMenu',
  components: {
    LayoutTrigger,
  },
  props: {
    theme: propTypes.oneOf(['light']),

    splitType: {
      type: Number as PropType<MenuSplitTyeEnum>,
      default: MenuSplitTyeEnum.NONE,
    },

    isHorizontal: propTypes.bool,
    // menu Mode
    menuMode: {
      type: [String] as PropType<Nullable<MenuModeEnum>>,
      default: '',
    },
  },
  setup(props) {
    const go = useGo();

    const {
      getMenuMode,
      getMenuType,
      getCollapsed,
      getCollapsedShowTitle,
      getAccordion,
      getIsHorizontal,
      getIsSidebarType,
      getMenuWidth,
    } = useMenuSetting();
    const { getShowLogo } = useRootSetting();

    const { prefixCls } = useDesign('layout-menu');

    const { menusRef } = useSplitMenu(toRef(props, 'splitType'));

    const { getIsMobile } = useAppInject();

    const getComputedMenuMode = computed(() =>
      unref(getIsMobile) ? MenuModeEnum.INLINE : props.menuMode || unref(getMenuMode)
    );
    const getIsShowLogo = computed(() => unref(getShowLogo) && unref(getIsSidebarType));

    const getUseScroll = computed(() => {
      return (
        !unref(getIsHorizontal) &&
        (unref(getIsSidebarType) ||
          props.splitType === MenuSplitTyeEnum.LEFT ||
          props.splitType === MenuSplitTyeEnum.NONE)
      );
    });

    const getWrapperStyle = computed(
      (): CSSProperties => {
        return {
          height: `calc(100% - ${unref(getIsShowLogo) ? '48px' : '0px'})`,
        };
      }
    );

    const getLogoClass = computed(() => {
      return [
        `${prefixCls}-logo`,
        {
          [`${prefixCls}--mobile`]: unref(getIsMobile),
        },
      ];
    });

    const getTriggerMaxWidth = computed(() => {
      return unref(getMenuWidth) - 1;
    });

    /**
     * click menu
     * @param menu
     */

    function handleMenuClick(path: string) {
      go(path);
    }

    /**
     * before click menu
     * @param menu
     */
    async function beforeMenuClickFn(path: string) {
      if (!isUrl(path)) {
        return true;
      }
      openWindow(path);
      return false;
    }

    function renderHeader() {
      if (!unref(getIsShowLogo) && !unref(getIsMobile)) return null;
      return <AppLogo showTitle={!unref(getCollapsed)} class={unref(getLogoClass)} />;
    }

    const BasicMenuRef = ref(null);
    const SimpleMenuRef = ref(null);

    // 手动设置selectedKeys
    //  const { currentRoute } = useRouter();
    //  watch(currentRoute, () => {
    //   console.log('currentRoute',currentRoute.value.name=='list')
    //         // refresh active state
    //         SimpleMenuRef.value?.setActiveByPath('/demo/anttable')
    //   });

    function renderMenu() {
      const menus = unref(menusRef);
      // console.log(menus);
      if (!menus || !menus.length) return null;
      return !props.isHorizontal ? (
        <section>
          <SimpleMenu
            ref={SimpleMenuRef}
            beforeClickFn={beforeMenuClickFn}
            items={menus}
            accordion={unref(getAccordion)}
            collapse={unref(getCollapsed)}
            collapsedShowTitle={unref(getCollapsedShowTitle)}
            onMenuClick={handleMenuClick}
            showTipTitle={true}
          />
          <div
            class={unref(getCollapsed) ? prefixCls + '-trigger-collapsed' : prefixCls + '-trigger'}
            style={unref(getCollapsed) ? 'width:62px' : 'width:' + unref(getTriggerMaxWidth) + 'px'}
          >
            <LayoutTrigger />
          </div>
          <div class={prefixCls + '-menu-visible'}></div>
        </section>
      ) : (
        <div>
          <BasicMenu
            ref={BasicMenuRef}
            beforeClickFn={beforeMenuClickFn}
            isHorizontal={props.isHorizontal}
            type={unref(getMenuType)}
            collapsedShowTitle={unref(getCollapsedShowTitle)}
            showLogo={unref(getIsShowLogo)}
            mode={unref(getComputedMenuMode)}
            items={menus}
            accordion={unref(getAccordion)}
            onMenuClick={handleMenuClick}
          />
        </div>
      );
    }

    return () => {
      return (
        <>
          {renderHeader()}
          {unref(getUseScroll) ? (
            <ScrollContainer class="scroll-container-menu" style={unref(getWrapperStyle)}>
              {() => renderMenu()}
            </ScrollContainer>
          ) : (
            renderMenu()
          )}
        </>
      );
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-layout-menu';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.cddc-ant-tooltip-inner .cddc-web-simple-menu-sub-title {
  width: 100% !important;
  text-overflow: inherit;
}

.@{prefix-cls} {
  &-logo {
    height: @layout-header-height;
    padding: 10px 4px 10px 10px;

    img {
      width: @logo-width;
      height: @logo-width;
    }
  }

  &--mobile {
    .@{logo-prefix-cls} {
      &__title {
        opacity: 1;
      }
    }
  }

  &-trigger {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    height: 50px;
    cursor: pointer;
    background: #fff;

    .trigger-coll-ctx {
      display: flex;
      justify-content: center;
      position: relative;
      top: 50%;
      left: 0%;
      transform: translate(-32%, -50%);
    }
  }

  &-trigger-collapsed {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    height: 50px;
    cursor: pointer;
    background: #fff;

    .trigger-coll-ctx {
      display: flex;
      justify-content: center;
      position: relative;
      top: 50%;
      right: 50%;
      transform: translate(50%, -50%);
    }
  }

  &-menu-visible {
    width: 100%;
    height: 64px;
    overflow: visible;
  }
}

.@{logo-prefix-cls} {
  &__title {
    overflow: inherit;
  }
}

.scroll-container-menu {
  .scrollbar__view {
    padding: 0;
    margin: 0;
  }
}
</style>
