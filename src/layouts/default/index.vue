<template>
  <Layout :class="prefixCls">
    <LayoutFeatures />
    <Layout :class="layoutClass">
      <LayoutSideBar v-if="showSidebar" />
      <Layout :class="`${prefixCls}-main`">
        <LayoutMultipleHeader v-if="!isDashboard" />
        <LayoutContent />
        <LayoutFooter />
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts">
import { defineComponent, computed, unref } from 'vue';
import LayoutHeader from './header/index.vue';
import LayoutContent from './content/index.vue';
import LayoutSideBar from './sider/index.vue';
import LayoutMultipleHeader from './header/MultipleHeader.vue';
import { Layout } from '@geega-ui-plus/ant-design-vue';
import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useDesign } from '/@/hooks/web/useDesign';

import { useAppInject } from '/@/hooks/web/useAppInject';
import { isRunMicroEnv } from '/@/utils/micro';
import { permissionStore } from '/@/store/modules/permission';

export default defineComponent({
  name: 'DefaultLayout',
  components: {
    LayoutFeatures: createAsyncComponent(() => import('/@/layouts/default/feature/index.vue')),
    LayoutFooter: createAsyncComponent(() => import('/@/layouts/default/footer/index.vue')),
    LayoutHeader,
    LayoutContent,
    LayoutSideBar,
    LayoutMultipleHeader,
    Layout,
  },
  setup() {
    const { prefixCls } = useDesign('default-layout');

    const { getIsMobile } = useAppInject();

    const { getShowContent, getShowBread, getShowFullHeaderRef } = useHeaderSetting();
    const isDashboard = computed(() => permissionStore.getIsDashboardState);
    const { getShowSidebar, getIsMixSidebar } = useMenuSetting();

    const layoutClass = computed(() => ({ 'ant-layout-has-sider': unref(getIsMixSidebar) }));

    const isMicroEnv = isRunMicroEnv();

    // 根据用户角色和其他条件判断是否显示侧边栏
    const showSidebar = computed(() => {
      const isNormalUser = permissionStore.getIsNormalUserState;
      return !isNormalUser && (unref(getShowSidebar) || unref(getIsMobile)) && !isMicroEnv;
    });

    return {
      getShowFullHeaderRef,
      getShowSidebar,
      prefixCls,
      getIsMobile,
      isDashboard,
      getIsMixSidebar,
      layoutClass,
      getShowContent,
      getShowBread,
      isMicroEnv,
      showSidebar,
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-default-layout';

.@{prefix-cls} {
  display: flex;
  width: 100%;
  min-height: 100%;
  flex-direction: column;

  > .@{ant-prefix}-layout {
    min-height: 100%;
  }

  &-main {
    background: @layout-content-bg-color;
  }
}
</style>
