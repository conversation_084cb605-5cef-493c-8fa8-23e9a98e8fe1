import { Menu } from '/@/router/types';
import { isArray } from '/@/utils/is';

export interface ICrumb {
  path: string;
  name: string;
  disabled: boolean;
}

/**
 * 查找父级
 * @param menus
 * @param url
 * @param parents
 * @returns {ICrumb} Array
 */
export function findParents(menus: any[], url: string, parents: any[] = []) {
  for (let i = 0; i < menus.length; i++) {
    const { name, path, children } = menus[i];
    const tmpPath = parents.concat();
    let disabled = false;
    if (children) disabled = true;
    // tmpPath.push({ name, path, disabled });
    if (path === url) {
      menus.forEach((j) => {
        if (url.indexOf(j.path) !== -1 && j.path !== url)
          tmpPath.push({ name: j.name, path: j.path, disabled: false });
      });
      tmpPath.push({ name, path, disabled });
      return tmpPath;
    } else {
      tmpPath.push({ name, path, disabled });
    }
    if (children) {
      const findResult = findParents(children, url, tmpPath);
      if (findResult) return findResult;
    }
  }
}

/**
 * 获取所有面包屑数据
 * @param menus
 * @param path
 * @returns {ICrumb} Array
 */
export function getCrumbs(menus: Menu[], path: string): ICrumb[] {
  const urls = path.split('/').filter((i) => i);
  const result: ICrumb[] = [];
  for (let i = 0; i < urls.length; i++) {
    const curUrls = urls.slice(0, i + 1);
    const url = '/' + curUrls.join('/');
    const findResult = getCrumbData(menus, url);
    if (i == urls.length - 1 && findResult) findResult.disabled = true;
    if (findResult) result.push(findResult);
  }
  return result;
}
/**
 * 获取单个面包屑数据
 * @param menus
 * @param url
 * @returns {ICrumb}
 */
export function getCrumbData(menus: Menu[], url: string): ICrumb | void {
  for (let i = 0; i < menus.length; i++) {
    const { path, name, children } = menus[i];
    if (path === url) {
      let disabled = false;
      if (children) disabled = true;
      return { name, path, disabled };
    } else if (children && isArray(children)) {
      const result = getCrumbData(children, url);
      if (result) return result;
    }
  }
}
