<template>
  <div :class="prefixCls">
    <span
      v-for="(menu, index) in crumbs"
      :key="menu.path"
      :class="index === 0 ? 'first-crumb' : ''"
    >
      <span v-if="menu.disabled" class="text">{{ t(menu.name) }}</span>
      <router-link
        v-if="!menu.disabled"
        :to="menu.path"
        :class="['text', !menu.disabled ? 'active' : '']"
        >{{ t(menu.name) }}</router-link
      >
      <span class="m-r">{{ index !== crumbs.length - 1 ? '/' : '' }}</span>
    </span>
  </div>
</template>
<script lang="ts">
  import { useStore } from 'vuex';
  import { appStore } from '/@/store/modules/app';
  import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useRoute } from 'vue-router';
  import { getCrumbs, ICrumb } from './data';
  import { asyncRoutes } from '/@/router/routes';
  import { PermissionModeEnum } from '/@/enums/appEnum';
  import { transformRouteToMenu } from '/@/router/helper/menuHelper';
  import projectSetting from '/@/settings/projectSetting';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  export default defineComponent({
    name: 'LayoutBreadCrumbs',
    setup() {
      const state = reactive({
        crumbs: [] as ICrumb[],
      });
      const { permissionMode = projectSetting.permissionMode } = appStore.getProjectConfig;
      const { prefixCls } = useDesign('bread-crumbs');
      const { getters } = useStore();
      const route = useRoute();
      const menus =
        permissionMode === PermissionModeEnum.ROLE
          ? transformRouteToMenu(asyncRoutes)
          : getters['app-permission/getBackMenuListState'];
      onMounted(() => {
        state.crumbs = getCrumbs(menus, route.path);
      });
      watch(
        () => route.path,
        (newPath, oldPath) => {
          if (newPath !== oldPath) state.crumbs = getCrumbs(menus, newPath);
        }
      );
      return {
        ...toRefs(state),
        prefixCls,
        t,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-bread-crumbs';
  .@{prefix-cls} {
    margin: 0 0 0 14px;
    font-size: 14px;
    line-height: 1.1;
    border-radius: 0;
    position: relative;
    padding: 8px 0;
    background: @gplus-white-color;

    > span {
      color: #666;
    }

    > span:first-child .text {
      margin-left: 0;

      &::before {
        display: inline-block;
        width: 3px;
        height: 12px;
        margin-right: 8px;
        position: absolute;
        top: 50%;
        transform: translateY(-54%);
        background: @primary-color;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
        left: -7px;
        content: ' ';
      }
    }

    > span:last-child .text {
      color: #333;
    }

    .text {
      margin: 0 8px;
      color: #999;
    }

    .active:hover {
      color: #333;
    }
  }
</style>
