<template>
  <div @click.stop="toggleCollapsed" class="trigger-coll-ctx">
    <SvgIcon name="g-icon_regular_right" size="20" v-if="getCollapsed" />
    <SvgIcon name="g-icon_regular_left" size="20" v-else />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { SvgIcon } from '@geega-ui-plus/geega-ui';

  export default defineComponent({
    name: 'SiderTrigger',
    components: { SvgIcon },
    setup() {
      const { getCollapsed, toggleCollapsed } = useMenuSetting();

      return { getCollapsed, toggleCollapsed };
    },
  });
</script>

<style lang="less">
  .trigger-coll-ctx {
    svg {
      cursor: pointer;
      background: #f3f3f3;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }
</style>
