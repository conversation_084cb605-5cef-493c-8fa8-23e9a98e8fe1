<template>
  <Header :class="getHeaderClass">
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <!-- logo -->
      <AppLogo
        v-if="isNormalUser || getShowHeaderLogo || getIsMobile"
        :class="[`${prefixCls}-logo`, isNormalUser ? 'normal' : '']"
        :style="getLogoWidth"
      />
      <!-- <LayoutBreadcrumb v-if="getShowContent && getShowBread" /> -->
    </div>
    <!-- left end -->

    <!-- menu start -->
    <div :class="`${prefixCls}-menu`" v-if="getShowTopMenu && !getIsMobile">
      <LayoutMenu :isHorizontal="true" :splitType="getSplitType" :menuMode="getMenuMode" />
    </div>
    <!-- menu-end -->

    <!-- action  -->
    <div :class="`${prefixCls}-action`">
      <AppSearch v-if="getShowSearch" :class="`${prefixCls}-action__item `" @openPage="openPage" />

      <!-- <ErrorAction v-if="getUseErrorHandle" :class="`${prefixCls}-action__item error-action`" /> -->

      <!-- <Notify v-if="getShowNotice" :class="`${prefixCls}-action__item notify-item`" /> -->

      <FullScreen v-if="getShowFullScreen" :class="`${prefixCls}-action__item fullscreen-item`" />

      <AppLocalePicker
        v-if="getShowLang"
        :reload="true"
        :showText="false"
        :class="`${prefixCls}-action__item`"
      />

      <UserDropDown />

      <SettingDrawer v-if="getShowSetting" :class="`${prefixCls}-action__item`" />
    </div>
  </Header>
</template>
<script lang="ts">
import { defineComponent, unref, computed } from 'vue';

import { propTypes } from '/@/utils/propTypes';

import { Layout } from '@geega-ui-plus/ant-design-vue';
import { AppLogo, AppSearch, AppLocalePicker } from '@geega-ui-plus/geega-ui';
import LayoutMenu from '../menu/index.vue';
import LayoutTrigger from '../trigger/index.vue';

import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';

import { MenuModeEnum, MenuSplitTyeEnum } from '/@/enums/menuEnum';
import { SettingButtonPositionEnum } from '/@/enums/appEnum';

import { UserDropDown, LayoutBreadcrumb, FullScreen, Notify, ErrorAction } from './components';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';

import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
import { useLocale } from '/@/locales/useLocale';

import { useGo } from '/@/hooks/web/usePage';
import { permissionStore } from '/@/store/modules/permission';

// import { Ping } from '/@/utils/ping';

export default defineComponent({
  name: 'LayoutHeader',
  components: {
    Header: Layout.Header,
    AppLogo,
    LayoutTrigger,
    LayoutBreadcrumb,
    LayoutMenu,
    UserDropDown,
    AppLocalePicker,
    FullScreen,
    Notify,
    AppSearch,
    ErrorAction,
    SettingDrawer: createAsyncComponent(() => import('/@/layouts/default/setting/index.vue'), {
      loading: true,
    }),
  },
  props: {
    fixed: propTypes.bool,
  },
  setup(props) {
    const { prefixCls } = useDesign('layout-header');
    const go = useGo();
    const {
      getShowTopMenu,
      getShowHeaderTrigger,
      getSplit,
      getIsMixMode,
      getMenuWidth,
      getIsMixSidebar,
    } = useMenuSetting();
    const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();

    const {
      getUseLockPage,
      getShowFullScreen,
      getShowNotice,
      getShowContent,
      getShowBread,
      getShowHeaderLogo,
      getShowHeader,
      getShowSearch,
      getShowLang,
    } = useHeaderSetting();

    const { getShowLocalePicker } = useLocale();
    const isNormalUser = computed(() => permissionStore.getIsNormalUserState);
    const { getIsMobile } = useAppInject();

    const getHeaderClass = computed(() => {
      return [
        prefixCls,
        {
          [`${prefixCls}--fixed`]: props.fixed,
          [`${prefixCls}--mobile`]: unref(getIsMobile),
          [`${prefixCls}--light`]: true,
        },
      ];
    });

    const getShowSetting = computed(() => {
      if (!unref(getShowSettingButton)) {
        return false;
      }
      const settingButtonPosition = unref(getSettingButtonPosition);

      if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
        return unref(getShowHeader);
      }
      return settingButtonPosition === SettingButtonPositionEnum.HEADER;
    });

    const getLogoWidth = computed(() => {
      if (!unref(getIsMixMode) || unref(getIsMobile)) {
        return {};
      }

      const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);

      return { width: `${width}px` };
    });

    const getSplitType = computed(() => {
      return unref(getSplit) ? MenuSplitTyeEnum.TOP : MenuSplitTyeEnum.NONE;
    });

    const getMenuMode = computed(() => {
      return unref(getSplit) ? MenuModeEnum.HORIZONTAL : null;
    });

    const openPage = (path) => {
      go(path);
    };

    // 检查网络
    // const momPing = new Ping({
    //   timeout:3000
    // });
    // setInterval(()=>{
    //   momPing.ping("http://mom-b.caas-cloud-test.geega.com", (err, data) => {
    //       if (err) {
    //         console.log("error loading resource", err);
    //       }
    //       console.log(data)
    //   });
    // },2000)

    return {
      prefixCls,
      getHeaderClass,
      getShowHeaderLogo,
      isNormalUser,
      getShowHeaderTrigger,
      getIsMobile,
      getShowBread,
      getShowContent,
      getSplitType,
      getSplit,
      getMenuMode,
      getShowTopMenu,
      getShowLocalePicker,
      getShowFullScreen,
      getShowNotice,
      getUseLockPage,
      getUseErrorHandle,
      getLogoWidth,
      getIsMixSidebar,
      getShowSettingButton,
      getShowSetting,
      getShowSearch,
      getShowLang,
      openPage,
    };
  },
});
</script>
<style lang="less">
@import './index.less';
</style>
