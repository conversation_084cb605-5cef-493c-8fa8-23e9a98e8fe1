<template>
  <Dropdown placement="bottomLeft" :overlayClassName="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls]" class="flex">
      <img :class="`${prefixCls}__header`" :src="headerImg" />
      <span :class="`${prefixCls}__info md:block`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo.name || 'geega-user' }}
        </span>
      </span>
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem
          key="profile"
          itemKey="profile"
          :text="t('layout.header.dropdownItemUserInfo')"
          icon="ion:person-outline"
        >
          <template #suffix v-if="currUserInfo && currUserInfo.cardNoList && currUserInfo.cardNoList.length > 0">
            <check-circle-outlined color="#00996b" class="ml-1" />
          </template>
        </MenuItem>
        <MenuItem
          key="face"
          itemKey="face"
          :text="t('layout.header.dropdownItemFace')"
          icon="ion:camera-outline"
        >
          <template #suffix v-if="currUserInfo && currUserInfo.faceId">
            <check-circle-outlined color="#00996b" class="ml-1" />
          </template>
        </MenuItem>
        <MenuItem
          key="logout"
          itemKey="logout"
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="ion:power-outline"
        />
      </Menu>
    </template>
  </Dropdown>
  <LockAction @register="register" />
  <CardBindModal ref="cardBindModalRef" :userId="currentUserId" @success="handleBindSuccess" />
</template>
<script lang="ts">
// components
import { Dropdown, Menu } from '@geega-ui-plus/ant-design-vue';
import type { MenuInfo } from '@geega-ui-plus/ant-design-vue/es/menu/src/interface';
import { CheckCircleOutlined } from '@geega-ui-plus/icons-vue';
import { defineComponent, computed, ref } from 'vue';

import { DOC_URL } from '/@/settings/siteSetting';

import { userStore } from '/@/store/modules/user';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useI18n } from '/@/hooks/web/useI18n';
import { useDesign } from '/@/hooks/web/useDesign';
import { useModal } from '@geega-ui-plus/geega-ui';
import { useMessage } from '/@/hooks/web/useMessage';
import CardBindModal from '/@/components/CardBindModal/index.vue';
import { uploadApi } from '/@/components/Uploader/utils';

import headerImg from '/@/assets/images/header.svg';
import { propTypes } from '/@/utils/propTypes';
import { openWindow } from '/@/utils';

import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
import {
  DeviceBindingFacePost,
  DeviceCaptureFaceInfo,
  V1LocationHomeCurrentIp,
  V1LocationHomeCurrentUser,
} from '/@/api/cddc.req';
import { useIsStation } from '/@/composables/useIsStation';
import { createLocalStorage } from '/@/utils/cache';

export default defineComponent({
  name: 'UserDropdown',
  components: {
    Dropdown,
    Menu,
    CheckCircleOutlined,
    MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
    LockAction: createAsyncComponent(() => import('../lock/LockModal.vue')),
    CardBindModal,
  },
  props: {
    theme: propTypes.oneOf(['light']),
  },
  setup() {
    const { prefixCls } = useDesign('header-user-dropdown');
    const { t } = useI18n();
    const { getShowDoc } = useHeaderSetting();
    const { createMessage } = useMessage();
    const cardBindModalRef = ref();
    const currentUserId = ref('');
    const cardNoList = ref<string[]>([]);
    const currUserInfo = ref();

    const getUserInfo = computed(() => {
      return userStore.getUserFullInfo || {};
    });

    // 是否为工位端
    const isStation = useIsStation();

    // 获取当前用户ID
    async function fetchCurrentUserId() {
      try {
        const result = await V1LocationHomeCurrentUser();
        if (result?.id) {
          currentUserId.value = result.id;
          cardNoList.value = result.cardNoList || [];
          currUserInfo.value = result;
          const ls = createLocalStorage();
          ls.set('userId', result.id);
        } else {
          // 如果没有获取到用户信息，确保 currUserInfo 为空对象而不是 undefined
          currUserInfo.value = null;
        }
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        // 发生错误时也要确保 currUserInfo 不是 undefined
        currUserInfo.value = null;
      }
    }

    // 初始化时获取用户ID
    fetchCurrentUserId();

    const [register, { openModal }] = useModal();

    function handleLock() {
      openModal(true);
    }

    // 处理个人信息
    function handleProfile() {
      if (!currentUserId.value) {
        createMessage.error('用户信息不存在');
        return;
      }
      // 检查已绑定卡数量
      if (cardNoList.value?.length >= 2) {
        createMessage.warning('最多允许绑定2张卡号，请前往解绑');
        return;
      }
      cardBindModalRef.value?.open();
    }
    // 人脸采集
    const captureFace = async () => {
      try {
        // 检查是否已绑定人脸
        if (currUserInfo.value?.faceId) {
          createMessage.warning('人脸已绑定，无需重复采集');
          return;
        }
        // 1. 获取设备IP
        const deviceIp = await V1LocationHomeCurrentIp();

        if (!deviceIp) {
          createMessage.error('无法获取设备IP');
          return;
        }

        // 2. 捕获人脸信息
        const captureResponse = await DeviceCaptureFaceInfo({
          deviceIp,
        });

        if (!captureResponse) {
          createMessage.error('人脸采集失败');
          return;
        }

        // 3. 转换Base64为File对象
        const base64Image = captureResponse as string;
        const byteCharacters = atob(base64Image.split(',')[1] || base64Image);
        const byteArrays: number[] = [];

        for (let i = 0; i < byteCharacters.length; i++) {
          byteArrays.push(byteCharacters.charCodeAt(i));
        }

        const byteArray = new Uint8Array(byteArrays);
        const blob = new Blob([byteArray], { type: 'image/jpeg' });
        const file = new File([blob], `face_${currentUserId.value}.jpg`, { type: 'image/jpeg' });

        // 4. 上传图片
        const uploadResult = await uploadApi(file);

        if (!uploadResult || !uploadResult.url) {
          createMessage.error('人脸图片上传失败');
          return;
        }

        // 5. 绑定人脸
        await DeviceBindingFacePost({
          employeeId: currentUserId.value,
          faceUrl: uploadResult.url,
          faceId: uploadResult.id,
        });

        createMessage.success('人脸采集并绑定成功');
      } catch (error) {
        createMessage.error('人脸采集过程发生错误：' + (error as Error).message);
      }
    };

    // 绑定成功回调
    function handleBindSuccess() {
      // createMessage.success('绑定成功');
      fetchCurrentUserId(); // 刷新用户信息
    }

    //  login out
    function handleLoginOut() {
      userStore.gucLogout();
    }

    // open doc
    function openDoc() {
      openWindow(DOC_URL);
    }

    function handleMenuClick(info: MenuInfo) {
      const key = info.key as string;
      switch (key) {
        case 'logout':
          handleLoginOut();
          break;
        case 'doc':
          openDoc();
          break;
        case 'lock':
          handleLock();
          break;
        case 'profile':
          handleProfile();
          break;
        case 'face':
          captureFace();
          break;
      }
    }

    return {
      prefixCls,
      t,
      isStation,
      getUserInfo,
      handleMenuClick,
      getShowDoc,
      headerImg,
      register,
      cardBindModalRef,
      handleBindSuccess,
      currentUserId,
      currUserInfo,
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-header-user-dropdown';

.@{prefix-cls} {
  height: @header-content-height;
  padding: 0 0 0 10px;
  padding-right: 10px;
  overflow: hidden;
  font-size: 12px;
  cursor: pointer;
  align-items: center;

  img {
    width: 22px;
    height: 22px;
    margin-right: 4px;
  }

  &__header {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  &__name {
    font-size: 14px;
    color: @gplus-white-color;
  }

  &--light {
    .@{prefix-cls}__name {
      color: @text-color;
    }

    .@{prefix-cls}__desc {
      color: @header-light-desc-color;
    }
  }

  &-dropdown-overlay {
    .@{ant-prefix}-dropdown-menu-item {
      min-width: 160px;
    }
  }
}
</style>
