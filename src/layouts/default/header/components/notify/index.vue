<template>
  <div :class="prefixCls">
    <Popover title="" trigger="click" :overlayClassName="`${prefixCls}__overlay`">
      <Badge :count="count" dot :numberStyle="numberStyle">
        <Icon class="coder-icon" icon="g-ic-line-earbell" />
      </Badge>
      <template #content>
        <Tabs>
          <template v-for="item in tabListData" :key="item.key">
            <TabPane>
              <template #tab>
                {{ item.name }}
                <span v-if="item.list.length !== 0">({{ item.list.length }})</span>
              </template>
              <NoticeList :list="item.list" />
            </TabPane>
          </template>
        </Tabs>
      </template>
    </Popover>
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Popover, Tabs, Badge } from '@geega-ui-plus/ant-design-vue';
  import { tabListData } from './data';
  import NoticeList from './NoticeList.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { Icon } from '@geega-ui-plus/geega-ui';

  export default defineComponent({
    components: { Popover, Tabs, TabPane: Tabs.TabPane, Badge, NoticeList, Icon },
    setup() {
      const { prefixCls } = useDesign('header-notify');

      let count = 0;

      for (let i = 0; i < tabListData.length; i++) {
        count += tabListData[i].list.length;
      }

      return {
        prefixCls,
        tabListData,
        count,
        numberStyle: {},
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      max-width: 360px;
    }

    .@{ant-prefix}-tabs-content {
      width: 300px;
    }

    .@{ant-prefix}-badge {
      font-size: 18px;

      .@{ant-prefix}-badge-multiple-words {
        padding: 0 4px;
      }

      svg {
        width: 0.9em;
      }
    }

    .@{ant-prefix}-badge-dot {
      width: 8px;
      height: 8px;
      background: #f76f5d;
    }
  }
</style>
