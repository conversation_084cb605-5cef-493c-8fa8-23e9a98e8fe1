
  import i18nVue from '@geega/i18n-vue';export interface ListItem {
  id: string;
  avatar: string;
  title: string;
  datetime: string;
  type: string;
  read?: boolean;
  description: string;
  clickClose?: boolean;
  extra?: string;
  color?: string;
}

export interface TabItem {
  key: string;
  name: string;
  list: ListItem[];
  unreadlist?: ListItem[];
}

export const tabListData: TabItem[] = [
  {
    key: '1',
    name: i18nVue.t('layouts.default.header.components.notify.data.22c17472', { defaultValue: '通知' }),
    list: [
      {
        id: '000000001',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
        title: i18nVue.t('layouts.default.header.components.notify.data.1d4a8f0e', { defaultValue: '你收到了 14 份新周报' }),
        description: '',
        datetime: '2017-08-09',
        type: '1',
      },
      {
        id: '000000002',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
        title: i18nVue.t('layouts.default.header.components.notify.data.b36bb6df', { defaultValue: '你推荐的 曲妮妮 已通过第三轮面试' }),
        description: '',
        datetime: '2017-08-08',
        type: '1',
      },
      {
        id: '000000003',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png',
        title: i18nVue.t('layouts.default.header.components.notify.data.1f1b2cea', { defaultValue: '这种模板可以区分多种通知类型' }),
        description: '',
        datetime: '2017-08-07',
        // read: true,
        type: '1',
      },
      {
        id: '000000004',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png',
        title: i18nVue.t('layouts.default.header.components.notify.data.cd664804', { defaultValue: '左侧图标用于区分不同的类型' }),
        description: '',
        datetime: '2017-08-07',
        type: '1',
      },
    ],
  },
  {
    key: '2',
    name: i18nVue.t('layouts.default.header.components.notify.data.a75f2bb4', { defaultValue: '消息' }),
    list: [
      {
        id: '000000006',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg',
        title: i18nVue.t('layouts.default.header.components.notify.data.1b69c8bb', { defaultValue: '曲丽丽 评论了你' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.8586007b', { defaultValue: '描述信息描述信息描述信息' }),
        datetime: '2017-08-07',
        type: '2',
        clickClose: true,
      },
      {
        id: '000000007',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg',
        title: i18nVue.t('layouts.default.header.components.notify.data.f29caf2a', { defaultValue: '朱偏右 回复了你' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.01743473', { defaultValue: '这种模板用于提醒谁与你发生了互动' }),
        datetime: '2017-08-07',
        type: '2',
        clickClose: true,
      },
      {
        id: '000000008',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg',
        title: i18nVue.t('layouts.default.header.components.notify.data.bb73a5ab', { defaultValue: '标题' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.01743473', { defaultValue: '这种模板用于提醒谁与你发生了互动' }),
        datetime: '2017-08-07',
        type: '2',
        clickClose: true,
      },
    ],
  },
  {
    key: '3',
    name: i18nVue.t('layouts.default.header.components.notify.data.e80a6e01', { defaultValue: '待办' }),
    list: [
      {
        id: '000000009',
        avatar: '',
        title: i18nVue.t('layouts.default.header.components.notify.data.20239f22', { defaultValue: '任务名称' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.86772cbd', { defaultValue: '任务需要在 2017-01-12 20:00 前启动' }),
        datetime: '',
        extra: i18nVue.t('layouts.default.header.components.notify.data.2aca173f', { defaultValue: '未开始' }),
        color: '',
        type: '3',
      },
      {
        id: '000000010',
        avatar: '',
        title: i18nVue.t('layouts.default.header.components.notify.data.1790288a', { defaultValue: '第三方紧急代码变更' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.2e7262b9', { defaultValue: '冠霖 需在 2017-01-07 前完成代码变更任务' }),
        datetime: '',
        extra: i18nVue.t('layouts.default.header.components.notify.data.75f54885', { defaultValue: '马上到期' }),
        color: 'red',
        type: '3',
      },
      {
        id: '000000011',
        avatar: '',
        title: i18nVue.t('layouts.default.header.components.notify.data.15f5c522', { defaultValue: '信息安全考试' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.44371a04', { defaultValue: '指派竹尔于 2017-01-09 前完成更新并发布' }),
        datetime: '',
        extra: i18nVue.t('layouts.default.header.components.notify.data.a47e4b3b', { defaultValue: '已耗时 8 天' }),
        color: 'gold',
        type: '3',
      },
      {
        id: '000000012',
        avatar: '',
        title: i18nVue.t('layouts.default.header.components.notify.data.d5020d63', { defaultValue: 'ABCD 版本发布' }),
        description: i18nVue.t('layouts.default.header.components.notify.data.44371a04', { defaultValue: '指派竹尔于 2017-01-09 前完成更新并发布' }),
        datetime: '',
        extra: i18nVue.t('layouts.default.header.components.notify.data.ff276dc5', { defaultValue: '进行中' }),
        color: 'blue',
        type: '3',
      },
    ],
  },
];
