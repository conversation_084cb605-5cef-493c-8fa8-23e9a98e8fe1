@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.@{header-prefix-cls} {
  display: flex;
  height: @layout-header-height;
  padding: 0;
  margin-left: -1px;
  line-height: @layout-header-height;
  align-items: center;
  justify-content: space-between;
  padding-right: 4px;

  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: @layout-header-fixed-z-index;
    width: 100%;
  }

  .@{ant-prefix}-menu-horizontal {
    background: @layout-header-height;
    color: @gplus-white-color;
    height: @layout-header-height + 1;
  }

  &-logo {
    height: @layout-header-height;
    color: @gplus-white-color !important;
    min-width: 192px;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }

    &-__title {
      font-size: 16px;
      color: @gplus-white-color !important;
    }
  }

  &-left {
    display: flex;
    height: 100%;
    align-items: center;

    .@{header-trigger-prefix-cls} {
      display: flex;
      height: 100%;
      padding: 1px 10px 0 10px;
      cursor: pointer;
      align-items: center;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }
    }
  }

  &-menu {
    height: 100%;
    min-width: 0;
    flex: 1;
    align-items: center;
  }

  &-action {
    display: flex;
    // min-width: 180px;
    // padding-right: 12px;
    align-items: center;

    &__item {
      display: flex;
      height: @header-content-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;
      align-items: center;
      color: @gplus-white-color !important;

      .@{ant-prefix}-badge {
        height: @header-content-height;
        padding: 0 8px;
        line-height: @header-content-height;
      }

      .@{ant-prefix}-badge-dot {
        top: 24px;
        right: 10px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    background: @layout-header-bg;
    color: @gplus-white-color;

    .@{header-prefix-cls}-logo {
      color: @text-color;

      &:hover {
        background: @header-light-bg-hover-color;
      }
      &.normal {
        &:hover {
          background: none;
        }
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @text-color;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px;
        }
      }

      &-icon,
      span[role='img'] {
        color: @gplus-white-color;
      }
    }
  }
}
