<template>
  <div :style="getPlaceholderDomStyle" :class="`${prefixCls}` + '-place-hoolder'" v-if="getIsShowPlaceholderDom"></div>
  <div :style="getWrapStyle" :class="getClass">
    <LayoutHeader v-if="getShowInsetHeaderRef" />
    <BreadCrumbs v-if="getShowContent && getShowBread" />
    <MultipleTabs v-if="getShowTabs && !isNormalUser" />
  </div>
</template>
<script lang="ts">
import { defineComponent, unref, computed, CSSProperties } from 'vue';

import LayoutHeader from './index.vue';
import MultipleTabs from '../tabs/index.vue';

import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useFullContent } from '/@/hooks/web/useFullContent';
import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';
import { headerHeightRef } from '../content/useContentViewHeight';
import BreadCrumbs from '../bread-crumbs/index.vue';
import { isRunMicroEnv } from '/@/utils/micro';
import { permissionStore } from '/@/store/modules/permission';


const HEADER_HEIGHT = 50;
const BREAD_HEIGHT = 36;
const TABS_HEIGHT = 40;
export default defineComponent({
  name: 'LayoutMultipleHeader',
  components: { LayoutHeader, MultipleTabs, BreadCrumbs },
  setup() {
    const { prefixCls } = useDesign('layout-multiple-header');

    const { getCalcContentWidth, getSplit } = useMenuSetting();
    const { getIsMobile } = useAppInject();
    const {
      getFixed,
      getShowInsetHeaderRef,
      getShowFullHeaderRef,
      getShowHeader,
      getShowContent,
      getShowBread
    } = useHeaderSetting();

    const { getFullContent } = useFullContent();

    const { getShowMultipleTab } = useMultipleTabSetting();

    const isNormalUser = computed(() => permissionStore.getIsNormalUserState);

    const getShowTabs = computed(() => {
      return unref(getShowMultipleTab) && !unref(getFullContent) && !isRunMicroEnv();
    });

    const getIsShowPlaceholderDom = computed(() => {
      return unref(getFixed) || unref(getShowFullHeaderRef);
    });

    const getWrapStyle = computed(
      (): CSSProperties => {
        const style: CSSProperties = {};
        if (unref(getFixed)) {
          style.width = unref(getIsMobile) || unref(isNormalUser) ? '100%' : unref(getCalcContentWidth);
        }
        if (unref(getShowFullHeaderRef)) {
          style.top = `${HEADER_HEIGHT}px`;
        }
        return style;
      }
    );

    const getIsFixed = computed(() => {
      return unref(getFixed) || unref(getShowFullHeaderRef);
    });

    const getPlaceholderDomStyle = computed(
      (): CSSProperties => {
        // 如果在乾坤内 则只保留面包屑的高度--可以根据项目实际情况调整头部高度
        if (isRunMicroEnv()) {
          return {
            height: '0px',
          };
        }

        let height = 0;
        if (
          (unref(getShowFullHeaderRef) || !unref(getSplit)) &&
          unref(getShowHeader) &&
          !unref(getFullContent)
        ) {
          height += HEADER_HEIGHT;
        }

        // 只有管理员才显示tabs和面包屑的高度
        if (!unref(isNormalUser)) {
          if (unref(getShowMultipleTab) && !unref(getFullContent)) {
            height += TABS_HEIGHT;
          }

          if (unref(getShowContent) && unref(getShowBread)) {
            height += BREAD_HEIGHT;
          }
        }
        headerHeightRef.value = height;
        return {
          height: `${height}px`,
        };
      }
    );

    const getClass = computed(() => {
      return [
        prefixCls,
        {
          [`${prefixCls}--fixed`]: unref(getIsFixed),
        },
      ];
    });

    return {
      prefixCls,
      getClass,
      getIsShowPlaceholderDom,
      getPlaceholderDomStyle,
      getWrapStyle,
      getShowContent,
      getShowTabs,
      getShowBread,
      getShowInsetHeaderRef,
      isNormalUser,
    };
  },
});
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-layout-multiple-header';

@place-hoolder: ~'@{prefix-cls}-place-hoolder';

.@{place-hoolder} {
  border: 0;
}

.@{prefix-cls} {
  transition: width 0.2s;
  flex: 0 0 auto;

  &--fixed {
    position: fixed;
    top: 0;
    z-index: @multiple-tab-fixed-z-index;
    width: 100%;
  }

  &-place-hoolder {
    flex: 0 0 auto;
  }
}
</style>
