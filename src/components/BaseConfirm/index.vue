<template>
  <a-popconfirm
    v-model:visible="visible"
    :title="title"
    :ok-text="'确认'"
    cancel-text="取消"
    v-bind="$attrs"
    :getPopupContainer="() => getPopupContainer()"
    destroyPopupOnHide
    placement="topRight"
  >
    <template #[item]="data" v-for="item in Object.keys($slots)" :key="item">
      <slot :name="item" v-bind="data || {}"></slot>
    </template>
    <slot>
      <a-button type="link" danger :disabled="disabled">删除</a-button>
    </slot>
  </a-popconfirm>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { getPopupContainer } from '/@/utils';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: '确认删除？',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const visible = ref(false);

    function resizeClosePopconfirm() {
      visible.value = false;
    }

    onMounted(() => {
      window.addEventListener('resize', resizeClosePopconfirm);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', resizeClosePopconfirm);
    });

    return {
      visible,
      getPopupContainer,
    };
  },
});
</script>

<style lang="less">
.cddc-ant-popover-content .cddc-ant-popover-inner-content {
  padding: 16px;

  .cddc-ant-popover-message {
    padding: 0 0 8px;

    .anticon {
      top: 4.0005px;
    }
  }

  .cddc-ant-popover-buttons {
    margin-bottom: 0;
  }
}
</style>
