<script lang="ts" setup>
import {
  getObjectSize,
  BVHRaycastTool,
  useRendererContext,
  isMesh,
  RaycastEvent,
} from '../ThreeJs';
import { ModelFile } from './ModelFile';
import { watchImmediate } from '@vueuse/core';
import { Color, DoubleSide, Mesh, MeshStandardMaterial } from 'three';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { useDisposableStack } from '../ThreeJs/composables';
import { V1CommonFileUploadDownloadFileIdPost } from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';
import { useTweakPane } from '/@/composables/useTweakpane';

export interface ModelFileProps {
  fileId?: string;
  markedParts?: string[];
  modelCache?: Record<string, File>;
}

const props = defineProps<ModelFileProps>();
const emit = defineEmits<{
  selected: [event: RaycastEvent];
  beforeLoadingModel: [];
  loaded: [scene: ModelFile];
}>();

const renderParams = {
  mat: {
    normalColor: '#acacac',
    highlightColor: '#cc3030',
    markColor: '#25a825',
  },
};

const ctx = useRendererContext();

const disposableStack = useDisposableStack();

const modelFile = new ModelFile();
disposableStack.add(modelFile);

const modelMat = new MeshStandardMaterial({
  color: new Color(renderParams.mat.normalColor),
  metalness: 0.45,
  roughness: 0.35,
  side: DoubleSide,
});
disposableStack.add(modelMat);

const highlightModelMat = new MeshStandardMaterial({
  color: new Color(renderParams.mat.highlightColor),
  metalness: 0.45,
  roughness: 0.35,
  side: DoubleSide,
});
disposableStack.add(highlightModelMat);

const markModelMat = new MeshStandardMaterial({
  color: new Color(renderParams.mat.markColor),
  metalness: 0.45,
  roughness: 0.35,
  side: DoubleSide,
});
disposableStack.add(markModelMat);

const state = {
  selectedPart: null as Mesh | null | undefined,
};

const raycastTool = new BVHRaycastTool(ctx, { enabledEvents: ['click'], debounce: 100 });
disposableStack.add(raycastTool);

raycastTool.addEventListener('pointermove', (evt) => {
  const mesh = evt.mesh;

  const outlinePass = ctx.getData<OutlinePass>('outlinePass')!;

  outlinePass.selectedObjects = [mesh].filter((n) => n != null);
});

raycastTool.addEventListener('click', (evt) => {
  const mesh = evt.mesh;

  const oldSelectedPart = state.selectedPart;

  state.selectedPart = mesh;

  if (oldSelectedPart) {
    oldSelectedPart.material = getMaterial(oldSelectedPart);
  }

  if (mesh) {
    mesh.material = getMaterial(mesh);
  }

  emit('selected', evt);
});

watchImmediate(() => props.fileId, reloadModelFile);

watchImmediate(() => props.markedParts, updateModelPartMaterials);

useTweakPane({
  enable: false,
  setup(pane) {
    pane.title = 'Rendering Parameter';
    pane.expanded = true;

    pane.addBinding(renderParams.mat, 'normalColor').on('change', ({ value }) => {
      modelMat.color.set(value);
    });

    pane.addBinding(renderParams.mat, 'highlightColor').on('change', ({ value }) => {
      highlightModelMat.color.set(value);
    });

    pane.addBinding(renderParams.mat, 'markColor').on('change', ({ value }) => {
      markModelMat.color.set(value);
    });
  },
});

async function reloadModelFile() {
  const fileId = props.fileId;

  if (fileId) {
    clearOldModel();

    const url = await V1CommonFileUploadDownloadFileIdPost({ fileId });

    emit('beforeLoadingModel');

    try {
      const data = props.modelCache?.[fileId];
      const dataBuffer = await data?.arrayBuffer();
      await modelFile.loadModel(url, dataBuffer);
    } catch (error) {
      emit('loaded', modelFile);

      console.error(error);
      message.error('模型加载失败！');
      return;
    }

    updateModelPartMaterials();
    ctx.scene.add(modelFile);

    raycastTool.objects = [modelFile];

    const modelSize = getObjectSize(modelFile);
    modelFile.position.set(0, modelSize.y / 2, 0);

    const modelLength = modelSize.length() / 1.5;

    ctx.camera.position.set(modelLength, modelLength, modelLength);
    ctx.camera.lookAt(modelFile.position);

    emit('loaded', modelFile);
  } else {
    clearOldModel();
  }
}

function clearOldModel() {
  raycastTool.objects = [];

  modelFile.clear();
}

function updateModelPartMaterials() {
  state.selectedPart = null;

  modelFile.traverse((item) => {
    if (!isMesh(item)) {
      return;
    }

    item.material = getMaterial(item);
  });
}

function getMaterial(mesh: Mesh) {
  const shouldMark = props.markedParts?.includes(mesh.name);
  const shouldHighlight = state.selectedPart === mesh;
  if (shouldHighlight) {
    return highlightModelMat;
  }

  if (shouldMark) {
    return markModelMat;
  }

  return modelMat;
}
</script>

<template>
  <slot></slot>
</template>

<style lang="less" scoped></style>
