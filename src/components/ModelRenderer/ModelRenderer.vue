<script lang="ts" setup>
import {
  Color,
  DirectionalLight,
  DoubleSide,
  Fog,
  HemisphereLight,
  Mesh,
  PCFSoftShadowMap,
  PlaneGeometry,
  ShadowMaterial,
  Vector3,
} from 'three';
import { SimpleThree, ThreeContextProvider } from '../ThreeJs';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';
import DevInspector from './Devtools/DevInspector.vue';
import { shallowRef } from 'vue';

export interface ModelRendererProps {
  inspector?: boolean;
}

const props = defineProps<ModelRendererProps>();

const rendererCtx = shallowRef<SimpleThree>();

function setup(ctx: SimpleThree) {
  rendererCtx.value = ctx;
  setupPostProcessing(ctx);
  setupEnv(ctx);

  ctx.orbit.maxPolarAngle = Math.PI / 2;
  ctx.orbit.screenSpacePanning = false;

  ctx.camera.position.set(6, 6, 6);
  ctx.camera.lookAt(new Vector3());

  ctx.$r.shadowMap.enabled = true;
  ctx.$r.shadowMap.type = PCFSoftShadowMap;

  addShadowPlane(ctx);
}

function setupPostProcessing(ctx: SimpleThree) {
  {
    const outlinePass = new OutlinePass(ctx.size, ctx.scene, ctx.camera);
    ctx.composer.addPass(outlinePass);
    ctx.setData('outlinePass', outlinePass);

    outlinePass.edgeStrength = 3.0;
    outlinePass.edgeGlow = 0;
    outlinePass.edgeThickness = 1;
    outlinePass.pulsePeriod = 0;
    outlinePass.usePatternTexture = false;
    outlinePass.visibleEdgeColor.set('#00ff00');
    outlinePass.hiddenEdgeColor.set('#ff0000');
  }

  {
    ctx.composer.addPass(new OutputPass());
  }

  {
    const effectFXAA = new ShaderPass(FXAAShader);
    effectFXAA.uniforms['resolution'].value.set(1 / ctx.size.width, 1 / ctx.size.height);

    ctx.addEventListener('resize', (evt) => {
      effectFXAA.uniforms['resolution'].value.set(1 / evt.size.width, 1 / evt.size.height);
    });

    ctx.composer.addPass(effectFXAA);
  }
}

function addShadowPlane(ctx: SimpleThree) {
  const panel = new PlaneGeometry(2000, 2000);
  const mesh = new Mesh(
    panel,
    new ShadowMaterial({
      color: 0x000000,
      transparent: true,
      opacity: 0.1,
      side: DoubleSide,
    })
  );
  mesh.receiveShadow = true;

  panel.rotateX(-Math.PI / 2);
  ctx.scene.add(mesh);
}

function setupEnv(ctx: SimpleThree) {
  // setup env light
  const scene = ctx.scene;
  scene.background = new Color(0xe0e0e0);
  scene.fog = new Fog(0xe0e0e0, 10, 100);

  // scene.add(new AmbientLight(0xffffff, 2));

  const hemiLight = new HemisphereLight(0xffffff, 0x8d8d8d, 3);
  hemiLight.position.set(0, 10, 0);
  scene.add(hemiLight);

  const dirLight = new DirectionalLight(0xffffff, 3);
  dirLight.castShadow = true;
  dirLight.shadow.mapSize.setScalar(2048);
  dirLight.position.set(-10, 20, 10);
  scene.add(dirLight);

  // scene.add(new DirectionalLightHelper(dirLight, 10))
}
</script>

<template>
  <div class="flex size-full">
    <ThreeContextProvider
      class="flex-1"
      :setup="setup"
      :initOption="{ hideViewHelper: true, disableCSS2D: true }"
    >
      <slot />
    </ThreeContextProvider>
    <div v-if="inspector && rendererCtx" class="inspector-wrapper">
      <DevInspector :context="rendererCtx" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.inspector-wrapper {
  width: 240px;
}
</style>
