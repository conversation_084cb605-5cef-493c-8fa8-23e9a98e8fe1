<script lang="ts" setup>
import type { Object3D } from 'three';
import type { IVector3 } from '../../ThreeJs';

export interface Object3DPropertiesProps {
  data: Object3D;
}

const props = defineProps<Object3DPropertiesProps>();

function toString(vec: IVector3) {
  const precision = 3;
  return `${vec.x.toFixed(precision)}, ${vec.y.toFixed(precision)}, ${vec.z.toFixed(precision)}`;
}
</script>

<template>
  <div class="flex flex-col">
    <div>position: {{ toString(data.position) }}</div>
    <div>rotation: {{ toString(data.rotation) }}</div>
    <div>scale: {{ toString(data.scale) }}</div>
  </div>
</template>

<style lang="less" scoped></style>
