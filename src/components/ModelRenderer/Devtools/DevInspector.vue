<script lang="ts" setup>
import { readonly, ref, shallowRef } from 'vue';
import { type SimpleThree } from '../../ThreeJs';
import type { Object3D } from 'three';
import type { TreeItem } from '@geega-ui-plus/geega-ui';
import Object3DProperties from './Object3DProperties.vue';
import { useIntervalFn } from '@vueuse/core';

export interface DevInspectorProps {
  context: SimpleThree;
}
const props = defineProps<DevInspectorProps>();

const ctx = props.context;

const treeData = ref<TreeItem[]>([]);

const selectedItem = shallowRef<Object3D>();

useIntervalFn(() => updateTreeData(), 500);

function updateTreeData() {
  const result = generateTreeItem(ctx.scene);
  treeData.value = result.children || [];
}

function generateTreeItem(obj: Object3D): TreeItem {
  const item: TreeItem = {
    key: obj.id,
    title: `${obj.type}-${obj.name || obj.id}`,
    data: readonly(obj),
    children: [],
  };

  item.children = obj.children.map((n) => generateTreeItem(n));

  return item;
}

function handleSelect(_, evt) {
  selectedItem.value = evt.node?.data;
}
</script>

<template>
  <div class="h-full flex flex-col bg-black">
    <div class="flex-1 h-0">
      <a-tree :tree-data="treeData" @select="handleSelect"> </a-tree>
    </div>
    <div class="h-200px border-0 border-t border-solid border-gray">
      <Object3DProperties v-if="selectedItem" :data="selectedItem" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
