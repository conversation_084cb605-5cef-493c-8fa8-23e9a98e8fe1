import { type Object3D } from 'three';
import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

export interface IModelLoader {
  fileExtensions: string[];
  parse: (data: ArrayBuffer, path: string) => Promise<Object3D>;
}

const gltfLoader: IModelLoader = {
  fileExtensions: ['glb'],
  async parse(data, path) {
    const gltf = new GLTFLoader();
    gltf.setMeshoptDecoder(MeshoptDecoder);
    gltf.setDRACOLoader(new DRACOLoader())

    const result = await gltf.parseAsync(data, path);

    return result.scene;
  },
};

const loaders: IModelLoader[] = [gltfLoader];

export const modelLoader = {
  getLoader(fileExtension: string) {
    return loaders.find((n) => n.fileExtensions.includes(fileExtension));
  },
};
