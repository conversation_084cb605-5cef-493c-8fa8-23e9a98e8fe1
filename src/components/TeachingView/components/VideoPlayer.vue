<template>
  <div class="camera-box">
    <!-- <div class="title">{{ title }}</div> -->
    <div class="video-container">
      <video ref="videoRef" controls autoplay :src="src" class="video-player"></video>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue';

defineProps<{
  title?: string;
  src: string;
}>();

const videoRef = ref<HTMLVideoElement | null>(null);

onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.currentTime = 0;
  }
});
</script>

<style lang="less" scoped>
.camera-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);

  .title {
    margin-bottom: 1.5vh;
    font-size: 1.1vw;
    font-weight: 500;
    flex-shrink: 0;
    color: #fff;
  }

  .video-container {
    position: relative;
    flex: 1;
    min-height: 0;
    background: #000;
    overflow: hidden;
    border-radius: 4px;

    .video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

@media screen and (max-width: 1024px) {
  .camera-box {
    padding: 1.2vh 0.8vw;

    .title {
      margin-bottom: 1.2vh;
      font-size: 1vw;
    }
  }
}
</style>
