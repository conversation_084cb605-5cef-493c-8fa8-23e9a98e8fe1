<script setup lang="ts">
import { computed } from 'vue';
import type { PointData } from '../data';

const props = defineProps<{
  data: Array<PointData>;
}>();

// 将数据分为三个区域: 左侧连接头、左贴片、右贴片、右侧连接头
const sections = computed(() => {
  const defaultStatus = 'default';

  // 初始化三个区域的状态
  const sections = {
    left: defaultStatus,
    leftPatch: defaultStatus,
    rightPatch: defaultStatus,
    right: defaultStatus,
  };

  // 根据数据更新状态
  props.data?.forEach((item) => {
    const position = Number(item.objectFlag);
    switch (position) {
      case 0:
        sections.left = getStatusClass(item.result);
        break;
      case 1:
        sections.leftPatch = getStatusClass(item.result);
        break;
      case 2:
        sections.rightPatch = getStatusClass(item.result);
        break;
      case 3:
        sections.right = getStatusClass(item.result);
        break;
    }
  });

  return sections;
});

const getStatusClass = (result: number) => {
  switch (result) {
    case 0:
      return 'red';
    case 1:
      return 'green';
    case 2:
      return 'yellow';
    default:
      return 'default';
  }
};
</script>

<template>
  <div class="pipeline-installation-container">
    <div class="pipeline-content">
      <!-- 左侧连接头 -->
      <div class="pipeline-connector left-connector">
        <div class="connector-content" :class="sections.left"></div>
      </div>

      <!-- 中间管道和固定架 -->
      <div class="pipeline-center">
        <div class="pipeline-bracket left-bracket" :class="sections.leftPatch"></div>
        <div class="pipeline-bracket right-bracket" :class="sections.rightPatch"></div>
      </div>

      <!-- 右侧连接头 -->
      <div class="pipeline-connector right-connector">
        <div class="connector-content" :class="sections.right"></div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.pipeline-installation-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #1c2536;
  --ignore-bg-img: url('@/assets/svg/point/pipeline-bg.png');
  background-image: var(--ignore-bg-img);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
}

.pipeline-content {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  width: min(78%, 1200px);
  height: clamp(200px, 25vh, 35vh);
  margin: 0 auto;
}

.pipeline-connector {
  position: relative;
  width: clamp(45px, 12%, 100px);
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.left-connector {
    .connector-content {
      width: 100%;
      height: 100%;
      --ignore-left-img: url('@/assets/svg/point/pipeline-left.svg');
      background-image: var(--ignore-left-img);
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;

      &.red {
        --ignore-left-img: url('@/assets/svg/point/pipeline-left-r.svg');
        animation: blink 1s ease-in-out 2;
      }

      &.green {
        --ignore-left-img: url('@/assets/svg/point/pipeline-left-g.svg');
      }

      &.yellow {
        --ignore-left-img: url('@/assets/svg/point/pipeline-left-y.svg');
        animation: blink 1s ease-in-out 2;
      }
    }
  }

  &.right-connector {
    .connector-content {
      width: 100%;
      height: 100%;
      --ignore-right-img: url('@/assets/svg/point/pipeline-right.svg');
      background-image: var(--ignore-right-img);
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;

      &.red {
        --ignore-right-img: url('@/assets/svg/point/pipeline-right-r.svg');
        animation: blink 1s ease-in-out 2;
      }

      &.green {
        --ignore-right-img: url('@/assets/svg/point/pipeline-right-g.svg');
      }

      &.yellow {
        --ignore-right-img: url('@/assets/svg/point/pipeline-right-y.svg');
        animation: blink 1s ease-in-out 2;
      }
    }
  }
}

.pipeline-center {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  height: 100%;
}

.pipeline-bracket {
  position: absolute;
  width: clamp(50px, 15%, 120px);
  height: clamp(50px, 24%, 90px);
  top: 60%;
  z-index: 0;

  &.left-bracket {
    left: 20%;
    --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag.svg');
    background-image: var(--ignore-bracket-img);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    &.red {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-r.svg');
      animation: blink 1s ease-in-out 2;
    }

    &.green {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-g.svg');
    }

    &.yellow {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-y.svg');
      animation: blink 1s ease-in-out 2;
    }
  }

  &.right-bracket {
    right: 19%;
    --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag.svg');
    background-image: var(--ignore-bracket-img);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    &.red {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-r.svg');
      animation: blink 1s ease-in-out 2;
    }

    &.green {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-g.svg');
    }

    &.yellow {
      --ignore-bracket-img: url('@/assets/svg/point/pipeline-tag-y.svg');
      animation: blink 1s ease-in-out 2;
    }
  }
}

// 添加媒体查询以适应不同屏幕
@media screen and (max-width: 1400px) {
  .pipeline-content {
    height: clamp(178px, 23vh, 35vh);
  }
  .pipeline-bracket {
    width: clamp(40px, 14%, 140px);
    height: clamp(40px, 25%, 110px);
  }
}
@media screen and (min-width: 1450px) {
  .pipeline-content {
    height: clamp(200px, 23vh, 35vh);
  }
}
@media screen and (min-width: 1900px) {
  .pipeline-content {
    width: min(77%, 1400px);
  }

  .pipeline-bracket {
    width: clamp(70px, 14%, 140px);
    height: clamp(70px, 23%, 110px);
    &.left-bracket {
      left: 19%;
    }
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
