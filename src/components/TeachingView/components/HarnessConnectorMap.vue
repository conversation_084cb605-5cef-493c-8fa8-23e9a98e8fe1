<template>
  <div class="harness-connector-map">
    <div class="connector-container">
      <!-- 4条竖向连接器 -->
      <div class="connector-item" v-for="(connector, index) in connectors" :key="index">
        <!-- 使用SVG图标 -->
        <img
          :src="getConnectorImage(connector.status)"
          :alt="`连接器 ${index + 1}`"
          class="connector-svg"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import type { PointData } from '../data';

// 导入SVG资源
import lineDefault from '/@/assets/svg/point/line-default.svg';
import lineGreen from '/@/assets/svg/point/line-green.svg';
import lineRed from '/@/assets/svg/point/line-red.svg';
import lineYellow from '/@/assets/svg/point/line-yellow.svg';

const props = defineProps<{
  data: Array<PointData>;
}>();

/**
 * 计算4个连接器的状态
 * 根据objectFlag区分不同的检测点：
 * 0: 第1个连接器
 * 1: 第2个连接器
 * 2: 第3个连接器
 * 3: 第4个连接器
 */
const connectors = computed(() => {
  const defaultStatus = 'default';

  // 默认4个连接器
  const connectors = [
    { id: 0, status: defaultStatus },
    { id: 1, status: defaultStatus },
    { id: 2, status: defaultStatus },
    { id: 3, status: defaultStatus },
  ];

  // 根据数据更新状态
  props.data?.forEach((item) => {
    const position = Number(item.objectFlag);
    if (position >= 0 && position < 4) {
      connectors[position].status = getStatusClass(item.result);
    }
  });

  return connectors;
});

/**
 * 根据检测结果返回对应的状态类名
 * @param result 检测结果 0:异常(red) 1:正常(green) 2:警告(yellow)
 */
const getStatusClass = (result: number) => {
  switch (result) {
    case 0:
      return 'error';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'default';
  }
};

/**
 * 根据状态获取对应的SVG图标
 * @param status 状态
 * @returns SVG图标路径
 */
const getConnectorImage = (status: string) => {
  switch (status) {
    case 'success':
      return lineGreen;
    case 'error':
      return lineRed;
    case 'warning':
      return lineYellow;
    default:
      return lineDefault;
  }
};
</script>

<style lang="less" scoped>
.harness-connector-map {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1c2536;
  background-image: url('/@/assets/svg/point/line-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.connector-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: 100%;
  height: 100%;
}

.connector-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 90%;
  position: relative;
  flex: 1;
  max-width: 120px;
}

.connector-svg {
  width: 100%;
  // max-width: 45px;
  height: 100%;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.3));
}

// 错误状态闪烁效果
.connector-item {
  &:has(img[src*='line-red']) {
    .connector-svg {
      animation: blink 1s ease-in-out 2;
    }
  }

  &:has(img[src*='line-yellow']) {
    .connector-svg {
      animation: blink 1s ease-in-out 2;
    }
  }
}

// 闪烁动画
@keyframes blink {
  0%,
  50%,
  100% {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0.5;
  }
}

// 响应式设计
@media screen and (min-width: 2176px) {
  .connector-container {
    gap: 70px;
  }
}
@media screen and (max-width: 1400px) {
  .connector-container {
    gap: 15px;
    max-width: 500px;
  }

  .connector-svg {
    max-width: 55px;
  }
}

@media screen and (max-width: 1024px) {
  .connector-container {
    gap: 12px;
    max-width: 400px;
  }

  .connector-item {
    height: 85%;
    max-width: 90px;
  }

  .connector-svg {
    max-width: 45px;
  }
}

@media screen and (max-width: 768px) {
  .connector-container {
    gap: 10px;
    max-width: 350px;
  }

  .connector-item {
    height: 80%;
    max-width: 80px;
  }

  .connector-svg {
    max-width: 40px;
  }
}

@media screen and (max-width: 480px) {
  .connector-container {
    gap: 8px;
    max-width: 300px;
  }

  .connector-item {
    height: 75%;
    max-width: 70px;
  }

  .connector-svg {
    max-width: 35px;
  }
}
</style>
