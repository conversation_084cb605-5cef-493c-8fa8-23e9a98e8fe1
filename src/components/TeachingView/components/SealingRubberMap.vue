<script setup lang="ts">
import { computed } from 'vue';
import type { PointData } from '../data';

const props = defineProps<{
  data: Array<PointData>;
}>();

// 将数据分为三个区域
const sections = computed(() => {
  const defaultStatus = 'default';

  // 初始化三个区域的状态
  const sections = {
    top: defaultStatus,
    middle: defaultStatus,
    bottom: defaultStatus,
  };

  // 根据数据更新状态
  props.data?.forEach((item) => {
    const position = Number(item.objectFlag);
    switch (position) {
      case 0:
        sections.top = getStatusClass(item.result);
        break;
      case 1:
        sections.middle = getStatusClass(item.result);
        break;
      case 2:
        sections.bottom = getStatusClass(item.result);
        break;
    }
  });

  return sections;
});

const getStatusClass = (result: number) => {
  switch (result) {
    case 0:
      return 'red';
    case 1:
      return 'green';
    case 2:
      return 'yellow';
    default:
      return 'default';
  }
};
</script>

<template>
  <div class="sealing-rubber-container">
    <!-- 上部胶条 -->
    <div class="rubber-section top-rubber">
      <div class="rubber-content" :class="sections.top"></div>
    </div>

    <!-- 中部胶条 -->
    <div class="rubber-section middle-rubber">
      <div class="rubber-content" :class="sections.middle"></div>
    </div>

    <!-- 下部胶条 -->
    <div class="rubber-section bottom-rubber">
      <div class="rubber-content" :class="sections.bottom"></div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.sealing-rubber-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  gap: 8px;
  --ignore-dark-bg: #1c2536;
}

.rubber-section {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ignore-dark-bg);

  // 设置不同区域的高度比例
  &.top-rubber {
    height: 40%;

    .rubber-content {
      width: 70%;
      height: 80%;
      --ignore-down-img: url('@/assets/svg/point/sealing-down.svg');
      background-image: var(--ignore-down-img);
      background-size: 100% 100%;
      &.red {
        --ignore-down-img: url('@/assets/svg/point/sealing-down-r.svg');
        animation: blink 1s ease-in-out 2;
      }
      &.green {
        --ignore-down-img: url('@/assets/svg/point/sealing-down-g.svg');
      }
      &.yellow {
        --ignore-down-img: url('@/assets/svg/point/sealing-down-y.svg');
        animation: blink 1s ease-in-out 2;
      }
    }
  }

  &.middle-rubber {
    height: 20%;
    .rubber-content {
      width: clamp(200px, 70%, 700px);
      height: 40%;
      --ignore-midd-img: url('@/assets/svg/point/sealing-middle.svg');
      background-image: var(--ignore-midd-img);
      background-size: 100% 100%;
      &.red {
        --ignore-midd-img: url('@/assets/svg/point/sealing-middle-r.svg');
        animation: blink 1s ease-in-out 2;
      }
      &.green {
        --ignore-midd-img: url('@/assets/svg/point/sealing-middle-g.svg');
      }
      &.yellow {
        --ignore-midd-img: url('@/assets/svg/point/sealing-middle-y.svg');
        animation: blink 1s ease-in-out 2;
      }
    }
  }

  &.bottom-rubber {
    height: 40%;
    .rubber-content {
      width: 70%;
      height: 80%;
      padding: 0 20px;
      --ignore-up-img: url('@/assets/svg/point/sealing-up.svg');
      background-image: var(--ignore-up-img);
      background-size: 100% 100%;
      &.red {
        --ignore-up-img: url('@/assets/svg/point/sealing-up-r.svg');
        animation: blink 1s ease-in-out 2;
      }
      &.green {
        --ignore-up-img: url('@/assets/svg/point/sealing-up-g.svg');
      }
      &.yellow {
        --ignore-up-img: url('@/assets/svg/point/sealing-up-y.svg');
        animation: blink 1s ease-in-out 2;
      }
    }
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
