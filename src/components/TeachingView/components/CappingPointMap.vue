<script setup lang="ts">
import { computed } from 'vue';
import type { PointData } from '../data';

const props = defineProps<{
  data: Array<PointData>;
}>();

const GRID_COLS = 10;
const GRID_ROWS = 8;
const spacing = 3;

// 创建一个包含10列的模板，中间有间距
const gridTemplateColumns = computed(() => {
  return `repeat(5, 1fr) 0.37vw repeat(5, 1fr)`;
});

const positions = computed(() => {
  const defaultPositions = Array.from({ length: GRID_COLS * GRID_ROWS }, (_, index) => {
    const row = Math.floor(index / GRID_COLS);
    const col = index % GRID_COLS;
    const size =
      row === 0 || row === GRID_ROWS - 1
        ? 'large'
        : row === 1 || row === GRID_ROWS - 2
          ? 'medium'
          : 'small';

    return {
      id: index,
      status: 'default',
      size,
      isRightGroup: col >= 5,
    };
  });

  props.data?.forEach((item) => {
    const positionIndex = Number(item.objectFlag) || 0;
    if (positionIndex >= 0 && positionIndex < GRID_COLS * GRID_ROWS) {
      defaultPositions[positionIndex].status = getStatusClass(item.result);
    }
  });

  return defaultPositions;
});

const getStatusClass = (result: number) => {
  switch (result) {
    case 0:
      return 'red';
    case 1:
      return 'green';
    case 2:
      return 'yellow';
    default:
      return 'default';
  }
};
</script>

<template>
  <div
    class="relative grid"
    :style="{
      gridTemplateColumns: gridTemplateColumns,
      gap: `${spacing}px`,
    }"
  >
    <template v-for="position in positions" :key="position.id">
      <div
        class="flex items-center justify-center point-bg"
        :class="[position.status, position.size]"
        :style="{
          gridColumn: position.isRightGroup ? `${(position.id % GRID_COLS) + 2}` : undefined,
        }"
      ></div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.point-bg {
  --ignore-dark-bg: #1c2536;
  background: var(--ignore-dark-bg);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  --ignore-white-bg: #fff;
  --ignore-gray-bg: #8b8b8b;
  --ignore-green-bg: #00996b;
  --ignore-yellow-bg: #fc8800;
  --ignore-red-bg: #f6685d;
  --ignore-black-bg: #2c2c2c;
  --ignore-gray-bg-2: #7c7c7c;

  &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: radial-gradient(50% 50% at 50% 50%, var(--ignore-gray-bg) 0%, var(--ignore-gray-bg-2) 53.48%, var(--ignore-gray-bg-2) 80.01%, var(--ignore-gray-bg) 100%);
  }

  // 大圆点样式
  &.large::after {
    width: 2.775vw;
    height: 2.43vw;
    border-radius: 1.5vw;
  }
  &.large::before {
    content: '';
    position: absolute;
    border-radius: 1.365vw;
    transition: all 0.3s ease;
    --ignore-trans-bg: rgba(255, 255, 255, 0.5);
    border: 0.5px solid var(--ignore-trans-bg);
    --ignore-grey-bg: #747474;
    --ignore-grey-bg-2: #5f5f5f;
    background: linear-gradient(180deg, var(--ignore-grey-bg-2) 0%, var(--ignore-gray-bg) 100%);
    width: 1.95vw;
    height: 1.59vw;
    z-index: 10;
  }

  // 中等圆环样式
  &.medium::after {
    width: 1.665vw;
    height: 1.665vw;
  }
  &.medium::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    transition: all 0.3s ease;
    --ignore-trans-bg: rgba(255, 255, 255, 0.5);
    border: 0.5px solid var(--ignore-trans-bg);
    background-color: var(--ignore-black-bg);
    width: 0.9vw;
    height: 0.9vw;
    z-index: 10;
  }

  // 小圆点样式
  &.small::after {
    width: 0.975vw;
    height: 0.975vw;
  }

  // 状态样式
  &.green::after {
    background: var(--ignore-green-bg);
  }
  &.green {
    &.large::before {
      --ignore-m-green-bg: #02b780;
      background: var(--ignore-m-green-bg);
    }
  }

  &.red::after {
    background: var(--ignore-red-bg);
  }
  &.red {
    &.large::before {
      --ignore-m-red-bg: #f46156;
      background: var(--ignore-m-red-bg);
    }
  }

  &.yellow::after {
    background: var(--ignore-yellow-bg);
  }
  &.yellow {
    &.large::before {
      --ignore-m-yellow-bg: #fa9550;
      background: var(--ignore-m-yellow-bg);
    }
  }

  &.default {
    &.medium::after {
      background: var(--ignore-gray-bg);
    }
    &.large::after,
    &.small::after {
      background: var(--ignore-gray-bg);
    }
  }

  // 闪烁动画
  &.yellow::after,
  &.red::after {
    animation: blink 1s ease-in-out 2;
  }

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
  }
}
</style>
