<script lang="ts" setup>
import { remove, uniq } from 'lodash-es';
import type { ChildElement } from '/@/api/cddc.model';
import { V1ManageSysOrgUnitUserTreePost } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { traverseTree } from '/@/utils/tree';
import { computed } from 'vue';

// 自定义组件，专用于选择组织用户，因为同一个用户会存在多个组织，
// 而 a-select-tree 本身不支持拥有相同 id 的 treeData，
// 因此需要一个 id <=> value 的映射关系，
// 否则 a-tree-select 会认为同一个 id 的节点是相同的节点，
// 导致诡异的 bug 出现。

type TreeNode = ChildElement & {
  isLeaf: boolean;
  label: string;
  value: string;
};

const vValue = defineModel<string[]>('value', { default: [] });

const treeValue = computed({
  get() {
    return getNodeValueByIds(vValue.value);
  },
  set(values: string[]) {
    const ids = getNodeIdByValues(values);
    vValue.value = ids;
  },
});

const treeList = useAsyncData(async () => {
  const resp = await V1ManageSysOrgUnitUserTreePost();

  return filterNoneEmptyNodes(resp);
}, []);

treeList.load();

function filterNoneEmptyNodes(nodes: ChildElement[], prefix = '0') {
  const _nodes: TreeNode[] = [];

  nodes.forEach((node, idx) => {
    const currentPrefix = [prefix, idx].join('-');

    if (node.children) {
      const children = filterNoneEmptyNodes(node.children, currentPrefix);
      node.children = children;
    }

    const shouldSkip = node.type === 'UNIT' && !node.children?.length;

    if (!shouldSkip) {
      _nodes.push({
        ...node,
        label: node.name!,
        value: currentPrefix,
        isLeaf: node.type === 'USER',
      });
    }
  });

  return _nodes;
}

const getPopupContainer = () => document.body;

function handleChange(
  keys,
  labels,
  node: {
    checked: boolean;
    preValue: string[];
    triggerValue: string;
    triggerNode: { props: TreeNode };
  }
) {
  const triggerNode = node.triggerNode.props;

  const oldValue = treeValue.value.slice();

  if (node.checked) {
    const values = triggerNode.isLeaf ? [node.triggerValue] : getAllLeafValues(triggerNode);
    oldValue.push(...values);
  } else {
    const values = triggerNode.isLeaf ? [node.triggerValue] : getAllLeafValues(triggerNode);

    const allIds = getNodeValueByIds(getNodeIdByValues(values));
    remove(oldValue, (val) => allIds.includes(val));
  }

  treeValue.value = uniq(oldValue);
}

function getAllLeafValues(node: TreeNode) {
  const values: string[] = [];

  traverseTree(node, (node) => {
    if (node.isLeaf) {
      values.push(node.value);
    }
  });

  return values;
}

function getNodeIdByValues(values: string[]) {
  const ids: string[] = [];

  traverseTree(treeList.data.value, (node) => {
    if (values.includes(node.value)) {
      ids.push(node.id!);
    }
  });

  return uniq(ids);
}

function getNodeValueByIds(ids: string[]) {
  const values: string[] = [];

  traverseTree(treeList.data.value, (node) => {
    if (ids.includes(node.id!)) {
      values.push(node.value);
    }
  });

  return uniq(values);
}
</script>

<template>
  <a-tree-select
    :value="treeValue"
    :tree-data="treeList.data.value"
    placeholder="请选择"
    :tree-checkable="true"
    :tree-default-expand-all="true"
    :multiple="true"
    :max-tag-count="'responsive'"
    :get-popup-container="getPopupContainer"
    tree-node-filter-prop="label"
    @change="handleChange"
    :dropdown-style="{ zIndex: 9999 }"
  />
</template>

<style lang="less" scoped></style>
