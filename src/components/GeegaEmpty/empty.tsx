import { Empty } from '@geega-ui-plus/ant-design-vue';
import noDataPng from '/@/assets/images/empty.png';
  import i18nVue from '@geega/i18n-vue';

export const customizeRenderEmpty = () => (
  <div style="min-height: 160px">
    <Empty
      style="position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);"
      description={i18nVue.t('components.GeegaEmpty.empty.abb9dc60', { defaultValue: '暂无数据' })}
      image={noDataPng}
      image-style={{ height: '60px', display: 'flex', 'justify-content': 'center' }}
    />
  </div>
);
