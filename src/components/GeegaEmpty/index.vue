<template>
  <Empty :class="prefixCls" :image="noDataPng" :description="props.description" />
</template>

<script setup lang="ts">

  import { Empty } from '@geega-ui-plus/ant-design-vue';

  import { useDesign } from '/@/hooks/web/useDesign';
  import noDataPng from '/@/assets/images/empty.png';
  import i18nVue from '@geega/i18n-vue';


  interface Props {
    description: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    description: () => {
      return i18nVue.t('components.GeegaEmpty.index.abb9dc60', { defaultValue: '暂无数据' });
    },
  })

  const { prefixCls } = useDesign('empty');
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-empty';

.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  :deep(.@{ant-prefix}-empty-image) {
    margin-bottom: 2px;
    font-size: 12px;
    img {
      height: 60px;
    }
  }
  :deep(.@{ant-prefix}-empty-description) {
    color: #666;
    margin-bottom: 0;
  }
}
</style>
