<template>
  <BasicModal
    v-bind="$attrs"
    width="680px"
    centered
    :title="$t('components.GeegaIconModal.index.d33131f0', { defaultValue: '选择图标' })"
    @register="registerModal"
    @ok="handleOk(null)"
    :mask-closable="false"
  >
    <div class="operate-list flex">
      <a class="tab tab-checked" @click="chooseTab(1)"
        >{{$t('components.GeegaIconModal.index.7aa89077', { defaultValue: '图标库' })}}</a
      >
    </div>
    <div v-show="iconType === 1" class="content">
      <div class="menu-icons">
        <a v-for="icon in dynamics" :key="icon" @click="handleOk(icon)">
          <div :class="iconType === 1 && icon === iconValue ? 'icon-card-checked' : 'icon-card'">
            <SvgIcon :name="icon" size="24" />
            <Tooltip>
              <template #title>{{ icon }}</template>
              <span class="icon-text">{{ icon }}</span>
            </Tooltip>
          </div>
        </a>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { Input, Tooltip, Button } from '@geega-ui-plus/ant-design-vue';
  import { BasicModal, SvgIcon, useModalInner } from '@geega-ui-plus/geega-ui';
  import GeegaIcons from '../GeegaIcons/index';
  import i18nVue from '@geega/i18n-vue';

  export default defineComponent({
    name: 'AddAuthorize',
    components: {
      Input,
      BasicModal,
      SvgIcon,
      InputTextArea: Input.TextArea,
      Tooltip,
      Button,
    },
    emits: ['success', 'cancel'],
    setup(_, { emit }) {
      const iconValue = ref<string>('');
      const iconType = ref<Number>(1);
      const dynamics = ref(GeegaIcons.icons);
      const { icons } = GeegaIcons;

      const [registerModal, { closeModal }] = useModalInner((e) => {
        console.log(i18nVue.t('components.GeegaIconModal.index.963456f1', { defaultValue: '传过来的值' }), e)
        dynamics.value = icons;
      });

      const handleOk = (icon) => {
        switch (iconType.value) {
          case 1:
            iconValue.value = icon;
            break;
        }
        emit('success', { icon: iconValue, iconType: iconType.value });
        closeModal();
      };

      const chooseTab = (val) => {
        iconType.value = val;
      };

      return {
        handleOk,
        dynamics,
        iconValue,
        registerModal,
        iconType,
        chooseTab
      };
    },
  });
</script>
<style lang="less" scoped>
  .operate-list {
    margin-bottom: 12px;
  }
  .icon-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: 96px;
    height: 76px;
    padding: 4px;
    margin: 0 8px 8px 0;
    color: #333;
  }
  .icon-card:hover {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: 96px;
    height: 76px;
    padding: 4px;
    margin: 0 8px 8px 0;
    border-radius: 4px;
    color: #00996b;
    background: #f2faf7;
  }

  .icon-card-checked {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: 96px;
    height: 76px;
    padding: 4px;
    margin: 0 8px 8px 0;
    border-radius: 4px;
    color: #00996b;
    background: #f2faf7;
  }
  .icon-text {
    overflow: hidden;
    font-size: 12px;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    width: 126px;
  }
  .menu-icons {
    display: flex;
    flex-flow: wrap;
    max-height: 250px;
    overflow-y: auto;
  }
  .icon-help-text {
    margin-top: 6px;
    color: #999;
  }

  .tab {
    margin-right: 32px;
    font-size: 16px;
    color: #333;
  }
  .tab-checked {
    width: fit-content;
    height: 34px;
    border-bottom: 1px solid @primary-color;
    font-weight: 500;
    color: @primary-color;
  }

  .tab-unchecked {
    width: fit-content;
    height: 40px;
  }
  .icon-line {
    margin: 16px 0 10px;
    border-bottom: 1px solid #f0f0f0;
  }
  .content {
    border: 1px solid #ececec;
    border-radius: 4px;
  }
</style>
