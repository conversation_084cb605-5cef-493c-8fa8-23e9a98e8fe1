<script lang="ts" setup>
import { reactive, computed } from 'vue';
import { chooseFiles, convertFileSize } from './file';
import { message } from '@geega-ui-plus/ant-design-vue';
import { ImportStatus, type ImportResponse } from './types';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  PaperClipOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@geega-ui-plus/icons-vue';

type ImportSize = {
  type: string;
  size: number;
};

const props = withDefaults(
  defineProps<{
    title?: string;
    /**
     * 支持的文件类型，默认 `.xlsx,.xls`
     */
    accept?: string;
    /**
     * 支持上传的文件大小，单位 KB，默认不限制
     */
    maxSize?: number;
    /**
     * 根据文件类型限制大小
     * 类似：[{ type: '.zip', size: xxx }]
     */
    sizeLimitRule?: Array<ImportSize>;
    /**
     * 模板请求方式
     */
    tplExportEvent?: () => void;
    /**
     * 导入 api
     */
    api?: (data?: any) => Promise<any>;
    /**
     * 提示语，支持 tip slot
     */
    tip?: string;

    otherParams?: Record<string, any>;

    maxRow?: number;
  }>(),
  {
    title: '导入',
    accept: '.xlsx,.xls',
    maxSize: 1024 * 4,
    maxRow: 1000,
  }
);

const emit = defineEmits(['success']);

const state = reactive({
  visible: false,
  file: null as File | null,
  processing: false,
  processResult: null as ImportResponse | null,
});

const finalTip = computed(() => {
  return (
    props.tip ??
    `支持${props.accept}格式，文件不超过${convertFileSize(props.maxSize)}，行数不超过${
      props.maxRow
    }条`
  );
});

async function showFileDialog() {
  state.visible = true;
  state.file = null;
  state.processResult = null;
  state.processing = false;
}

async function selectFiles() {
  const [file] = await chooseFiles({
    accept: props.accept,
  });

  if (!isValid(file)) return;

  state.file = file;
}

function isValid(file?: File) {
  if (!file) return false;

  const ext = file.name.split(/\./g).pop();

  const accepts = (props.accept || '').split(',')

  if (!ext || !accepts.some(o => o == '.' + ext.toLowerCase())) {
    message.warn(`请上传 ${props.accept} 格式的文件`);
    return false;
  }

  if (props.sizeLimitRule && Array.isArray(props.sizeLimitRule)) {
    for (const rule of props.sizeLimitRule) {
      if (rule.type === '.' + ext && file.size / 1024 > rule.size) {
        message.warn(`文件请上传小于 ${convertFileSize(rule.size)} 的文件`);
        return false;
      }
    }
  } else {
    if (props.maxSize && file.size / 1024 > props.maxSize) {
      message.warn(`请上传小于 ${convertFileSize(props.maxSize)} 的文件`);
      return false;
    }
  }

  return true;
}

async function startProcess() {
  if (!state.file || !props.api) return;

  state.processing = true;
  const data = new FormData();
  data.set('file', state.file);

  const otherParams = props.otherParams || {};

  for (let key in otherParams) {
    data.set(key, otherParams[key]);
  }

  state.processResult = {
    status: ImportStatus.Processing,
  };

  const result: ImportResponse = {
    status: ImportStatus.PreStart,
    msgs: [] as string[],
  };
  let res = null;
  try {
    res = await props.api(data);
    result.status = ImportStatus.Success;
    result.msgs = [];
  } catch (err: any) {
    result.status = ImportStatus.Failed;
    result.msgs = err.message ? [err.message] : [];
  }

  state.processResult = result;

  if (result.status === ImportStatus.Success) {
    emit('success', res);
    state.visible = false;
  }
}

async function reselectFile() {
  showFileDialog();
  selectFiles();
}

</script>

<template>
  <div class="import-file contents" @click="showFileDialog">
    <slot>
      <a-button type="primary" ghost class="text flex items-center gap-1">
        <UploadOutlined />
        导入
      </a-button>
    </slot>
  </div>

  <a-modal v-model:visible="state.visible" v-bind="$attrs" :title="title" :width="450">
    <div class="flex flex-col gap-4 pt-15px" v-if="!state.processing">
      <div>
        <a-button type="primary" :disabled="!!state.file" @click="selectFiles">选择文件</a-button>
        <span class="pl-4" v-show="!state.file">未选择文件</span>
      </div>

      <slot name="tip">
        <span class="text-[#999]">{{ finalTip }}</span>
      </slot>

      <div v-if="tplExportEvent">
        <a class="flex-inline items-center gap-1" @click="() => tplExportEvent?.()">
          <DownloadOutlined />
          <span> 模板下载 </span>
        </a>
      </div>

      <div class="file" v-if="state.file">
        <PaperClipOutlined />
        <span class="ml-1">
          {{ state.file.name }}
        </span>
        <span class="flex-1 flex justify-end">
          <em class="flex cursor-pointer" @click="state.file = null">
            <DeleteOutlined class="icon-delete" />
          </em>
        </span>
      </div>
    </div>
    <div class="processing pt-15px" v-else-if="state.processResult">
      <!-- 检验中 -->
      <div class="title" v-show="state.processResult.status === ImportStatus.Processing">
        <ClockCircleOutlined class="icon" style="color: #f18243" />
        <span class="import-modal-text">校验中，请稍后…</span>
      </div>

      <!-- 导入成功 -->
      <div v-show="state.processResult.status === ImportStatus.Success">
        <div class="title">
          <CheckCircleOutlined class="icon" style="color: #24b376" />
          <span class="import-modal-text"> 导入成功 </span>
        </div>
      </div>

      <!-- 导入失败-->
      <div class="po" v-show="state.processResult.status === ImportStatus.Failed">
        <div class="title">
          <CloseCircleOutlined class="icon" style="color: red" />
          <span class="import-modal-text">导入失败</span>
        </div>
        <div class="error-list">
          <div class="error-msg" v-for="err in state.processResult.msgs || []">
            <ExclamationCircleOutlined class="icon" style="color: red" />
            <span class="break-all">{{ err }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button class="secondary" @click="state.visible = false">取消</a-button>
      <a-button
        type="primary"
        v-if="[ImportStatus.Success, ImportStatus.Failed].includes(state.processResult?.status!)"
        @click="reselectFile"
      >
        重新上传
      </a-button>
      <a-button
        type="primary"
        v-else
        @click="startProcess"
        :disabled="!state.file || state.processResult?.status === ImportStatus.Processing"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>

<style lang="less" scoped>
.processing {
  text-align: center;

  .title {
    display: inline-flex;
    align-items: center;
    font-size: 16px;
  }

  .icon {
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .error-list {
    display: flex;
    flex-direction: column;
    text-align: left;
    justify-content: center;
    gap: 4px;
    margin-top: 12px;

    .error-msg {
      display: flex;
      align-items: center;
    }
  }
}

.file {
  background: fade(#00c07f, 12);
  padding: 4px 6px;
  display: flex;
  align-items: center;
}
</style>
