

function toFixed(num: number, fractionDigits: number) {
  return +num.toFixed(fractionDigits)
}

interface ChooseFilesOption {
  /**
   * @default ''
   */
  accept?: string

  /**
   * @default false
   */
  multiple?: boolean
}

export function chooseFiles(opt: ChooseFilesOption = {}): Promise<File[]> {
  return new Promise((resolve, reject) => {
    const input = createInputElement()

    input.accept = opt.accept ?? ''
    input.multiple = opt.multiple ?? false

    input.onchange = () => {
      const files = [...((input?.files as any) || [])]

      resolve(files)
      input.remove()
    }

    input.onerror = (e) => {
      reject(e)
      input.remove()
    }

    input.click()
  })
}

function createInputElement() {
  const input: HTMLInputElement =
    document.querySelector(`[data-choose-file='']`) || document.createElement('input')

  input.type = 'file'
  input.style.display = 'none'
  input.setAttribute('data-choose-file', '')

  document.body.appendChild(input)

  return input
}

const MB = 1024
const GB = MB * 1024

export const convertFileSize = (size: number) => {
  if (size > GB) {
    return toFixed(size / GB, 2) + 'GB'
  }

  if (size > MB) {
    return toFixed(size / MB, 2) + 'MB'
  }

  return size + 'KB'
}
