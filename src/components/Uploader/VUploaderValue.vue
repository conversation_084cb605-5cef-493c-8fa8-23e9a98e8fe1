<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { VUploadFile } from './types'
import VUploader from './VUploader.vue'
const props = withDefaults(
  defineProps<{
    /**
     * 文件列表
     */
    value?: VUploadFile[]
  }>(),
  {},
)

defineEmits(['update:value'])

const files = useVModel(props, 'value')
</script>

<template>
  <VUploader v-bind="$attrs" v-model="files"></VUploader>
</template>

<style lang="less" scoped></style>

<style lang="less"></style>
