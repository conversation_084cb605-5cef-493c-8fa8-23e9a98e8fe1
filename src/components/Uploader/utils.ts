import { uniqueId } from 'lodash-es';
import type { VUploadFile } from './types';
import { toFixed } from '/@/utils';
import type {
  UploadProgressEvent,
  UploadRequestOption,
} from '@geega-ui-plus/ant-design-vue/es/vc-upload/interface';
import { defHttp } from '/@/utils/http/axios';
import videoSvg from './resources/video.svg';
import type { AttachmentListElement } from '/@/api/cddc.model';
import { ContentTypeEnum } from '/@/enums/httpEnum';

export const VideoIcon = videoSvg;

export type UploadApi = (file: File, opt?: UploadRequestOption) => Promise<UploadedResponse>;

export type ResponseFile = AttachmentListElement;

export interface UploadedResponse extends ResponseFile {
  name: string;
  uid?: string;
  url?: string;
}

export const uploadApi: UploadApi = async (file, opt) => {
  const form = new FormData();

  form.set('file', file);

  const businessType = opt?.data?.businessType || 'product';

  const item: ResponseFile = await defHttp.post({
    url: `/v1/common/file-upload/${businessType}`,
    headers: {
      'Content-type': ContentTypeEnum.FORM_DATA,
    },
    onUploadProgress(e: UploadProgressEvent) {
      if (e.total! > 0) {
        e.percent = (e.loaded! / e.total!) * 100;
      }

      opt?.onProgress?.(e);
    },
    timeout: 10000 * 3600,
    data: form,
  });

  return {
    ...item,
    uid: item.id,
    url: item.url,
    name: file.name,
  };
};

export function createFile(opt: UploadedResponse) {
  opt.name ||= opt.fileName || opt.url?.split('/').pop()!;

  const file = new File([], opt.name) as unknown as VUploadFile;

  file.uid = opt.uid || opt.id || uniqueId('upload-file');

  file.response = opt;

  file.url = opt.url;

  const isVideo = getResourceType(file.name || file.url);
  file.thumbUrl = isVideo == 'video' ? videoSvg : file.url;

  return file;
}

export function createFiles(opt?: UploadedResponse[]) {
  return (opt || []).map((r) => createFile(r));
}

const MB = 1024;
const GB = MB * 1024;

export const convertFileSize = (size: number) => {
  if (size > GB) {
    return toFixed(size / GB, 2) + 'GB';
  }

  if (size > MB) {
    return toFixed(size / MB, 2) + 'MB';
  }

  return size + 'KB';
};

export function getResourceType(nameOrUrl = 'unknown') {
  return /\.mp4$/.test(nameOrUrl) ? 'video' : 'image';
}

export function getUploadResponse(attachments?: VUploadFile[]) {
  return (attachments || []).map((n) => n.response!);
}

export function isAllUploaded(attachments: VUploadFile[]) {
  return !attachments.some((n) => n.response);
}
