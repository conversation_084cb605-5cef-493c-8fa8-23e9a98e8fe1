<script lang="ts" setup>
import Quill, { Parchment, Range } from 'quill';
import { onMounted, reactive, ref, shallowRef, watch } from 'vue';
import type Uploader from 'quill/modules/uploader';
import { V1CommonFileUploadBaseBusinessTypePost } from '/@/api/cddc.req';
import { getOrigin } from '/@/utils/env';
import { useInjectFormItemContext } from '@geega-ui-plus/ant-design-vue/es/form';

export interface QuillEditorProps {
  value?: string;
  showToolbar?: boolean;

  /**
   * inline mode
   */
  inline?: boolean;
}

const props = defineProps<QuillEditorProps>();

const emit = defineEmits(['update:value', 'change', 'blur', 'focus']);

const quillEl = ref<HTMLDivElement>();
const toolbarEl = ref<HTMLDivElement>();

const quill = shallowRef<Quill>();

const formItemCtx = useInjectFormItemContext();

watch(
  () => props.value,
  (html) => setValue(html)
);

onMounted(async () => {
  quill.value = await initQuill();
  setValue(props.value);
});

const state = reactive({
  focus: false,
});

async function initQuill() {
  const registry = new Parchment.Registry();

  {
    // register basic blots
    registry.register((await import('quill/blots/block')).default);
    registry.register((await import('quill/blots/break')).default);
    registry.register((await import('quill/blots/container')).default);
    registry.register((await import('quill/blots/cursor')).default);
    registry.register((await import('quill/blots/inline')).default);
    registry.register((await import('quill/blots/scroll')).default);
    registry.register((await import('quill/blots/text')).default);

    // register formats
    registry.register((await import('./image')).ImageExt);
  }

  const ins = new Quill(quillEl.value!, {
    registry,
    placeholder: '请输入',
    modules: {
      toolbar: props.showToolbar ? toolbarEl.value : false,
      uploader: {
        handler: uploadFile,
      },
    },
  });

  ins.on('editor-change', (eventName, newRange, oldRange) => {
    if (eventName === 'selection-change') {
      if (newRange == null && oldRange) {
        state.focus = false;
        emit('blur');
      } else if (newRange && oldRange == null) {
        state.focus = true;
        emit('focus');
      }
    }
  });

  ins.on('text-change', () => {
    const val = getValue();
    emit('update:value', val);
    emit('change', val);

    formItemCtx?.onFieldChange();
  });

  return ins;
}

async function uploadFile(this: Uploader, range: Range, files: File[]) {
  const file = files[0];
  if (!file) return;

  const formData = new FormData();

  formData.append('file', file);

  const resp = await V1CommonFileUploadBaseBusinessTypePost(formData as any, {
    businessType: 'archives',
  });

  const origin = getOrigin();
  const url = new URL('/', origin);
  url.pathname = resp.path!;

  this.quill.insertEmbed(range.index, 'image', url.toString());
  this.quill.setSelection(range.index + 1);
}

function setValue(newVal = '') {
  const ins = quill.value;
  if (!ins) return;

  const oldVal = getValue();
  if (oldVal === newVal) return;

  const isFocus = ins.hasFocus();

  ins.setText('');
  ins.clipboard.dangerouslyPasteHTML(newVal);

  if (isFocus) {
    ins.focus();
  } else {
    ins.blur();
  }
}

function getValue() {
  return props.inline ? _getText() : _getHtml();
}

function _getText() {
  const ins = quill.value;

  if (!ins) return '';

  return ins.getText().trim();
}

function _getHtml() {
  const ins = quill.value;

  if (!ins) return '';

  let html = ins.getSemanticHTML();

  // remove empty lines
  html = html.replaceAll(/^<p>\s*<\/p>$/g, '');

  return html.trim();
}

defineExpose({
  quill,
});
</script>

<template>
  <div class="quill-container" :class="{ 'is-focus': state.focus }">
    <div v-if="showToolbar" class="toolbars" ref="toolbarEl">
      <button class="ql-image"></button>
    </div>
    <div class="quill-editor" ref="quillEl"></div>
  </div>
</template>

<style lang="less">
.quill-container {
  border: 1px solid #bfbfbf;
  border-radius: 4px;
  transition: border 0.2s ease;

  &:hover,
  &.is-focus {
    border-color: #1fab7c;
  }
}

.cddc-ant-form-item-has-error {
  .quill-container {
    border-color: #f76560;
  }
}

.quill-editor {
  .ql-editor {
    padding: 2px 4px;
    width: 100%;

    p {
      margin: 0;
    }
  }

  .ql-blank {
    position: relative;

    &::before {
      position: absolute;
      top: 4px;
      left: 7px;

      color: rgba(0, 0, 0, 0.6);
      content: attr(data-placeholder);
      pointer-events: none;
      position: absolute;
    }
  }
}
</style>
