<template>
  <div class="video-player-container">
    <div v-if="title" class="video-title">{{ title }}</div>
    <div class="video-content">
      <video ref="videoRef" controls autoplay :src="src" class="video-element">
        <source :src="src" type="video/mp4" />
        您的浏览器不支持视频播放
      </video>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue';

defineProps<{
  title?: string;
  src: string;
}>();

const videoRef = ref<HTMLVideoElement | null>(null);

onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.currentTime = 0;
  }
});
</script>

<style lang="less" scoped>
.video-player-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .video-title {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
    flex-shrink: 0;
  }

  .video-content {
    position: relative;
    flex: 1;
    min-height: 0;
    background: #000;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .video-element {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
</style>
