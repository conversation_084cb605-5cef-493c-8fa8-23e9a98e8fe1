<template>
  <BasicDrawer
    v-bind="drawerConfig"
    :title="title"
    @register="registerDrawer"
    showFooter
    @ok="debounceSubmit"
  >
    <BasicForm @register="registerForm">
      <template #name="{ model, field }">
        <a-select
          v-model:value="model[field]"
          style="width: 100%"
          placeholder="请选择"
          :getPopupContainer="getPopupContainer"
          :options="nameOptions"
          :disabled="nameDisabled"
          @change="handleNameChange"
        >
        </a-select>
      </template>
      <template #modelList>
        <div class="algorithm-list">
          <a-form-item-rest>
            <div class="algorithm-item" v-for="(item, index) in modelList" :key="index">
              <a-select
                v-model:value="item.modelCode"
                style="width: 280px"
                placeholder="请选择"
                @change="modelChange"
                :getPopupContainer="getPopupContainer"
                :options="algorithmOptions"
                :fieldNames="{ label: 'desc', value: 'code' }"
              >
              </a-select>
              <div class="checkbox-group">
                <a-radio-group
                  v-model:value="item.modelJsonObj.isIndicator"
                  @change="validateAlgorithms"
                >
                  <a-radio :value="1">合格率</a-radio>
                  <a-radio :value="2">动作达标率</a-radio>
                </a-radio-group>
              </div>
              <a-button
                v-if="modelList.length > 1"
                danger
                class="delete-btn"
                @click="removeAlgorithm(index)"
              >
                删除
              </a-button>
            </div>
            <a-button
              type="dashed"
              block
              @click="addAlgorithm"
              :disabled="modelList.length >= algorithmOptions.length"
              >+ 添加算法</a-button
            >
          </a-form-item-rest>
        </div>
      </template>
      <slot name="extraFormItems"></slot>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue';
import { BasicDrawer, useDrawer, BasicForm, useForm } from '@geega-ui-plus/geega-ui';
import { getPopupContainer } from '/@/utils';
import {
  V1ManageCommonActionTypeEnum,
  V1ManageTranProjectPost,
  V1ManageTranProjectPut,
  V1ManageTranProjectListPost,
} from '/@/api/cddc.req';
import { cloneDeep } from 'lodash-es';
import { AlgorithmTypeEnum, getAlgorithmOptions } from '/@/enums/algorithmEnum';
import { useDebounce } from '/@/hooks/core/useDebounce';

interface Props {
  title?: string;
  projectType: 1 | 2 | 3;
  formSchemas: any[];
  record?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  title: '新增项目',
  record: () => ({}),
});

const emit = defineEmits<{
  (e: 'register', ...args: any[]): void;
  (e: 'success', data: any): void;
}>();

// 抽屉配置
const drawerConfig = reactive({
  width: 600,
});

// 算法类型选择
const selectedAlgorithmType = ref<number>(AlgorithmTypeEnum.TIGHTENING);
const nameDisabled = ref<boolean>(false);
// 算法列表
const modelList = ref<
  {
    modelCode: string | null;
    modelJsonObj: {
      isIndicator: 1 | 2;
      isQualified: boolean;
      isActionQualified: boolean;
    };
  }[]
>([
  {
    modelCode: null,
    modelJsonObj: {
      isIndicator: 1,
      isQualified: true,
      isActionQualified: false,
    },
  },
]);

// 算法选项
interface AlgorithmOption {
  code: string;
  desc: string;
  disabled?: boolean;
}

const algorithmOptions = ref<AlgorithmOption[]>([]);

// 算法类型选项
const algorithmTypeOptions = [
  { label: '拧紧', value: AlgorithmTypeEnum.TIGHTENING },
  { label: '堵盖', value: AlgorithmTypeEnum.PLUG },
  { label: '密封胶条', value: AlgorithmTypeEnum.SEAL },
  { label: '管线安装', value: AlgorithmTypeEnum.PIPELINE },
  { label: '线束插接', value: AlgorithmTypeEnum.CONDUIT },
];

// 编辑数据
const editData = ref<Record<string, any>>({});

// 注册抽屉
const [registerDrawer, { openDrawer }] = useDrawer();

// 注册表单
const [
  registerForm,
  { validate, setFieldsValue, updateSchema, resetFields, clearValidate },
] = useForm({
  rowProps: {
    gutter: [24, 12],
  },
  schemas: props.formSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
});

// 拉取模型数据
const fetchModelData = async () => {
  const res = await V1ManageCommonActionTypeEnum();
  algorithmOptions.value = res.map((item) => ({
    code: item.code || '',
    desc: item.desc || '',
  }));
};
fetchModelData();
// 根据算法类型获取对应的算法选项
const updateAlgorithmOptionsByType = (type: number) => {
  const options = getAlgorithmOptions(type);
  algorithmOptions.value = options.map((item) => ({
    code: item.value,
    desc: item.label,
    disabled: modelList.value.some((model) => model.modelCode === item.value),
  }));
};

// 算法类型变化处理
const handleAlgorithmTypeChange = (value: number) => {
  selectedAlgorithmType.value = value;
  updateAlgorithmOptionsByType(value);
  // 清空已选算法，因为切换类型后算法选项已变化
  modelList.value = [
    {
      modelCode: null,
      modelJsonObj: {
        isIndicator: 1,
        isQualified: true,
        isActionQualified: false,
      },
    },
  ];
  validateAlgorithms();
};

// 初始化时加载默认算法类型的选项
// updateAlgorithmOptionsByType(selectedAlgorithmType.value);

// 验证算法列表
const validateAlgorithms = () => {
  setFieldsValue({ modelList: modelList.value });
};

// 算法变化
const modelChange = () => {
  validateAlgorithms();
  // 禁用已选
  algorithmOptions.value = algorithmOptions.value.map((item) => ({
    ...item,
    disabled: modelList.value.some((model) => model.modelCode === item.code),
  }));
};

// 添加算法
const addAlgorithm = () => {
  modelList.value.push({
    modelCode: null,
    modelJsonObj: {
      isIndicator: 1,
      isQualified: true,
      isActionQualified: false,
    },
  });
  modelChange();
};

// 删除算法
const removeAlgorithm = (index: number) => {
  modelList.value.splice(index, 1);
  modelChange();
};

// 提交表单
const handleSubmit = async () => {
  try {
    const values = await validate();
    const submitData = {
      ...values,
      projectType: props.projectType,
    };

    if (editData.value.id) {
      submitData.id = editData.value.id;
    }

    // 处理 modelList
    submitData.modelList = submitData.modelList.map(({ modelJsonObj, ...rest }) => {
      const updatedModelJson = {
        ...modelJsonObj,
        isQualified: modelJsonObj?.isIndicator === 1,
        isActionQualified: modelJsonObj?.isIndicator === 2,
      };
      return {
        ...rest,
        modelJson: JSON.stringify(updatedModelJson),
      };
    });

    submitData.status = editData.value.id ? editData.value.status : 'ENABLE';

    if (editData.value.id) {
      await V1ManageTranProjectPost(submitData);
    } else {
      await V1ManageTranProjectPut(submitData);
    }

    emit('success', submitData);
    openDrawer(false);
  } catch (error) {
    console.error('表单提交失败：', error);
  }
};
// 防抖处理
const [debounceSubmit] = useDebounce(handleSubmit, 500);

// 重置表单
const resetForm = async () => {
  resetFields();
  modelList.value = [
    {
      modelCode: null,
      modelJsonObj: {
        isIndicator: 1,
        isQualified: true,
        isActionQualified: false,
      },
    },
  ];
  modelChange();
  clearValidate(['modelList']);
};

// 设置表单数据
const setFormData = async (data: Record<string, any>) => {
  await nextTick();
  // 关联算法设置
  if (!data.modelList || !data.modelList.length) {
    validateAlgorithms();
    clearValidate(['modelList']);
  } else {
    data.modelList?.filter((item) => {
      let jsonData = item.modelJson
        ? JSON.parse(item.modelJson)
        : {
            isIndicator: 1,
            isQualified: true,
            isActionQualified: false,
          };
      jsonData.isIndicator = jsonData.isQualified ? 1 : jsonData.isActionQualified ? 2 : 1;
      item.modelJsonObj = jsonData;
    });
    modelList.value = cloneDeep(data.modelList);
  }
  // 工位转换
  if (data.locationList?.length) {
    data.locationIds = data.locationList?.map((item) => item.id);
  }

  editData.value = data;
  setFieldsValue(data);

  // 如果是考核或比赛项目，且是编辑状态，禁用 name 字段
  if ((props.projectType === 2 || props.projectType === 3) && data.id) {
    nameDisabled.value = true;
  } else {
    nameDisabled.value = false;
  }

  modelChange();
};

// 名称选项
const nameOptions = ref<any[]>([]);
const formModel = ref<any>({});

// 获取名称选项
const fetchNameOptions = async () => {
  const res = await V1ManageTranProjectListPost({
    projectType: 1,
  });
  nameOptions.value = res.map((item: any) => ({
    label: item.name,
    value: item.name,
    key: item.id,
    countAlgorithm: item.countAlgorithm,
  }));
};

// 处理名称变化
const handleNameChange = (value: string, option: any) => {
  clearValidate(['name']);
  setFieldsValue({
    countAlgorithm: option.countAlgorithm || '',
  });
};

// 初始化时获取名称选项
fetchNameOptions();

defineExpose({
  openDrawer,
  setFormData,
  resetForm,
});
</script>

<style lang="less" scoped>
.algorithm-list {
  .algorithm-type-selector {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .algorithm-type-label {
      margin-right: 8px;
      min-width: 70px;
    }
  }

  .algorithm-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .checkbox-group {
      margin: 0 16px;
    }

    .delete-btn {
      margin-left: auto;
      --ignore-dark-color:rgb(248, 116, 112);
      border-color:var(--ignore-dark-color);
    }
  }
}
:deep(.cddc-ant-form-item-control-input-content) {
  > div > div {
    width: 100%;
  }
}
</style>
