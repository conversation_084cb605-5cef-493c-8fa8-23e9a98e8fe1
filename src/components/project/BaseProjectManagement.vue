<template>
  <div class="project-management-container">
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <!-- <BaseConfirm
              v-if="projectType !== 1"
              @confirm.stop="handleDelete(record)"
            ></BaseConfirm> -->
          </a-space>
        </template>
        <template v-else-if="column.dataIndex === 'status'">
          <a-switch
            size="small"
            :checked="record.status === 'ENABLE'"
            @change="handleDeactivate(record)"
          />
        </template>
        <slot v-else name="customCell" :column="column" :record="record"></slot>
      </template>
      <template #toolbar>
        <a-button type="primary" ghost @click="handleAdd">新增{{ projectName }}</a-button>
        <slot name="extraToolbar"></slot>
      </template>
    </BasicTablePlus>

    <component
      :is="drawerComponent"
      ref="drawerRef"
      :title="drawerTitle"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { useProjectManagement } from '/@/composables/useProjectManagement';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { ref } from 'vue';

interface Props {
  projectType: 1 | 2 | 3;
  projectName: string;
  columns: EnhancedColumn[];
  drawerComponent: any;
}

const props = defineProps<Props>();
const drawerRef = ref();

const {
  drawerTitle,
  registerTable,
  handleAdd,
  handleEdit,
  handleDeactivate,
  handleDelete,
  handleSuccess,
} = useProjectManagement({
  projectType: props.projectType,
  projectName: props.projectName,
  columns: props.columns,
  drawerRef,
});
</script>

<style lang="less" scoped>
.project-management-container {
  padding: 10px 8px;
  background: #fff;
}
</style>
