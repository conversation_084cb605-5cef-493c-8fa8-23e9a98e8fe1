<template>
  <BasicModal
    @register="registerModal"
    title="刷卡绑定"
    :showCancelBtn="true"
    :maskClosable="false"
    centered
    destroyOnClose
    :width="400"
    @cancel="handleModalClose"
    @ok="handleConfirmBind"
  >
    <div class="card-bind-modal" :class="{ 'has-info': cardInfo }">
      <div class="modal-content">
        <div class="card-status">
          <IdcardOutlined class="card-icon" />
          <p class="card-text">{{ cardInfo ? '已读取卡片信息' : '请刷身份证或工卡...' }}</p>
        </div>
        <div class="card-info" v-if="cardInfo">
          <div class="info-row">
            <span class="info-label">卡号</span>
            <span class="info-value">{{ cardInfo.cardNo }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import { useWebSocket } from '/@/utils/websocket';
import { IdcardOutlined } from '@geega-ui-plus/icons-vue';
import { DeviceBindingCardPost, V1ManageSysUserBindCardPost } from '/@/api/cddc.req';
import { useGlobSetting } from '/@/hooks/setting';

interface CardInfo {
  cardNo: string;
  cardType?: string;
  employeeId: string;
  deviceIp?: string;
}

interface Props {
  userId: string | number;
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
});

const emit = defineEmits(['update:modelValue', 'success']);

const { createMessage } = useMessage();
const { socketUrl } = useGlobSetting();
const wsInstance = ref<any>(null);
const cardInfo = ref<CardInfo | null>(null);

const [registerModal, { openModal }] = useModal();

// 初始化绑定WebSocket
const initBindWebSocket = () => {
  if (!props.userId) {
    createMessage.error('连接失败：用户ID不存在');
    return;
  }

  cleanupBind();
  // let socketUrl = '*************:19090'; //test
  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    userId: props.userId,
    onMessage: handleBindMessage,
    onError: (error) => {
      console.error('WebSocket错误:', error);
      cleanupBind();
      createMessage.error('连接失败，请重试');
      setTimeout(() => {
        closeModal();
      }, 2000);
    },
    onClose: () => {
      console.log('WebSocket连接已关闭');
    },
    onOpen: () => {
      console.log('WebSocket连接已建立');
      console.log('当前用户ID:', props.userId);
    },
    createMessage: {
      action: 'swipeCardLogin',
      content: {
        userId: props.userId,
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {
        userId: props.userId,
      },
    },
  });

  wsInstance.value = ws;
  ws.connect();
};

// 处理绑定消息
const handleBindMessage = (data: any) => {
  if (data.status === 'success') return;
  if (data.cardNo) {
    data.employeeId = data.employeeId || props.userId || '';
    cardInfo.value = data;
    createMessage.success('读卡成功，请确认绑定');
  } else {
    createMessage.error(data.message || '读卡失败');
    cardInfo.value = null;
  }
};

// 清理绑定相关资源
const cleanupBind = () => {
  try {
    if (wsInstance.value) {
      wsInstance.value.disconnect();
      wsInstance.value = null;
    }
  } catch (error) {
    console.error('断开 WebSocket 连接时发生错误:', error);
  }
};

// 弹框关闭处理函数
const handleModalClose = () => {
  cleanupBind();
  cardInfo.value = null;
};

// 确认绑定处理函数
const handleConfirmBind = async () => {
  if (!cardInfo.value) {
    createMessage.warning('请先刷卡');
    return;
  }

  try {
    // 发送后端绑定请求
    const isBind = await V1ManageSysUserBindCardPost({
      id: String(props.userId),
      cardNo: cardInfo.value.cardNo,
    });

    // 后端需要分别绑定一下
    const isBind2 = await DeviceBindingCardPost({
      cardNo: cardInfo.value.cardNo,
      employeeId: String(props.userId)
    })

    if (isBind && isBind2) {
      createMessage.success('人员绑定成功');
      cleanupBind();
      closeModal();
      emit('success');
    } else {
      createMessage.error('人员绑定失败');
    }
  } catch (error) {
    createMessage.error('绑定请求发送失败');
  }
};
// 暴露方法给父组件
const open = () => {
  if (!props.userId) {
    createMessage.error('用户ID不存在');
    return;
  }
  openModal();
  initBindWebSocket();
};

defineExpose({
  open,
});

onUnmounted(() => {
  cleanupBind();
});

const closeModal = () => {
  openModal(false);
  cardInfo.value = null;
};
</script>

<style lang="less" scoped>
.card-bind-modal {
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;

  .modal-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &.has-info {
    .card-status {
      padding: 24px;

      .card-icon {
        font-size: 40px;
      }
    }
  }

  .card-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    width: 100%;
    background: rgba(0, 153, 107, 0.05);
    border: 1px solid rgba(0, 153, 107, 0.15);
    border-radius: 12px;
    transition: all 0.3s ease;

    .card-icon {
      font-size: 48px;
      color: #00996b;
      margin-bottom: 16px;
      animation: pulse 1.5s infinite;
      filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
    }

    .card-text {
      margin: 0;
      color: #333;
      font-size: 15px;
      font-weight: 500;
    }
  }

  .card-info {
    width: 100%;
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;

    .info-row {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        width: 60px;
        color: #666;
        font-size: 14px;
      }

      .info-value {
        flex: 1;
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
