<script lang="ts" setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { message, type UploadFile } from '@geega-ui-plus/ant-design-vue';
import { uploadApi, type UploadApi } from './utils';
import { Upload, type UploadProps } from '@geega-ui-plus/ant-design-vue/es';
import type { UploadRealFile } from './types';
import { convertFileSize } from '/@/components/ImportFile/file';
import uploadIcon from '/@/assets/svg/upload-icon.svg';
import { LinkOutlined } from '@geega-ui-plus/icons-vue';

export interface UploadFileProps {
  /**
   * 上传的 API
   */
  api?: UploadApi;
  /**
   * 支持的文件类型，默认 `.jpg,.jpeg,.png,.gif,.bmp`
   */
  accept?: string;
  /**
   * 支持上传的文件大小，单位 KB，默认不限制
   */
  maxSize?: number;
  /**
   * 最多支持上传的文件个数，默认不限制
   */
  maxCount?: number;
  /**
   * 是否多选，默认 true
   */
  multiple?: boolean;
  /**
   * 文件列表
   */
  modelValue?: UploadRealFile[];

  /**
   * 业务类型
   */
  businessType?: string;

  disabled?: boolean;

  fileItemActionBtns?: {
    btnName: string;
    key: string;
    hidden?: boolean;
  }[];

  fileItemClick?: (key: string, file?: UploadFile, actions?: any) => any;
  fileNameClick?: (file: UploadFile, actions?: any) => any;
}

const props = withDefaults(defineProps<UploadFileProps>(), {
  accept: '.jpg,.jpeg,.png,.gif,.bmp',
  multiple: true,
  businessType: 'product',
  maxSize: 20 * 1024,
});

const emit = defineEmits(['update:modelValue', 'upload-success']);

const files = useVModel(props, 'modelValue');

const customRequest: UploadProps['customRequest'] = async (opt) => {
  const req = props.api || uploadApi;

  try {
    const file = opt.file as File;
    // 确保后缀为小写
    const fixedName = file.name.replace(/\.\w+$/, (n) => n.toLowerCase());
    const fixedFile = new File([file], fixedName, { type: file.type });

    const info = await req(fixedFile, { businessType: props.businessType }, opt);

    // 上传成功后触发事件，将文件信息传递给父组件
    emit('upload-success', info);

    opt.onSuccess?.(info);
  } catch (error: any) {
    const err = error instanceof Error ? error : new Error(error);

    opt.onError?.(err);
  }
};

const checkBeforeUpload: UploadProps['beforeUpload'] = (file, fileList) => {
  const valid = isValid();

  if (!valid) {
    return Upload.LIST_IGNORE;
  }

  return true;

  function isValid() {
    const INVALID = false;

    const currentFileCount = files.value?.length || 0;
    const currentFileIdx = fileList.indexOf(file);
    const restCount = (props.maxCount || Infinity) - (currentFileCount + currentFileIdx + 1);

    if (restCount < 0) {
      message.warn(`文件上传个数最多支持 ${props.maxCount} 个`);
      return INVALID;
    }

    const ext = file.name.split(/\./g).pop();

    const accepts = (props.accept || '').split(',')

    if (!ext || !accepts.some(o => o == '.' + ext.toLowerCase())) {
      message.warn(`请上传 ${props.accept} 格式的文件`);
      return INVALID;
    }

    if (props.maxSize && file.size / 1024 > props.maxSize) {
      message.warn(`请上传小于 ${convertFileSize(props.maxSize)} 的文件`);

      return INVALID;
    }

    if (file.size === 0) {
      message.warn(`文件大小为 0，请重新选择文件`);

      return INVALID;
    }

    return true;
  }
};

const uploadBtnIf = computed(
  () => (props.maxCount == null || (files.value?.length || 0) < props.maxCount) && !props.disabled
);

const tipText = computed(
  () =>
     `支持格式：${props.accept.split(',').join('、')}, 单个文件不能超过${
       props.maxSize! >= 1024 * 1024
         ? (props.maxSize! / (1024 * 1024)) + 'GB'
         : (props.maxSize! / 1024) + 'MB'
     }`
);
</script>

<template>
  <div class="upload-image">
    <a-upload
      v-model:fileList="files"
      :customRequest="customRequest"
      :multiple="multiple"
      :accept="accept"
      :before-upload="checkBeforeUpload"
      list-type="text"
      v-bind="$attrs"
      :show-upload-list="{
        showRemoveIcon: !disabled,
      }"
    >
      <slot>
        <div v-if="uploadBtnIf" class="upload-btn-wrap w-100% p-20px">
          <p class="flex justify-center pb-10px">
            <img class="w-50px" :src="uploadIcon" />
          </p>
          <div class="font-size-15px"
            >将文件拖到此处或<span class="color-[#00996b]">点击上传</span></div
          >
          <div class="color-#999 pt-10px font-size-13px select-none">
            {{ tipText }}
          </div>
        </div>
      </slot>
      <template v-if="fileItemActionBtns?.length" #itemRender="{ file, actions }">
        <div class="flex items-center select-none mt-1">
          <div
            @click="fileNameClick?.(file, actions)"
            :class="{
              'color-red': file.status === 'error',
              'cursor-pointer': fileNameClick,
              'text-[#00996b]': fileNameClick,
              underline: fileNameClick,
            }"
            class="pr-30px flex flex-1 w-0 truncate items-center"
          >
            <LinkOutlined class="flex!" />
            <div class="pl-5px break-all">{{ file.name }}</div>
          </div>
          <template v-for="btn in fileItemActionBtns">
            <div
              class="pr-10px cursor-pointer color-[#00996b] break-keep"
              v-if="!btn.hidden"
              @click="fileItemClick?.(btn.key, file, actions)"
            >
              {{ btn.btnName }}
            </div>
          </template>
          <div v-if="file.status === 'error'" class="pr-10px cursor-pointer color-[red]">
            上传失败!
          </div>
        </div>
        <div class="flex items-center" v-if="file.status === 'uploading'">
          <div class="bg-[#ccc] h-4px w-[calc(100%-50px)]">
            <div class="h-100% bg-[#00996b]" :style="{ width: `${file.percent || 0}%` }"></div>
          </div>
          <div class="flex-1 flex justify-end color-[#999]"> {{ file.percent || 0 }}% </div>
        </div>
      </template>
    </a-upload>
  </div>
</template>

<style lang="less" scoped>
:deep(.cddc-ant-upload) {
  width: 100%;
}
.upload-btn-wrap {
  text-align: center;
  background: #e9f6f2;
  border: 1px dashed #bfbfbf;
  border-radius: 2px;
  cursor: pointer;
  &:hover {
    border: 1px dashed #00c07f;
  }
}
</style>
