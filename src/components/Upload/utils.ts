import type { UploadRequestOption } from '@geega-ui-plus/ant-design-vue/es/vc-upload/interface';
import type { UploadProgressEvent } from '@geega-ui-plus/ant-design-vue/es/vc-upload/interface';
import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();

export type UploadApi = (
  file: File,
  requestParams: { businessType: string },
  opt?: UploadRequestOption
) => Promise<UploadedResponse>;

export type ResponseFile = {
  fileId: string;
  url: string;
};

export interface UploadedResponse {
  uid?: string;
  id?: string;
  url?: string;
  name: string;
}

export const uploadApi: UploadApi = async (file, requestParams, opt) => {
  const form = new FormData();

  form.set('file', file);

  try {
    const resp =
      (
        await defHttp.getAxios().request({
          url: `${globSetting.urlPrefix}/v1/common/file-upload/${requestParams.businessType}`,

          method: 'POST',
          data: form,
          headers: {
            'Content-type': ContentTypeEnum.FORM_DATA,
            ignoreCancelToken: true,
          },
          timeout: 1000 * 60 * 5, // 5分钟
          onUploadProgress(e: UploadProgressEvent) {
            if (e.total! > 0) {
              // 保留两位小数
              e.percent = Number(((e.loaded! / e.total!) * 100).toFixed(2));
            }

            opt?.onProgress?.(e);
          },
        })
      ).data.result || ({} as any);

    const uid = resp.id;

    return {
      uid,
      id: uid,
      url: resp.url,
      name: resp.fileName,
    };
  } catch (e: any) {
    // 处理上传失败的情况，提取详细错误信息
    let errorMessage = '文件上传失败';
    if (e?.response?.data?.message) {
      errorMessage = e.response.data.message;
    } else if (e?.response?.data?.error) {
      errorMessage = e.response.data.error;
    } else if (e?.message) {
      errorMessage = e.message;
    } else if (e?.code === 'ECONNABORTED') {
      errorMessage = '上传超时，请检查网络连接或重试';
    } else if (e?.code === 'NETWORK_ERROR') {
      errorMessage = '网络错误，请检查网络连接';
    }

    const error = new Error(errorMessage);

    // 将原始错误信息也保存在 error 对象中，方便调试
    if (e?.response) {
      (error as any).response = e.response;
      (error as any).status = e.response.status;
    }

    opt?.onError?.(error);
    throw error;
  }
};
