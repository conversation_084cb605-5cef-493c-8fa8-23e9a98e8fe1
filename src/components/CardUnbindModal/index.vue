<template>
  <BasicModal
    @register="registerModal"
    title="解绑"
    :showCancelBtn="true"
    :maskClosable="false"
    centered
    destroyOnClose
    :width="420"
    :minHeight="150"
    @cancel="handleModalClose"
    @ok="handleConfirmUnbind"
  >
    <div class="card-unbind-modal">
      <div class="unbind-info">
        <div class="info-item">
          <span class="label">人员姓名:</span>
          <span>{{ userName }}</span>
        </div>
        <div class="info-item" v-for="(card, index) in cardNos" :key="index">
          <span class="label">绑定卡号:</span>
          <span>{{ card }}</span>
          <a-button
            type="link"
            class="unbind-btn"
            :class="{ 'unbind-marked': cardsToUnbind.includes(card) }"
            @click="toggleUnbind(card)"
          >
            {{ cardsToUnbind.includes(card) ? '已解绑' : '解绑' }}
          </a-button>
        </div>

        <div v-if="cardNos.length === 0" class="empty-state">
          <IdcardOutlined class="empty-icon" />
          <p>没有绑定的卡片</p>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import { IdcardOutlined } from '@geega-ui-plus/icons-vue';
import { DeviceDeleteCardNoCardNoPost, V1ManageSysUserUnbindCardPost } from '/@/api/cddc.req';

interface Props {
  userId: string | number;
  userName?: string;
  cardNoList?: string[];
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  userName: '',
  cardNoList: () => [],
  onSuccess: () => {},
});

const emit = defineEmits(['success']);

const { createMessage } = useMessage();
const cardNos = ref<string[]>([]);
const userName = ref<string>('');
const cardsToUnbind = ref<string[]>([]);
const [registerModal, { openModal }] = useModal();

watch(
  () => props.cardNoList,
  (val) => {
    cardNos.value = [...(val || [])];
  },
  { deep: true }
);

watch(
  () => props.userName,
  (val) => {
    userName.value = val || '';
  }
);

// 切换卡片解绑状态
const toggleUnbind = (cardNo: string) => {
  if (cardsToUnbind.value.includes(cardNo)) {
    // 取消解绑
    cardsToUnbind.value = cardsToUnbind.value.filter((card) => card !== cardNo);
  } else {
    // 标记为解绑
    cardsToUnbind.value.push(cardNo);
  }
};

// 弹框关闭处理函数
const handleModalClose = () => {
  closeModal();
};

// 确认解绑处理函数
const handleConfirmUnbind = async () => {
  if (!props.userId) {
    createMessage.error('用户ID不存在');
    return;
  }

  if (cardsToUnbind.value.length === 0) {
    createMessage.warning('没有需要解绑的卡片');
    return;
  }

  try {
    // 依次调用两个解绑接口
    await Promise.all(
      cardsToUnbind.value.map(async (cardNo) => {
        await DeviceDeleteCardNoCardNoPost({ cardNo });
        await V1ManageSysUserUnbindCardPost({
          id: String(props.userId),
          cardNo,
        });
      })
    );

    createMessage.success('解绑成功');
    closeModal();
    emit('success');
  } catch (error) {
    createMessage.error('解绑请求发送失败');
  }
};

// 暴露方法给父组件
const open = (userData?: { id?: string | number; name?: string; cardNoList?: string[] }) => {
  if (userData) {
    if (userData.name) {
      userName.value = userData.name;
    }
    if (userData.cardNoList) {
      cardNos.value = [...userData.cardNoList];
    }
  }
  // 重置解绑状态
  cardsToUnbind.value = [];
  openModal();
};

defineExpose({
  open,
});

const closeModal = () => {
  openModal(false);
  cardsToUnbind.value = [];
};
</script>

<style lang="less" scoped>
.card-unbind-modal {
  padding: 12px 0;

  .unbind-info {
    .info-item {
      display: flex;
      margin-bottom: 16px;
      align-items: center;

      .label {
        width: 70px;
        margin-right: 8px;
        color: rgba(0, 0, 0, 0.65);
      }

      .unbind-btn {
        margin-left: auto;
        color: #ff4d4f;

        &.unbind-marked {
          color: #999;
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px;
      color: rgba(0, 0, 0, 0.45);

      .empty-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
