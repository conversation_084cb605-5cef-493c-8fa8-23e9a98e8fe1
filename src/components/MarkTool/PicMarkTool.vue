<script lang="ts" setup>
import { Icon } from '@geega-ui-plus/geega-ui';
import { useVModel, type Position } from '@vueuse/core';
import { computed, reactive, ref } from 'vue';
import DraggableDot from './DraggableDot.vue';
import { clamp } from 'lodash-es';

export interface PicMarkToolProps {
  src: string;
  modelValue?: number[][];
  maxPointCount?: number;
}

const imgEl = ref<HTMLImageElement>();
const props = defineProps<PicMarkToolProps>();

const state = reactive({
  imgClientSize: {
    width: 0,
    height: 0,
  },
  imgNativeSize: {
    width: 0,
    height: 0,
  },
});

const emit = defineEmits(['update:modelValue', 'reload-image']);

const colors = {
  edge: 'rgba(0, 0, 255, 1)',
  area: 'rgba(0, 255, 0, 0.4)',
  dot: 'rgba(255, 0, 0, 1)',
};

const value = useVModel(props, 'modelValue', emit, { defaultValue: [] });

function updateImageSize() {
  const img = imgEl.value;
  if (img?.naturalWidth) {
    state.imgClientSize = {
      width: img.clientWidth,
      height: img.clientHeight,
    };
    state.imgNativeSize = {
      width: img.naturalWidth,
      height: img.naturalHeight,
    };
  } else {
    state.imgClientSize = {
      width: 0,
      height: 0,
    };
    state.imgNativeSize = {
      width: 0,
      height: 0,
    };
  }
}

function imgCoordToDrawCoord(points: number[][]) {
  if (!props.src) return points;
  if (!state.imgClientSize.width) return points;

  return points.map((point) => {
    const xPercent = point[0] / state.imgNativeSize.width;
    const yPercent = point[1] / state.imgNativeSize.height;

    return [xPercent * state.imgClientSize.width, yPercent * state.imgClientSize.height];
  });
}

function drawCoordToImgCoord(points: number[][]) {
  if (!props.src) return points;
  if (!state.imgClientSize.width) return points;

  return points.map((point) => {
    let xPercent = point[0] / state.imgClientSize.width;
    let yPercent = point[1] / state.imgClientSize.height;

    xPercent = clamp(xPercent, 0, 1);
    yPercent = clamp(yPercent, 0, 1);

    return [xPercent * state.imgNativeSize.width, yPercent * state.imgNativeSize.height];
  });
}

const relativePoints = computed(() => imgCoordToDrawCoord(value.value || []));

const svgAreaPath = computed(() => {
  const d = relativePoints.value
    .map(([x, y], idx) => (idx == 0 ? `M${x},${y}` : `L${x},${y}`))
    .join(' ');

  return d ? `${d} z` : '';
});

function handleDrawEvent(evt: MouseEvent) {
  const img = imgEl.value;
  if (!img) return;

  const point = drawCoordToImgCoord([[evt.offsetX, evt.offsetY]]);

  const oldValue = value.value || [];

  const modelValue = [...oldValue, ...point];

  if (props.maxPointCount && modelValue.length > props.maxPointCount) {
    return;
  }

  value.value = modelValue;
}

function clearPoints() {
  value.value = [];
}

function onPointMove(index: number, pos: Position, evt: MouseEvent) {
  const pointToChange = relativePoints.value.at(index);

  if (!pointToChange) {
    return;
  }

  if (!state.imgNativeSize.width) return;

  const newPoint = drawCoordToImgCoord([[pointToChange[0] + evt.movementX, pointToChange[1] + evt.movementY]])[0]

  const newPoints = [...value.value || []]

  newPoints.splice(index, 1, newPoint);
  value.value = newPoints;
}
</script>

<template>
  <div class="pic-mark-tool">
    <div class="draw-tools mb-2">
      <div class="icon" @click="clearPoints">
        <Icon icon="carbon:trash-can" />
        <span>重置</span>
      </div>
      <div class="icon" @click="emit('reload-image')">
        <Icon icon="carbon:reset" />
        <span>重新获取相机图片</span>
      </div>
    </div>
    <div class="draw-area relative">
      <img ref="imgEl" class="mark-image" draggable="false" :src="src" @click="handleDrawEvent"
        @load="updateImageSize" />
      <div class="points pointer-events-none absolute top-0 left-0 size-full">
        <svg ns="http://www.w3.org/2000/svg" class="size-full" v-if="src">
          <path :d="svgAreaPath" :fill="colors.area" :stroke="colors.edge" />
          <template v-for="(point, index) in relativePoints" :key="index">
            <DraggableDot class="pointer-events-auto cursor-move" tag="circle"
              @dragging="(pos, evt) => onPointMove(index, pos, evt)" :value="point" :cx="point[0]" :cy="point[1]" :r="5"
              :fill="colors.dot" />
          </template>
        </svg>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.draw-area {
  aspect-ratio: 96 / 54;
}

.mark-image {
  display: inline-block;
  object-fit: scale-down;
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;

  &::before {
    content: '加载中...';
    font-size: 20px;
    width: 100%;
    height: 100%;
    background-color: rgba(204, 204, 204);

    display: flex;
    align-items: center;
    justify-content: center;

    position: absolute;
    top: 50%;
    left: 50%;

    transform: translate(-50%, -50%);
  }
}

.draw-tools {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;

  .icon {
    color: #999;
    border-radius: 1px;
    border: 1px solid currentColor;
    cursor: pointer;

    @size: 30px;
    // width: @size;
    padding: 0 4px;
    height: @size;

    display: flex;
    align-items: center;
    justify-content: center;

    &:hover,
    &.active {
      color: @primary-color;
    }
  }
}
</style>
