<script lang="ts" setup>
import { useDraggable } from '@vueuse/core';
import { ref } from 'vue';

export interface DraggableDotProps {
  value: number[];
  tag?: string;
}

const props = defineProps<DraggableDotProps>();

const el = ref();

const emit = defineEmits(['dragging']);

useDraggable(el, {
  preventDefault: true,
  stopPropagation: true,
  onMove(position, event) {
    emit('dragging', position, event);
  },
});
</script>

<template>
  <component ref="el" :is="tag || 'div'" />
</template>

<style lang="less" scoped></style>
