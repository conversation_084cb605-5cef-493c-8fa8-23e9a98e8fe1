<template>
  <div :class="prefixCls">
    <span class="title">
      <slot>
        {{ title }}
      </slot>
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import { useDesign } from '/@/hooks/web/useDesign';
import i18nVue from '@geega/i18n-vue';
export default defineComponent({
  props: {
    title: {
      type: String,
      default: i18nVue.t('components.GeegaBasicTitle.src.BasicTitle.5dd8e09c', {
        defaultValue: '基本信息',
      }),
    },
    first: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { prefixCls } = useDesign('title-wrapper');
    return {
      ...toRefs(props),
      prefixCls,
    };
  },
});
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-title-wrapper';

.@{prefix-cls} {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 12px 0 12px 0;

  &::before {
    display: inline-block;
    width: 3px;
    height: 12px;
    margin-right: 8px;
    background: @primary-color;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    content: ' ';
  }

  .title {
    margin-top: 0;
    font-size: 14px;
    color: @heading-color;
  }
}
</style>
