<template>
  <div class="camera-box">
    <div class="title">{{ title }}</div>
    <div class="video-container">
      <div v-if="showStatusTag" class="status-tag" :class="statusClass"></div>
      <video :id="videoId" autoplay playsinline muted></video>
      <div v-if="isConnecting" class="camera-loading">
        <a-spin tip="摄像头连接中..." />
      </div>
      <!-- 连接失败提示 -->
      <div v-if="connectionFailed" class="connection-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">摄像头连接失败</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { createLocalStorage } from '/@/utils/cache';
import { V1OpenApiGetUrlStream_channel } from '/@/api/cddc.req';
import WebRtcStreamer from '/@/utils/helper/webrtcstreamer';
import { useGlobSetting } from '/@/hooks/setting';
export default defineComponent({
  name: 'CameraView',
  props: {
    title: {
      type: String,
      default: '摄像头',
    },
    showStatusTag: {
      type: Boolean,
      default: false,
    },
    statusResult: {
      type: Number,
      default: -1,
    },
  },
  emits: ['connected', 'disconnected', 'error'],
  setup(props, { emit }) {
    const isConnecting = ref(false);
    const isTimeout = ref(false);
    const connectionFailed = ref(false);
    const retryCount = ref(0);
    const maxRetries = 3;
    const videoId = `camera-${Math.random().toString(36).substring(2, 11)}`;
    let webRtcServer: InstanceType<typeof WebRtcStreamer> | null = null;
    let retryTimer: number | null = null;
    let connectionTimeoutTimer: number | null = null;
    let { webrtcUrl } = useGlobSetting();
    let videoEventHandlers: { playing?: () => void; error?: () => void } = {};
    const statusClass = computed(() => {
      switch (props.statusResult) {
        case 1:
          return 'success';
        case 0:
          return 'error';
        case 2:
          return 'warning';
        default:
          return '';
      }
    });

    /**
     * 清理视频连接和事件监听器
     */
    const cleanupVideoConnection = () => {
      // 清理WebRTC连接
      if (webRtcServer) {
        webRtcServer.disconnect();
        webRtcServer = null;
      }

      // 清理视频元素事件监听器
      const videoElement = document.getElementById(videoId) as HTMLVideoElement;
      if (videoElement) {
        if (videoEventHandlers.playing) {
          videoElement.removeEventListener('playing', videoEventHandlers.playing);
        }
        if (videoEventHandlers.error) {
          videoElement.removeEventListener('error', videoEventHandlers.error);
        }

        // 清理视频流
        if (videoElement.srcObject) {
          try {
            const stream = videoElement.srcObject as MediaStream;
            stream.getTracks().forEach(track => {
              track.stop();
            });
            videoElement.srcObject = null;
          } catch (error) {
            console.warn('清理视频元素时出错:', error);
          }
        }
      }

      // 重置事件处理器
      videoEventHandlers = {};
    };

    const getRtspUrl = async (isRetry = false) => {
      try {
        // 清理所有定时器
        if (retryTimer) {
          clearTimeout(retryTimer);
          retryTimer = null;
        }
        if (connectionTimeoutTimer) {
          clearTimeout(connectionTimeoutTimer);
          connectionTimeoutTimer = null;
        }

        if (!isRetry) {
          retryCount.value = 0;
        }

        isConnecting.value = true;
        isTimeout.value = false;
        connectionFailed.value = false;

        console.log(`摄像头开始连接 (尝试 ${retryCount.value + 1}/${maxRetries})`);

        const ls = createLocalStorage();
        const locationId = ls.get('locationId');
        if (!locationId) {
          message.error('缺少工位ID');
          handleConnectionFailure();
          return;
        }

        const streamUrl = await V1OpenApiGetUrlStream_channel({ channel: locationId });
        console.log('获取视频流地址:', streamUrl);

        if (!streamUrl || typeof streamUrl !== 'string') {
          console.error('获取视频流地址失败：返回值无效', streamUrl);
          handleConnectionFailure();
          return;
        }
        const ip = streamUrl.match(/rtsp:\/\/(.*?):/)?.[1];
        if (!ip) {
          console.error('解析视频流地址失败:', streamUrl);
          handleConnectionFailure();
          return;
        }
        const rtcUrl = webrtcUrl || `http://${ip}:33001`;

        // 清理现有连接和事件监听器
        cleanupVideoConnection();

        // 添加延迟确保资源完全释放
        await new Promise(resolve => setTimeout(resolve, isRetry ? 1000 : 100));

        webRtcServer = new WebRtcStreamer(videoId, rtcUrl);

        const videoElement = document.getElementById(videoId) as HTMLVideoElement;
        if (videoElement) {
          const handlePlaying = () => {
            if (!isTimeout.value) {
              console.log('摄像头WebRTC视频流连接成功');
              retryCount.value = 0; // 重置重试计数
              emit('connected');
              isConnecting.value = false;
              connectionFailed.value = false;
              // 清理连接超时定时器
              if (connectionTimeoutTimer) {
                clearTimeout(connectionTimeoutTimer);
                connectionTimeoutTimer = null;
              }
            }
          };

          const handleError = () => {
            console.error('摄像头WebRTC视频流连接失败');
            handleConnectionFailure();
          };

          // 保存事件处理器引用以便后续清理
          videoEventHandlers.playing = handlePlaying;
          videoEventHandlers.error = handleError;

          // 添加事件监听器
          videoElement.addEventListener('playing', handlePlaying);
          videoElement.addEventListener('error', handleError);
        }

        // 设置连接超时
        connectionTimeoutTimer = window.setTimeout(() => {
          if (isConnecting.value) {
            console.warn('摄像头连接超时');
            handleConnectionFailure();
          }
        }, 15000); // 15秒超时

        webRtcServer.connect(streamUrl, '', '', '', '');

        return streamUrl;
      } catch (error) {
        console.error('获取视频流失败:', error);
        handleConnectionFailure();
        return null;
      }
    };

    /**
     * 处理连接失败，包含重试逻辑
     */
    const handleConnectionFailure = () => {
      isTimeout.value = true;
      isConnecting.value = false;

      // 清理连接超时定时器
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer);
        connectionTimeoutTimer = null;
      }

      // 清理连接
      cleanupVideoConnection();

      // 检查是否需要重试
      if (retryCount.value < maxRetries) {
        retryCount.value++;
        console.log(`摄像头准备重试连接 (${retryCount.value}/${maxRetries})`);

        // 延迟重试，避免频繁请求
        retryTimer = window.setTimeout(() => {
          getRtspUrl(true);
        }, 5000 * retryCount.value); // 递增延迟：5s, 10s, 15s
      } else {
        // 所有重试都失败了
        connectionFailed.value = true;
        emit('error');
        emit('disconnected');
        message.error('摄像头连接失败，请检查设备');
        console.error('摄像头连接失败，已达到最大重试次数');
      }
    };

    onMounted(() => {
      getRtspUrl();
    });

    onUnmounted(() => {
      console.log('CameraView组件卸载，清理资源');

      // 清理所有定时器
      if (retryTimer) {
        clearTimeout(retryTimer);
        retryTimer = null;
      }
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer);
        connectionTimeoutTimer = null;
      }

      // 清理视频连接
      cleanupVideoConnection();

      // 重置状态
      isConnecting.value = false;
      isTimeout.value = false;
      connectionFailed.value = false;
      retryCount.value = 0;
    });

    return {
      videoId,
      isConnecting,
      connectionFailed,
      statusClass,
    };
  },
});
</script>

<style lang="less" scoped>
.camera-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  border-radius: 4px;

  .title {
    margin-bottom: 2vh;
    font-size: 1.1vw;
    font-weight: 500;
    flex-shrink: 0;
  }

  .video-container {
    position: relative;
    aspect-ratio: 16/9;
    flex: 1;
    min-height: 0;
    background: #000;
    overflow: hidden;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .status-tag {
      position: absolute;
      top: 1vh;
      right: 1vw;
      font-size: 0.9vw;
      color: #fff;
      z-index: 99;
      width: 5.6vw;
      height: 5.6vw;

      &.success {
        background: url('@/assets/images/screen/train-icon-qualified.png') no-repeat center / 100%
          100%;
      }

      &.error {
        background: url('@/assets/images/screen/train-icon-unqualified.png') no-repeat center / 100%
          100%;
      }
      &.warning {
        background: url('@/assets/images/screen/train-icon-warning.png') no-repeat center / 100%
          100%;
      }
    }

    .camera-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.6);
      z-index: 10;

      :deep(.ant-spin) {
        color: #fff;

        .ant-spin-text {
          color: #fff;
          font-size: 14px;
          margin-top: 8px;
        }
      }
    }

    .connection-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10;

      .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .error-text {
        color: #fff;
        font-size: 16px;
        text-align: center;
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .camera-box {
    .title {
      margin-bottom: 1.6vh;
      font-size: 1vw;
    }

    .video-container {
      .status-tag {
        font-size: 0.8vw;
        width: 5vw;
        height: 5vw;
      }
    }
  }
}
</style>
