# BasicTablePlus

基于 BasicTable 的增强表格组件，提供了更便捷的配置方式和更多的功能特性。

## 特性

- 内置常用的默认配置
- 支持搜索表单与表格列的字段分离
- 支持控制列的显示/隐藏
- 默认集成操作列
- 简化的搜索表单配置

## 基础用法

```typescript
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';

// 定义列配置
const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'project',
    width: 160,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: [{ label: '项目A', value: '1' }],
      },
    },
  },
];

// 使用 useTable
const [registerTable] = useTable({
  columns,
  api: fetchData,
  tableProps: {
    showIndexColumn: false,
    useSearchForm: false,
  },
  beforeFetch: (params) => {
    return params;
  },
});
```

```vue
<template>
  <BasicTablePlus @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <a-button type="link" @click="handleView(record)">查看</a-button>
      </template>
    </template>
  </BasicTablePlus>
</template>
```

## API

### EnhancedColumn

| 参数      | 说明         | 类型                    | 默认值 |
| --------- | ------------ | ----------------------- | ------ |
| dataIndex | 列数据字段名 | string                  | -      |
| title     | 列标题       | string                  | -      |
| width     | 列宽度       | number                  | -      |
| show      | 是否显示列   | boolean                 | true   |
| search    | 搜索配置     | boolean \| SearchConfig | -      |

### SearchConfig

| 参数           | 说明                              | 类型   | 默认值      |
| -------------- | --------------------------------- | ------ | ----------- |
| field          | 搜索字段名（可与 dataIndex 不同） | string | dataIndex   |
| component      | 搜索组件类型                      | string | 'Input'     |
| componentProps | 搜索组件属性                      | object | -           |
| colProps       | 列布局配置                        | object | { span: 6 } |
| label          | 搜索标签文本                      | string | title       |

### TableOptions

| 参数        | 说明           | 类型                             | 默认值 |
| ----------- | -------------- | -------------------------------- | ------ |
| columns     | 列配置         | EnhancedColumn[]                 | []     |
| api         | 数据请求方法   | (...args: any[]) => Promise<any> | -      |
| beforeFetch | 请求前数据处理 | (params: any) => any             | -      |
| tableProps  | 表格属性       | object                           | {}     |
| formConfig  | 表单配置       | object                           | -      |
| showAction  | 是否显示操作列 | boolean                          | true   |

## 高级用法

### 1. 搜索字段与显示字段分离

```typescript
const columns: EnhancedColumn[] = [
  {
    title: '技能状态',
    dataIndex: 'skillStatus',
    search: {
      field: 'status', // 搜索时使用 status 字段
      component: 'Select',
      componentProps: {
        options: [{ label: '合格', value: '1' }],
      },
    },
  },
];
```

### 2. 隐藏列但保留搜索

```typescript
const columns: EnhancedColumn[] = [
  {
    title: '隐藏字段',
    dataIndex: 'hiddenField',
    show: false, // 表格中不显示
    search: {
      component: 'Input',
    },
  },
];
```

### 3. 自定义操作列

```vue
<template>
  <BasicTablePlus @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <a-space>
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
        </a-space>
      </template>
    </template>
  </BasicTablePlus>
</template>
```

### 4. 禁用操作列

```typescript
const [registerTable] = useTable({
  columns,
  showAction: false,
});
```

## 默认配置

组件内置了一些常用的默认配置，可以通过 tableProps 覆盖：

```typescript
const defaultOptions = {
  bordered: true,
  showIndexColumn: false,
  useSearchForm: true,
  showTableSetting: true,
  canResize: false,
  canColDrag: true,
  fetchSetting: {
    pageField: 'page',
    sizeField: 'pageSize',
    listField: 'records',
    totalField: 'totalRows',
  },
  // ... 更多配置
};
```
