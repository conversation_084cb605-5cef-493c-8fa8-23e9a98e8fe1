<template>
  <BasicTable
    @register="emitRegister"
    ref="tableRef"
    v-bind="$props"
    @resizeColumn="handleResizeColumn"
  >
    <template v-for="item in Object.keys($slots)" #[replaceTableSlotKey(item)]="data" :key="item">
      <slot :name="item" v-bind="data || {}"></slot>
    </template>
    <template #toolbar>
      <div class="tool-left">
        <slot name="toolbar"></slot>
      </div>
      <slot name="custColumn"> </slot>
    </template>
  </BasicTable>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { BasicTable } from '@geega-ui-plus/geega-ui';
import type { PropType } from 'vue';
import { resizeColumn } from './useTable';

export default defineComponent({
  name: 'BasicTablePlus',
  components: {
    BasicTable,
  },
  props: {
    api: {
      type: Function as PropType<(...args: any[]) => Promise<any>>,
      default: null,
    },
    tableProps: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const tableRef = ref(null);
    const emitRegister = (instance: any) => {
      emit('register', instance);
    };

    // 排除slot key
    const replaceTableSlotKey = (item) => {
      if (item === 'toolbar') {
        return '';
      } else {
        return item;
      }
    };

    const handleResizeColumn = (w, col) => {
      resizeColumn(tableRef.value, w, col);
    };

    return {
      tableRef,
      emitRegister,
      handleResizeColumn,
      replaceTableSlotKey,
    };
  },
});
</script>
