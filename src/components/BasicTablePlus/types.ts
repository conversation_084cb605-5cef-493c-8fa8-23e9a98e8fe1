import type { FormSchema, BasicColumn, BasicTableProps, FormProps } from '@geega-ui-plus/geega-ui';
import type { RowProps } from '@geega-ui-plus/ant-design-vue/lib/grid/Row';

export interface SearchConfig {
  field?: string;
  component: FormSchema['component'];
  slot?: string;
  componentProps?: FormSchema['componentProps'];
  colProps?: FormSchema['colProps'];
  label?: string;
}

export interface EnhancedColumn extends Omit<BasicColumn, 'dataIndex'> {
  dataIndex: string;
  show?: boolean;
  search?: boolean | SearchConfig;
}

export interface TableOptions {
  columns?: EnhancedColumn[];
  api?: (...args: any[]) => Promise<any>;
  beforeFetch?: (params: any) => any;
  tableProps?: Partial<BasicTableProps>;
  formConfig?: Partial<FormProps>;
  showAction?: boolean;
}

export interface DefaultTableOptions {
  rowKey: string;
  bordered: boolean;
  showIndexColumn: boolean;
  useSearchForm: boolean;
  showTableSetting: boolean;
  canResize: boolean;
  canColDrag: boolean;
  clickToRowSelect: boolean;
  resizeHeightOffset: number;
  pagination: {
    pageSize: number;
    pageSizeOptions: string[];
    defaultPageSize: number;
    showQuickJumper: boolean;
    showSizeChanger: boolean;
  };
  fetchSetting: {
    pageField: string;
    sizeField: string;
    listField: string;
    totalField: string;
  };
  tableSetting: {
    redo: boolean;
    size: boolean;
    setting: boolean;
    fullScreen: boolean;
  };
  formConfig: {
    layout: 'horizontal';
    labelWidth: string;
    labelAlign: 'left';
    baseColProps: { span: number };
    autoSubmitOnEnter: boolean;
    compact: boolean;
    rowProps: RowProps;
  };
}
