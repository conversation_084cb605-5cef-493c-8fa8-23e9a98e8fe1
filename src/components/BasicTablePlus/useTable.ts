import type { FormSchema } from '@geega-ui-plus/geega-ui';
import { useTable as originalUseTable } from '@geega-ui-plus/geega-ui';
import type { TableOptions, DefaultTableOptions, SearchConfig } from './types';

// 默认配置
const defaultOptions: DefaultTableOptions = {
  rowKey: 'id',
  bordered: true,
  showIndexColumn: false,
  useSearchForm: true,
  showTableSetting: true,
  canResize: true,
  canColDrag: true,
  clickToRowSelect: false, // 禁止点击行时自动选中
  resizeHeightOffset: 0,
  pagination: {
    pageSize: 20,
    pageSizeOptions: ['10', '20', '50', '100'],
    defaultPageSize: 20,
    showQuickJumper: true,
    showSizeChanger: true,
  },
  fetchSetting: {
    pageField: 'page',
    sizeField: 'pageSize',
    listField: 'records',
    totalField: 'totalRows',
  },
  tableSetting: {
    redo: false,
    size: false,
    setting: true,
    fullScreen: false,
  },
  formConfig: {
    layout: 'horizontal',
    labelWidth: 'auto',
    labelAlign: 'left',
    baseColProps: { span: 6 },
    autoSubmitOnEnter: true,
    compact: true,
    rowProps: {
      gutter: [12, 0],
      align: 'bottom',
    },
  },
};

// 接口查询参数
export function getQueryData() {
  return {
    currentPage: 1,
    pageSize: 20,
    totalRows: 0,
    totalPages: 0,
    sort: [],
    data: {},
  };
}

// 请求参数处理
export const handleParams = (params: any) => {
  const { page, pageSize, field, order, ...restParams } = params;
  const baseParams = {
    ...getQueryData(),
    currentPage: page,
    pageSize: pageSize || 20,
    data: {},
  };

  // 处理查询条件
  Object.keys(restParams).forEach((key) => {
    if (restParams[key] !== undefined && restParams[key] !== null && restParams[key] !== '') {
      baseParams.data[key] = restParams[key];
    }
  });

  // 处理排序
  if (field && order) {
    const sortDirection = { ascend: 'asc', descend: 'desc' }[order];
    if (sortDirection) {
      baseParams.sort = [{ field, direction: sortDirection }];
    }
  }

  return baseParams;
};

export const resizeColumn = async (tableRef, w: number, col: any) => {
  try {
    if (col && typeof w === 'number' && w > 50) {
      const columns = tableRef?.getColumns();
      if (columns) {
        const index = columns.findIndex((item) => item.dataIndex === col.dataIndex);
        if (index !== -1) {
          columns[index] = {
            ...columns[index],
            width: w,
          };
          tableRef?.setColumns(columns);
        }
      }
    }
  } catch (error) {
    console.error('Resize column error:', error);
  }
};

export function useTable(options: TableOptions = {}) {
  const {
    columns = [],
    api,
    tableProps = {},
    formConfig: customFormConfig,
    showAction = true,
  } = options;

  // 处理显示列和操作列
  const finalColumns = columns
    .filter((col) => col.show !== false)
    .map((column) => {
      const newColumn = { ...column };
      newColumn.ellipsis = true;
      // 设置默认对齐方式为左对齐
      if (!newColumn.align) {
        newColumn.align = 'left';
      }
      // 设置列是否可以拖拽调整宽度
      // 序号列和操作列不支持拖拽
      if (newColumn.dataIndex === 'index' || newColumn.dataIndex === 'action') {
        newColumn.resizable = false;
      } else {
        // 设置默认最大最小宽度
        newColumn.minWidth = 120;
        newColumn.maxWidth = 220;
        newColumn.resizable = true;
      }

      // 如果已经有customRender，就不覆盖
      if (!newColumn.customRender) {
        newColumn.customRender = ({ text }) => {
          // 检查值是否为空（null、undefined、空字符串）
          if (text === null || text === undefined || text === '') {
            return '-';
          }
          return text;
        };
      }
      return newColumn;
    });

  if (showAction) {
    // 默认操作列配置
    const defaultActionColumn = {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      fixed: 'right',
      resizable: false,
    };

    // 合并用户自定义的操作列配置
    const actionColumn = {
      ...defaultActionColumn,
      ...(tableProps.actionColumn || {}),
      // 确保这些属性不被覆盖
      dataIndex: 'action',
      resizable: false,
    };
    tableProps.actionColumn = actionColumn;
  }

  // 从columns中提取搜索表单配置（包括隐藏列）
  const searchSchema = columns
    .filter((col) => col.search)
    .map((col) => {
      const searchConfig = typeof col.search === 'object' ? (col.search as SearchConfig) : {};
      return {
        field: searchConfig.field || col.dataIndex,
        label: searchConfig.label || col.title,
        component: searchConfig.component || 'Input',
        slot: searchConfig?.slot,
        componentProps: searchConfig.componentProps || {},
        colProps: searchConfig.colProps,
      } as FormSchema;
    });

  // 使用原始useTable
  const mergedOptions = {
    ...defaultOptions,
    ...tableProps,
    columns: finalColumns,
    api: api
      ? async (params: any) => {
          if (!api) return;
          const data = await api(handleParams(params));
          // 确保total是数字类型
          if (data && typeof data.totalRows === 'string') {
            data.totalRows = parseInt(data.totalRows, 10);
          }
          return data;
        }
      : undefined,
    dataSource: !api && tableProps.dataSource ? tableProps.dataSource : undefined,
    formConfig:
      searchSchema.length > 0 || customFormConfig
        ? {
            ...defaultOptions.formConfig,
            ...(customFormConfig || {}),
            schemas: searchSchema,
          }
        : undefined,
  };
  return originalUseTable(mergedOptions);
}
