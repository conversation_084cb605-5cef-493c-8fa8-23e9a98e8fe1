<script lang="ts" setup>
import ValidValue from './ValidValue.vue';

export interface Box1Props {
  item: {
    label?: string | number;
    value?: string | number;
    color?: string;
    unit?: string;
  };
}

defineProps<Box1Props>();
</script>

<template>
  <div class="screen-box-1">
    <div class="label">
      {{ item.label }}
    </div>
    <div class="divider-box">
      <div class="item-divider"></div>
    </div>
    <ValidValue :value="item.value" :color="item.color" :unit="item.unit" />
  </div>
</template>

<style lang="less" scoped>
.screen-box-1 {
  background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png');
  padding: 20px 16px;
  display: flex;
  justify-content: center;
  flex-direction: column;

  .label {
    font-size: 24px;
    font-weight: 400;
  }

  .divider-box {
    height: 4px;
    margin: 20px 0;
    display: flex;
    align-items: center;
  }

  .item-divider {
    width: 18px;
    height: 4px;

    background: #00f3c4;
  }

  .value {
    .color {
      font-size: 42px;
      font-style: italic;
      font-weight: 700;

      &.green {
        color: #29e8ab;
      }

      &.red {
        color: #f53f3f;
      }
    }
  }

  .unit {
    color: rgba(255, 255, 255, 0.6);
    font-size: 18px;
    position: relative;
    top: -2px;
    margin-left: 4px;
  }
}
</style>
