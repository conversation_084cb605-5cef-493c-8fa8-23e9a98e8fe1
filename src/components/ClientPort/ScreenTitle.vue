<script lang="ts" setup>
import { SvgIcon } from '@geega-ui-plus/geega-ui';
</script>

<template>
  <div class="screen-title-wrapper">
    <div class="screen-title">
      <slot></slot>
    </div>
    <SvgIcon name="g-header-decoration" :size="12" color="#45505C" />
    <SvgIcon name="g-header-decoration" :size="12" color="#828C9842" />
    <slot name="suffix"></slot>
  </div>
</template>

<style lang="less" scoped>
.screen-title-wrapper {
  display: flex;
  align-items: center;
}

.screen-title {
  font-size: clamp(12px, 1.26vw, 24px);
  margin-right: 8px;
}
</style>
