<script lang="ts" setup></script>

<template>
  <div class="screen-box">
    <div class="screen-box-border border-top"></div>
    <div class="screen-box-border border-bottom"></div>
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.screen-box {
  position: relative;
  padding: 20px 16px;
  border: 1px solid #4f6872;

  --ignore-dark-bg: linear-gradient(
    180deg,
    rgba(178, 220, 253, 0.1) -29.42%,
    rgba(206, 233, 242, 0) 198.89%
  );
  background: var(--ignore-dark-bg);
}

.screen-box-border {
  pointer-events: none;
  display: contents;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 36px;
    background: no-repeat url('@/assets/svg/screen/box-corner-tl.svg');
  }

  &.border-top {
    &::before,
    &::after {
      top: -1px;
    }

    &::before {
      left: -1px;
    }

    &::after {
      right: -1px;
      transform: scaleX(-1);
    }
  }

  &.border-bottom {
    &::before,
    &::after {
      bottom: -1px;
    }

    &::before {
      left: -1px;
      transform: scaleY(-1);
    }

    &::after {
      right: -1px;
      transform: scale(-1);
    }
  }
}
</style>
