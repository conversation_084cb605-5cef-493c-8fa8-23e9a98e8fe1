<script lang="ts" setup>
import { computed } from 'vue';
import { useIsStation } from '/@/composables/useIsStation';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { V1LocationHomeCurrentLocation } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { watchImmediate } from '@vueuse/core';

const isStation = useIsStation();

const currentLocation = useAsyncData(() => V1LocationHomeCurrentLocation({}), {});

watchImmediate(isStation, () => {
  if (isStation.value) {
    currentLocation.load();
  }
});

const locationString = computed(() => {
  const data = currentLocation.data.value;

  return `${data.levelName}_${data.name}`;
});
</script>

<template>
  <span class="flex items-center gap-2" v-if="isStation">
    <SvgIcon name="g-icon-desktop" />
    <span>{{ locationString }}</span>
  </span>
</template>

<style lang="less" scoped></style>
