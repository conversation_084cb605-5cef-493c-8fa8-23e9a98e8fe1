<script lang="ts" setup>
import { SvgIcon } from '@geega-ui-plus/geega-ui';

defineProps<{
  content?: string;
  color?: string;
  type?: string
}>();
</script>

<template>
  <div class="stamp-text-wrapper" :class="type" :style="{ '--color': color }">
    <div class="stamp-bg-icon">
      <SvgIcon name="g-bg-stamp" size="80" />
    </div>
    <span class="stamp-text">
      <slot>{{ content }}</slot>
    </span>
  </div>
</template>

<style lang="less" scoped>
.stamp-text-wrapper {
  @size: 80px;
  width: @size;
  height: @size;
  position: relative;

  color: var(--color, #f53f3f);

  &.error {
    color: var(--color, #f53f3f);
  }

  &.warning {
    color: var(--color, #fc8800);
  }

  .stamp-text {
    font-size: 18px;
    font-weight: 500;
    position: absolute;
    top: 50%;
    left: 50%;
    width: max-content;

    transform: translate(-50%, -50%) rotate(-15deg);
    display: inline-block;
  }

  .stamp-bg-icon {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
}
</style>
