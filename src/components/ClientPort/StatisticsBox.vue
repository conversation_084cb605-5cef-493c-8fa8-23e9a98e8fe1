<script lang="ts" setup></script>

<template>
  <div class="statistics-box">
    <div class="three-dot"></div>
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.statistics-box {
  --ignore-dark-color: #ffffff0a;
  background: no-repeat right center / auto 100% url('@/assets/svg/screen/bg-arrow.svg'),
    var(--ignore-dark-color);
  padding: 16px 24px;
  position: relative;
  margin-top: 10px;
}

.three-dot {
  @size: 6px;

  position: absolute;
  top: -@size - 4px;
  left: 0;

  width: @size;
  height: @size;

  background: #00f3c4;

  &::before,
  &::after {
    content: '';
    position: absolute;
    display: block;

    top: 0;

    width: @size;
    height: @size;
    background: #434b53;
  }

  @gap: 4px;

  &::before {
    left: @size + @gap;
  }

  &::after {
    left: (@size + @gap) * 2;
  }
}
</style>
