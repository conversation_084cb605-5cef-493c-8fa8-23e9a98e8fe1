<script lang="ts" setup>
import { computed } from 'vue';
import ProgressBar from '/@/components/ClientPort/ProgressBar.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import type { V1LocationHomeProgressStatisticsRecordIDGetResponseResult } from '/@/api/cddc.model';
import ScreenBox1 from './ScreenBox1.vue';

const props = defineProps<{
  /**
   * 报表数据
   */
  data?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
  loading?: boolean;
}>();

const renderData = computed(() => props.data || {});
// 秒转分钟
const secondsToMinutes = (seconds: number) => {
  return Math.floor(seconds / 60);
};

const trainData = computed(() => {
  const data = props.data;

  return [
    {
      label: '训练时长',
      unit: 'min',
      color: 'green',
      value: secondsToMinutes(data?.trainDuration || 0) || 0,
    },
    {
      label: '训练效率',
      unit: '次/分钟',
      color: 'green',
      value: (() => {
        const duration = secondsToMinutes(data?.trainDuration ?? 0);
        if (!duration) return 0;
        return Number(((data?.trainFrequency ?? 0) / duration).toFixed(1)) || 0;
      })(),
    },
  ];
});
</script>

<template>
  <div class="progress-report">
    <ScreenTitle> 训练进度 </ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="props.loading">
      <ScreenBox class="progress-items h-full">
        <div class="flex-1 flex gap-5">
          <ScreenBox1
            class="progress-data-item flex-1"
            v-for="(item, idx) in trainData"
            :key="idx"
            :item="item"
          />
        </div>
        <div class="progress-row">
          <div class="progress-label">训练进度</div>
          <ProgressBar
            status="error"
            :current="renderData.trainFrequency"
            :total="renderData.requestFrequency"
            unit="次"
          />
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.progress-report {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-row {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;

  --ignore-dark-bg: linear-gradient(
    228deg,
    rgba(62, 71, 81, 0.2) 13.78%,
    rgba(62, 71, 81, 0) 120.36%
  );

  background: var(--ignore-dark-bg);
  padding: 0 16px;

  .progress-label {
    font-size: 24px;
  }
}

.progress-data-item {
  each(range(2), {
      &:nth-child(@{value}n) {
        --ignore-dark-image: url('@/assets/images/screen/progress-report-box1-bg-@{value}.png');

        background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png'),
        no-repeat right bottom / auto 30% var(--ignore-dark-image);
      }
    });
}
</style>
