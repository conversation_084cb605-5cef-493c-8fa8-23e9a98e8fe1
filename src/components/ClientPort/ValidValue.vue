<script lang="ts" setup>
defineProps<{
  color?: string;
  value?: string | number;
  unit?: string;
}>();
</script>

<template>
  <div class="valid-value">
    <span class="value" :class="color">
      <slot>
        {{ value }}
      </slot>
    </span>
    <span class="unit">
      <slot name="unit">
        {{ unit }}
      </slot>
    </span>
  </div>
</template>

<style lang="less" scoped>
.valid-value {
  font-size: 42px;
  font-style: italic;
  font-weight: 700;
  display: inline-flex;
  align-items: end;

  line-height: 45px;

  .value {
    &.green {
      color: #29e8ab;
    }

    &.red {
      color: #f53f3f;
    }
  }

  .unit {
    font-size: 16px;
    font-weight: 400;
    color: #ffffff99;
    position: relative;
    margin-left: 4px;
    bottom: -6px;
  }
}
</style>
