<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import ScreenBox1 from '/@/components/ClientPort/ScreenBox1.vue';
import type { V1LocationHomeRankRecordIDGetResponseResult } from '/@/api/cddc.model';

const props = defineProps<{
  loading?: boolean;
  data?: V1LocationHomeRankRecordIDGetResponseResult;
}>();

const trainData = computed(() => {
  const data = props.data || {};

  return [
    {
      label: '训练合格率排名',
      value:
        data.qualifiedRate != null && Number(data.qualifiedRate) >= 0 ? data.qualifiedRate : '-',
      color: 'green',
    },
    {
      label: '技能考核排名',
      color: 'green',
      value: data?.checkRanking != null && Number(data.checkRanking) >= 0 ? data.checkRanking : '-',
    },
    {
      label: '技能比赛排名',
      value: data.raceRanking != null && Number(data.raceRanking) >= 0 ? data.raceRanking : '-',
      color: 'green',
    },
  ];
});
</script>

<template>
  <div class="project-rank">
    <ScreenTitle> 数据排名</ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="loading">
      <ScreenBox class="h-full">
        <div class="latest-data">
          <ScreenBox1 class="train-item" v-for="(item, idx) in trainData" :key="idx" :item="item" />
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.project-rank {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.latest-data {
  height: 100%;
  display: grid;
  grid-template-areas: 'a a' 'b c';
  // grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .train-item {
    each(range(3), {
      &:nth-child(@{value}n) {
        --ignore-dark-image: url('@/assets/svg/screen/bg-box-icon-@{value}.svg');

        background: no-repeat center / 100% 100% url('@/assets/images/screen/bg-box.png'),
        no-repeat right top / auto 80% var(--ignore-dark-image);
      }
    });

    &:nth-child(1) {
      grid-area: a;
    }
  }
}
</style>
