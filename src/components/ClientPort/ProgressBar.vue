<script lang="ts" setup>
import { clamp } from 'lodash-es';
import { computed } from 'vue';
import { toFixed } from '/@/utils';

export interface IProgressBarProps {
  status?: 'success' | 'error';
  unit?: string;

  current?: number;
  total?: number;
}

const props = defineProps<IProgressBarProps>();

const percentageValue = computed(() => {
  const p = (props.current! / props.total!) * 100 || 0;

  return clamp(p, 0, 100);
});

const percentage = computed(() => `${toFixed(percentageValue.value)}%`);

const shouldReverseText = computed(() => percentageValue.value > 80);
</script>

<template>
  <div class="progress-bar" :class="status" :style="`--percentage: ${percentage};`">
    <div class="progress-bar-bg">
      <div class="progress-bar-fg"> </div>
    </div>
    <div class="progress-bar-tick"></div>
    <div class="progress-thumb">
      <div class="progress-thumb-text" :class="{ 'reverse-text': shouldReverseText }">
        {{ percentage }}
      </div>
    </div>
    <div class="progress-text">
      <span class="value"> {{ current || 0 }}{{ unit }} </span>
      <span> /{{ total || 0 }}{{ unit }}</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
.progress-bar {
  position: relative;
  margin-top: 45px;
}

.progress-bar-bg {
  height: 15px;
  --ignore-dark-color: rgba(255, 255, 255, 0.04);
  background: var(--ignore-dark-color);
  overflow: hidden;

  &::after {
    content: '';
    width: 1px;
    height: 38px;
    --ignore-dark-bg: #ffffffcc;
    background: var(--ignore-dark-bg);
    position: absolute;
    right: 0;
    top: 0;
  }
}

.progress-bar-tick {
  width: 100%;
  height: 16px;
  background: repeat-x 1px top / auto url('@/assets/svg/screen/progress-bar-tick.svg');
}

.progress-thumb {
  position: absolute;
  z-index: 10;
  @img-x-offset: 15px;

  left: var(--percentage);

  @thumb-height: 42px;
  height: @thumb-height;
  top: -@thumb-height;

  color: #29e8ab;
  font-size: 24px;
  font-style: italic;
  font-weight: 700;

  &::before {
    position: absolute;
    content: '';
    top: 0;
    left: -@img-x-offset;
    width: 40px;
    height: @thumb-height;
    background: no-repeat 1px center / auto 100%
      url('@/assets/images/screen/progress-bar-thumb-green.png');
  }

  &::after {
    content: '';
    width: 1px;
    height: 38px;

    --ignore-dark-bg: #fff;
    background: var(--ignore-dark-bg);

    position: absolute;
    left: 0;
    bottom: 0;
    transform: translateY(100%);
  }

  .progress-thumb-text {
    position: absolute;
    top: 0;
    left: calc(@img-x-offset + 4px);

    &.reverse-text {
      left: unset;
      right: calc(@img-x-offset + 4px);
    }
  }
}

.progress-bar-fg {
  height: 100%;
  width: var(--percentage);
  background: repeat -2px center / auto 100% url('@/assets/svg/screen/progress-bar-bg-item-green.svg');
}

.progress-text {
  text-align: right;
  font-size: 18px;
  color: #9fa2a7;
  .value {
    color: #29e8ab;
  }
}

.progress-bar.error {
  .progress-bar-fg {
    --ignore-dark-img: url('@/assets/svg/screen/progress-bar-bg-item-red.svg');
    background-image: var(--ignore-dark-img);
  }

  .progress-thumb {
    &::before {
      background-image: url('@/assets/images/screen/progress-bar-thumb-red.png');
    }
  }

  .progress-thumb,
  .progress-text .value {
    color: #f53f3f;
  }
}
</style>
