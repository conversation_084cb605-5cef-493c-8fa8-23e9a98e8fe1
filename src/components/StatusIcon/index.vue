<template>
  <Gbadge v-if="dot" :status="classType" :text="text || getStatusText(type)"></Gbadge>
  <GTag v-else :type="classType">{{ text || getStatusText(type) }}</GTag>
</template>
<script lang="ts">
import { defineComponent, computed } from 'vue';
import { SvgIcon, Gbadge, GTag } from '@geega-ui-plus/geega-ui';

type ColorTypeKey = "success" | "waiting" | "processing" | "close" | "overtime" | "overtime-close" | "no-return" | "returned" | "overtime-no-return" | "discard" | "error";
export default defineComponent({
  props: {
    // success：已完成 waiting：待处理 processing：处理中 close 已关闭
    type: {
      type: String,
      default: 'success'
    },
    // 显示圆点,默认显示标签
    dot: {
      type: Boolean,
      default: false
    },
    // 显示的文本
    text: {
      type: String,
      default: ''
    },
    // 自定义ICON
    icon: {
      type: String,
      default: ''
    },
    // 自定义对应文案
    options: {
      type: Object,
      default: () => {
        return {
          'success': '已完成' ,
          'waiting': '待处理',
          'processing': '处理中',
          'close': '正常关闭',
          'overtime': '超时关闭',
          'overtime-close': '超时未完成',
          'no-return': '待归还',
          'returned': '已归还',
          'overtime-no-return': '超期未还',
          'discard': '已作废',
        }
      }
    },
    onlyText: {
      type: Boolean,
      default: false
    }
  },
  components: {
    SvgIcon,
    Gbadge,
    GTag
  },
  setup(props) {
    // console.log(props.options)
    const colorCategories = {
      success: ["success", "returned"],
      warning: ["warning", "waiting", "no-return"],
      processing: ["processing"],
      disabled: ["disabled", "close", "discard", "closed"],
      error: ["overtime", "overtime-close", "overtime-no-return", "error"]
    };

    const colorType: Record<ColorTypeKey, string> = Object.keys(colorCategories).reduce((acc, category) => {
      colorCategories[category as keyof typeof colorCategories].forEach((type) => {
        acc[type as ColorTypeKey] = category;
      });
      return acc;
    }, {} as Record<ColorTypeKey, string>);

    const classType = computed(() => {
      return colorType[props.type] || 'normal'
    })
    const icon = computed(() => {
      return props.icon
    })

    const textOptions = computed(() => {
      return props.options
    })

    const getTypeIcon = (val) => {
      const icons = {
        'success': 'g-read',
        'returned': 'g-read',
        'waiting': 'g-unread',
        'no-return': 'g-loading',
        'processing': 'g-loading',
        'close': 'g-stop',
        'overtime': 'g-overtime',
        'overtime-close': 'g-overtime',
        'overtime-no-return': 'g-overtime',
        'discard': 'g-stop',
      }
      return icons[val]
    }

    const getStatusText = (val) => {
      return textOptions.value[val]
    }
    return {
      classType,
      icon,
      getStatusText,
      getTypeIcon
    }
  }
})
</script>
<style lang="less" scoped>
:deep(.eam-ant-badge-status-dot) {
  width: 8px;
  height: 8px
}
</style>
