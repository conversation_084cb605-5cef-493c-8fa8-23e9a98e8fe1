<!-- 项目选择器组件 -->
<template>
  <div class="project-box">
    <div class="project-card">
      <span class="section-title">{{ title }}</span>
      <template v-if="!showProjcetInfo">
        <a-select
          v-model:value="recordVal"
          style="width: 200px"
          size="large"
          :options="filteredProjectList"
          @change="handleProjectChange"
          :fieldNames="{ label: 'name', value: 'recordId' }"
          placeholder="请选择项目"
        ></a-select>
        <a-tag :color="projectStatus?.color" v-if="recordVal && projectStatus">{{
          projectStatus?.text
        }}</a-tag>
      </template>
      <template v-else>
        <span class="project-name">{{ currentProjectName }}</span>
      </template>
    </div>
    <!-- 时间和作业次数信息 -->
    <div v-if="showTimeInfo" class="time-info">
      <div class="info-item">
        <ClockCircleOutlined class="info-icon" />
        <span>{{ raceType === 1 ? '剩余时间：' : '已用时间：' }}</span>
        <span class="highlight time" :class="{ 'text-red': isTimeWarning }">{{
          raceType == 1 ? remainingTime : elapsedTime
        }}</span>
      </div>
      <div class="info-divider"></div>
      <div class="info-item">
        <CheckCircleOutlined class="info-icon" />
        <span>{{ operationsLabel }}：</span>
        <span class="highlight">{{ validOperations }}次</span>
      </div>
    </div>
    <slot v-else></slot>
    <!-- 右侧控制区域 -->
    <div class="right-controls" v-if="$slots.rightControls || workStation.levelName">
      <slot name="rightControls"></slot>
      <div class="location-info" v-if="workStation.levelName">
        <SvgIcon name="g-icon-desktop" />
        {{ workStation.levelName }}-{{ workStation.name }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { createLocalStorage } from '/@/utils/cache';
import { ProjectStatusMap } from '/@/enums/projectEnum';
import {
  V1LocationHomeCurrentLocation,
  V1LocationHomeMyProjectLocationIdUserId,
  V1ManageTranProjectId,
} from '/@/api/cddc.req';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { permissionStore } from '/@/store/modules/permission';
import { getUserInfo } from '/@/api/location/home';
import { ClockCircleOutlined, CheckCircleOutlined } from '@geega-ui-plus/icons-vue';
import { useRoute } from 'vue-router';
const props = defineProps({
  title: {
    type: String,
    default: '我的项目',
  },
  showProjcetInfo: {
    type: Boolean,
    default: true,
  },
  showTimeInfo: {
    type: Boolean,
    default: false,
  },
  remainingTime: {
    type: String,
    default: '00:00',
  },
  elapsedTime: {
    type: String,
    default: '00:00',
  },
  isTimeWarning: {
    type: Boolean,
    default: false,
  },
  validOperations: {
    type: Number,
    default: 0,
  },
  raceType: {
    type: Number,
    default: 1,
  },
  operationsLabel: {
    type: String,
    default: '有效作业次数',
  },
});

const emit = defineEmits(['change', 'pList', 'userInfo']);

// 工位信息
const workStation = ref<any>({});
const route = useRoute();
const userData = ref<any>({}); // 获取用户信息

// 项目列表
const projectList = ref<any>([]);
// 选择项目
const recordVal = ref<any>();

// 根据页面类型过滤项目列表
const filteredProjectList = computed(() => {
  const path = route.path;
  // 如果是训练页面，所有项目都可选
  if (path.includes('/cddc/training/index')) {
    return projectList.value;
  }
  // 如果是技能考核或技能比赛页面，只有训练完成的项目可选
  if (path.includes('/cddc/skill-assessment/index') || path.includes('/cddc/skill-competition/index')) {
    return projectList.value.filter((project) =>
      ['TO_BE_CHECK', 'FAILED_CHECK', 'PASSED_CHECK', 'COMPLETED'].includes(project.status)
    );
  }
  return projectList.value;
});

// 是否为工位端
const isNormalUser = computed(() => permissionStore.getIsNormalUserState);

// 项目状态
const projectStatus = computed(() => {
  const currentProject = projectList.value.find((item) => item.recordId === recordVal.value);
  return currentProject ? ProjectStatusMap[currentProject.status] : null;
});

// 当前选中的项目
const currentProjectName = ref('');

// 获取当前项目名称
const fetchCurrentProjectName = async () => {
  try {
    const projectId = route.query.projectId as string;
    if (projectId) {
      const res = await V1ManageTranProjectId({ id: projectId });
      if (res) {
        currentProjectName.value = res?.name || '';
        emit('change', res);
      }
    }
  } catch (error) {
    console.error('Failed to get project name:', error);
    currentProjectName.value = '';
  }
};

// 工位信息
const getWorkStation = async () => {
  try {
    // if (!isNormalUser.value) {
    //   emit('pList', []);
    //   return;
    // }
    const res = await V1LocationHomeCurrentLocation({});
    const ls = createLocalStorage();
    workStation.value = res;
    ls.set('workStationInfo', res);
    if (res.locationId) {
      ls.set('locationId', res.locationId);
      getProjectList(res);
    }
  } catch (error) {
    console.error('Failed to get workstation info:', error);
  }
};

// 获取用户信息
const userInfo = async () => {
  try {
    const res = await getUserInfo();
    userData.value = res;
    const ls = createLocalStorage();
    ls.set('userId', res.id);
    emit('userInfo', res);
  } catch (error) {
    console.log('error :>> ', error);
  }
};

// 获取项目列表
const getProjectList = async (data: { locationId?: string }) => {
  const ls = createLocalStorage();
  const userId = userData.value.id;

  if (data.locationId && userId) {
    const res = await V1LocationHomeMyProjectLocationIdUserId(
      {
        projectType: 1,
      },
      {
        locationId: data.locationId,
        userId,
      }
    );
    projectList.value = res;
    emit('pList', res);
    // 优先使用本地存储的记录ID，如果没有则使用第一个项目
    recordVal.value = res.length > 0 ? ls.get('currRecordId') || res[0]?.recordId : null;

    if (recordVal.value) {
      handleProjectChange(recordVal.value);
    }
  }
};

// 项目切换
const handleProjectChange = (value: string) => {
  const ls = createLocalStorage();
  ls.set('currRecordId', value);
  const currentProject = projectList.value.find((item) => item.recordId === value);
  if (!props.showProjcetInfo) {
    emit('change', currentProject);
  }
};

// 初始化
onMounted(async () => {
  await userInfo();
  await getWorkStation();
  await fetchCurrentProjectName();
});
</script>

<style lang="less" scoped>
.project-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.project-card {
  display: flex;
  align-items: center;
  gap: 12px;

  .section-title {
    font-size: 16px;
    font-weight: 500;
  }

  .project-name {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
  }

  .cddc-ant-tag {
    padding: 2px 8px 3px 8px;
  }
}

.right-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 16px;

  .location-info {
    color: rgba(255, 255, 255, 0.45);
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.time-info {
  display: flex;
  align-items: center;
  gap: 1.5vw;
  margin-left: 2vw;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5vw;

    .info-icon {
      font-size: 16px;
    }

    .highlight {
      color: #29e8ab;
      font-weight: 500;

      &.time {
        font-family: 'Roboto Mono', monospace;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 1px;
        color: #29e8ab;

        &.text-red {
          color: #f53f3f;
        }
      }
    }
  }

  .info-divider {
    width: 1px;
    height: 14px;
    background: rgba(255, 255, 255, 0.2);
  }
}
</style>
