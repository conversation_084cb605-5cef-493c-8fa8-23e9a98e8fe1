<script lang="ts" setup>
import { useEventListener } from '@vueuse/core';
import type { Vector2Like } from 'three';
import { computed } from 'vue';

const emit = defineEmits(['update:pos']);

export interface DraggableObjectProps {
  pos: Vector2Like;
}

const props = defineProps<DraggableObjectProps>();

const state = {
  isDragging: false,
};

function startDrag() {
  state.isDragging = true;
}

useEventListener(window, 'mousemove', (ev) => {
  if (!state.isDragging) return;

  const newPos = {
    x: ev.movementX + props.pos.x,
    y: ev.movementY + props.pos.y,
  };

  emit('update:pos', newPos);
});

useEventListener(window, 'mouseup', (_ev) => {
  state.isDragging = false;
});

const style = computed(() => {
  return {
    left: `${props.pos.x}px`,
    top: `${props.pos.y}px`,
  };
});
</script>

<template>
  <div class="draggable-object">
    <slot :start-drag="startDrag" :style="style"></slot>
  </div>
</template>

<style lang="less" scoped>
.draggable-object {
  display: contents;
}
</style>
