<script lang="ts" setup>
import { computed } from 'vue';

export interface ResponsiveTagListProps {
  items: any[];
  maxCount: number;
}

const props = defineProps<ResponsiveTagListProps>();

const hiddenCount = computed(() => props.items.length - props.maxCount);
</script>

<template>
  <div class="responsive-tag-list">
    <template v-for="(item, idx) in items.slice(0, maxCount)">
      <slot :item="item" :index="idx"></slot>
    </template>
    <a-tag class="py-0! mr-0!" v-if="hiddenCount > 0" type="success"> + {{ hiddenCount }}...</a-tag>
  </div>
</template>

<style lang="less" scoped>
.responsive-tag-list {
}
</style>
