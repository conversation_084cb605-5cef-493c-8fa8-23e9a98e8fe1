import { groupBy } from 'lodash-es';
import type { QuestionBankDetailListElement } from '/@/api/cddc.model';
import { QUESTION_TYPE } from './Questions';
import { QUESTION_DIFFICULT_DEGREE } from '/@/enums/questionEnum';

/**
 * Examples:
 *
 * 0 => A
 * 1 => B
 *
 * @param num
 * @returns
 */
export function number2alphaChar(num: number) {
  const alpha = String.fromCharCode(num + 65);
  return alpha;
}

/**
 *
 * Examples:
 *
 * 0 => 一
 * 1 => 二
 * 2 => 三
 *
 * @param num
 * @returns
 */
export function number2chineseChar(num: number): string {
  const chineseChars = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];

  if (num < 0 || num >= chineseChars.length) {
    throw new Error('数字超出范围');
  }

  return chineseChars[num];
}

interface IGroupedQuestions<T extends QuestionBankDetailListElement> {
  orderType: string;
  questions: T[];
}

export function groupQuestions<T extends QuestionBankDetailListElement>(
  questions: T[] = []
): IGroupedQuestions<T>[] {
  const _questions = groupBy(
    questions || [],
    (item) => `${item.questionType}-${item.difficultDegree}`
  );

  const result: IGroupedQuestions<T>[] = [];

  const difficultyOrder = [
    QUESTION_DIFFICULT_DEGREE.EASY,
    QUESTION_DIFFICULT_DEGREE.NORMAL,
    QUESTION_DIFFICULT_DEGREE.HARD,
  ];

  const groupOrder = [
    QUESTION_TYPE.FILL_IN_THE_BLANK,
    QUESTION_TYPE.SINGLE_CHOICE,
    QUESTION_TYPE.JUDGMENT,
    QUESTION_TYPE.MULTIPLE_CHOICE,
    QUESTION_TYPE.SHORT_ANSWER,
  ].flatMap((t) => difficultyOrder.map((d) => `${t}-${d}`));

  for (const orderType of groupOrder) {
    const groupedQuestions = _questions[orderType];
    if (!groupedQuestions?.length) {
      continue;
    }

    result.push({
      orderType,
      questions: groupedQuestions,
    });
  }

  return result;
}
