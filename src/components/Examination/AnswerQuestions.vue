<script lang="ts" setup>
import { computed } from 'vue';
import {  type IQuestion } from './Questions';
import type {
  ResultQuestionTypeList,
} from '/@/api/cddc.model';
import { getDictLabel } from '/@/dict';
import { QUESTION_TYPE, QuestionTypeOptions } from './Questions/enum';
import { groupQuestions, number2chineseChar, number2alphaChar } from './utils';
import { QuestionDifficultOptions } from '/@/enums/questionEnum';

export interface AnswerQuestionsProps {
  questionTypes?: ResultQuestionTypeList[];
  questions: IQuestion[];
  userAnswers: Record<string, any>;
}

const props = defineProps<AnswerQuestionsProps>();
const emit = defineEmits(['update:userAnswers']);

const groupedQuestions = computed(() => {
  const grouped = groupQuestions(props.questions);

  return grouped.map((item) => {
    const [questionType, difficulty] = item.orderType.split('-');

    const typeConfig = props.questionTypes?.find(
      (n) => n.questionType === questionType && n.difficultDegree?.toString() === difficulty
    );
    const singleScore = typeConfig?.singleScore || 0;

    const unit = questionType === QUESTION_TYPE.FILL_IN_THE_BLANK ? '空' : '题';

    const difficultyName = getDictLabel(QuestionDifficultOptions, difficulty).toString();
    const typeName = getDictLabel(QuestionTypeOptions, questionType).toString();
    const name = `${typeName}-${difficultyName}`;

    const totalDescription = `共 ${item.questions.length} ${unit}`;
    const scoreDescription = typeConfig ? `每题 ${singleScore} 分，` : ``;

    const title = `${name}（${scoreDescription}${totalDescription}）`;

    return {
      name,
      typeConfig,
      title: title,
      score: singleScore,
      orderType: item.orderType,
      questions: item.questions,
    };
  });
});

const updateAnswer = (questionId: string | undefined, value: any) => {
  if (!questionId) return;
  const newAnswers = { ...props.userAnswers };
  newAnswers[questionId] = value;

  emit('update:userAnswers', newAnswers);
};

const getQuestionTypeName = (type: string | undefined) => {
  if (!type) return '未知题型';

  // 从QuestionTypeOptions中查找匹配的选项
  const option = QuestionTypeOptions.find(item => item.value === type);
  if (option) {
    return option.label;
  }

  // 处理枚举中未定义的特殊类型
  const extraTypes: Record<string, string> = {
    'ESSAY': '论述题',
    'CODING': '编程题'
  };

  return extraTypes[type] || type;
};

const getQuestionScore = (question: IQuestion) => {
  if (!question.id) return 0;

  // 直接使用问题本身的属性
  const questionType = question.questionType;
  const difficulty = question.difficultDegree?.toString();

  const typeConfig = props.questionTypes?.find(
    (n) => n.questionType === questionType && n.difficultDegree?.toString() === difficulty
  );

  return typeConfig?.singleScore || 0;
};
</script>

<template>
  <div class="questions">
    <div class="grouped-questions" v-for="(gQuestions, idx) in groupedQuestions">
      <div class="type-title">
        {{ number2chineseChar(idx) }} 、
        <slot name="group-title" :group="gQuestions">
          {{ gQuestions.title }}
        </slot>
      </div>

      <div class="question" v-for="(question, idx) in gQuestions.questions">
        <div class="question-header">
          <div class="question-index">{{ idx + 1 }}</div>
          <div class="question-type">
            {{ getQuestionTypeName(question.questionType) }}
            <span class="question-score">({{ getQuestionScore(question) }}分)</span>
          </div>
        </div>

        <div class="question-content">
          <div class="question-stem" v-html="question.questionStem || ''"></div>

          <!-- 单选题 -->
          <div v-if="question.questionType === QUESTION_TYPE.SINGLE_CHOICE" class="options-list">
            <a-radio-group v-model:value="props.userAnswers[question.id || '']" @change="updateAnswer(question.id, $event.target.value)">
              <a-radio
                v-for="option in question.questionBankOptionList || []"
                :key="option.orderNum"
                :value="option.orderNum"
                class="option-item"
              >
                {{ number2alphaChar((Number(option.orderNum || 0))) }}. {{ option.optionDesc || '' }}
              </a-radio>
            </a-radio-group>
          </div>

          <!-- 多选题 -->
          <div v-else-if="question.questionType === QUESTION_TYPE.MULTIPLE_CHOICE" class="options-list">
            <a-checkbox-group
              v-model:value="props.userAnswers[question.id || '']"
              @change="(checkedValues) => updateAnswer(question.id, checkedValues)"
            >
              <a-checkbox
                v-for="option in question.questionBankOptionList || []"
                :key="option.orderNum"
                :value="option.orderNum"
                class="option-item"
              >
                {{ number2alphaChar((Number(option.orderNum || 0))) }}. {{ option.optionDesc || '' }}
              </a-checkbox>
            </a-checkbox-group>
          </div>

          <!-- 判断题 -->
          <div v-else-if="question.questionType === QUESTION_TYPE.JUDGMENT" class="options-list">
            <a-radio-group v-model:value="props.userAnswers[question.id || '']" @change="updateAnswer(question.id, $event.target.value)">
              <a-radio value="Y" class="option-item">正确</a-radio>
              <a-radio value="N" class="option-item">错误</a-radio>
            </a-radio-group>
          </div>

          <!-- 简答题 -->
          <div v-else-if="question.questionType === QUESTION_TYPE.SHORT_ANSWER" class="options-list">
            <a-textarea
              v-model:value="props.userAnswers[question.id || '']"
              :rows="4"
              placeholder="请输入您的答案"
              @change="(e) => updateAnswer(question.id, e.target.value)"
            />
          </div>

          <!-- 其他题型 -->
          <div v-else class="options-list">
            <a-textarea
              v-model:value="props.userAnswers[question.id || '']"
              :rows="4"
              placeholder="请输入您的答案"
              @change="(e) => updateAnswer(question.id, e.target.value)"
            />
          </div>
        </div>
        <slot name="question-item-suffix" :question="question"></slot>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.questions {
  display: flex;
  flex-direction: column;
}

.type-title {
  font-weight: bold;
  margin-bottom: 24px;
}

.question {
  margin-bottom: 30px;

  :deep(p) {
    margin: 0;
  }

  .question-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .question-index {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #00996b;
      color: #fff;
      border-radius: 50%;
      font-weight: 500;
      margin-right: 12px;
    }

    .question-type {
      color: rgba(0, 0, 0, 0.65);

      .question-score {
        color: #ff4d4f;
        margin-left: 4px;
      }
    }
  }

  .question-content {
    .question-stem {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
      line-height: 1.6;
    }

    .options-list {
      // margin-left: 40px;

      .option-item {
        // display: block;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
