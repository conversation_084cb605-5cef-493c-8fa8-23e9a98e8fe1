<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue';
import { EditQuestion, type IQuestion } from './Questions';
import { BasicForm, BasicModal, useForm, useModal } from '@geega-ui-plus/geega-ui';
import { V1ManageQuestionBankColumnGetQuestionBankColumnListPost } from '/@/api/cddc.req';
import type {
  ExamRecordElement,
  PaperElement,
  ResultAnswerList,
  QuestionBankColumnDTOElement,
} from '/@/api/cddc.model';
import { useMessage } from '/@/hooks/web/useMessage';
import { message } from '@geega-ui-plus/ant-design-vue';
import PreviewQuestions from './PreviewQuestions.vue';
import { groupQuestions } from './utils';

export interface UserAnswerInfo {
  info: ExamRecordElement;
  answers: ResultAnswerList[];
}

export interface RegeneratePromptOption {
  questionPoint: string;
  question: IQuestion;
}

export interface ExamPreviewProps {
  allowEdit?: boolean;
  examData?: PaperElement;
  questions?: IQuestion[];
  userAnswer?: UserAnswerInfo;
  generateNewQuestion?: (params: RegeneratePromptOption) => Promise<IQuestion>;
}

const props = defineProps<ExamPreviewProps>();
const emit = defineEmits(['update:questions']);

const [editModalRegister, editModalActions] = useModal();
const [promptModalRegister, promptModalActions] = useModal();
const allFields = ref<QuestionBankColumnDTOElement[]>([]);

const { createDeteleConfirm } = useMessage();

enum PromptType {
  Replace,
  Append,
}

const promptState = {
  type: PromptType.Append,
  question: null as IQuestion | null,
};

const [showPointForm, pointFormActions] = useForm({
  schemas: [
    {
      field: 'questionPoint',
      label: '题目要点',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        maxlength: 150,
        showCount: true,
      },
    },
  ],
  showActionButtonGroup: false,
});

const groupedQuestions = computed(() => {
  const grouped = groupQuestions(props.questions);

  return grouped.map((item) => {
    const [questionType, difficulty] = item.orderType.split('-');

    const typeConfig = props.examData?.questionTypeList?.find(
      (n) => n.questionType === questionType && n.difficultDegree?.toString() === difficulty
    );
    const singleScore = typeConfig?.singleScore || 0;

    return {
      typeConfig,
      score: singleScore,
      orderType: item.orderType,
      questions: item.questions,
    };
  });
});

const questionInfo = computed(() => {
  const info = {
    score: 0,
    count: 0,
  };

  groupedQuestions.value.forEach((item) => {
    const qCount = item.questions.length || 0;

    info.score += (item.score || 0) * qCount;

    info.count += qCount;
  });

  info.score = +info.score.toFixed(1);

  return info;
});

const editTools = [
  {
    title: '编辑',
    onClick: (question: IQuestion) => {
      editModalActions.openModal(true, {
        question,
      });
    },
  },
  {
    title: '重新生成',
    onClick: async (question: IQuestion) => {
      await pointFormActions.resetFields();
      promptState.type = PromptType.Replace;
      promptState.question = question;

      promptModalActions.setModalProps({
        title: '重新生成',
      });
      promptModalActions.openModal();
    },
  },
  {
    title: '插入新题',
    onClick: async (question: IQuestion) => {
      await pointFormActions.resetFields();

      promptState.type = PromptType.Append;
      promptState.question = question;

      promptModalActions.setModalProps({
        title: '插入新题',
      });
      promptModalActions.openModal();
    },
  },
  {
    title: '删除',
    onClick: (question: IQuestion) => {
      createDeteleConfirm({
        content: '请确定是否删除该题目？',
        onOk: () => {
          removeQuestion(question);
        },
      });
    },
  },
];

function removeQuestion(question: IQuestion) {
  const _questions = props.questions?.slice() || [];

  const idx = _questions.findIndex((n) => n.id === question.id);
  _questions.splice(idx, 1);

  emit('update:questions', _questions);
}

function updateQuestion(question: IQuestion) {
  const _questions = props.questions?.slice() || [];

  const idx = _questions.findIndex((n) => n.id === question.id);
  _questions.splice(idx, 1, question);

  emit('update:questions', _questions);
}

async function confirmPrompt() {
  const values = await pointFormActions.validate();

  const params: RegeneratePromptOption = {
    ...values,
    ...promptState,
  };

  promptModalActions.setModalProps({
    okButtonProps: {
      loading: true,
    },
  });

  try {
    const newQuestion = await props.generateNewQuestion?.(params);

    if (!newQuestion) {
      message.warn('根据题目要点生成题目失败！');
      return;
    }

    const _questions = props.questions?.slice() || [];
    const idx = _questions.findIndex((n) => n.id === params.question.id);

    if (promptState.type === PromptType.Replace) {
      _questions.splice(idx, 1, newQuestion);
    } else {
      _questions.splice(idx + 1, 0, newQuestion);
    }

    emit('update:questions', _questions);

    promptModalActions.setModalProps({ visible: false });
  } catch (error) {
    console.error(error);
  } finally {
    promptModalActions.setModalProps({
      okButtonProps: {
        loading: false,
      },
    });
  }
}

async function loadAllFields() {
  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnListPost({});
  allFields.value = resp || [];
}

const groupQuestionPropertyList = computed(() => {
  const questionProperties = props.examData?.questionPropertyList || [];
  const accumulator: Array<{ columnKey: string; columnValues: string[] }> = [];

  for (const { columnKey, columnValue } of questionProperties) {
    if (!columnKey || !columnValue) {
      continue;
    }

    const existingItem = accumulator.find((item) => item.columnKey === columnKey);

    if (existingItem) {
      existingItem.columnValues.push(columnValue);
    } else {
      accumulator.push({
        columnKey,
        columnValues: [columnValue],
      });
    }
  }

  return accumulator;
});

onMounted(loadAllFields);
</script>

<template>
  <div>
    <div class="exam-header">
      <div class="title">
        {{ examData?.title }}
      </div>
      <div class="flex">
        <div class="flex-1">
          <span v-for="(field, idx) in groupQuestionPropertyList || []">
            <span :class="{ 'ml-4': idx !== 0 }"
              >{{ allFields.find((o) => o.columnKey === field.columnKey)?.columnName }}：</span
            >
            <span class="exam-header-value">{{ field.columnValues.join(', ') }}</span>
          </span>
        </div>
        <div>
          <span>试卷总分：</span>
          <span>{{ questionInfo.score }}分</span>
          <span class="ml-4">题量：</span>
          <span>{{ questionInfo.count }}题</span>
        </div>
      </div>
      <div class="flex" v-if="userAnswer">
        <div class="flex-1">
          <span>组织：</span>
          <span class="exam-header-value">
            <a-tooltip placement="bottom">
              <template #title>
                <div class="flex flex-col">
                  <div v-for="name in userAnswer.info.unitNames">{{ name }}</div>
                </div>
              </template>
              {{ userAnswer.info.unitNames?.at(0) || '--' }}
            </a-tooltip>
          </span>
          <span class="ml-4">人员：</span>
          <span class="exam-header-value">{{ userAnswer.info.userName || '--' }}</span>
        </div>
        <div>
          <span>考试得分：</span>
          <span>{{ userAnswer.info.score }}分</span>
        </div>
      </div>
    </div>

    <PreviewQuestions
      class="mt-20px"
      :question-types="examData?.questionTypeList || []"
      :questions="questions || []"
    >
      <template #question-item-suffix="{ question }">
        <div class="edit-tools" v-if="allowEdit">
          <template v-for="(tool, idx) in editTools">
            <a-button class="text-btn" type="link" @click="tool.onClick(question)">
              {{ tool.title }}
            </a-button>
            <div v-if="idx < editTools.length - 1">|</div>
          </template>
        </div>
        <div class="mb-4" v-else></div>
      </template>
    </PreviewQuestions>

    <EditQuestion @register="editModalRegister" :before-submit-fn="updateQuestion" />

    <BasicModal @register="promptModalRegister" @ok="confirmPrompt" :mask-closable="false">
      <BasicForm @register="showPointForm" />
    </BasicModal>
  </div>
</template>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: large;
  text-align: center;
  margin-bottom: 12px;
}

.question {
  .edit-tools {
    opacity: 0;

    display: flex;
    gap: 10px;

    color: #00996b;

    .text-btn {
      padding: 0;
    }
  }

  &:hover {
    .edit-tools {
      opacity: 1;
    }
  }
}

.exam-header-value {
  text-decoration: underline;
}
</style>
