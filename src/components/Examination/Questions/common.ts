import type { QuestionBankDetailListElement } from '/@/api/cddc.model';

export type IQuestion = QuestionBankDetailListElement & {
  bankId?: string;
};

export type IQuestionOption = NonNullable<IQuestion['questionBankOptionList']>[number];

export interface CommonQuestionProps {
  index: number;
  question: IQuestion;

  showAnswer?: boolean;
}

export interface CommonEditProps {
  value: IQuestion;
  showSource?: boolean
}
