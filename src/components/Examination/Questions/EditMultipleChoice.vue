<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { type CommonEditProps, type IQuestion, type IQuestionOption } from './common';
import { VueDraggable } from 'vue-draggable-plus';
import { number2alpha<PERSON>har } from '../utils';
import { computed, nextTick, ref, watch } from 'vue';
import { buildUUID } from '/@/utils/uuid';
import { remove } from 'lodash-es';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import { SvgIcon, type Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from './RichTextEditor.vue';

const props = defineProps<CommonEditProps>();
const emit = defineEmits(['update:value']);

const formRef = ref<FormInstance>();

const vValue = useVModel(props, 'value', emit);

const questionFormRules: Record<string, Rule | Rule[]> = {
  questionStem: [
    {
      required: true,
      message: '请输入题干',
      trigger: 'change',
    },
  ],
  questionBankOptionList: [
    {
      required: true,
      trigger: 'change',
      async validator(_rule, value: IQuestion['questionBankOptionList']) {
        if (!value?.length) {
          throw new Error('请至少创建两个选项');
        }

        const hasEmptyOption = value.some((n) => !n.optionDesc);

        if (hasEmptyOption) {
          throw new Error('选项内容不能为空');
        }
      },
    },
  ],
  _answer: [
    {
      required: true,
      trigger: 'change',
      async validator() {
        const hasAnswer = vValue.value.questionBankOptionList?.filter((n) => n.correctAnswer === 1);
        if (!hasAnswer?.length) {
          throw new Error('请选择答案');
        }
      },
    },
  ],
};

watch(
  () => props.value,
  async () => {
    await nextTick();
    formRef.value?.clearValidate();
  }
);

function addOption() {
  vValue.value.questionBankOptionList ||= [];

  vValue.value.questionBankOptionList.push({
    id: buildUUID(),
    correctAnswer: 0,
    optionDesc: '',
  });
}

function deleteOption(item: IQuestionOption) {
  remove(vValue.value.questionBankOptionList || [], (n) => n.id === item.id);
}

const questionAnswer = computed({
  get() {
    return vValue.value.questionBankOptionList
      ?.filter((item) => item.correctAnswer)
      .map((item) => item.id);
  },
  set(ids: string[] = []) {
    vValue.value._answer = ids;

    vValue.value.questionBankOptionList?.forEach((item) => {
      item.correctAnswer = ids.includes(item.id!) ? 1 : 0;
    });
  },
});

const answerOpts = computed(() => {
  return vValue.value.questionBankOptionList?.map((item, idx) => {
    return {
      label: number2alphaChar(idx),
      value: item.id,
    };
  });
});

defineExpose({
  async validate() {
    await formRef.value?.validate();
  },
});
</script>

<template>
  <a-form layout="vertical" :model="vValue" :rules="questionFormRules" ref="formRef">
    <a-form-item label="题干" name="questionStem">
      <RichTextEditor v-model:value="vValue.questionStem" :maxlength="200" showCount placeholder="请输入题干" />
      <a-button class="add-question-btn" type="link" :disabled="(vValue.questionBankOptionList || []).length >= 20" @click="addOption">
        <SvgIcon name="g-plus" />
        <span> 添加选项 </span>
      </a-button>
    </a-form-item>

    <a-form-item label="选项" name="questionBankOptionList">
      <VueDraggable
        class="flex flex-col gap-1"
        v-model="vValue.questionBankOptionList!"
        handle=".darg-handle"
      >
        <div
          class="flex items-center gap-2"
          v-for="(item, idx) in vValue.questionBankOptionList"
          :key="item.id"
        >
          <span class="darg-handle">
            <SvgIcon name="g-sort-handle" />
          </span>
          <span> {{ number2alphaChar(idx) }} </span>
          <span class="flex-1 w-0">
            <a-input v-model:value="item.optionDesc" :maxlength="200" placeholder="请输入选项" />
          </span>
          <a-button
            type="link"
            class="!pl-0"
            @click="deleteOption(item)"
            :disabled="(vValue.questionBankOptionList?.length || 0) < 3"
          >
            <SvgIcon name="g-trash" />
          </a-button>
        </div>
      </VueDraggable>
    </a-form-item>

    <a-form-item label="答案" name="_answer">
      <a-checkbox-group v-model:value="questionAnswer" :options="answerOpts" />
    </a-form-item>

    <a-form-item label="解析" name="answerAnalysis">
      <RichTextEditor v-model:value="vValue.answerAnalysis" :maxlength="200" showCount placeholder="请输入解析" />
    </a-form-item>
    <a-form-item
      v-if="!!showSource"
      label="来源"
      name="source"
      :rules="[
        {
          required: true,
          message: '请输入来源',
          trigger: 'change',
        },
      ]"
    >
      <RichTextEditor v-model:value="vValue.source" :maxlength="200" showCount placeholder="请输入来源" />
    </a-form-item>
  </a-form>
</template>

<style lang="less" scoped>
.darg-handle {
  cursor: move;
  display: flex;

  color: #999999;
}

.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
