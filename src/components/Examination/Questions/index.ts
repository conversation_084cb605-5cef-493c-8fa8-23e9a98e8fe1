import type { IQuestion } from './common';
import EditQuestion from './EditQuestion.vue';
import { QUESTION_TYPE } from './enum';
import QuestionChoice from './QuestionChoice.vue';
import QuestionConfirm from './QuestionConfirm.vue';
import QuestionFill from './QuestionFill.vue';
import QuestionShortAnswer from './QuestionShortAnswer.vue';

export {
  //
  QUESTION_TYPE,
  QuestionChoice,
  QuestionConfirm,
  QuestionFill,
  EditQuestion,
};

export type {
  //
  IQuestion,
};

export const QuestionComponentsMap = {
  [QUESTION_TYPE.SINGLE_CHOICE]: QuestionChoice,
  [QUESTION_TYPE.JUDGMENT]: QuestionConfirm,
  [QUESTION_TYPE.FILL_IN_THE_BLANK]: QuestionFill,
  [QUESTION_TYPE.MULTIPLE_CHOICE]: QuestionChoice,
  [QUESTION_TYPE.SHORT_ANSWER]: QuestionShortAnswer,
};
