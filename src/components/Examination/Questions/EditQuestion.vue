<script lang="ts" setup>
import { BasicModal, useModalInner, BasicForm, useForm } from '@geega-ui-plus/geega-ui';
import { QUESTION_TYPE, QuestionTypeOptions } from './enum';
import EditChoice from './EditChoice.vue';
import EditConfirm from './EditConfirm.vue';
import EditFill from './EditFill.vue';
import EditMultipleChoice from './EditMultipleChoice.vue';
import type { IQuestion } from './common';
import { computed, ref, nextTick } from 'vue';
import { cloneDeep } from 'lodash-es';
import { buildUUID } from '/@/utils/uuid';
import { getDictLabel } from '/@/dict';
import ParamsConfig from '/@/views/exam/home/<USER>/ParamsConfig.vue';
import { getParamsConfigFormSchemas } from './addQuestion';
import { QUESTION_DIFFICULT_DEGREE, QuestionDifficultOptions } from '/@/enums/questionEnum';
import EditShortAnswer from './EditShortAnswer.vue';

export interface EditQuestionProps {
  allowChangeType?: boolean;
  beforeSubmitFn?: (question: IQuestion) => any;
  allowParamsConfig?: boolean;
}

const formRef = ref();

const paramsConfigRef = ref<InstanceType<typeof ParamsConfig>>();

const props = defineProps<EditQuestionProps>();

const updatedValue = ref<IQuestion>({});

const [modalRegister, modalAction] = useModalInner((opt) => {
  const _q: IQuestion = cloneDeep(opt.question || {});

  _q.questionBankOptionList?.forEach((item) => {
    item.id ||= buildUUID();
  });

  // @ts-expect-error
  _q.difficultDegree = _q.difficultDegree?.toString() ?? QUESTION_DIFFICULT_DEGREE.EASY;

  formRef.value?.clearValidate?.();

  updatedValue.value = _q;

  if (props.allowParamsConfig) {
    nextTick(async () => {
      await formActions.resetFields();
      await formActions.setFieldsValue({
        questionPropertyList: [
          {
            columnKey: undefined,
            columnValue: undefined,
          },
        ],
        bankTypeId: opt.questionBankId,
      });
      await formActions.clearValidate();
    });
  }
});

const dialogTitle = computed(() => {
  const label = getDictLabel(QuestionTypeOptions, updatedValue.value.questionType);

  return `${props.allowParamsConfig ? '新增' : '编辑'}${String(label || '')}`;
});

const EditComponentMap = {
  [QUESTION_TYPE.SINGLE_CHOICE]: EditChoice,
  [QUESTION_TYPE.JUDGMENT]: EditConfirm,
  [QUESTION_TYPE.FILL_IN_THE_BLANK]: EditFill,
  [QUESTION_TYPE.MULTIPLE_CHOICE]: EditMultipleChoice,
  [QUESTION_TYPE.SHORT_ANSWER]: EditShortAnswer,
};

async function updateQuestionType() {
  const type = updatedValue.value.questionType;

  if (type === QUESTION_TYPE.JUDGMENT) {
    updatedValue.value.questionBankOptionList = [
      {
        id: buildUUID(),
        optionDesc: '对',
        correctAnswer: 1,
      },
      {
        id: buildUUID(),
        optionDesc: '错',
      },
    ];
  } else if (type === QUESTION_TYPE.FILL_IN_THE_BLANK) {
    updatedValue.value.questionBankOptionList = [
      {
        id: buildUUID(),
        optionDesc: '',
        correctAnswer: 1,
      },
    ];
  } else if (type === QUESTION_TYPE.SHORT_ANSWER) {
    updatedValue.value.questionBankOptionList = [
      {
        id: buildUUID(),
        optionDesc: '',
        correctAnswer: 1,
      },
    ];
  } else if (type === QUESTION_TYPE.SINGLE_CHOICE) {
    updatedValue.value.questionBankOptionList = [
      {
        id: buildUUID(),
        optionDesc: '',
        correctAnswer: 1,
      },
      {
        id: buildUUID(),
        optionDesc: '',
      },
    ];
  } else if (type === QUESTION_TYPE.MULTIPLE_CHOICE) {
    updatedValue.value.questionBankOptionList = [
      {
        id: buildUUID(),
        optionDesc: '',
      },
      {
        id: buildUUID(),
        optionDesc: '',
      },
    ];
  }
}

async function confirm() {
  let errorMark = false;

  let values = {};
  if (props.allowParamsConfig) {
    try {
      values = await formActions.validate();
    } catch (error) {
      errorMark = true;
    }
  }
  try {
    await formRef.value.validate();
  } catch (error) {
    errorMark = true;
  }
  if (errorMark) return;

  modalAction.setModalProps({
    okButtonProps: {
      loading: true,
    },
  });

  try {
    await props.beforeSubmitFn?.(
      cloneDeep({
        ...updatedValue.value,
        ...(props.allowParamsConfig ? values : {}),
      })
    );
  } catch (error) {
    console.log(error);
  }

  modalAction.setModalProps({
    okButtonProps: {
      loading: false,
    },
  });

  modalAction.closeModal();
}

const [registerForm, formActions] = useForm({
  model: {
    questionPropertyList: [
      {
        columnKey: undefined,
        columnValue: undefined,
      },
    ],
  },
  schemas: getParamsConfigFormSchemas({
    onChange: () => {
      paramsConfigRef.value?.clearConfig?.();
    },
  }),
  showActionButtonGroup: false,
});
</script>

<template>
  <BasicModal
    @register="modalRegister"
    :title="dialogTitle"
    @ok="confirm"
    width="400px"
    :mask-closable="false"
    wrapClassName="creat-question-modal"
  >
    <div>
      <BasicForm v-if="allowParamsConfig" @register="registerForm">
        <template #params-filter-slot="{ model }">
          <ParamsConfig
            v-model:value="model.questionPropertyList"
            ref="paramsConfigRef"
            :bankTypeId="model.bankTypeId"
            :control-column-key-once="true"
            @valueSplice="formActions.validateFields(['questionPropertyList'])"
          />
        </template>
      </BasicForm>
      <div class="flex items-center gap-2 mb-2">
        <span>题型</span>
        <a-select
          v-if="allowChangeType"
          v-model:value="updatedValue.questionType"
          :options="QuestionTypeOptions"
          @change="updateQuestionType"
        />
        <span class="ml-1">难度</span>
        <a-select
          v-if="allowChangeType"
          v-model:value="updatedValue.difficultDegree"
          :options="QuestionDifficultOptions"
        />
      </div>

      <component
        ref="formRef"
        :is="EditComponentMap[updatedValue.questionType!]"
        :show-source="true"
        v-model:value="updatedValue"
      />
    </div>
  </BasicModal>
</template>

<style lang="less" scoped>
:deep(.@{ant-prefix}-form-item-control-input-content) > div > div {
  width: 100%;
}
</style>

<style lang="less">
.creat-question-modal {
  .scrollbar__bar.is-vertical {
    display: none;
  }
}
</style>
