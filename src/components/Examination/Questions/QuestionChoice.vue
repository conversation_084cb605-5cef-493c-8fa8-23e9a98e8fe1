<script lang="ts" setup>
import { number2alphaChar } from '../utils';
import { computed } from 'vue';
import type { CommonQuestionProps } from './common';

export interface FillQuestionProps extends CommonQuestionProps {
  userAnswer?: number[];
}

const props = defineProps<FillQuestionProps>();

const answers = computed(() => {
  const rightAnswers: number[] = [];

  props.question.questionBankOptionList?.forEach((item, idx) => {
    if (item.correctAnswer === 1) {
      rightAnswers.push(idx);
    }
  });

  return rightAnswers.map((n) => number2alphaChar(n)).join(' ');
});

const userAnswerStr = computed(() => props.userAnswer?.map((n) => number2alphaChar(+n)).join(', '));
</script>

<template>
  <div class="question">
    <div class="title flex gap-1">
      <span class="order-number" v-if="index != null"> {{ index }}、 </span>
      <span class="break-all" v-html="question.questionStem"></span>
    </div>
    <ol class="choices">
      <li
        class="choice break-all"
        v-for="answer in question.questionBankOptionList"
        v-html="answer.optionDesc"
      />
    </ol>

    <div
      v-if="userAnswer != null"
      class="user-answer"
      :class="{
        'right-answer': userAnswerStr === answers,
      }"
    >
      <span class="break-keep">已选答案：</span>
      <span class="break-all">
        {{ userAnswerStr }}
      </span>
    </div>

    <template v-if="showAnswer">
      <div class="answers">
        <span class="break-keep">参考答案：</span>
        <span class="break-all">
          {{ answers }}
        </span>
      </div>
      <div class="analyze" v-if="question.answerAnalysis">
        <span class="label">答案解析：</span>
        <span class="content break-all" v-html="question.answerAnalysis"></span>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
@import './question.less';
</style>
