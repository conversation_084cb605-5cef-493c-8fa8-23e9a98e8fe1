<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { type CommonEditProps, type IQuestion } from './common';
import { nextTick, ref, watch } from 'vue';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import { type Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from './RichTextEditor.vue';

const props = defineProps<CommonEditProps>();
const emit = defineEmits(['update:value']);

const formRef = ref<FormInstance>();

const vValue = useVModel(props, 'value', emit);

const questionFormRules: Record<string, Rule | Rule[]> = {
  questionStem: [
    {
      required: true,
      message: '请输入题干',
      trigger: 'change',
    },
  ],
  questionBankOptionList: [
    {
      required: true,
      trigger: 'change',
      async validator(_rule, value: IQuestion['questionBankOptionList']) {
        if (!value?.length) {
          throw new Error('请至少创建一个答案');
        }

        const hasEmptyOption = value.some((n) => !n.optionDesc);

        if (hasEmptyOption) {
          throw new Error('答案不能为空');
        }
      },
    },
  ],
};

watch(
  () => props.value,
  async () => {
    await nextTick();
    formRef.value?.clearValidate();
  }
);

defineExpose({
  async validate() {
    await formRef.value?.validate();
  },
});
</script>

<template>
  <a-form layout="vertical" :model="vValue" :rules="questionFormRules" ref="formRef">
    <a-form-item label="题干" name="questionStem">
      <RichTextEditor
        v-model:value="vValue.questionStem"
        :maxlength="200"
        showCount
        placeholder="请输入题干"
      />
    </a-form-item>

    <a-form-item label="答案" name="questionBankOptionList">
      <div
        class="flex items-center gap-2"
        v-for="(item, _idx) in vValue.questionBankOptionList"
        :key="item.id"
      >
        <span class="flex-1 w-0">
          <a-input v-model:value="item.optionDesc" :maxlength="200" placeholder="请输入答案" />
        </span>
      </div>
    </a-form-item>

    <a-form-item label="解析" name="answerAnalysis">
      <RichTextEditor
        v-model:value="vValue.answerAnalysis"
        :maxlength="200"
        showCount
        placeholder="请输入解析"
      />
    </a-form-item>
    <a-form-item
      v-if="!!showSource"
      label="来源"
      name="source"
      :rules="[
        {
          required: true,
          message: '请输入来源',
          trigger: 'change',
        },
      ]"
    >
      <RichTextEditor
        v-model:value="vValue.source"
        :maxlength="200"
        showCount
        placeholder="请输入来源"
      />
    </a-form-item>
  </a-form>
</template>

<style lang="less" scoped>
.darg-handle {
  cursor: move;
  display: flex;

  color: #999999;
}

.add-question-btn {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid #00996b;

  display: inline-flex;
  align-items: center;
}
</style>
