import type { FormSchema } from '@geega-ui-plus/geega-ui';
import {
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
} from '/@/api/cddc.req';
import type { QuestionPropertyListElement  } from '/@/api/cddc.model';

interface Opt {
  onChange?: (...rest: any[]) => void
}

export function getParamsConfigFormSchemas(opt: Opt = {}): FormSchema[] {
  return [
    {
      field: 'bankTypeId',
      label: '题库',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: async () => {
          const resp = await V1ManageQuestionBankTypeGetQuestionBankTypeListPost({})
          return resp?.map(o => ({ label: o.name, value: o.id }))
        },
        onChange: (value?: string) => {
          opt.onChange?.('bankTypeId', value)
        },
        showSearch: true,
        optionFilterProp: 'label'
      },
    },
    {
      field: 'questionPropertyList',
      label: '参数筛选',
      required: true,
      component: 'Select',
      slot: 'params-filter-slot',
      rules: [
        {
          required: true,
          async validator(rule, value: QuestionPropertyListElement [] = []) {

            const everyFillComplete = value.every(o => o.columnKey && o.columnValue)

            if (!everyFillComplete) {
              throw rule.message;
            }
          },
          message: '请完善参数筛选',
        },
      ]
    },
  ]
}
