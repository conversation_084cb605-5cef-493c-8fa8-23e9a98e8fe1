<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { IQuestion } from './common';
import { QuestionComponentsMap } from '.';
import { BasicModal, useModalInner } from '@geega-ui-plus/geega-ui';
import { getDictLabel } from '/@/dict';
import { QuestionTypeOptions } from './enum';

const question = ref<IQuestion>({});

const [modalRegister, _modalActions] = useModalInner((opt) => {
  question.value = opt.question || {};
});

const Component = computed(() => {
  const q = question.value || {};

  return QuestionComponentsMap[q.type];
});

const modalTitle = computed(() =>
  getDictLabel(QuestionTypeOptions, question.value.type).toString()
);
</script>

<template>
  <BasicModal
    @register="modalRegister"
    :title="modalTitle"
    :show-ok-btn="false"
    width="400px"
    :height="400"
    :mask-closable="false"
    wrapClassName="creat-question-modal"
  >
    <component :is="Component" :question="question" show-answer />
  </BasicModal>
</template>

<style lang="less">
.creat-question-modal {
  .scrollbar__bar.is-vertical {
  display: none;
}
}
</style>
