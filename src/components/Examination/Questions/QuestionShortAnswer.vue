<script lang="ts" setup>
import type { CommonQuestionProps } from './common';

export interface FillQuestionProps extends CommonQuestionProps {
  userAnswer?: string[];
}

const props = defineProps<FillQuestionProps>();
</script>

<template>
  <div class="question">
    <div class="title flex gap-1 mb-4">
      <span class="order-number" v-if="index != null"> {{ index }}、 </span>
      <span class="break-all" v-html="question.questionStem"></span>
    </div>

    <div v-if="userAnswer != null" class="flex">
      <span class="break-keep">已填答案：</span>
      <div class="answer-content">
        <span v-for="(answer, idx) in userAnswer" class="inline-block mr-6px">
          <span
            class="fill-answer user-answer"
            :class="{
              'right-answer': answer === question.questionBankOptionList?.at(idx)?.optionDesc,
            }"
            v-html="answer"
          >
          </span>
        </span>
      </div>
    </div>

    <template v-if="showAnswer">
      <div class="answers flex">
        <span class="break-keep">参考答案：</span>
        <div class="answer-content">
          <span v-for="answer in question.questionBankOptionList" class="inline-block mr-6px">
            <span class="fill-answer break-all" v-html="answer.optionDesc"> </span>
          </span>
        </div>
      </div>
      <div class="analyze" v-if="question.answerAnalysis">
        <span class="label">答案解析：</span>
        <span class="content break-all" v-html="question.answerAnalysis"></span>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
@import './question.less';

.fill-answer {
  display: inline-block;
  text-decoration: underline;
}
</style>
