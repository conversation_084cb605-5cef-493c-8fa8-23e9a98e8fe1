<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { type CommonEditProps, type IQuestion } from './common';
import { number2alphaChar } from '../utils';
import { computed, nextTick, ref, watch } from 'vue';
import type { FormInstance } from '@geega-ui-plus/ant-design-vue';
import type { Rule } from '@geega-ui-plus/geega-ui';
import RichTextEditor from './RichTextEditor.vue';

const props = defineProps<CommonEditProps>();
const emit = defineEmits(['update:value']);

const vValue = useVModel(props, 'value', emit);

const formRef = ref<FormInstance>();

const questionAnswer = computed({
  get() {
    return vValue.value.questionBankOptionList?.find((item) => item.correctAnswer)?.id;
  },
  set(id: string) {
    vValue.value._answer = id;

    vValue.value.questionBankOptionList?.forEach((item) => {
      item.correctAnswer = item.id === id ? 1 : 0;
    });
  },
});

const questionFormRules: Record<string, Rule | Rule[]> = {
  questionStem: [
    {
      required: true,
      message: '请输入题干',
      trigger: 'change',
    },
  ],
  questionBankOptionList: [
    {
      required: true,
      trigger: 'change',
      async validator(_rule, value: IQuestion['questionBankOptionList']) {
        if (!value?.length) {
          throw new Error('请至少创建两个选项');
        }

        const hasEmptyOption = value.some((n) => !n.optionDesc);

        if (hasEmptyOption) {
          throw new Error('选项内容不能为空');
        }
      },
    },
  ],
  _answer: [
    {
      required: true,
      trigger: 'change',
      async validator() {
        const hasAnswer = vValue.value.questionBankOptionList?.filter((n) => n.correctAnswer === 1);
        if (!hasAnswer?.length) {
          throw new Error('请选择答案');
        }
      },
    },
  ],
};

watch(
  () => props.value,
  async () => {
    await nextTick();
    formRef.value?.clearValidate();
  }
);

defineExpose({
  async validate() {
    await formRef.value?.validate();
  },
});
</script>

<template>
  <a-form layout="vertical" :model="vValue" :rules="questionFormRules" ref="formRef">
    <a-form-item label="题干" name="questionStem">
      <RichTextEditor v-model:value="vValue.questionStem" :maxlength="200" showCount placeholder="请输入题干" />
    </a-form-item>

    <a-form-item label="选项" name="questionBankOptionList">
      <div
        class="flex-inline items-center gap-1 mr-4"
        v-for="(item, idx) in vValue.questionBankOptionList"
        :key="item.id"
      >
        <span> {{ number2alphaChar(idx) }} </span>
        <span> {{ item.optionDesc }} </span>
      </div>
    </a-form-item>

    <a-form-item label="答案" name="_answer">
      <a-radio-group v-model:value="questionAnswer">
        <a-radio
          v-for="(item, idx) in vValue.questionBankOptionList"
          :key="item.id"
          :value="item.id"
        >
          {{ number2alphaChar(idx) }}
        </a-radio>
      </a-radio-group>
    </a-form-item>

    <a-form-item label="解析" name="answerAnalysis">
      <RichTextEditor v-model:value="vValue.answerAnalysis" :maxlength="200" showCount placeholder="请输入解析" />
    </a-form-item>
    <a-form-item
      v-if="!!showSource"
      label="来源"
      name="source"
      :rules="[
        {
          required: true,
          message: '请输入来源',
          trigger: 'change',
        },
      ]"
    >
      <RichTextEditor v-model:value="vValue.source" :maxlength="200" showCount placeholder="请输入来源" />
    </a-form-item>
  </a-form>
</template>

<style lang="less" scoped></style>
