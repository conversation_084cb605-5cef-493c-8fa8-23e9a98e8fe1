<script lang="ts" setup>
import { EditQuestion, type IQuestion } from './Questions';
import { useModal } from '@geega-ui-plus/geega-ui';
import { useMessage } from '/@/hooks/web/useMessage';
import PreviewQuestions from './PreviewQuestions.vue';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';

export interface ExamPreviewProps {
  allowEdit?: boolean;
  questionTypes?: ResultQuestionTypeList[];
  questions?: IQuestion[];
}

const props = defineProps<ExamPreviewProps>();

const emit = defineEmits(['update:questions', 'remove']);

const [editModalRegister, editModalActions] = useModal();

const { createDeteleConfirm } = useMessage();

const editTools = [
  {
    title: '编辑',
    onClick: (question: IQuestion) => {
      editModalActions.openModal(true, {
        question,
      });
    },
  },
  {
    title: '删除',
    onClick: (question: IQuestion) => {
      createDeteleConfirm({
        content: '请确定是否删除该题目？',
        onOk: () => {
          removeQuestion(question);
        },
      });
    },
  },
];

function removeQuestion(question: IQuestion) {
  changeQuestions(question, 'del');
}

function updateQuestion(question: IQuestion) {
  changeQuestions(question, 'edit');
}

function changeQuestions(question: IQuestion, mode: string) {
  const _questions = props.questions?.slice() || [];

  const idx = _questions.findIndex((n) => n.id === question.id);

  if (mode === 'edit') {
    _questions.splice(idx, 1, question);
  } else if (mode === 'del') {
    _questions.splice(idx, 1);
  }
  emit('update:questions', _questions);
}
</script>

<template>
  <div>
    <PreviewQuestions :questions="questions || []">
      <template #question-item-suffix="{ question }">
        <div class="edit-tools" v-if="allowEdit">
          <template v-for="(tool, idx) in editTools">
            <a-button class="text-btn" type="link" @click="tool.onClick(question)">
              {{ tool.title }}
            </a-button>
            <div v-if="idx < editTools.length - 1">|</div>
          </template>
        </div>
        <div class="mb-4" v-else></div>
      </template>
    </PreviewQuestions>

    <EditQuestion @register="editModalRegister" :before-submit-fn="updateQuestion" />
  </div>
</template>

<style lang="less" scoped>
.question {
  .edit-tools {
    opacity: 0;

    display: flex;
    gap: 10px;

    color: #00996b;

    .text-btn {
      padding: 0;
    }
  }

  &:hover {
    .edit-tools {
      opacity: 1;
    }
  }
}
</style>
