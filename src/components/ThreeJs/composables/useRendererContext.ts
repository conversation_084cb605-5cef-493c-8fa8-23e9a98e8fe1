import { inject, provide } from 'vue';

import type { SimpleThree } from '../SimpleThree';

export interface RendererContext {
  $$: SimpleThree;
}

export const RendererContextKey = Symbol.for('three-renderer');

export const provideRenderContext = <T extends SimpleThree>(ctx: T) =>
  provide(RendererContextKey, ctx);

export const useRendererContext = <T extends SimpleThree = SimpleThree>() => {
  const value = inject<RendererContext>(RendererContextKey) as unknown as T;

  if (!value) {
    throw new Error('Can not find ThreeJS renderer context');
  }

  return value;
};
