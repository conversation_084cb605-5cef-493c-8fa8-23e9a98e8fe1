import { onMounted, onUnmounted } from 'vue';
import { useRendererContext } from './useRendererContext';
import type { SimpleThree } from '../SimpleThree';

export interface UseRenderFnOption {
  runAt?: 'before' | 'after';
  ctx?: SimpleThree;
}

export function useRenderFn(fn: () => void, opt: UseRenderFnOption) {
  const { runAt = 'before', ctx } = opt;
  const $$ = ctx ?? useRendererContext();

  onMounted(() => $$.addRenderFn(fn, runAt));
  onUnmounted(() => $$.removeRenderFn(fn, runAt));
}
