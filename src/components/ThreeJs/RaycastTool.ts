import {
  Mesh,
  Vector2,
  Object3D,
  <PERSON><PERSON>er<PERSON><PERSON><PERSON>,
  EventDispatcher,
  Raycaster,
  type Vector2Like,
} from 'three';
import type { SimpleThree } from './SimpleThree';
import { acceleratedRaycast, MeshBVH } from 'three-mesh-bvh';
import { debounce } from 'lodash-es';

Mesh.prototype.raycast = acceleratedRaycast;

export type RaycastEventTypes = keyof RaycastToolEventMap;

export class RaycastEvent {
  pos: Vector2Like;

  mesh?: Mesh | null;
  type: RaycastEventTypes;

  constructor(type: RaycastEventTypes, pos: Vector2Like, data?: Mesh | null) {
    this.pos = pos;

    this.type = type;
    this.mesh = data;
  }
}

export type RaycastToolEventMap = {
  pointermove: RaycastEvent;
  click: RaycastEvent;
};

export interface BVHRaycastToolOptions {
  enabledEvents?: RaycastEventTypes[];
  debounce?: number;
}

export class BVHRaycastTool extends EventDispatcher<RaycastToolEventMap> {
  _objects?: Object3D[];
  _raycaster = new Raycaster();

  enabledEvents: RaycastEventTypes[] = ['pointermove', 'click'];

  get objects() {
    return this._objects;
  }

  set objects(value) {
    this._objects = value;
    this.updateBVH(false);
  }

  /**
   * used by check click event
   */
  _mousePos: Vector2Like = { x: 0, y: 0 };

  constructor(
    private $$: SimpleThree,
    readonly opt?: BVHRaycastToolOptions
  ) {
    super();

    this._raycaster.firstHitOnly = true;
    if (opt?.enabledEvents) {
      this.enabledEvents = opt?.enabledEvents;
    }

    this._onMousemove = opt?.debounce
      ? debounce(this._onMousemove, opt?.debounce)
      : this._onMousemove;

    $$.dom.addEventListener('pointermove', this._onMousemove);
    $$.dom.addEventListener('pointerup', this._onMouseup);
    $$.dom.addEventListener('pointerdown', this._onMousedown);
  }

  _onMouseup = (evt: MouseEvent) => {
    // check click
    const shouldBeClick =
      (this._mousePos.x - evt.offsetX) ** 2 + (this._mousePos.y - evt.offsetY) ** 2 <= 4;

    if (!shouldBeClick) {
      return;
    }

    if (!this.enabledEvents.includes('click')) {
      return
    }

    this._doRaycast(evt, 'click');
  };

  _onMousemove = (evt: MouseEvent) => {
    const eventName = evt.type === 'pointermove' ? 'pointermove' : 'click';

    if (!this.enabledEvents.includes(eventName)) {
      return;
    }

    this._doRaycast(evt, 'pointermove');
  };

  _onMousedown = (evt: MouseEvent) => {
    this._mousePos = {
      x: evt.offsetX,
      y: evt.offsetY,
    };
  };

  _doRaycast = (evt: MouseEvent, type: RaycastEventTypes) => {
    if (!this.objects?.length) {
      return;
    }

    const pos = new Vector2(evt.offsetX, evt.offsetY);

    const test = this.pickUp(pos);

    const mesh = test.find((n) => n.object instanceof Mesh)?.object as Mesh | null;

    this.dispatchEvent(new RaycastEvent(type, pos, mesh));
  };

  _generateBVH(mesh: Mesh, force: boolean) {
    const geom = mesh.geometry as BufferGeometry;

    if (geom.boundsTree instanceof MeshBVH && !force) {
      return;
    }

    geom.boundsTree = new MeshBVH(geom);
  }

  updateBVH(force = false) {
    for (const item of this.objects || []) {
      if (item instanceof Mesh) {
        this._generateBVH(item, force);
      }

      item.traverse((child) => {
        if (child instanceof Mesh) {
          this._generateBVH(child, force);
        }
      });
    }
  }

  pickUp(pos: Vector2) {
    const ctx = this.$$;

    const size = ctx.size;

    const p = new Vector2();

    p.x = (pos.x / size.width) * 2 - 1;
    p.y = -(pos.y / size.height) * 2 + 1;

    this._raycaster.setFromCamera(p, ctx.camera);

    const intersections = this._raycaster.intersectObjects(this.objects || []);

    return intersections;
  }

  dispose() {
    const $$ = this.$$;

    $$.dom.removeEventListener('pointermove', this._onMousemove);
    $$.dom.removeEventListener('pointerup', this._onMousemove);
    $$.dom.removeEventListener('pointerdown', this._onMousedown);
  }
}
