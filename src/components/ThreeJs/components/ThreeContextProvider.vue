<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { SimpleThree, type SimpleThreeOption } from '../SimpleThree';
import { provideRenderContext } from '../composables';
import { useResizeObserver } from '@vueuse/core';

export interface ThreeContextProps {
  initOption?: SimpleThreeOption
  setup?: ($$: SimpleThree) => any;
}

const props = defineProps<ThreeContextProps>();

const renderCtx = new SimpleThree(props.initOption);

const canvasRef = ref<HTMLCanvasElement>();

provideRenderContext(renderCtx);

const state = reactive({
  initialized: false,
});

onMounted(initThree);

onUnmounted(() => {
  renderCtx.dispose();
});

useResizeObserver(canvasRef, () => {
  renderCtx.resize();
});

async function initThree() {
  canvasRef.value?.appendChild(renderCtx.$r.domElement);

  if (renderCtx.$css2?.domElement) {
    canvasRef.value?.appendChild(renderCtx.$css2.domElement);
  }

  await props.setup?.(renderCtx);

  renderCtx.resize();
  state.initialized = true;
}
</script>

<template>
  <div class="render-model-box">
    <div ref="canvasRef"></div>
    <slot v-if="state.initialized"></slot>
  </div>
</template>

<style lang="less" scoped>
.render-model-box {
  width: 100%;
  height: 100%;
  margin: 0;
  border: 0;
  padding: 0;
  position: relative;

  .loading {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 100;
    top: 0;
    left: 0;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
