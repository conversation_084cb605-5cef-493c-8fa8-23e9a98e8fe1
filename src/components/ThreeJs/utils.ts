import { VertexNormalsHelper } from 'three/examples/jsm/helpers/VertexNormalsHelper';
import { Object3D, Mesh, Box3, Vector3 } from 'three';
import type { IDisposable } from './types';

export function isMesh(object: Object3D): object is Mesh {
  return object instanceof Mesh;
}

export const isSupportedWebGL = (() => {
  try {
    var canvas = document.createElement('canvas');
    return !!(
      window.WebGLRenderingContext &&
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    );
  } catch (e) {
    return false;
  }
})();

export function getObjectWrapperBox(object: Object3D) {
  return new Box3().setFromObject(object);
}

export function getObjectCenterPoint(object: Object3D) {
  return getObjectWrapperBox(object).getCenter(new Vector3());
}

export function getObjectSize(object: Object3D) {
  const box = getObjectWrapperBox(object);

  return box.getSize(new Vector3());
}

export function isDisposable(object: Object3D): object is Object3D & IDisposable {
  return 'dispose' in object && typeof object.dispose === 'function';
}

/**
 * getBoundingClientRect 的 width/height 在旋转之后，会调换
 *
 * 因此取 clientWidth 和 clientHeight
 *
 * @param el
 */
export function getElementSize(el: HTMLElement) {
  return {
    width: el.clientWidth,
    height: el.clientHeight,
  };
}

export function ensureArray<T>(o?: T | T[]): T[] {
  return o == null ? [] : Array.isArray(o) ? o : [o];
}

export function generateMeshNormal(object: Object3D) {
  const group = new Object3D();

  let failedCount = 0;
  object.traverse((node) => {
    if (isMesh(node)) {
      try {
        const vnh = new VertexNormalsHelper(node, 10);
        group.add(vnh);
      } catch (error) {
        failedCount++;
      }
    }
  });

  if (failedCount) {
    console.warn('generate mesh normal failed:', failedCount);
  }
  return group;
}
