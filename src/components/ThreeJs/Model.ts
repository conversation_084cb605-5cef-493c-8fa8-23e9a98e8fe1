import { Object3D } from 'three';
import { MaterialManager } from './MaterialManager';
import type { Material } from 'three';

export abstract class Model<TMaterialType = string> extends Object3D {
  protected materials = new MaterialManager<TMaterialType>();

  _materialType: TMaterialType;

  material: Material;

  get materialType() {
    return this._materialType;
  }

  constructor(defaultMaterial: TMaterialType) {
    super();

    this.initMaterials();
    this._materialType = defaultMaterial;
    this.material = this.materials.get(this._materialType)!.clone();
  }

  abstract initMaterials(): void;

  /**
   * 更新模型材质
   *
   * @param type
   */
  setMaterial(type: TMaterialType) {
    if (type === this._materialType) {
      return;
    }

    this._materialType = type;

    const newMaterial = this.materials.get(type)!;
    this.material.copy(newMaterial);
  }

  dispose() {
    for (const m of this.materials.values()) {
      m.dispose();
    }
  }
}
