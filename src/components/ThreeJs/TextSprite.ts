import { SpriteMaterial, CanvasTexture, Sprite, Vector2 } from 'three';
import { ensureArray } from './utils';

export class TextSprite extends Sprite {
  static canvas?: HTMLCanvasElement;

  texts: string[] = [];

  fontSize = 40;
  font = 'Arial';
  color = '#ff0000';

  _spriteMaterial: SpriteMaterial;

  /**
   * Auto calculate if not set.
   */
  width?: number;

  /**
   * @default 100
   */
  lineHeight = 60;
  background = 'rgba(0,0,0,0)';

  padding = new Vector2(20, 10);

  debug = false;

  constructor(txt: string | string[] = '') {
    super();
    this._spriteMaterial = new SpriteMaterial();

    this.texts = ensureArray(txt);

    this.build();
  }

  _getCanvas() {
    if (!TextSprite.canvas) {
      TextSprite.canvas = document.createElement('canvas');
    }

    if (this.debug) {
      const $ = TextSprite.canvas;
      document.body.append($);
      $.style.position = 'fixed';
      $.style.top = '0';
      $.style.left = '0';
      $.style.zIndex = '99999999';
      $.style.border = '1px solid red';
    }

    return TextSprite.canvas;
  }

  build() {
    const canvas = this._getCanvas();
    const _height = this.lineHeight;

    canvas.height = this.texts.length * _height + this.padding.y * 2;

    const ctx = canvas.getContext('2d')!;

    // set style
    ctx.font = `${this.fontSize}px ${this.font}`;

    // auto calculate width, must after set style
    const _width = this._calcWidth();
    canvas.width = _width + this.padding.x * 2;

    // clear background
    ctx.fillStyle = this.background;
    ctx.beginPath();
    ctx.roundRect(0, 0, canvas.width, canvas.height, 20);
    ctx.fill();

    // draw text

    ctx.font = `${this.fontSize}px ${this.font}`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = this.color;

    this.texts.forEach((txt, idx) => drawText(txt, idx, this.padding.x, this.padding.y));

    if (this._spriteMaterial) {
      this._spriteMaterial.map?.dispose();
    }

    this._spriteMaterial.map = new CanvasTexture(canvas);

    this.material = this._spriteMaterial;
    //
    this.scale.set(canvas.width, canvas.height, 1).multiplyScalar(0.008);

    function drawText(text: string, idx: number, offsetX: number, offsetY: number) {
      const h = idx * _height;

      ctx.fillText(text, offsetX, offsetY + h + _height / 2);
    }
  }

  _calcWidth() {
    if (this.width) return this.width;

    const canvas = TextSprite.canvas!;
    const ctx = canvas.getContext('2d')!;

    return Math.max(...this.texts.map((txt) => ctx.measureText(txt).width));
  }

  setText(text: string | string[], width?: number, height?: number) {
    this.texts = ensureArray(text);

    if (width) this.width = width;
    if (height) this.lineHeight = height;

    this.build();
  }

  dispose() {
    this._spriteMaterial.map?.dispose();
    this._spriteMaterial.dispose();
  }
}
