import {
  Scene,
  WebGLRenderer,
  PerspectiveC<PERSON>ra,
  Color,
  HemisphereLight,
  AxesHelper,
  Vector2,
  Vector3,
  type Vector2Like,
  type Vector3<PERSON>ike,
  EventDispatcher,
} from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer';
import { ViewHelper } from 'three/examples/jsm/helpers/ViewHelper';
import { getElementSize } from './utils';
import type { IDisposable } from './types';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';

export class SimpleThreeResizeEvent {
  readonly type = 'resize';
  constructor(readonly size: Vector2) {}
}

export type SimpleThreeEventMap = {
  resize: SimpleThreeResizeEvent;
};

export interface SimpleThreeOption {
  debug?: boolean;
  hideViewHelper?: boolean;
  disableCSS2D?: boolean;
}

export class SimpleThree extends EventDispatcher<SimpleThreeEventMap> {
  $r: WebGLRenderer;
  $css2?: CSS2DRenderer;

  scene = new Scene();

  camera = new PerspectiveCamera(45, 1, 0.001, 1_0000);

  orbit: OrbitControls;

  composer: EffectComposer;

  debug = false;

  _beforeRenderFns = new Set<() => void>();
  _afterRenderFns = new Set<() => void>();

  _disposables = new Set<IDisposable>();

  _userData: Record<string, any> = {};

  _shouldResize = false;

  get size() {
    const _size = getElementSize(this.$r.domElement);
    return new Vector2(_size.width, _size.height);
  }

  get dom() {
    return this.$css2?.domElement || this.$r.domElement;
  }

  _viewHelper?: ViewHelper;

  constructor(opt?: SimpleThreeOption) {
    super();
    this.$r = new WebGLRenderer({
      antialias: true,
      alpha: true,
      precision: 'lowp',
    });

    if (!opt?.disableCSS2D) {
      this._initCSS2D();
    }

    this.$r.autoClear = false;

    this.$r.setPixelRatio(window.devicePixelRatio);

    this.$r.domElement.style.position = 'absolute';
    this.$r.domElement.style.width = '100%';
    this.$r.domElement.style.height = '100%';

    if (!opt?.hideViewHelper) {
      this._initViewHelper();
    }

    {
      // init orbit controls
      const orbit = new OrbitControls(this.camera, this.dom);
      orbit.minDistance = 0.001;

      orbit.minZoom = 0.001;
      orbit.maxZoom = 3;
      orbit.zoomSpeed = 5;
      orbit.rotateSpeed = 1;

      orbit.dampingFactor = 0.1;
      orbit.enableDamping = true;
      this.orbit = orbit;
    }

    this.debug = !!opt?.debug;
    if (this.debug) {
      this._initDebug();
    }

    this.$r.setAnimationLoop(this._renderLoop);

    {
      // init composer
      this.composer = new EffectComposer(this.$r);

      const renderPass = new RenderPass(this.scene, this.camera);
      this.composer.addPass(renderPass);
    }
  }

  _initCSS2D() {
    this.$css2 = new CSS2DRenderer();
    this.$css2.domElement.style.position = 'absolute';
    this.$css2.domElement.style.top = '0';
    this.$css2.domElement.style.left = '0';
  }

  _initViewHelper() {
    const helper = new ViewHelper(this.camera, this.dom);
    this._viewHelper = helper;
  }

  _initDebug() {
    this.scene.background = new Color(0xf0f0f0);

    const light = new HemisphereLight(0xffffff, 0x080808, 1);
    light.position.set(-1.25, 1, 1.25);
    this.scene.add(light);

    const axesHelper = new AxesHelper(50);
    this.scene.add(axesHelper);
  }

  _render() {
    this.composer.render();

    this.$css2?.render(this.scene, this.camera);
  }

  _renderLoop = () => {
    for (const fn of this._beforeRenderFns.values()) {
      fn();
    }

    if (this._shouldResize) {
      this._shouldResize = false;
      this._resize();
    }

    this.orbit.update();

    this._render();

    this._viewHelper?.render(this.$r);

    for (const fn of this._afterRenderFns.values()) {
      fn();
    }
  };

  _resize() {
    const size = this.size;

    const camera = this.camera;
    camera.aspect = size.width / size.height;
    camera.updateProjectionMatrix();

    this.$r.setSize(size.width, size.height, false);
    this.composer.setSize(size.width, size.height);
    this.$css2?.setSize(size.width, size.height);

    this.dispatchEvent(new SimpleThreeResizeEvent(size));
  }

  addRenderFn(fn: () => void, runAt: 'before' | 'after' = 'before') {
    if (runAt === 'before') {
      this._beforeRenderFns.add(fn);
    } else {
      this._afterRenderFns.add(fn);
    }
  }

  removeRenderFn(fn: () => void, runAt: 'before' | 'after' = 'before') {
    if (runAt === 'before') {
      this._beforeRenderFns.delete(fn);
    } else {
      this._afterRenderFns.delete(fn);
    }
  }

  getData<T = unknown>(key: string) {
    return this._userData[key] as T | undefined;
  }

  setData(key: string, value: unknown) {
    this._userData[key] = value;
  }

  resize() {
    this._shouldResize = true;
  }

  worldPosToScreenPos(worldPosition: Vector3Like) {
    const normalizedPosition = new Vector3();
    normalizedPosition.copy(worldPosition).project(this.camera);

    const screenPosition = new Vector2();
    screenPosition.x = ((normalizedPosition.x + 1) * this.dom.clientWidth) / 2;
    screenPosition.y = ((-normalizedPosition.y + 1) * this.dom.clientHeight) / 2;

    return screenPosition;
  }

  screenPosToCameraPos(screenPos: Vector2Like) {
    const normalizedPosition = new Vector3();
    normalizedPosition.x = (screenPos.x / this.dom.clientWidth) * 2 - 1;
    normalizedPosition.y = -(screenPos.y / this.dom.clientHeight) * 2 + 1;
    normalizedPosition.z = -1;

    return normalizedPosition.unproject(this.camera);
  }

  addDisposable(disposable: IDisposable | (() => void)) {
    if (typeof disposable === 'function') {
      this._disposables.add({
        dispose: disposable,
      });
    } else {
      this._disposables.add(disposable);
    }
  }

  dispose() {
    this._disposables.forEach((item) => item.dispose());

    this.$r.domElement.remove()
    this.$css2?.domElement.remove()

    this._viewHelper?.dispose();
    this.scene.clear();

    this.$r!.dispose();
    this.$r?.forceContextLoss();
  }
}
