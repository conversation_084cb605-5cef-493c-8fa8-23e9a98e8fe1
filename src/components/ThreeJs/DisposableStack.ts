import type { IDisposable } from './types';

export class DisposableStack implements IDisposable {
  _disposables = new Set<IDisposable>();

  add(disposable: IDisposable | (() => void)) {
    if (typeof disposable === 'function') {
      this._disposables.add({
        dispose: disposable,
      });
    } else {
      this._disposables.add(disposable);
    }

    return disposable
  }

  dispose(): void {
    this._disposables.forEach((item) => item.dispose());
  }
}
