<template>
  <div :class="prefixCls">
    <span>{{ text }}</span><SvgIcon v-bind:name="icon" :size="size" @click="handleCopy" />
  </div>
</template>

<script lang="ts">
  import { defineComponent,unref,computed,ref } from 'vue';
  import { SvgIcon } from '@geega-ui-plus/geega-ui';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useCopyToClipboard } from '/@/hooks/web/useCopyToClipboard';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();

  export default defineComponent({
    components: { SvgIcon },
    props: {
      text: {
        type: String,
        required: true,
      },
      clipIcon: {
        type: String,
        default:'g-ic-fill-right-1',
      },
      iconName: {
        type: String,
        default:'g-ic-fill-copy',
      },
      size:{
        type: String,
        required: true,
        default:'16',
      }
    },
    setup(props) {
      const { prefixCls } = useDesign('geega-table-copy');
      const { createMessage } = useMessage()
      const isCopyed = ref(false);

      function handleCopy() {
        const { isSuccessRef } = useCopyToClipboard(unref(props.text));
        isCopyed.value = unref(isSuccessRef);
        unref(isSuccessRef) &&
        createMessage.success(t('common.copySuccess'));

        setTimeout(() => {
          isCopyed.value = false;
        }, 3000);
      }

      const icon = computed((): String => {
        const { iconName,clipIcon } = props;
        return unref(isCopyed) ? clipIcon : iconName
      });

      return {
        prefixCls,
        handleCopy,
        icon
      };
    },
  });
</script>

<style lang="less" scoped>

@prefix-cls: ~'@{namespace}-geega-table-copy';

.@{prefix-cls} {
  span {
    margin-right:2px;
  }
  svg{
    width: 14px;
    height: 14px;
    cursor: pointer;
    vertical-align: -0.125em;
    color: @primary-color;
  }
}

</style>
