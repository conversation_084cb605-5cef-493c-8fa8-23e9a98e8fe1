<script lang="ts" setup>
import { Icon } from '@geega-ui-plus/geega-ui';
import { computed } from 'vue';
import rank1 from '/@/assets/icons/rank-1.png';
import rank2 from '/@/assets/icons/rank-2.png';
import rank3 from '/@/assets/icons/rank-3.png';


export interface RankItem {
  rank: number;
  label: string;
  desc: string;
  avatar?: string;
  isHighlight?: boolean;
}

export interface RankBoardProps {
  columns?: number;
  data: RankItem[];
}

const props = defineProps<RankBoardProps>();

const gridStyle = computed(() => {
  return {
    gridTemplateColumns: `repeat(${props.columns || 1}, 1fr)`,
  };
});
</script>

<template>
  <div class="rank-list" :style="gridStyle">
    <div
      v-for="(item,idx) in data"
      :key="item.rank"
      class="rank-item"
      :class="{ highlight: item.isHighlight }"
    >
      <div class="rank-position" :class="`rank-${item.rank}`">
        <template v-if="item.rank <= 3">
          <img :src="(idx+1) == 1 ? rank1 :( idx+1 )== 2 ? rank2 : rank3" width="32" height="32"></img>
        </template>
        <template v-else>
          <div class="rank-shape">{{ item.rank }}</div>
        </template>
      </div>
      <div class="user-info">
        <span class="user-name">{{ item.label }}</span>
        <span class="user-time">{{ item.desc }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.rank-list {
  display: grid;
  gap: 16px;
}

.rank-item {
  display: flex;
  align-items: center;
  gap: 12px;

  border-radius: 4px;
  padding: 12px 24px;
  background: rgb(61, 61, 61);
  border: 1px solid transparent;

  transition: all ease 0.4s;

  // &:hover,
  &.highlight {
    background: rgb(41, 50, 42);
    border: 1px solid rgba(0, 153, 107, 0.6);
  }
}

.rank-position {
  width: 40px;
  text-align: center;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &.rank-1 {
    --ignore-dark-color: #daa741;
    color: var(--ignore-dark-color);
  }

  &.rank-2 {
    --ignore-dark-color: silver;
    color: var(--ignore-dark-color);
  }

  &.rank-3 {
    --ignore-dark-color: #be7f3e;
    color: var(--ignore-dark-color);
  }

  .rank-shape {
    display: flex;
    justify-content: center;
    align-items: center;

    font-size: 14px;

    @size: 24px;
    width: @size;
    height: @size;

    border-radius: 999px;
    color: #999;
    background: #666;
  }
}

.user-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.user-time {
  font-size: 14px;
  color: #666;
}
</style>
