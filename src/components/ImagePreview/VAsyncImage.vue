<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useIntersectionObserver } from '@vueuse/core';
import { V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId } from '/@/api/cddc.req';

export interface VAsyncImageProps {
  alt?: string;
  imgId?: string;
}

const props = defineProps<VAsyncImageProps>();

const state = reactive({
  shouldLoad: false,
});

const imgEl = ref<HTMLElement>();

const imgSrc = ref('');

useIntersectionObserver(imgEl, ([entry]) => {
  if (entry.isIntersecting) {
    state.shouldLoad = true;
    getImageSrc();
  }
});

async function getImageSrc() {
  if (!props.imgId) {
    return;
  }

  const resp = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId({
    recordDetailAlarmId: props.imgId,
  });

  imgSrc.value = resp;
}
</script>

<template>
  <img ref="imgEl" class="v-async-image" :src="imgSrc" :alt="alt" loading="lazy" />
</template>

<style lang="less" scoped>
.v-async-image {
  display: inline-block;
  object-fit: contain;
  width: 100%;
  height: 100%;
  position: relative;

  &::before {
    content: '加载中...';
    width: 100%;
    height: 100%;
    background-color: rgba(204, 204, 204);

    display: flex;
    align-items: center;
    justify-content: center;

    position: absolute;
    top: 50%;
    left: 50%;

    transform: translate(-50%, -50%);
  }
}
</style>
