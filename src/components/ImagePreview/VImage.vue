<script lang="ts" setup>
import { Icon } from '@geega-ui-plus/geega-ui';

export interface VImageProps {
  src?: string;
  alt?: string;
}

const props = defineProps<VImageProps>();

const emit = defineEmits(['preview']);

const maskTools = [
  {
    tool: 'preview',
    text: '预览',
    icon: 'ant-design:eye-outlined',
    func: () => emit('preview'),
  },
];
</script>

<template>
  <div class="v-image">
    <slot>
      <img class="v-image-img" :src="src" :alt="alt" />
    </slot>
    <div class="v-image-mask">
      <div class="v-image-tools">
        <div class="v-image-tool" v-for="tool in maskTools">
          <Icon :icon="tool.icon" />
          <span>{{ tool.text }}</span>
        </div>
      </div>
      <!--  -->
    </div>
  </div>
</template>

<style lang="less" scoped>
.v-image {
  position: relative;
}

.v-image-img {
  display: block;
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.v-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  cursor: pointer;
  transition: all ease 0.5;
  transition-property: opacity;

  &:hover {
    opacity: 1;
  }
}

.v-image-tools {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .v-image-tool {
    display: inline-flex;
    gap: 2px;
    align-items: center;
  }
}
</style>
