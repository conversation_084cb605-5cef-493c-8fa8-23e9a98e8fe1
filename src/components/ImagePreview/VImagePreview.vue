<script lang="ts" setup>
import { Icon } from '@geega-ui-plus/geega-ui';
import { useVModel } from '@vueuse/core';

export interface VImageProps {
  src?: string;
  alt?: string;
  visible?: boolean;
}

const props = defineProps<VImageProps>();

const emit = defineEmits(['preview', 'update:visible']);

const vVisible = useVModel(props, 'visible');

function closePreview() {
  vVisible.value = false;
}
</script>

<template>
  <div class="v-image-preview" :class="{ 'is-show': vVisible }" @click.self="closePreview">
    <div class="contents">
      <div class="close-icon" @click="closePreview">
        <Icon icon="ant-design:close-outlined"></Icon>
      </div>
    </div>
    <slot>
      <img class="v-image-preview-img" :src="src" :alt="alt" />
    </slot>
  </div>
</template>

<style lang="less" scoped>
.v-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  width: 100vw;
  height: 100vh;
  display: none;
  background: rgba(0, 0, 0, 0.8);

  align-items: center;
  justify-content: center;

  &.is-show {
    display: flex;
  }
}

.close-icon {
  cursor: pointer;
  position: absolute;
  right: 1rem;
  top: 1rem;
}

.v-image-preview-img {
  display: block;
  object-fit: contain;
  width: 100%;
  height: 100%;
}
</style>
