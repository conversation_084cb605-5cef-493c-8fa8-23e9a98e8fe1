<script lang="ts" setup>
import { computed, watch } from 'vue';
import { getPopupContainer } from '/@/utils';
import { useVModel } from '@vueuse/core';
import { useInjectFormItemContext } from '@geega-ui-plus/ant-design-vue/es/form';

export interface UniqueSelectListProps {
  options?: {
    label: string;
    value: string;
  }[];
  value?: (string | undefined | null)[];
}

const props = defineProps<UniqueSelectListProps>();

const emit = defineEmits(['update:value']);

const vValue = useVModel(props, 'value', emit, {
  deep: true,
});

const formItemContext = useInjectFormItemContext();

const availableOptions = computed(() => {
  return (props.options || []).map((item) => ({
    ...item,
    disabled: vValue.value?.includes(item.value),
  }));
});

function removeItem(index: number) {
  vValue.value?.splice(index, 1);
}

function addItem() {
  const option = availableOptions.value.filter((item) => !item.disabled).at(0);
  const newValues = vValue.value || [];
  newValues.push(option?.value);

  vValue.value = newValues;
}

watch(
  () => vValue.value,
  () => {
    formItemContext?.onFieldChange();
  },
  {
    deep: true,
  }
);
</script>

<template>
  <div>
    <a-form-item-rest>
      <div class="select-item" v-for="(_item, index) in vValue" :key="index">
        <a-select
          v-model:value="vValue![index]"
          :options="availableOptions"
          style="width: 280px"
          placeholder="请选择"
          :getPopupContainer="getPopupContainer"
          :fieldNames="{ label: 'desc', value: 'code' }"
        >
        </a-select>
        <a-button
          v-if="(vValue?.length || 0) > 1"
          danger
          class="delete-btn"
          @click="removeItem(index)"
        >
          删除
        </a-button>
      </div>
    </a-form-item-rest>
    <a-button
      v-if="(vValue?.length || 0) < availableOptions.length"
      type="dashed"
      block
      @click="addItem"
    >
      + 添加
    </a-button>
  </div>
</template>

<style lang="less" scoped>
.select-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .checkbox-group {
    margin-left: 16px;

    &.error {
      :deep(.ant-checkbox-wrapper) {
        color: #ff4d4f;
      }
    }
  }

  .delete-btn {
    margin-left: 8px;
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
  }
}

// 自定义表单错误提示样式
:deep(.cddc-ant-form-item-explain-error) {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 44px; // 基于行高22px计算
  line-height: 22px;
}
</style>
