import type { ModalFuncProps } from '@geega-ui-plus/ant-design-vue/lib/modal/Modal';

import { Modal, message as Message, notification } from '@geega-ui-plus/ant-design-vue';

import { NotificationArgsProps, ConfigProps } from '@geega-ui-plus/ant-design-vue/lib/notification';
import { useI18n } from './useI18n';
import { useDesign } from '/@/hooks/web/useDesign';
// import { Icon } from '@geega-ui-plus/geega-ui';
import { CheckCircleFilled,InfoCircleFilled,ExclamationCircleFilled,CloseCircleFilled } from '@geega-ui-plus/icons-vue';
import { createVNode } from 'vue';
export interface NotifyApi {
  info(config: NotificationArgsProps): void;
  success(config: NotificationArgsProps): void;
  error(config: NotificationArgsProps): void;
  warn(config: NotificationArgsProps): void;
  warning(config: NotificationArgsProps): void;
  open(args: NotificationArgsProps): void;
  close(key: String): void;
  config(options: ConfigProps): void;
  destroy(): void;
}

export declare type NotificationPlacement = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
export interface ModalOptionsEx extends Omit<ModalFuncProps, 'iconType'> {}
export type ModalOptionsPartial = Partial<ModalOptionsEx> & Pick<ModalOptionsEx, 'content'>;


function renderContent({ content }: Pick<ModalOptionsEx, 'content'>) {
  return <div innerHTML={`<div style="padding-left: 2px;font-size:14px;width: 86%;">${content as string}</div>`}></div>;
}

const getBaseOptions = () => {
  const { t } = useI18n();
  const { prefixCls } = useDesign('message-modal');
  return {
    cancelText: () => {
      return <span>{t('common.cancelText')}</span>;
    },
    okText: () => {
      return <span>{t('common.okText')}</span>;
    },
    centered: true,
    autoFocusButton: null,
    class: prefixCls,
  };
};

function createModalOptions(options: ModalOptionsPartial): ModalOptionsPartial {
  return {
    ...getBaseOptions(),
    ...options,
    // icon:() => {
    //   return <Icon size="20" icon={options.icon} />;
    // },
    content: renderContent(options),
  };
}

function createSuccessModal(options: ModalOptionsPartial) {
  return Modal.success(createModalOptions({icon: createVNode(CheckCircleFilled,{
    style:{
      fontSize:'24px',
      display:'inline-block',
      width: '24px'
    }
  }),...options}));
}

function createErrorModal(options: ModalOptionsPartial) {
  return Modal.error(createModalOptions({icon:createVNode(CloseCircleFilled,{
    style:{
      fontSize:'24px',
      display:'inline-block',
      width: '24px'
    }
  }),...options}));
}

function createInfoModal(options: ModalOptionsPartial) {
  return Modal.info(createModalOptions({icon:createVNode(InfoCircleFilled,{
    style:{
      fontSize:'24px',
      display:'inline-block',
      width: '24px'
    }
  }),...options}));
}

function createWarningModal(options: ModalOptionsPartial) {
  return Modal.warning(createModalOptions({icon:createVNode(ExclamationCircleFilled,{
    style:{
      fontSize:'24px',
      display:'inline-block',
      width: '24px'
    }
  }),...options}));
}

function createConfirm(options: ModalOptionsPartial) {
  const { prefixCls } = useDesign(options.type as string);
  const icon =  options.type=='info'?InfoCircleFilled:options.type=='error'?CloseCircleFilled:options.type=='success'?CheckCircleFilled:ExclamationCircleFilled;
  const opt: ModalFuncProps = {
    centered: true,
    class: prefixCls,
    cancelButtonProps: {
      type: 'default',
    },
    okButtonProps: {
    },
    icon:createVNode(
      icon,{
      style:{
        fontSize:'24px',
        display:'inline-block',
        width: '24px',
        color:options.type=='info'?'#4080FF':options.type=='error'?'#F76560':options.type=='success'?'#3EC05C':'#FF9A2E',
      }
    }),
    content: renderContent(options),
    ...options,
  };
  return Modal.confirm(opt) as unknown;
}

function createDeteleConfirm(options: ModalOptionsPartial) {
  const { t } = useI18n();
  return createConfirm({...options,title:t('common.delText')});
}

notification.config({
  placement: 'topRight',
  duration: 3,
});

/**
 * @description: message
 */
export function useMessage() {
  return {
    createMessage: Message,
    notification: notification as NotifyApi,
    createConfirm,
    createDeteleConfirm,
    createSuccessModal,
    createErrorModal,
    createInfoModal,
    createWarningModal,
  };
}
