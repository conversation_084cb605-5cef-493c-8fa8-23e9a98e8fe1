import type { GlobConfig } from '/#/config';

import { warn } from '/@/utils/log';
import { getAppEnvConfig } from '/@/utils/env';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VUE_APP_GLOB_APP_TITLE,
    VUE_APP_GLOB_API_URL,
    VUE_APP_GLOB_APP_SHORT_NAME,
    VUE_APP_GLOB_API_URL_PREFIX,
    VUE_APP_GLOB_UPLOAD_URL,
    VUE_APP_GLOB_GUC_APP_ID,
    VUE_APP_ENABLE_GUC,
    VUE_APP_GUC_API_URL,
    VUE_APP_GLOB_CM_URL,
    VUE_APP_GLOB_CM_APP_ID,
    VUE_APP_ENV,
    VUE_APP_GLOB_SOCKET_URL,
    VUE_APP_GLOB_TRAINING_URL,
    VUE_APP_GLOB_PREVIEW_URL,
    VUE_APP_GLOB_WEBRTC_URL,
    VUE_APP_VIDEO_PANEL_ROTATION_MINUTES,
  } = getAppEnvConfig();

  if (!/[a-zA-Z\_]*/.test(VUE_APP_GLOB_APP_SHORT_NAME)) {
    warn(
      `VUE_APP_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`
    );
  }

  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    title: VUE_APP_GLOB_APP_TITLE,
    apiUrl: VUE_APP_GLOB_API_URL,
    shortName: VUE_APP_GLOB_APP_SHORT_NAME,
    urlPrefix: VUE_APP_GLOB_API_URL_PREFIX,
    uploadUrl: VUE_APP_GLOB_UPLOAD_URL,
    appId: VUE_APP_GLOB_GUC_APP_ID,
    gucApiUrl: VUE_APP_GUC_API_URL,
    cmApiUrl: VUE_APP_GLOB_CM_URL,
    cmAppId: VUE_APP_GLOB_CM_APP_ID,
    enableGuc: VUE_APP_ENABLE_GUC,
    env: VUE_APP_ENV,
    socketUrl: VUE_APP_GLOB_SOCKET_URL,
    trainingUrl: VUE_APP_GLOB_TRAINING_URL,
    previewUrl: VUE_APP_GLOB_PREVIEW_URL,
    webrtcUrl: VUE_APP_GLOB_WEBRTC_URL,
    videoPanelRotationMinutes: VUE_APP_VIDEO_PANEL_ROTATION_MINUTES,
  };
  return glob as Readonly<GlobConfig>;
};
