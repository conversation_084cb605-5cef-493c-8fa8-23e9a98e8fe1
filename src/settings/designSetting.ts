import pkg from '../../package.json';

export default {
  namespace: pkg.namespace,
  gplusPreFixCls: pkg.gplusPreFixCls
};

// 应用主题颜色设置
// https://img.geega.com/geega-cli/geega_them.png
// https://git-front.geega.com/bsc/geega-ui-plus/geega-colors
export const APP_PRESET_COLOR_LIST: string[] = [
  '#00996b',
  '#03855e',
  '#3367d6',
  '#2e5bb9',
  '#005f76',
  '#035467',
];

// 头部
export const HEADER_PRESET_BG_COLOR_LIST: string[] = [
  '#00996b',
  '#03855e',
  '#3367d6',
  '#2e5bb9',
  '#005f76',
  '#035467',
  '#ffffff'
];

// sider
export const SIDE_BAR_BG_COLOR_LIST: string[] = [
  '#00996b',
  '#03855e',
  '#3367d6',
  '#2e5bb9',
  '#005f76',
  '#035467',
  '#ffffff'
];
