import { defHttp } from '/@/utils/http/axios';

/**
 * @description 试卷下载
 */
export function getPaper(params) {
  return defHttp.getStream<any>({
    url: '/v1/manage/exam-paper/download/' + params.id,
    params,
  });
}

// 题库字段配-映射管理 导入导出相关
// 模版下载
export const V1ManageQuestionBankColumnMappingExportTemplateUrl = '/v1/manage/question-bank-column-mapping/exportTemplate'
// 数据导出
export const V1ManageQuestionBankColumnMappingExportUrl = '/v1/manage/question-bank-column-mapping/export'
// 数据导入
export const  V1ManageQuestionBankColumnMappingImportColumnIdPostUrl = '/v1/manage/question-bank-column-mapping/import'




// 生成试题 文件导入模版Excel
export const V1ManageQuestionBankDownloadImportQuestionForExcelUrl = '/v1/manage/question-bank/downloadImportQuestionForExcel'

// 生成试题 文件导入模版Word
export const V1ManageQuestionBankDownloadImportQuestionForWordUrl = '/v1/manage/question-bank/downloadImportQuestionForWord'
