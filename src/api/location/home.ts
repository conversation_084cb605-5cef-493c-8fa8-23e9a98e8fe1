import { defHttp } from '/@/utils/http/axios';
import { LOCATION_API } from './constant';

/**
 * @description 获取首页进度统计
 */
export function getProgressStatistics() {
  return defHttp.request<any>({
    url: LOCATION_API.ProgressStatistics,
    method: 'get',
  });
}

/**
 * @description 获取首页用户信息
 */
export function getUserInfo() {
  return defHttp.request<any>({
    url: LOCATION_API.UserInfo,
    method: 'get',
  });
}

/**
 * @description 获取首页项目列表
 */
export function getProjectList() {
  return defHttp.request<any>({
    url: LOCATION_API.ProjectList,
    method: 'get',
  });
}

/**
 * @description 获取当前时间
 */
export function getCurrentDate() {
  return defHttp.request<any>({
    url: LOCATION_API.CurrentDate,
    method: 'get',
  });
}

/**
 * @description 获取当前工位
 */
export function getCurrentLocation(ip) {
  return defHttp.request<any>({
    url: `${LOCATION_API.CurrentLocation}/${ip}`,
    method: 'get',
  });
}

/**
 * @description 获取数据统计
 */
export function getDataStatistics() {
  return defHttp.request<any>({
    url: LOCATION_API.DataStatistics,
    method: 'get',
  });
}

/**
 * @description 获取最新训练数据统计
 */
export function getLastStatistics() {
  return defHttp.request<any>({
    url: LOCATION_API.LastStatistics,
    method: 'get',
  });
}
