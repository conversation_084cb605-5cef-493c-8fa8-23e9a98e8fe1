import { defHttp } from '/@/utils/http/axios';
import { LOCATION_API } from '/@/api/location/constant';

/**
 * @description 训练项目
 */
export function projectList(locationId, userId) {
  return defHttp.request<any>({
    url: `${LOCATION_API.TrainProject}/${locationId}/${userId}`,
    method: 'get',
  });
}

/**
 * @description 开始训练
 */
export function startTrain(params) {
  return defHttp.request<any>({
    url: LOCATION_API.StartTrain,
    method: 'post',
    params,
  });
}

/**
 * @description 停止训练
 */
export function stopTrain(params) {
  return defHttp.request<any>({
    url: LOCATION_API.StopTrain,
    method: 'post',
    params,
  });
}

/**
 * @description 初始化签到
 */
export function initSign(params) {
  return defHttp.request<any>({
    url: LOCATION_API.InitSign,
    method: 'post',
    params,
  });
}
