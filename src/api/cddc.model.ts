// This file was automatically generated by `scripts/gen-api.ts`, Please do not edit it directly!
/* tslint:disable */
export interface APIModels {
    /**
     * 绑定人员请求体
     */
    DeviceBindingCardPostRequestBody: DeviceBindingCardPostRequestBody;
    DeviceBindingCardPostResponse:    DeviceBindingCardPostResponse;
    /**
     * 绑定人员与卡请求体
     */
    DeviceBindingFacePostRequestBody:           DeviceBindingFacePostRequestBody;
    DeviceBindingFacePostResponse:              DeviceBindingFacePostResponse;
    DeviceCaptureFaceInfoGetQuery:              DeviceCaptureFaceInfoGetQuery;
    DeviceCaptureFaceInfoGetResponse:           DeviceCaptureFaceInfoGetResponse;
    DeviceDeleteAllEmployeeInfoDeleteResponse:  DeviceDeleteAllEmployeeInfoDeleteResponse;
    DeviceDeleteCardNoCardNoPostParam:          DeviceDeleteCardNoCardNoPostParam;
    DeviceDeleteCardNoCardNoPostResponse:       DeviceDeleteCardNoCardNoPostResponse;
    DeviceDeleteEmployeeIdPostParam:            DeviceDeleteEmployeeIDPostParam;
    DeviceDeleteEmployeeIdPostResponse:         DeviceDeleteEmployeeIDPostResponse;
    DeviceDeleteFaceInfoEmployeeIdPostParam:    DeviceDeleteFaceInfoEmployeeIDPostParam;
    DeviceDeleteFaceInfoEmployeeIdPostResponse: DeviceDeleteFaceInfoEmployeeIDPostResponse;
    /**
     * 注册人员请求体
     */
    DeviceRegisterPostRequestBody:                    DeviceRegisterPostRequestBody;
    DeviceRegisterPostResponse:                       DeviceRegisterPostResponse;
    DeviceSyncPostResponse:                           DeviceSyncPostResponse;
    V1CommonFileUploadBusinessTypePostParam:          V1CommonFileUploadBusinessTypePostParam;
    V1CommonFileUploadBusinessTypePostRequestBody:    V1CommonFileUploadBusinessTypePostRequestBody;
    V1CommonFileUploadBusinessTypePostResponse:       V1CommonFileUploadBusinessTypePostResponse;
    V1CommonFileUploadDownloadFileIdPostParam:        V1CommonFileUploadDownloadFileIDPostParam;
    V1CommonFileUploadDownloadFileIdPostResponse:     V1CommonFileUploadDownloadFileIDPostResponse;
    V1CommonFileUploadDownloadPathFileIdPostParam:    V1CommonFileUploadDownloadPathFileIDPostParam;
    V1CommonFileUploadDownloadPathFileIdPostResponse: V1CommonFileUploadDownloadPathFileIDPostResponse;
    V1CustomSendMessagePostRequestBody:               V1CustomSendMessagePostRequestBody;
    V1CustomSendMessagePostResponse:                  V1CustomSendMessagePostResponse;
    V1EvaluateConfigDeleteIdDeleteParam:              V1EvaluateConfigDeleteIDDeleteParam;
    V1EvaluateConfigDeleteIdDeleteResponse:           V1EvaluateConfigDeleteIDDeleteResponse;
    V1EvaluateConfigPagePostRequestBody:              V1EvaluateConfigPagePostRequestBody;
    V1EvaluateConfigPagePostResponse:                 V1EvaluateConfigPagePostResponse;
    /**
     * 评定项目保存DTO
     */
    V1EvaluateConfigSavePostRequestBody: V1EvaluateConfigSavePostRequestBody;
    V1EvaluateConfigSavePostResponse:    V1EvaluateConfigSavePostResponse;
    V1EvaluateConfigSelectGetResponse:   V1EvaluateConfigSelectGetResponse;
    /**
     * 评定项目状态切换DTO
     */
    V1EvaluateConfigStatusPostRequestBody: V1EvaluateConfigStatusPostRequestBody;
    V1EvaluateConfigStatusPostResponse:    V1EvaluateConfigStatusPostResponse;
    V1EvaluateTaskDeleteIdDeleteParam:     V1EvaluateTaskDeleteIDDeleteParam;
    V1EvaluateTaskDeleteIdDeleteResponse:  V1EvaluateTaskDeleteIDDeleteResponse;
    V1EvaluateTaskOrderInfoIdPostParam:    V1EvaluateTaskOrderInfoIDPostParam;
    V1EvaluateTaskOrderInfoIdPostResponse: V1EvaluateTaskOrderInfoIDPostResponse;
    V1EvaluateTaskPagePostRequestBody:     V1EvaluateTaskPagePostRequestBody;
    V1EvaluateTaskPagePostResponse:        V1EvaluateTaskPagePostResponse;
    /**
     * 评定任务保存DTO
     */
    V1EvaluateTaskSavePostRequestBody: V1EvaluateTaskSavePostRequestBody;
    V1EvaluateTaskSavePostResponse:    V1EvaluateTaskSavePostResponse;
    /**
     * 评定任务工单保存DTO
     */
    V1EvaluateTaskSubmitPostRequestBody:           V1EvaluateTaskSubmitPostRequestBody;
    V1EvaluateTaskSubmitPostResponse:              V1EvaluateTaskSubmitPostResponse;
    V1EvaluateTaskUserOrderUserIdPostParam:        V1EvaluateTaskUserOrderUserIDPostParam;
    V1EvaluateTaskUserOrderUserIdPostResponse:     V1EvaluateTaskUserOrderUserIDPostResponse;
    V1LocationHomeAlarmDetailsPagePostRequestBody: V1LocationHomeAlarmDetailsPagePostRequestBody;
    V1LocationHomeAlarmDetailsPagePostResponse:    V1LocationHomeAlarmDetailsPagePostResponse;
    /**
     * 学员培训不合格记录请求体
     */
    V1LocationHomeAlarmDetailsStatisticsPostRequestBody:                 V1LocationHomeAlarmDetailsStatisticsPostRequestBodyObject;
    V1LocationHomeAlarmDetailsStatisticsPostResponse:                    V1LocationHomeAlarmDetailsStatisticsPostResponse;
    V1LocationHomeCurrentDateGetResponse:                                V1LocationHomeCurrentDateGetResponse;
    V1LocationHomeCurrentIpGetResponse:                                  V1LocationHomeCurrentIPGetResponse;
    V1LocationHomeCurrentLocationGetQuery:                               V1LocationHomeCurrentLocationGetQuery;
    V1LocationHomeCurrentLocationGetResponse:                            V1LocationHomeCurrentLocationGetResponse;
    V1LocationHomeCurrentLocationIpGetParam:                             V1LocationHomeCurrentLocationIPGetParam;
    V1LocationHomeCurrentLocationIpGetResponse:                          V1LocationHomeCurrentLocationIPGetResponse;
    V1LocationHomeCurrentUserGetResponse:                                V1LocationHomeCurrentUserGetResponse;
    V1LocationHomeDataStatisticsRecordIdGetParam:                        V1LocationHomeDataStatisticsRecordIDGetParam;
    V1LocationHomeDataStatisticsRecordIdGetResponse:                     V1LocationHomeDataStatisticsRecordIDGetResponse;
    V1LocationHomeLastStatisticsRecordIdGetParam:                        V1LocationHomeLastStatisticsRecordIDGetParam;
    V1LocationHomeLastStatisticsRecordIdGetResponse:                     V1LocationHomeLastStatisticsRecordIDGetResponse;
    V1LocationHomeLoginSyncGetResponse:                                  V1LocationHomeLoginSyncGetResponse;
    V1LocationHomeMyProjectLocationIdUserIdGetParam:                     V1LocationHomeMyProjectLocationIDUserIDGetParam;
    V1LocationHomeMyProjectLocationIdUserIdGetQuery:                     V1LocationHomeMyProjectLocationIDUserIDGetQuery;
    V1LocationHomeMyProjectLocationIdUserIdGetResponse:                  V1LocationHomeMyProjectLocationIDUserIDGetResponse;
    V1LocationHomeProgressStatisticsRecordIdGetParam:                    V1LocationHomeProgressStatisticsRecordIDGetParam;
    V1LocationHomeProgressStatisticsRecordIdGetResponse:                 V1LocationHomeProgressStatisticsRecordIDGetResponse;
    V1LocationHomeRankAllProjectIdTypeStartEndGetParam:                  V1LocationHomeRankAllProjectIDTypeStartEndGetParam;
    V1LocationHomeRankAllProjectIdTypeStartEndGetResponse:               V1LocationHomeRankAllProjectIDTypeStartEndGetResponse;
    V1LocationHomeRankRecordIdGetParam:                                  V1LocationHomeRankRecordIDGetParam;
    V1LocationHomeRankRecordIdGetResponse:                               V1LocationHomeRankRecordIDGetResponse;
    V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIdGetParam:    V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIDGetParam;
    V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIdGetResponse: string;
    V1LocationStudyCheckProjectIdUserIdGetParam:                         V1LocationStudyCheckProjectIDUserIDGetParam;
    V1LocationStudyCheckProjectIdUserIdGetResponse:                      V1LocationStudyCheckProjectIDUserIDGetResponse;
    V1LocationStudyDetailDurationDetailIdGetParam:                       V1LocationStudyDetailDurationDetailIDGetParam;
    V1LocationStudyDetailDurationDetailIdGetResponse:                    V1LocationStudyDetailDurationDetailIDGetResponse;
    /**
     * 课程结束DTO
     */
    V1LocationStudyExitPostRequestBody: V1LocationStudyExitPostRequestBody;
    V1LocationStudyExitPostResponse:    V1LocationStudyExitPostResponse;
    /**
     * 课程初始化DTO
     */
    V1LocationStudyInitPostRequestBody:             V1LocationStudyInitPostRequestBody;
    V1LocationStudyInitPostResponse:                V1LocationStudyInitPostResponse;
    V1LocationStudyOperationLogPagePostRequestBody: V1LocationStudyOperationLogPagePostRequestBody;
    V1LocationStudyOperationLogPagePostResponse:    V1LocationStudyOperationLogPagePostResponse;
    /**
     * 课程结束DTO
     */
    V1LocationStudyOverPostRequestBody:            V1LocationStudyOverPostRequestBody;
    V1LocationStudyOverPostResponse:               V1LocationStudyOverPostResponse;
    V1LocationStudyRaceProjectIdUserIdGetParam:    V1LocationStudyRaceProjectIDUserIDGetParam;
    V1LocationStudyRaceProjectIdUserIdGetResponse: V1LocationStudyRaceProjectIDUserIDGetResponse;
    /**
     * 课程开始DTO
     */
    V1LocationStudyStartPostRequestBody: V1LocationStudyStartPostRequestBody;
    V1LocationStudyStartPostResponse:    V1LocationStudyStartPostResponse;
    /**
     * 课程开暂停DTO
     */
    V1LocationStudyStopPostRequestBody:                     V1LocationStudyStopPostRequestBody;
    V1LocationStudyStopPostResponse:                        V1LocationStudyStopPostResponse;
    V1LocationStudyTrainProjectLocationIdUserIdGetParam:    V1LocationStudyTrainProjectLocationIDUserIDGetParam;
    V1LocationStudyTrainProjectLocationIdUserIdGetResponse: V1LocationStudyTrainProjectLocationIDUserIDGetResponse;
    V1LocationStudyTrainRecordProjectIdUserIdGetParam:      V1LocationStudyTrainRecordProjectIDUserIDGetParam;
    V1LocationStudyTrainRecordProjectIdUserIdGetResponse:   V1LocationStudyTrainRecordProjectIDUserIDGetResponse;
    /**
     * 课程初始化DTO
     */
    V1LocationStudyVideoCreatePostRequestBody: V1LocationStudyVideoCreatePostRequestBody;
    V1LocationStudyVideoCreatePostResponse:    V1LocationStudyVideoCreatePostResponse;
    /**
     * 课程初始化DTO
     */
    V1LocationStudyVideoStopPostRequestBody:     V1LocationStudyVideoStopPostRequestBody;
    V1LocationStudyVideoStopPostResponse:        V1LocationStudyVideoStopPostResponse;
    V1ManageCommonActionTypeEnumGetResponse:     V1ManageCommonActionTypeEnumGetResponse;
    V1ManageCommonCountAlgorithmEnumGetResponse: V1ManageCommonCountAlgorithmEnumGetResponse;
    V1ManageCommonJudgeTypeEnumGetResponse:      V1ManageCommonJudgeTypeEnumGetResponse;
    V1ManageCommonPaperTypeEnumGetResponse:      V1ManageCommonPaperTypeEnumGetResponse;
    /**
     * 岗位查询DTO
     */
    V1ManageCommonPositionPostRequestBody:     V1ManageCommonPositionPostRequestBodyObject;
    V1ManageCommonPositionPostResponse:        V1ManageCommonPositionPostResponse;
    V1ManageCommonProjectTypeEnumGetResponse:  V1ManageCommonProjectTypeEnumGetResponse;
    V1ManageCommonQuestionTypeEnumGetResponse: V1ManageCommonQuestionTypeEnumGetResponse;
    V1ManageCommonTrainStatusEnumGetQuery:     V1ManageCommonTrainStatusEnumGetQuery;
    V1ManageCommonTrainStatusEnumGetResponse:  V1ManageCommonTrainStatusEnumGetResponse;
    V1ManageCommonUserIdNameGetResponse:       V1ManageCommonUserIDNameGetResponse;
    /**
     * 用户查询DTO
     */
    V1ManageCommonUserListPostRequestBody: V1ManageCommonUserListPostRequestBody;
    V1ManageCommonUserListPostResponse:    V1ManageCommonUserListPostResponse;
    /**
     * 车型查询DTO
     */
    V1ManageCommonVehiclePostRequestBody:     V1ManageCommonVehiclePostRequestBodyObject;
    V1ManageCommonVehiclePostResponse:        V1ManageCommonVehiclePostResponse;
    V1ManageDictDataDictDataIdDeleteParam:    V1ManageDictDataDictDataIDDeleteParam;
    V1ManageDictDataDictDataIdDeleteResponse: V1ManageDictDataDictDataIDDeleteResponse;
    V1ManageDictDataDictDataIdGetParam:       V1ManageDictDataDictDataIDGetParam;
    V1ManageDictDataDictDataIdGetResponse:    V1ManageDictDataDictDataIDGetResponse;
    V1ManageDictDataListPostRequestBody:      V1ManageDictDataListPostRequestBody;
    V1ManageDictDataListPostResponse:         V1ManageDictDataListPostResponse;
    V1ManageDictDataPostRequestBody:          V1ManageDictDataPostRequestBody;
    V1ManageDictDataPostResponse:             V1ManageDictDataPostResponse;
    V1ManageDictDataPutRequestBody:           V1ManageDictDataPutRequestBody;
    V1ManageDictDataPutResponse:              V1ManageDictDataPutResponse;
    /**
     * 试卷确认请求体DTO
     */
    V1ManageExamPaperConfirmPostRequestBody: V1ManageExamPaperConfirmPostRequestBody;
    V1ManageExamPaperConfirmPostResponse:    V1ManageExamPaperConfirmPostResponse;
    /**
     * 试卷复制请求体DTO
     */
    V1ManageExamPaperCopyInitPostRequestBody:        V1ManageExamPaperCopyInitPostRequestBody;
    V1ManageExamPaperCopyInitPostResponse:           V1ManageExamPaperCopyInitPostResponse;
    V1ManageExamPaperDownloadIdGetParam:             V1ManageExamPaperDownloadIDGetParam;
    V1ManageExamPaperDownloadIdGetQuery:             V1ManageExamPaperDownloadIDGetQuery;
    V1ManageExamPaperIdDeleteParam:                  V1ManageExamPaperIDDeleteParam;
    V1ManageExamPaperIdDeleteResponse:               V1ManageExamPaperIDDeleteResponse;
    V1ManageExamPaperIdGetParam:                     V1ManageExamPaperIDGetParam;
    V1ManageExamPaperIdGetResponse:                  V1ManageExamPaperIDGetResponse;
    V1ManageExamPaperPagePostRequestBody:            V1ManageExamPaperPagePostRequestBody;
    V1ManageExamPaperPagePostResponse:               V1ManageExamPaperPagePostResponse;
    V1ManageExamPaperPaperSourceRecordIdGetParam:    V1ManageExamPaperPaperSourceRecordIDGetParam;
    V1ManageExamPaperPaperSourceRecordIdGetResponse: V1ManageExamPaperPaperSourceRecordIDGetResponse;
    /**
     * 试卷修改请求体DTO
     */
    V1ManageExamPaperPostRequestBody: V1ManageExamPaperPostRequestBody;
    V1ManageExamPaperPostResponse:    V1ManageExamPaperPostResponse;
    /**
     * 试卷新增请求体DTO
     */
    V1ManageExamPaperPutRequestBody:                 V1ManageExamPaperPutRequestBody;
    V1ManageExamPaperPutResponse:                    V1ManageExamPaperPutResponse;
    V1ManageExamPaperQuestionIdDeleteParam:          V1ManageExamPaperQuestionIDDeleteParam;
    V1ManageExamPaperQuestionIdDeleteResponse:       V1ManageExamPaperQuestionIDDeleteResponse;
    V1ManageExamPaperQuestionListPaperIdGetParam:    V1ManageExamPaperQuestionListPaperIDGetParam;
    V1ManageExamPaperQuestionListPaperIdGetResponse: V1ManageExamPaperQuestionListPaperIDGetResponse;
    /**
     * 试卷试题新增请求体DTO
     */
    V1ManageExamPaperQuestionPutRequestBody:        V1ManageExamPaperQuestionPutRequestBody;
    V1ManageExamPaperQuestionPutResponse:           V1ManageExamPaperQuestionPutResponse;
    V1ManageExamPaperQuicklyReleasePostRequestBody: V1ManageExamPaperQuicklyReleasePostRequestBody;
    V1ManageExamPaperQuicklyReleasePostResponse:    V1ManageExamPaperQuicklyReleasePostResponse;
    /**
     * 试卷发布请求体DTO
     */
    V1ManageExamPaperReleasePostRequestBody:                 V1ManageExamPaperReleasePostRequestBody;
    V1ManageExamPaperReleasePostResponse:                    V1ManageExamPaperReleasePostResponse;
    V1ManageExamPaperSearchQuestionMultiplePostRequestBody:  V1ManageExamPaperSearchQuestionMultiplePostRequestBody;
    V1ManageExamPaperSearchQuestionMultiplePostResponse:     V1ManageExamPaperSearchQuestionMultiplePostResponse;
    V1ManageExamPaperSearchQuestionSinglePostRequestBody:    V1ManageExamPaperSearchQuestionSinglePostRequestBody;
    V1ManageExamPaperSearchQuestionSinglePostResponse:       V1ManageExamPaperSearchQuestionSinglePostResponse;
    V1ManageExamRecordCompletedStatisticsPaperIdGetParam:    V1ManageExamRecordCompletedStatisticsPaperIDGetParam;
    V1ManageExamRecordCompletedStatisticsPaperIdGetResponse: V1ManageExamRecordCompletedStatisticsPaperIDGetResponse;
    V1ManageExamRecordIdGetParam:                            V1ManageExamRecordIDGetParam;
    V1ManageExamRecordIdGetResponse:                         V1ManageExamRecordIDGetResponse;
    V1ManageExamRecordPagePostRequestBody:                   V1ManageExamRecordPagePostRequestBody;
    V1ManageExamRecordPagePostResponse:                      V1ManageExamRecordPagePostResponse;
    V1ManageJobLevelsJobLevelIdDeleteParam:                  V1ManageJobLevelsJobLevelIDDeleteParam;
    V1ManageJobLevelsJobLevelIdDeleteResponse:               V1ManageJobLevelsJobLevelIDDeleteResponse;
    V1ManageJobLevelsJobLevelIdGetParam:                     V1ManageJobLevelsJobLevelIDGetParam;
    V1ManageJobLevelsJobLevelIdGetResponse:                  V1ManageJobLevelsJobLevelIDGetResponse;
    V1ManageJobLevelsMaxLevelJobIdGetParam:                  V1ManageJobLevelsMaxLevelJobIDGetParam;
    V1ManageJobLevelsMaxLevelJobIdGetResponse:               V1ManageJobLevelsMaxLevelJobIDGetResponse;
    V1ManageJobLevelsPagePostRequestBody:                    V1ManageJobLevelsPagePostRequestBody;
    V1ManageJobLevelsPagePostResponse:                       V1ManageJobLevelsPagePostResponse;
    /**
     * JobLevelCreateDTO
     */
    V1ManageJobLevelsPostRequestBody: V1ManageJobLevelsPostRequestBody;
    V1ManageJobLevelsPostResponse:    V1ManageJobLevelsPostResponse;
    /**
     * JobLevelUpdateDTO
     */
    V1ManageJobLevelsPutRequestBody: V1ManageJobLevelsPutRequestBody;
    V1ManageJobLevelsPutResponse:    V1ManageJobLevelsPutResponse;
    V1ManageJobsJobIdDeleteParam:    V1ManageJobsJobIDDeleteParam;
    V1ManageJobsJobIdDeleteResponse: V1ManageJobsJobIDDeleteResponse;
    V1ManageJobsJobIdGetParam:       V1ManageJobsJobIDGetParam;
    V1ManageJobsJobIdGetResponse:    V1ManageJobsJobIDGetResponse;
    V1ManageJobsPagePostRequestBody: V1ManageJobsPagePostRequestBody;
    V1ManageJobsPagePostResponse:    V1ManageJobsPagePostResponse;
    /**
     * JobCreateDTO
     */
    V1ManageJobsPostRequestBody: V1ManageJobsPostRequestBody;
    V1ManageJobsPostResponse:    V1ManageJobsPostResponse;
    /**
     * JobUpdateDTO
     */
    V1ManageJobsPutRequestBody: V1ManageJobsPutRequestBody;
    V1ManageJobsPutResponse:    V1ManageJobsPutResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartActionDefectPostRequestBody: V1ManageLargeScreenChartActionDefectPostRequestBody;
    V1ManageLargeScreenChartActionDefectPostResponse:    V1ManageLargeScreenChartActionDefectPostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartActionPassRatePostRequestBody: V1ManageLargeScreenChartActionPassRatePostRequestBody;
    V1ManageLargeScreenChartActionPassRatePostResponse:    V1ManageLargeScreenChartActionPassRatePostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartJobNumPostRequestBody: V1ManageLargeScreenChartJobNumPostRequestBody;
    V1ManageLargeScreenChartJobNumPostResponse:    V1ManageLargeScreenChartJobNumPostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartPassRatePostRequestBody: V1ManageLargeScreenChartPassRatePostRequestBody;
    V1ManageLargeScreenChartPassRatePostResponse:    V1ManageLargeScreenChartPassRatePostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartTrainNumPostRequestBody: V1ManageLargeScreenChartTrainNumPostRequestBody;
    V1ManageLargeScreenChartTrainNumPostResponse:    V1ManageLargeScreenChartTrainNumPostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartTrainUserPostRequestBody: V1ManageLargeScreenChartTrainUserPostRequestBody;
    V1ManageLargeScreenChartTrainUserPostResponse:    V1ManageLargeScreenChartTrainUserPostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenChartUnitUsePostRequestBody: V1ManageLargeScreenChartUnitUsePostRequestBody;
    V1ManageLargeScreenChartUnitUsePostResponse:    V1ManageLargeScreenChartUnitUsePostResponse;
    /**
     * 大屏数据查询DTO
     */
    V1ManageLargeScreenProjectPostRequestBody:                    V1ManageLargeScreenProjectPostRequestBody;
    V1ManageLargeScreenProjectPostResponse:                       V1ManageLargeScreenProjectPostResponse;
    V1ManageLearningMaterialsMaterialIdDeleteParam:               V1ManageLearningMaterialsMaterialIDDeleteParam;
    V1ManageLearningMaterialsMaterialIdDeleteResponse:            V1ManageLearningMaterialsMaterialIDDeleteResponse;
    V1ManageLearningMaterialsMaterialIdGetParam:                  V1ManageLearningMaterialsMaterialIDGetParam;
    V1ManageLearningMaterialsMaterialIdGetResponse:               V1ManageLearningMaterialsMaterialIDGetResponse;
    V1ManageLearningMaterialsPagePostRequestBody:                 V1ManageLearningMaterialsPagePostRequestBody;
    V1ManageLearningMaterialsPagePostResponse:                    V1ManageLearningMaterialsPagePostResponse;
    V1ManageLearningMaterialsPostRequestBody:                     V1ManageLearningMaterialsPostRequestBody;
    V1ManageLearningMaterialsPostResponse:                        V1ManageLearningMaterialsPostResponse;
    V1ManageLearningMaterialsPutRequestBody:                      V1ManageLearningMaterialsPutRequestBody;
    V1ManageLearningMaterialsPutResponse:                         V1ManageLearningMaterialsPutResponse;
    V1ManageLearningProjectsPagePostRequestBody:                  V1ManageLearningProjectsPagePostRequestBody;
    V1ManageLearningProjectsPagePostResponse:                     V1ManageLearningProjectsPagePostResponse;
    V1ManageLearningProjectsPostRequestBody:                      V1ManageLearningProjectsPostRequestBody;
    V1ManageLearningProjectsPostResponse:                         V1ManageLearningProjectsPostResponse;
    V1ManageLearningProjectsProjectIdDeleteParam:                 V1ManageLearningProjectsProjectIDDeleteParam;
    V1ManageLearningProjectsProjectIdDeleteResponse:              V1ManageLearningProjectsProjectIDDeleteResponse;
    V1ManageLearningProjectsProjectIdGetParam:                    V1ManageLearningProjectsProjectIDGetParam;
    V1ManageLearningProjectsProjectIdGetResponse:                 V1ManageLearningProjectsProjectIDGetResponse;
    V1ManageLearningProjectsPutRequestBody:                       V1ManageLearningProjectsPutRequestBody;
    V1ManageLearningProjectsPutResponse:                          V1ManageLearningProjectsPutResponse;
    V1ManagePositionImportPostRequestBody:                        V1ManagePositionImportPostRequestBody;
    V1ManagePositionImportPostResponse:                           V1ManagePositionImportPostResponse;
    V1ManagePositionPagePostRequestBody:                          V1ManagePositionPagePostRequestBody;
    V1ManagePositionPagePostResponse:                             V1ManagePositionPagePostResponse;
    V1ManageProcessAlgorithmIdDeleteParam:                        V1ManageProcessAlgorithmIDDeleteParam;
    V1ManageProcessAlgorithmIdDeleteResponse:                     V1ManageProcessAlgorithmIDDeleteResponse;
    V1ManageProcessAlgorithmPagePostRequestBody:                  V1ManageProcessAlgorithmPagePostRequestBody;
    V1ManageProcessAlgorithmPagePostResponse:                     V1ManageProcessAlgorithmPagePostResponse;
    V1ManageProcessAlgorithmPostRequestBody:                      V1ManageProcessAlgorithmPostRequestBody;
    V1ManageProcessAlgorithmPostResponse:                         V1ManageProcessAlgorithmPostResponse;
    V1ManageProcessAlgorithmPutRequestBody:                       V1ManageProcessAlgorithmPutRequestBody;
    V1ManageProcessAlgorithmPutResponse:                          V1ManageProcessAlgorithmPutResponse;
    V1ManageProcessAlgorithmWithCodeGetQuery:                     V1ManageProcessAlgorithmWithCodeGetQuery;
    V1ManageProcessAlgorithmWithCodeGetResponse:                  V1ManageProcessAlgorithmWithCodeGetResponse;
    V1ManagePromptsBankTypeIdGetParam:                            V1ManagePromptsBankTypeIDGetParam;
    V1ManagePromptsBankTypeIdGetResponse:                         V1ManagePromptsBankTypeIDGetResponse;
    V1ManagePromptsBatchPostRequestBody:                          V1ManagePromptsBatchPostRequestBodyElement[];
    V1ManagePromptsBatchPostResponse:                             V1ManagePromptsBatchPostResponse;
    V1ManageQuestionBankBatchCompleteQuestionBankPostRequestBody: V1ManageQuestionBankBatchCompleteQuestionBankPostRequestBody;
    V1ManageQuestionBankBatchCompleteQuestionBankPostResponse:    V1ManageQuestionBankBatchCompleteQuestionBankPostResponse;
    V1ManageQuestionBankBatchInsertQuestionBankPostRequestBody:   V1ManageQuestionBankBatchInsertQuestionBankPostRequestBody;
    V1ManageQuestionBankBatchInsertQuestionBankPostResponse:      V1ManageQuestionBankBatchInsertQuestionBankPostResponse;
    /**
     * 批量保存Ai根据文件生成的题目DTO
     */
    V1ManageQuestionBankBatchSaveQuestionBankFromAiGenerateByFilePostRequestBody: V1ManageQuestionBankBatchSaveQuestionBankFromAIGenerateByFilePostRequestBody;
    V1ManageQuestionBankBatchSaveQuestionBankFromAiGenerateByFilePostResponse:    V1ManageQuestionBankBatchSaveQuestionBankFromAIGenerateByFilePostResponse;
    V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBody:                   V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBody;
    V1ManageQuestionBankBatchUpdateQuestionBankPostResponse:                      V1ManageQuestionBankBatchUpdateQuestionBankPostResponse;
    V1ManageQuestionBankColumnDeleteByIdGetQuery:                                 V1ManageQuestionBankColumnDeleteByIDGetQuery;
    V1ManageQuestionBankColumnDeleteByIdGetResponse:                              V1ManageQuestionBankColumnDeleteByIDGetResponse;
    V1ManageQuestionBankColumnGetByColumnIdAndKeyGetQuery:                        V1ManageQuestionBankColumnGetByColumnIDAndKeyGetQuery;
    V1ManageQuestionBankColumnGetByColumnIdAndKeyGetResponse:                     V1ManageQuestionBankColumnGetByColumnIDAndKeyGetResponse;
    V1ManageQuestionBankColumnGetQuestionBankColumnByIdGetQuery:                  V1ManageQuestionBankColumnGetQuestionBankColumnByIDGetQuery;
    V1ManageQuestionBankColumnGetQuestionBankColumnByIdGetResponse:               V1ManageQuestionBankColumnGetQuestionBankColumnByIDGetResponse;
    /**
     * 题库字段配置查询DTO
     */
    V1ManageQuestionBankColumnGetQuestionBankColumnListPostRequestBody:              V1ManageQuestionBankColumnGetQuestionBankColumnListPostRequestBody;
    V1ManageQuestionBankColumnGetQuestionBankColumnListPostResponse:                 V1ManageQuestionBankColumnGetQuestionBankColumnListPostResponse;
    V1ManageQuestionBankColumnMappingExportGetQuery:                                 V1ManageQuestionBankColumnMappingExportGetQuery;
    V1ManageQuestionBankColumnMappingExportTemplateGetQuery:                         V1ManageQuestionBankColumnMappingExportTemplateGetQuery;
    V1ManageQuestionBankColumnMappingGetByColumnIdAndValueAndPropertyKeyGetQuery:    V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetQuery;
    V1ManageQuestionBankColumnMappingGetByColumnIdAndValueAndPropertyKeyGetResponse: V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponse;
    V1ManageQuestionBankColumnMappingGetByColumnIdAndValueGetQuery:                  V1ManageQuestionBankColumnMappingGetByColumnIDAndValueGetQuery;
    V1ManageQuestionBankColumnMappingGetByColumnIdAndValueGetResponse:               V1ManageQuestionBankColumnMappingGetByColumnIDAndValueGetResponse;
    V1ManageQuestionBankColumnMappingImportColumnIdPostParam:                        V1ManageQuestionBankColumnMappingImportColumnIDPostParam;
    V1ManageQuestionBankColumnMappingImportColumnIdPostQuery:                        V1ManageQuestionBankColumnMappingImportColumnIDPostQuery;
    V1ManageQuestionBankColumnMappingImportColumnIdPostResponse:                     V1ManageQuestionBankColumnMappingImportColumnIDPostResponse;
    /**
     * 题库字段值查询DTO
     */
    V1ManageQuestionBankColumnMappingListPostRequestBody: V1ManageQuestionBankColumnMappingListPostRequestBody;
    V1ManageQuestionBankColumnMappingListPostResponse:    V1ManageQuestionBankColumnMappingListPostResponse;
    /**
     * 题库字段配置新增/更新DTO
     */
    V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody: V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody;
    V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostResponse:    V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostResponse;
    V1ManageQuestionBankDeleteQuestionBankGetQuery:                          V1ManageQuestionBankDeleteQuestionBankGetQuery;
    V1ManageQuestionBankDeleteQuestionBankGetResponse:                       V1ManageQuestionBankDeleteQuestionBankGetResponse;
    /**
     * 题库-AI试题生成配置请求体DTO
     */
    V1ManageQuestionBankGenerateQuestionByFilePostRequestBody: V1ManageQuestionBankGenerateQuestionByFilePostRequestBody;
    V1ManageQuestionBankGenerateQuestionByFilePostResponse:    V1ManageQuestionBankGenerateQuestionByFilePostResponse;
    /**
     * 题库-生成试卷题目请求体DTO
     */
    V1ManageQuestionBankGenerateQuestionPostRequestBody:    V1ManageQuestionBankGenerateQuestionPostRequestBody;
    V1ManageQuestionBankGenerateQuestionPostResponse:       V1ManageQuestionBankGenerateQuestionPostResponse;
    V1ManageQuestionBankGetInfoGetQuery:                    V1ManageQuestionBankGetInfoGetQuery;
    V1ManageQuestionBankGetInfoGetResponse:                 V1ManageQuestionBankGetInfoGetResponse;
    V1ManageQuestionBankGetQuestionBankPagePostRequestBody: V1ManageQuestionBankGetQuestionBankPagePostRequestBody;
    V1ManageQuestionBankGetQuestionBankPagePostResponse:    V1ManageQuestionBankGetQuestionBankPagePostResponse;
    /**
     * 题库-Excel/Word导入试题入库请求体DTO
     */
    V1ManageQuestionBankImportQuestionBatchInsertPostRequestBody: V1ManageQuestionBankImportQuestionBatchInsertPostRequestBody;
    V1ManageQuestionBankImportQuestionBatchInsertPostResponse:    V1ManageQuestionBankImportQuestionBatchInsertPostResponse;
    /**
     * 题库-Excel导入试题请求体DT
     */
    V1ManageQuestionBankImportQuestionByExcelPostRequestBody: V1ManageQuestionBankImportQuestionByExcelPostRequestBody;
    V1ManageQuestionBankImportQuestionByExcelPostResponse:    V1ManageQuestionBankImportQuestionByExcelPostResponse;
    /**
     * 题库-Excel导入试题请求体DT
     */
    V1ManageQuestionBankImportQuestionByWordPostRequestBody: V1ManageQuestionBankImportQuestionByWordPostRequestBody;
    V1ManageQuestionBankImportQuestionByWordPostResponse:    V1ManageQuestionBankImportQuestionByWordPostResponse;
    /**
     * 题库列表新增请求体DTO
     */
    V1ManageQuestionBankInsertQuestionBankPostRequestBody: V1ManageQuestionBankInsertQuestionBankPostRequestBody;
    V1ManageQuestionBankInsertQuestionBankPostResponse:    V1ManageQuestionBankInsertQuestionBankPostResponse;
    /**
     * 题库-AI试题生成错误信息请求体DTO
     */
    V1ManageQuestionBankReceiveAiGenerateErrorPostRequestBody:  V1ManageQuestionBankReceiveAIGenerateErrorPostRequestBody;
    V1ManageQuestionBankReceiveAiGenerateErrorPostResponse:     V1ManageQuestionBankReceiveAIGenerateErrorPostResponse;
    V1ManageQuestionBankTypeDeleteByIdGetQuery:                 V1ManageQuestionBankTypeDeleteByIDGetQuery;
    V1ManageQuestionBankTypeDeleteByIdGetResponse:              V1ManageQuestionBankTypeDeleteByIDGetResponse;
    V1ManageQuestionBankTypeGetQuestionBankTypeByIdGetQuery:    V1ManageQuestionBankTypeGetQuestionBankTypeByIDGetQuery;
    V1ManageQuestionBankTypeGetQuestionBankTypeByIdGetResponse: V1ManageQuestionBankTypeGetQuestionBankTypeByIDGetResponse;
    /**
     * 题库类型查询DTO
     */
    V1ManageQuestionBankTypeGetQuestionBankTypeListPostRequestBody: V1ManageQuestionBankTypeGetQuestionBankTypeListPostRequestBody;
    V1ManageQuestionBankTypeGetQuestionBankTypeListPostResponse:    V1ManageQuestionBankTypeGetQuestionBankTypeListPostResponse;
    /**
     * 题库类型新增/更新DTO
     */
    V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostRequestBody: V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostRequestBody;
    V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostResponse:    V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostResponse;
    /**
     * 题库列表更新请求体DTO
     */
    V1ManageQuestionBankUpdateQuestionBankPostRequestBody:                V1ManageQuestionBankUpdateQuestionBankPostRequestBody;
    V1ManageQuestionBankUpdateQuestionBankPostResponse:                   V1ManageQuestionBankUpdateQuestionBankPostResponse;
    V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIdGetQuery:    V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetQuery;
    V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIdGetResponse: V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponse;
    /**
     * 上传管理查询DTO
     */
    V1ManageQuestionBankUploadGetQuestionBankUploadListPostRequestBody: V1ManageQuestionBankUploadGetQuestionBankUploadListPostRequestBody;
    V1ManageQuestionBankUploadGetQuestionBankUploadListPostResponse:    V1ManageQuestionBankUploadGetQuestionBankUploadListPostResponse;
    V1ManageQuestionUserIdGetParam:                                     V1ManageQuestionUserIDGetParam;
    V1ManageQuestionUserIdGetResponse:                                  V1ManageQuestionUserIDGetResponse;
    V1ManageQuestionUserPagePostRequestBody:                            V1ManageQuestionUserPagePostRequestBody;
    V1ManageQuestionUserPagePostResponse:                               V1ManageQuestionUserPagePostResponse;
    /**
     * 用户修改状态请求体DTO
     */
    V1ManageQuestionUserStatusPostRequestBody:           V1ManageQuestionUserStatusPostRequestBody;
    V1ManageQuestionUserStatusPostResponse:              V1ManageQuestionUserStatusPostResponse;
    V1ManageSceneCluesClueIdDeleteParam:                 V1ManageSceneCluesClueIDDeleteParam;
    V1ManageSceneCluesClueIdDeleteResponse:              V1ManageSceneCluesClueIDDeleteResponse;
    V1ManageSceneCluesClueIdGetParam:                    V1ManageSceneCluesClueIDGetParam;
    V1ManageSceneCluesClueIdGetResponse:                 V1ManageSceneCluesClueIDGetResponse;
    V1ManageSceneCluesListPostRequestBody:               V1ManageSceneCluesListPostRequestBody;
    V1ManageSceneCluesListPostResponse:                  V1ManageSceneCluesListPostResponse;
    V1ManageSceneCluesPostRequestBody:                   V1ManageSceneCluesPostRequestBody;
    V1ManageSceneCluesPostResponse:                      V1ManageSceneCluesPostResponse;
    V1ManageSceneCluesPutRequestBody:                    V1ManageSceneCluesPutRequestBody;
    V1ManageSceneCluesPutResponse:                       V1ManageSceneCluesPutResponse;
    V1ManageSceneExamPagePostRequestBody:                V1ManageSceneExamPagePostRequestBody;
    V1ManageSceneExamPagePostResponse:                   V1ManageSceneExamPagePostResponse;
    V1ManageSceneExamSubmitPostRequestBody:              V1ManageSceneExamSubmitPostRequestBody;
    V1ManageSceneExamSubmitPostResponse:                 V1ManageSceneExamSubmitPostResponse;
    V1ManageSceneQuestionsListPostRequestBody:           V1ManageSceneQuestionsListPostRequestBody;
    V1ManageSceneQuestionsListPostResponse:              V1ManageSceneQuestionsListPostResponse;
    V1ManageSceneQuestionsPostRequestBody:               V1ManageSceneQuestionsPostRequestBody;
    V1ManageSceneQuestionsPostResponse:                  V1ManageSceneQuestionsPostResponse;
    V1ManageSceneQuestionsPutRequestBody:                V1ManageSceneQuestionsPutRequestBody;
    V1ManageSceneQuestionsPutResponse:                   V1ManageSceneQuestionsPutResponse;
    V1ManageSceneQuestionsQuestionIdDeleteParam:         V1ManageSceneQuestionsQuestionIDDeleteParam;
    V1ManageSceneQuestionsQuestionIdDeleteResponse:      V1ManageSceneQuestionsQuestionIDDeleteResponse;
    V1ManageSceneQuestionsQuestionIdGetParam:            V1ManageSceneQuestionsQuestionIDGetParam;
    V1ManageSceneQuestionsQuestionIdGetResponse:         V1ManageSceneQuestionsQuestionIDGetResponse;
    V1ManageSceneQuestionsSceneIdRandomGetParam:         V1ManageSceneQuestionsSceneIDRandomGetParam;
    V1ManageSceneQuestionsSceneIdRandomGetQuery:         V1ManageSceneQuestionsSceneIDRandomGetQuery;
    V1ManageSceneQuestionsSceneIdRandomGetResponse:      V1ManageSceneQuestionsSceneIDRandomGetResponse;
    V1ManageScenesCurrentUserListGetQuery:               V1ManageScenesCurrentUserListGetQuery;
    V1ManageScenesCurrentUserListGetResponse:            V1ManageScenesCurrentUserListGetResponse;
    V1ManageScenesPagePostRequestBody:                   V1ManageScenesPagePostRequestBody;
    V1ManageScenesPagePostResponse:                      V1ManageScenesPagePostResponse;
    V1ManageScenesPostRequestBody:                       V1ManageScenesPostRequestBody;
    V1ManageScenesPostResponse:                          V1ManageScenesPostResponse;
    V1ManageScenesPutRequestBody:                        V1ManageScenesPutRequestBody;
    V1ManageScenesPutResponse:                           V1ManageScenesPutResponse;
    V1ManageScenesSceneIdDeleteParam:                    V1ManageScenesSceneIDDeleteParam;
    V1ManageScenesSceneIdDeleteResponse:                 V1ManageScenesSceneIDDeleteResponse;
    V1ManageScenesSceneIdGetParam:                       V1ManageScenesSceneIDGetParam;
    V1ManageScenesSceneIdGetResponse:                    V1ManageScenesSceneIDGetResponse;
    V1ManageStatisticalReportProjectPostRequestBody:     V1ManageStatisticalReportProjectPostRequestBody;
    V1ManageStatisticalReportProjectPostResponse:        V1ManageStatisticalReportProjectPostResponse;
    V1ManageStatisticalReportProjectUserPostRequestBody: V1ManageStatisticalReportProjectUserPostRequestBody;
    V1ManageStatisticalReportProjectUserPostResponse:    V1ManageStatisticalReportProjectUserPostResponse;
    V1ManageStatisticalReportTrainNumPostRequestBody:    V1ManageStatisticalReportTrainNumPostRequestBody;
    V1ManageStatisticalReportTrainNumPostResponse:       V1ManageStatisticalReportTrainNumPostResponse;
    V1ManageSysConfigListGetResponse:                    V1ManageSysConfigListGetResponse;
    /**
     * 系统配置修改请求体DTO
     */
    V1ManageSysConfigPutRequestBody:      V1ManageSysConfigPutRequestBody;
    V1ManageSysConfigPutResponse:         V1ManageSysConfigPutResponse;
    V1ManageSysOrgDeleteIdDeleteParam:    V1ManageSysOrgDeleteIDDeleteParam;
    V1ManageSysOrgDeleteIdDeleteResponse: V1ManageSysOrgDeleteIDDeleteResponse;
    V1ManageSysOrgIdGetParam:             V1ManageSysOrgIDGetParam;
    V1ManageSysOrgIdGetResponse:          V1ManageSysOrgIDGetResponse;
    /**
     * 组织修改请求体DTO
     */
    V1ManageSysOrgPostRequestBody: V1ManageSysOrgPostRequestBody;
    V1ManageSysOrgPostResponse:    V1ManageSysOrgPostResponse;
    /**
     * 新增请求体DTO
     */
    V1ManageSysOrgPutRequestBody:           V1ManageSysOrgPutRequestBody;
    V1ManageSysOrgPutResponse:              V1ManageSysOrgPutResponse;
    V1ManageSysOrgTreeGetQuery:             V1ManageSysOrgTreeGetQuery;
    V1ManageSysOrgTreeGetResponse:          V1ManageSysOrgTreeGetResponse;
    V1ManageSysOrgUnitUserTreePostResponse: V1ManageSysOrgUnitUserTreePostResponse;
    V1ManageSysUnitDeleteIdDeleteParam:     V1ManageSysUnitDeleteIDDeleteParam;
    V1ManageSysUnitDeleteIdDeleteResponse:  V1ManageSysUnitDeleteIDDeleteResponse;
    V1ManageSysUnitIdGetParam:              V1ManageSysUnitIDGetParam;
    V1ManageSysUnitIdGetResponse:           V1ManageSysUnitIDGetResponse;
    /**
     * 工位修改请求体DTO
     */
    V1ManageSysUnitPostRequestBody: V1ManageSysUnitPostRequestBody;
    V1ManageSysUnitPostResponse:    V1ManageSysUnitPostResponse;
    /**
     * 工位新增请求体DTO
     */
    V1ManageSysUnitPutRequestBody:                            V1ManageSysUnitPutRequestBody;
    V1ManageSysUnitPutResponse:                               V1ManageSysUnitPutResponse;
    V1ManageSysUnitTerminalBindLocationLocationIdGetParam:    V1ManageSysUnitTerminalBindLocationLocationIDGetParam;
    V1ManageSysUnitTerminalBindLocationLocationIdGetResponse: V1ManageSysUnitTerminalBindLocationLocationIDGetResponse;
    V1ManageSysUnitTerminalCaptureIpGetParam:                 V1ManageSysUnitTerminalCaptureIPGetParam;
    V1ManageSysUnitTerminalCaptureIpGetResponse:              V1ManageSysUnitTerminalCaptureIPGetResponse;
    V1ManageSysUnitTerminalIdGetParam:                        V1ManageSysUnitTerminalIDGetParam;
    V1ManageSysUnitTerminalIdGetResponse:                     V1ManageSysUnitTerminalIDGetResponse;
    V1ManageSysUnitTerminalListGetResponse:                   V1ManageSysUnitTerminalListGetResponse;
    V1ManageSysUnitTreePostResponse:                          V1ManageSysUnitTreePostResponse;
    /**
     * 用户绑定卡号DTO
     */
    V1ManageSysUserBindCardPostRequestBody: V1ManageSysUserBindCardPostRequestBody;
    V1ManageSysUserBindCardPostResponse:    V1ManageSysUserBindCardPostResponse;
    /**
     * 删除用户批量
     */
    V1ManageSysUserDeleteBatchPostRequestBody: V1ManageSysUserDeleteBatchPostRequestBody;
    V1ManageSysUserDeleteBatchPostResponse:    V1ManageSysUserDeleteBatchPostResponse;
    V1ManageSysUserDeleteIdDeleteParam:        V1ManageSysUserDeleteIDDeleteParam;
    V1ManageSysUserDeleteIdDeleteResponse:     V1ManageSysUserDeleteIDDeleteResponse;
    V1ManageSysUserIdGetParam:                 V1ManageSysUserIDGetParam;
    V1ManageSysUserIdGetResponse:              V1ManageSysUserIDGetResponse;
    V1ManageSysUserJobPagePostRequestBody:     V1ManageSysUserJobPagePostRequestBody;
    V1ManageSysUserJobPagePostResponse:        V1ManageSysUserJobPagePostResponse;
    V1ManageSysUserJobStatisticsGetQuery:      V1ManageSysUserJobStatisticsGetQuery;
    V1ManageSysUserJobStatisticsGetResponse:   V1ManageSysUserJobStatisticsGetResponse;
    V1ManageSysUserPagePostRequestBody:        V1ManageSysUserPagePostRequestBody;
    V1ManageSysUserPagePostResponse:           V1ManageSysUserPagePostResponse;
    /**
     * 用户修改状态请求体DTO
     */
    V1ManageSysUserStatusPostRequestBody: V1ManageSysUserStatusPostRequestBody;
    V1ManageSysUserStatusPostResponse:    V1ManageSysUserStatusPostResponse;
    /**
     * 用户绑定卡号DTO
     */
    V1ManageSysUserUnbindCardPostRequestBody: V1ManageSysUserUnbindCardPostRequestBody;
    V1ManageSysUserUnbindCardPostResponse:    V1ManageSysUserUnbindCardPostResponse;
    /**
     * 用户修改请求体DTO
     */
    V1ManageSysUserUpdatePostRequestBody: V1ManageSysUserUpdatePostRequestBody;
    V1ManageSysUserUpdatePostResponse:    V1ManageSysUserUpdatePostResponse;
    /**
     * 用户修改工号请求体DTO
     */
    V1ManageSysUserWorkNoPutRequestBody:                      V1ManageSysUserWorkNoPutRequestBody;
    V1ManageSysUserWorkNoPutResponse:                         V1ManageSysUserWorkNoPutResponse;
    V1ManageTrainBookingsBookingIdDeleteParam:                V1ManageTrainBookingsBookingIDDeleteParam;
    V1ManageTrainBookingsBookingIdDeleteResponse:             V1ManageTrainBookingsBookingIDDeleteResponse;
    V1ManageTrainBookingsBookingIdGetParam:                   V1ManageTrainBookingsBookingIDGetParam;
    V1ManageTrainBookingsBookingIdGetResponse:                V1ManageTrainBookingsBookingIDGetResponse;
    V1ManageTrainBookingsPostRequestBody:                     V1ManageTrainBookingsPostRequestBody;
    V1ManageTrainBookingsPostResponse:                        V1ManageTrainBookingsPostResponse;
    V1ManageTrainBookingsPutRequestBody:                      V1ManageTrainBookingsPutRequestBody;
    V1ManageTrainBookingsPutResponse:                         V1ManageTrainBookingsPutResponse;
    V1ManageTrainStudyRecordsAlarmDetailsPagePostRequestBody: V1ManageTrainStudyRecordsAlarmDetailsPagePostRequestBody;
    V1ManageTrainStudyRecordsAlarmDetailsPagePostResponse:    V1ManageTrainStudyRecordsAlarmDetailsPagePostResponse;
    /**
     * 学员培训不合格记录请求体
     */
    V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBody:                 V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBodyObject;
    V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostResponse:                    V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostResponse;
    V1ManageTrainStudyRecordsCheckRankProjectIdGetParam:                            V1ManageTrainStudyRecordsCheckRankProjectIDGetParam;
    V1ManageTrainStudyRecordsCheckRankProjectIdGetResponse:                         V1ManageTrainStudyRecordsCheckRankProjectIDGetResponse;
    V1ManageTrainStudyRecordsDataStatisticsRecordIdGetParam:                        V1ManageTrainStudyRecordsDataStatisticsRecordIDGetParam;
    V1ManageTrainStudyRecordsDataStatisticsRecordIdGetResponse:                     V1ManageTrainStudyRecordsDataStatisticsRecordIDGetResponse;
    V1ManageTrainStudyRecordsIdGetParam:                                            V1ManageTrainStudyRecordsIDGetParam;
    V1ManageTrainStudyRecordsIdGetResponse:                                         V1ManageTrainStudyRecordsIDGetResponse;
    V1ManageTrainStudyRecordsLastStatisticsRecordIdGetParam:                        V1ManageTrainStudyRecordsLastStatisticsRecordIDGetParam;
    V1ManageTrainStudyRecordsLastStatisticsRecordIdGetResponse:                     V1ManageTrainStudyRecordsLastStatisticsRecordIDGetResponse;
    V1ManageTrainStudyRecordsMonthlyStatisticsGetResponse:                          V1ManageTrainStudyRecordsMonthlyStatisticsGetResponse;
    V1ManageTrainStudyRecordsPagePostRequestBody:                                   V1ManageTrainStudyRecordsPagePostRequestBody;
    V1ManageTrainStudyRecordsPagePostResponse:                                      V1ManageTrainStudyRecordsPagePostResponse;
    V1ManageTrainStudyRecordsProgressStatisticsRecordIdGetParam:                    V1ManageTrainStudyRecordsProgressStatisticsRecordIDGetParam;
    V1ManageTrainStudyRecordsProgressStatisticsRecordIdGetResponse:                 V1ManageTrainStudyRecordsProgressStatisticsRecordIDGetResponse;
    V1ManageTrainStudyRecordsProjectsGetQuery:                                      V1ManageTrainStudyRecordsProjectsGetQuery;
    V1ManageTrainStudyRecordsProjectsGetResponse:                                   V1ManageTrainStudyRecordsProjectsGetResponse;
    V1ManageTrainStudyRecordsRaceRankProjectIdGetParam:                             V1ManageTrainStudyRecordsRaceRankProjectIDGetParam;
    V1ManageTrainStudyRecordsRaceRankProjectIdGetResponse:                          V1ManageTrainStudyRecordsRaceRankProjectIDGetResponse;
    V1ManageTrainStudyRecordsRankAllProjectIdTypeStartEndGetParam:                  V1ManageTrainStudyRecordsRankAllProjectIDTypeStartEndGetParam;
    V1ManageTrainStudyRecordsRankAllProjectIdTypeStartEndGetResponse:               V1ManageTrainStudyRecordsRankAllProjectIDTypeStartEndGetResponse;
    V1ManageTrainStudyRecordsRankRecordIdGetParam:                                  V1ManageTrainStudyRecordsRankRecordIDGetParam;
    V1ManageTrainStudyRecordsRankRecordIdGetResponse:                               V1ManageTrainStudyRecordsRankRecordIDGetResponse;
    V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIdGetParam:    V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIDGetParam;
    V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIdGetResponse: string;
    V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmIdGetParam:       V1ManageTrainStudyRecordsRecordDetailAlarmURLRecordDetailAlarmIDGetParam;
    V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmIdGetResponse:    V1ManageTrainStudyRecordsRecordDetailAlarmURLRecordDetailAlarmIDGetResponse;
    V1ManageTrainStudyRecordsUsersGetResponse:                                      V1ManageTrainStudyRecordsUsersGetResponse;
    V1ManageTranProjectBindProjectLocationIdGetParam:                               V1ManageTranProjectBindProjectLocationIDGetParam;
    V1ManageTranProjectBindProjectLocationIdGetQuery:                               V1ManageTranProjectBindProjectLocationIDGetQuery;
    V1ManageTranProjectBindProjectLocationIdGetResponse:                            V1ManageTranProjectBindProjectLocationIDGetResponse;
    V1ManageTranProjectDeleteIdDeleteParam:                                         V1ManageTranProjectDeleteIDDeleteParam;
    V1ManageTranProjectDeleteIdDeleteResponse:                                      V1ManageTranProjectDeleteIDDeleteResponse;
    V1ManageTranProjectIdGetParam:                                                  V1ManageTranProjectIDGetParam;
    V1ManageTranProjectIdGetResponse:                                               V1ManageTranProjectIDGetResponse;
    /**
     * 项目查询DTO
     */
    V1ManageTranProjectListPostRequestBody: V1ManageTranProjectListPostRequestBodyObject;
    V1ManageTranProjectListPostResponse:    V1ManageTranProjectListPostResponse;
    V1ManageTranProjectPagePostRequestBody: V1ManageTranProjectPagePostRequestBody;
    V1ManageTranProjectPagePostResponse:    V1ManageTranProjectPagePostResponse;
    /**
     * 培训项目新增请求体DTO
     */
    V1ManageTranProjectPostRequestBody: V1ManageTranProjectPostRequestBody;
    V1ManageTranProjectPostResponse:    V1ManageTranProjectPostResponse;
    /**
     * 培训项目新增请求体DTO
     */
    V1ManageTranProjectPutRequestBody: V1ManageTranProjectPutRequestBody;
    V1ManageTranProjectPutResponse:    V1ManageTranProjectPutResponse;
    /**
     * 培训项目新增请求体DTO
     */
    V1ManageTranProjectStatusPostRequestBody:              V1ManageTranProjectStatusPostRequestBody;
    V1ManageTranProjectStatusPostResponse:                 V1ManageTranProjectStatusPostResponse;
    V1ManageTranProjectWithNameGetQuery:                   V1ManageTranProjectWithNameGetQuery;
    V1ManageTranProjectWithNameGetResponse:                V1ManageTranProjectWithNameGetResponse;
    V1ManageUserLearningsIncrementTimePutRequestBody:      V1ManageUserLearningsIncrementTimePutRequestBody;
    V1ManageUserLearningsIncrementTimePutResponse:         V1ManageUserLearningsIncrementTimePutResponse;
    V1ManageUserLearningsLearningDetailIdGetParam:         V1ManageUserLearningsLearningDetailIDGetParam;
    V1ManageUserLearningsLearningDetailIdGetResponse:      V1ManageUserLearningsLearningDetailIDGetResponse;
    V1ManageUserLearningsLearningDetailIdStartPutParam:    V1ManageUserLearningsLearningDetailIDStartPutParam;
    V1ManageUserLearningsLearningDetailIdStartPutResponse: V1ManageUserLearningsLearningDetailIDStartPutResponse;
    V1ManageUserLearningsPagePostRequestBody:              V1ManageUserLearningsPagePostRequestBody;
    V1ManageUserLearningsPagePostResponse:                 V1ManageUserLearningsPagePostResponse;
    V1ManageVehicleImportPostRequestBody:                  V1ManageVehicleImportPostRequestBody;
    V1ManageVehicleImportPostResponse:                     V1ManageVehicleImportPostResponse;
    V1ManageVehiclePagePostRequestBody:                    V1ManageVehiclePagePostRequestBody;
    V1ManageVehiclePagePostResponse:                       V1ManageVehiclePagePostResponse;
    V1MobileHomeCurrentDateGetResponse:                    V1MobileHomeCurrentDateGetResponse;
    V1MobileHomeCurrentUserGetResponse:                    V1MobileHomeCurrentUserGetResponse;
    V1MobileHomeExamRecordHistoryPaperIdUserIdGetParam:    V1MobileHomeExamRecordHistoryPaperIDUserIDGetParam;
    V1MobileHomeExamRecordHistoryPaperIdUserIdGetResponse: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponse;
    V1MobileHomeExamRecordIdGetParam:                      V1MobileHomeExamRecordIDGetParam;
    V1MobileHomeExamRecordIdGetResponse:                   V1MobileHomeExamRecordIDGetResponse;
    V1MobileHomeExamRecordPaperIdGetParam:                 V1MobileHomeExamRecordPaperIDGetParam;
    V1MobileHomeExamRecordPaperIdGetResponse:              V1MobileHomeExamRecordPaperIDGetResponse;
    V1MobileHomeExamRecordPaperInitIdGetParam:             V1MobileHomeExamRecordPaperInitIDGetParam;
    V1MobileHomeExamRecordPaperInitIdGetResponse:          V1MobileHomeExamRecordPaperInitIDGetResponse;
    V1MobileHomeLastedExamRecordPaperIdGetParam:           V1MobileHomeLastedExamRecordPaperIDGetParam;
    V1MobileHomeLastedExamRecordPaperIdGetResponse:        V1MobileHomeLastedExamRecordPaperIDGetResponse;
    /**
     * 考试记录查询DTO
     */
    V1MobileHomeListPostRequestBody:  V1MobileHomeListPostRequestBodyObject;
    V1MobileHomeListPostResponse:     V1MobileHomeListPostResponse;
    V1MobileHomeLoginSyncGetResponse: V1MobileHomeLoginSyncGetResponse;
    V1MobileHomePagePostRequestBody:  V1MobileHomePagePostRequestBody;
    V1MobileHomePagePostResponse:     V1MobileHomePagePostResponse;
    /**
     * 开始答题DTO
     */
    V1MobileStudyExamRecordStartPostRequestBody: V1MobileStudyExamRecordStartPostRequestBody;
    V1MobileStudyExamRecordStartPostResponse:    V1MobileStudyExamRecordStartPostResponse;
    /**
     * 结束答题DTO
     */
    V1MobileStudyOverPostRequestBody:             V1MobileStudyOverPostRequestBody;
    V1MobileStudyOverPostResponse:                V1MobileStudyOverPostResponse;
    V1OpenApiGetFlvHlsStream_channel_flvGetParam: V1OpenAPIGetFlvHLSStreamChannelFlvGetParam;
    V1OpenApiGetFlvHlsStreamGetQuery:             V1OpenAPIGetFlvHLSStreamGetQuery;
    V1OpenApiGetUrlStream_channelGetParam:        V1OpenAPIGetURLStreamChannelGetParam;
    V1OpenApiGetUrlStream_channelGetResponse:     V1OpenAPIGetURLStreamChannelGetResponse;
    V1OpenApiSysConfigListGetResponse:            V1OpenAPISysConfigListGetResponse;
    V1OpenApiUserLoginGucPostRequestBody:         V1OpenAPIUserLoginGucPostRequestBody;
    V1OpenApiUserLoginGucPostResponse:            V1OpenAPIUserLoginGucPostResponse;
    V1OpenApiUserLoginPublicKeyGetQuery:          V1OpenAPIUserLoginPublicKeyGetQuery;
    V1OpenApiUserLoginPublicKeyGetResponse:       V1OpenAPIUserLoginPublicKeyGetResponse;
    /**
     * 微信请求体DTO
     */
    V1OpenApiUserLoginWxJsSdkPostRequestBody: V1OpenAPIUserLoginWxJSSDKPostRequestBody;
    V1OpenApiUserLoginWxJsSdkPostResponse:    V1OpenAPIUserLoginWxJSSDKPostResponse;
    [property: string]: any;
}

/**
 * 绑定人员请求体
 */
export interface DeviceBindingCardPostRequestBody {
    /**
     * 卡号
     */
    cardNo: string;
    /**
     * 人员ID
     */
    employeeId: string;
    [property: string]: any;
}

export interface DeviceBindingCardPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

/**
 * 绑定人员与卡请求体
 */
export interface DeviceBindingFacePostRequestBody {
    /**
     * 人员ID
     */
    employeeId?: string;
    /**
     * 人脸文件URL
     */
    faceId?: string;
    /**
     * 人脸的URL
     */
    faceUrl: string;
    [property: string]: any;
}

export interface DeviceBindingFacePostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface DeviceCaptureFaceInfoGetQuery {
    deviceIp?: string;
    [property: string]: any;
}

export interface DeviceCaptureFaceInfoGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface DeviceDeleteAllEmployeeInfoDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface DeviceDeleteCardNoCardNoPostParam {
    cardNo: string;
    [property: string]: any;
}

export interface DeviceDeleteCardNoCardNoPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface DeviceDeleteEmployeeIDPostParam {
    employeeId: string;
    [property: string]: any;
}

export interface DeviceDeleteEmployeeIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface DeviceDeleteFaceInfoEmployeeIDPostParam {
    employeeId: string;
    [property: string]: any;
}

export interface DeviceDeleteFaceInfoEmployeeIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

/**
 * 注册人员请求体
 */
export interface DeviceRegisterPostRequestBody {
    /**
     * 员工ID
     */
    employeeId: string;
    /**
     * 人员姓名
     */
    name: string;
    [property: string]: any;
}

export interface DeviceRegisterPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface DeviceSyncPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1CommonFileUploadBusinessTypePostParam {
    businessType: string;
    [property: string]: any;
}

export interface V1CommonFileUploadBusinessTypePostRequestBody {
    file: string;
    [property: string]: any;
}

export interface V1CommonFileUploadBusinessTypePostResponse {
    code?:   string;
    msg?:    string;
    result?: AttachmentListElement;
    [property: string]: any;
}

/**
 * 参考文件列表
 */
export interface AttachmentListElement {
    /**
     * 文件名称
     */
    fileName?: string;
    /**
     * 文件大小
     */
    fileSize?: string;
    /**
     * 文件ID
     */
    id?: string;
    /**
     * 文件相对路径
     */
    path?: string;
    /**
     * 文件访问地址
     */
    url?: string;
    [property: string]: any;
}

export interface V1CommonFileUploadDownloadFileIDPostParam {
    fileId: string;
    [property: string]: any;
}

export interface V1CommonFileUploadDownloadFileIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1CommonFileUploadDownloadPathFileIDPostParam {
    fileId: string;
    [property: string]: any;
}

export interface V1CommonFileUploadDownloadPathFileIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1CustomSendMessagePostRequestBody {
    clientId?: string;
    message?:  string;
    [property: string]: any;
}

export interface V1CustomSendMessagePostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1EvaluateConfigDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1EvaluateConfigDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1EvaluateConfigPagePostRequestBody {
    currentPage?: number;
    data?:        V1EvaluateConfigPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 评定项目查询DTO
 */
export interface V1EvaluateConfigPagePostRequestBodyData {
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 状态：0-禁用，1-启用
     */
    status?: number;
    [property: string]: any;
}

export interface V1EvaluateConfigPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1EvaluateConfigPagePostResponseResult;
    [property: string]: any;
}

export interface V1EvaluateConfigPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1EvaluateConfigSelectGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 评定项目列表VO
 */
export interface V1EvaluateConfigSelectGetResponseResult {
    /**
     * 编辑人
     */
    editor?: string;
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 评定项目组合
     */
    evaluateProjectList?: string[];
    /**
     * 评定结果组合
     */
    evaluateResultList?: string[];
    /**
     * ID
     */
    id?: string;
    /**
     * 编辑时间
     */
    lastUpdateTime?: number;
    /**
     * 评定项目说明
     */
    remark?: string;
    /**
     * 状态：0-禁用，1-启用
     */
    status?: number;
    [property: string]: any;
}

/**
 * 评定项目保存DTO
 */
export interface V1EvaluateConfigSavePostRequestBody {
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 评定项目组合
     */
    evaluateProjectList?: string[];
    /**
     * 评定结果组合
     */
    evaluateResultList?: string[];
    /**
     * ID
     */
    id?: string;
    /**
     * 评定项目说明
     */
    remark?: string;
    /**
     * 状态：0-禁用，1-启用
     */
    status?: number;
    [property: string]: any;
}

export interface V1EvaluateConfigSavePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1EvaluateConfigSelectGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1EvaluateConfigSelectGetResponseResult[];
    [property: string]: any;
}

/**
 * 评定项目状态切换DTO
 */
export interface V1EvaluateConfigStatusPostRequestBody {
    /**
     * ID
     */
    id: string;
    /**
     * 状态：0-禁用，1-启用
     */
    status?: number;
    [property: string]: any;
}

export interface V1EvaluateConfigStatusPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1EvaluateTaskDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1EvaluateTaskDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1EvaluateTaskOrderInfoIDPostParam {
    id: string;
    [property: string]: any;
}

export interface V1EvaluateTaskOrderInfoIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1EvaluateTaskOrderInfoIDPostResponseResult;
    [property: string]: any;
}

/**
 * 评定任务工单详情VO
 */
export interface V1EvaluateTaskOrderInfoIDPostResponseResult {
    /**
     * 评定人员id
     */
    assessor?: string;
    /**
     * 评定人员名称
     */
    assessorName?: string;
    /**
     * 所用时长
     */
    duration?: number;
    /**
     * 评定项目配置ID
     */
    evaluateConfigId?: string;
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 评审任务人员组ID
     */
    evaluateTaskGroupId?: string;
    /**
     * 评审任务ID
     */
    evaluateTaskId?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 评定时间
     */
    lastUpdateTime?: number;
    /**
     * 评定结果：0-不通过，1-通过
     */
    result?: number;
    /**
     * 评审子项目结果
     */
    resultList?: ResultListElement[];
    /**
     * 评审人员组
     */
    reviewerList?: ReviewerListElement[];
    /**
     * 状态：0-待评审，1-合格，2-不合格
     */
    status?: number;
    [property: string]: any;
}

/**
 * 评定任务工单结果数据DTO
 */
export interface ResultListElement {
    /**
     * 评定项目
     */
    evaluateProject: string;
    /**
     * 评定结果：0-不通过，1-通过
     */
    result: number;
    [property: string]: any;
}

/**
 * 评审人员组VO
 */
export interface ReviewerListElement {
    /**
     * 实际评审操作标识
     */
    actualOperate?: boolean;
    /**
     * 评审人ID
     */
    userId?: string;
    /**
     * 评审人
     */
    userName?: string;
    [property: string]: any;
}

export interface V1EvaluateTaskPagePostRequestBody {
    currentPage?: number;
    data?:        V1EvaluateTaskPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 评定任务工单查询DTO
 */
export interface V1EvaluateTaskPagePostRequestBodyData {
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 状态：0-待评审，1-合格，2-不合格
     */
    status?: number;
    /**
     * 人员名称
     */
    userName?: string;
    [property: string]: any;
}

export interface V1EvaluateTaskPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1EvaluateTaskPagePostResponseResult;
    [property: string]: any;
}

export interface V1EvaluateTaskPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     PurpleAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 评定任务工单列表VO
 */
export interface PurpleAPIModel {
    /**
     * 评定人员id
     */
    assessor?: string;
    /**
     * 评定人员名称
     */
    assessorName?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * 评定项目配置ID
     */
    evaluateConfigId?: string;
    /**
     * 评定项目名称
     */
    evaluateName?: string;
    /**
     * 评定项目组合
     */
    evaluateProjectList?: string[];
    /**
     * 评定结果组合
     */
    evaluateResultList?: string[];
    /**
     * 评审任务人员组ID
     */
    evaluateTaskGroupId?: string;
    /**
     * 评审任务ID
     */
    evaluateTaskId?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 编辑时间
     */
    lastUpdateTime?: number;
    /**
     * 评审人员组
     */
    reviewerList?: ReviewerListElement[];
    /**
     * 状态：0-待评审，1-合格，2-不合格
     */
    status?: number;
    [property: string]: any;
}

/**
 * 评定任务保存DTO
 */
export interface V1EvaluateTaskSavePostRequestBody {
    /**
     * 评定项目配置ID
     */
    evaluateConfigId: string;
    /**
     * 评审任务人员组
     */
    taskGroupList: TaskGroupListElement[];
    [property: string]: any;
}

/**
 * 评定任务人员组DTO
 */
export interface TaskGroupListElement {
    /**
     * 评定人员组
     */
    assessorGroupList: string[];
    /**
     * 评审人员组
     */
    reviewerGroupList: string[];
    /**
     * 序号
     */
    sort: number;
    [property: string]: any;
}

export interface V1EvaluateTaskSavePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 评定任务工单保存DTO
 */
export interface V1EvaluateTaskSubmitPostRequestBody {
    /**
     * 所用时长(分钟)
     */
    duration: number;
    /**
     * 工单ID
     */
    id: string;
    /**
     * 评定结果：0-不通过，1-通过
     */
    result: number;
    /**
     * 评审子项目结果
     */
    resultList?: ResultListElement[];
    [property: string]: any;
}

export interface V1EvaluateTaskSubmitPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1EvaluateTaskUserOrderUserIDPostParam {
    userId: string;
    [property: string]: any;
}

export interface V1EvaluateTaskUserOrderUserIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1EvaluateTaskOrderInfoIDPostResponseResult[];
    [property: string]: any;
}

export interface V1LocationHomeAlarmDetailsPagePostRequestBody {
    currentPage?: number;
    data?:        V1LocationHomeAlarmDetailsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 学员培训不合格记录请求体
 */
export interface V1LocationHomeAlarmDetailsPagePostRequestBodyData {
    /**
     * 动作类型列表. 1: 未双手作业、2: 未垂直作业面、3: 拧紧未贴合、4: 拧紧枪未亮绿灯
     */
    actionTypes?: number[];
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 汇总记录ID
     */
    recordId: string;
    /**
     * 合格情况： 0-不合格， 1-合格
     */
    result?: number;
    /**
     * 动作达标情况： 0-不达标， 1-达标
     */
    standard?: number;
    /**
     * 开始时间
     */
    startTime?: number;
    [property: string]: any;
}

export interface V1LocationHomeAlarmDetailsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeAlarmDetailsPagePostResponseResult;
    [property: string]: any;
}

export interface V1LocationHomeAlarmDetailsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     AlarmItemElement[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 学员培训不合格记录响应体
 */
export interface AlarmItemElement {
    /**
     * 动作类别： 1-双手持枪，2-垂直作业面，3-拧紧贴合， 4-扳手绿灯
     */
    actionType?: number;
    /**
     * 作业错误的帧图片，base64
     */
    frameImage?: string;
    /**
     * 报警详情ID
     */
    recordDetailAlarmId?: string;
    /**
     * 合格情况： 0-不合格， 1-合格
     */
    result?: number;
    /**
     * 动作达标情况： 0-不达标， 1-达标
     */
    standard?: number;
    /**
     * 帧时间戳
     */
    timestamp?: string;
    [property: string]: any;
}

/**
 * 学员培训不合格记录请求体
 */
export interface V1LocationHomeAlarmDetailsStatisticsPostRequestBodyObject {
    /**
     * 动作类型列表. 1: 未双手作业、2: 未垂直作业面、3: 拧紧未贴合、4: 拧紧枪未亮绿灯
     */
    actionTypes?: number[];
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 汇总记录ID
     */
    recordId: string;
    /**
     * 合格情况： 0-不合格， 1-合格
     */
    result?: number;
    /**
     * 动作达标情况： 0-不达标， 1-达标
     */
    standard?: number;
    /**
     * 开始时间
     */
    startTime?: number;
    [property: string]: any;
}

export interface V1LocationHomeAlarmDetailsStatisticsPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeAlarmDetailsStatisticsPostResponseResult[];
    [property: string]: any;
}

export interface V1LocationHomeAlarmDetailsStatisticsPostResponseResult {
    /**
     * 动作次数
     */
    actionNum?: number;
    /**
     * 动作达标次数
     */
    actionNumOk?: number;
    /**
     * 不合格记录列表
     */
    alarmItems?: AlarmItemElement[];
    /**
     * 创建日期
     */
    createdDate?: string;
    /**
     * 作业次数
     */
    opNum?: number;
    /**
     * 作业合格次数
     */
    opNumOk?: number;
    /**
     * 技能状态
     */
    skillStatus?: string;
    /**
     * 状态
     */
    status?: string;
    /**
     * 训练时长(单位: 秒)
     */
    trainDuration?: number;
    [property: string]: any;
}

export interface V1LocationHomeCurrentDateGetResponse {
    code?:   string;
    msg?:    string;
    result?: number;
    [property: string]: any;
}

export interface V1LocationHomeCurrentIPGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1LocationHomeCurrentLocationGetQuery {
    ip?: string;
    [property: string]: any;
}

export interface V1LocationHomeCurrentLocationGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentLocationGetResponseResult;
    [property: string]: any;
}

/**
 * 工位信息查询响应体
 */
export interface V1LocationHomeCurrentLocationGetResponseResult {
    /**
     * 工位编号
     */
    code?: string;
    /**
     * 所有父级名称
     */
    levelName?: string;
    /**
     * 工位ID
     */
    locationId?: string;
    /**
     * 工位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1LocationHomeCurrentLocationIPGetParam {
    ip: string;
    [property: string]: any;
}

export interface V1LocationHomeCurrentLocationIPGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentLocationGetResponseResult;
    [property: string]: any;
}

export interface V1LocationHomeCurrentUserGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult;
    [property: string]: any;
}

/**
 * 用户信息查询响应体
 */
export interface V1LocationHomeCurrentUserGetResponseResult {
    /**
     * 账号
     */
    account?: string;
    /**
     * 工号
     */
    cardNoList?: string[];
    /**
     * 邮箱
     */
    email?: string;
    /**
     * 人脸文件ID
     */
    faceId?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 身份证
     */
    idCard?: string;
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 最后登录时间
     */
    lastLoginTime?: number;
    /**
     * 岗级
     */
    level?: number;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 用户组织列表
     */
    orgList?: OrgListElement[];
    /**
     * 同步方ID
     */
    originalId?: string;
    /**
     * 同步类型--GUC
     */
    originalType?: string;
    /**
     * 手机号
     */
    phone?: string;
    /**
     * 用户挂载项目
     */
    projectList?: ProjectListElement[];
    /**
     * 状态 启用ENABLE/停用DISABLE
     */
    status?: string;
    /**
     * 工号
     */
    workNo?: string;
    /**
     * 企微用户ID
     */
    wxUserId?: string;
    [property: string]: any;
}

/**
 * 组织信息查询响应体
 */
export interface OrgListElement {
    ancestors?: string;
    /**
     * 子节点集合
     */
    childNodes?: OrgListElement[];
    /**
     * 编码
     */
    code?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 名称
     */
    name?: string;
    /**
     * 父ID
     */
    parentId?: string;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type?: string;
    [property: string]: any;
}

/**
 * 用户挂载项目信息响应体
 */
export interface ProjectListElement {
    /**
     * ID
     */
    id?:      string;
    project?: Project;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 用户ID
     */
    userId?: string;
    [property: string]: any;
}

/**
 * 项目/课程信息响应体
 */
export interface Project {
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 最后更新人
     */
    lastUpdateBy?: string;
    /**
     * 最后更新时间
     */
    lastUpdateTime?: number;
    /**
     * 课程挂载工位
     */
    locationList?: LocationListElement[];
    /**
     * 课程挂载模型名称集合
     */
    modelList?: ProjectModelList[];
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType?: number;
    /**
     * 比赛类型(1、时间模式 2、次数模式)
     */
    raceType?: number;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 要求动作达标率
     */
    requestActionRate?: number;
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 效率要求
     */
    requestEfficiency?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 合格率要求
     */
    requestQualificationRate?: number;
    /**
     * 状态
     */
    status?: string;
    [property: string]: any;
}

/**
 * 工位信息查询响应体
 */
export interface LocationListElement {
    ancestors?: string;
    /**
     * 子节点集合
     */
    childNodes?: LocationListElement[];
    /**
     * 编码
     */
    code?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 名称
     */
    name?: string;
    /**
     * 父ID
     */
    parentId?: string;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type?: string;
    [property: string]: any;
}

/**
 * 课程挂载模型信息响应体
 */
export interface ProjectModelList {
    /**
     * id
     */
    id?: string;
    /**
     * 模型编码
     */
    modelCode?: string;
    modelJson?: string;
    /**
     * 模型名称
     */
    modelName?: string;
    /**
     * 项目id
     */
    projectId?: string;
    remark?:    string;
    [property: string]: any;
}

export interface V1LocationHomeDataStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1LocationHomeDataStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeDataStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员培训项目数据统计响应体
 */
export interface V1LocationHomeDataStatisticsRecordIDGetResponseResult {
    /**
     * 动作次数
     */
    actionNum?: number;
    /**
     * 动作Ok次数
     */
    actionNumOk?: number;
    /**
     * 作业次数
     */
    opNum?: number;
    /**
     * 作业OK次数
     */
    opNumOk?: number;
    /**
     * 训练次数
     */
    trainNum?: number;
    [property: string]: any;
}

export interface V1LocationHomeLastStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1LocationHomeLastStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeLastStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员最新训练数据统计响应体
 */
export interface V1LocationHomeLastStatisticsRecordIDGetResponseResult {
    /**
     * 作业次数
     */
    opNum?: number;
    /**
     * 作业OK次数
     */
    opNumOk?: number;
    /**
     * 技能状态 PASS_RATE_NO:合格率不达标 EFFICIENCY_NOT:效率不达标 TRAIN_FREQUENCY_NOT:训练次数不达标
     * TRAIN_DURATION_NOT 时长不达标 PASS_OK:合格
     */
    skillStatus?: string;
    /**
     * 已训时长
     */
    trainDuration?: number;
    [property: string]: any;
}

export interface V1LocationHomeLoginSyncGetResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1LocationHomeMyProjectLocationIDUserIDGetParam {
    locationId: string;
    userId:     string;
    [property: string]: any;
}

export interface V1LocationHomeMyProjectLocationIDUserIDGetQuery {
    projectType?: number;
    [property: string]: any;
}

export interface V1LocationHomeMyProjectLocationIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeMyProjectLocationIDUserIDGetResponseResult[];
    [property: string]: any;
}

/**
 * 工位项目信息查询响应体
 */
export interface V1LocationHomeMyProjectLocationIDUserIDGetResponseResult {
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 是否可以操作状态 ENABLE 本工位可以操作，DISABLE 不可以操作
     */
    opStatus?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 课程ID
     */
    recordId?: string;
    /**
     * 项目状态
     */
    status?: string;
    [property: string]: any;
}

export interface V1LocationHomeProgressStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1LocationHomeProgressStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员培训项目训练进度统计响应体
 */
export interface V1LocationHomeProgressStatisticsRecordIDGetResponseResult {
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 已训时长
     */
    trainDuration?: number;
    /**
     * 已训次数
     */
    trainFrequency?: number;
    [property: string]: any;
}

export interface V1LocationHomeRankAllProjectIDTypeStartEndGetParam {
    end:       number;
    projectId: string;
    start:     number;
    type:      string;
    [property: string]: any;
}

export interface V1LocationHomeRankAllProjectIDTypeStartEndGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeRankAllProjectIDTypeStartEndGetResponseResult[];
    [property: string]: any;
}

export interface V1LocationHomeRankAllProjectIDTypeStartEndGetResponseResult {
    /**
     * 记录
     */
    recordId?: string;
    /**
     * 用户名称
     */
    userName?: string;
    /**
     * 值
     */
    value?: number;
    [property: string]: any;
}

export interface V1LocationHomeRankRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1LocationHomeRankRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeRankRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1LocationHomeRankRecordIDGetResponseResult {
    /**
     * 考核排名
     */
    checkRanking?: string;
    /**
     * 时长排名
     */
    duration?: string;
    /**
     * 合格率排名
     */
    qualifiedRate?: string;
    /**
     * 比赛排名
     */
    raceRanking?: string;
    /**
     * 达标率排名
     */
    standardRate?: string;
    /**
     * 培训排名
     */
    trainNum?: string;
    [property: string]: any;
}

export interface V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIDGetParam {
    recordDetailAlarmId: string;
    [property: string]: any;
}

export interface V1LocationStudyCheckProjectIDUserIDGetParam {
    projectId: string;
    userId:    string;
    [property: string]: any;
}

export interface V1LocationStudyCheckProjectIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyCheckProjectIDUserIDGetResponseResult;
    [property: string]: any;
}

/**
 * 培训过程项目记录响应体
 */
export interface V1LocationStudyCheckProjectIDUserIDGetResponseResult {
    /**
     * 动作次数
     */
    actionNum?: number;
    /**
     * 动作Ok次数
     */
    actionNumOk?: number;
    /**
     * 动作达标率
     */
    actionPassRate?: string;
    /**
     * 有效次数
     */
    effectiveNum?: number;
    /**
     * ID
     */
    id?: string;
    /**
     * 最近效率
     */
    lastEfficiency?: number;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    /**
     * 作业次数
     */
    opNum?: number;
    /**
     * 作业OK次数
     */
    opNumOk?: number;
    /**
     * 作业合格率
     */
    passRate?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目名称
     */
    projectName?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType?: number;
    /**
     * 比赛类型(1、时间模式 2、次数模式)
     */
    raceType?: number;
    /**
     * 排名名次
     */
    ranking?: number;
    /**
     * 动作达标率要求
     */
    requestActionRate?: number;
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 效率要求
     */
    requestEfficiency?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 合格率要求
     */
    requestQualificationRate?: number;
    /**
     * 技能状态
     */
    skillStatus?: string;
    /**
     * 状态 TO_BE:待训练、ING:训练中，TO_BE_TEST:待考试，FAILED_TEST:考试未通过，PASSED_TEST:考试通过
     */
    status?: string;
    /**
     * 状态展示值
     */
    statusShow?: string;
    /**
     * 训练时长-min
     */
    trainDuration?: number;
    /**
     * 训练时长-second
     */
    trainDurationSecond?: number;
    /**
     * 训练次数
     */
    trainNum?: number;
    /**
     * 用户ID
     */
    userId?: string;
    /**
     * 用户名称
     */
    userName?: string;
    [property: string]: any;
}

export interface V1LocationStudyDetailDurationDetailIDGetParam {
    detailId: string;
    [property: string]: any;
}

export interface V1LocationStudyDetailDurationDetailIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * 课程结束DTO
 */
export interface V1LocationStudyExitPostRequestBody {
    detailId?: string;
    /**
     * 工位ID
     */
    locationId: string;
    [property: string]: any;
}

export interface V1LocationStudyExitPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 课程初始化DTO
 */
export interface V1LocationStudyInitPostRequestBody {
    /**
     * 工位ID
     */
    locationId?: string;
    /**
     * 培训项目ID
     */
    projectId?: string;
    /**
     * 训练的用户ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1LocationStudyInitPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyInitPostResponseResult;
    [property: string]: any;
}

/**
 * 项目培训签到初始化响应体
 */
export interface V1LocationStudyInitPostResponseResult {
    /**
     * 动作次数-总
     */
    actionNum?: number;
    /**
     * 动作Ok次数-总
     */
    actionNumOk?: number;
    /**
     * ID
     */
    id?: string;
    /**
     * 作业次数-总
     */
    opNum?: number;
    /**
     * 作业OK次数-总
     */
    opNumOk?: number;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType?: number;
    /**
     * 比赛类型(1、时间模式 2、次数模式)
     */
    raceType?: number;
    /**
     * 项目ID
     */
    recordId?: string;
    /**
     * 要求动作达标率
     */
    requestActionRate?: number;
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 合格率要求
     */
    requestQualificationRate?: number;
    /**
     * 初始化:INIT 进行中:ING 暂停STOP、结束COMPLETE
     */
    status?: string;
    /**
     * 训练时长-min-总
     */
    trainDuration?: number;
    /**
     * 已训次数
     */
    trainFrequency?: number;
    /**
     * 用户ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1LocationStudyOperationLogPagePostRequestBody {
    currentPage?: number;
    data?:        V1LocationStudyOperationLogPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 课程状态改变DTO
 */
export interface V1LocationStudyOperationLogPagePostRequestBodyData {
    detailId?: string;
    /**
     * 工位ID
     */
    locationId: string;
    [property: string]: any;
}

export interface V1LocationStudyOperationLogPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyOperationLogPagePostResponseResult;
    [property: string]: any;
}

export interface V1LocationStudyOperationLogPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     FluffyAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 培训操作日志VO
 */
export interface FluffyAPIModel {
    /**
     * 动作标签集合
     */
    actionLabels?: ActionLabelElement[];
    /**
     * 动作类别： 1-双手持枪，2-垂直作业面，3-拧紧贴合， 4-扳手绿灯
     */
    actionType?: number;
    /**
     * detailId
     */
    detailId?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 日志描述
     */
    logInfo?: string;
    /**
     * nodeId
     */
    nodeId?: string;
    /**
     * 操作时间
     */
    operationTime?: number;
    /**
     * 合格情况： 0-不合格， 1-合格， 2-仅动作不达标
     */
    result?: number;
    [property: string]: any;
}

/**
 * 拧紧业务动作结果VO
 */
export interface ActionLabelElement {
    actionType?: number;
    color?:      number;
    desc?:       string;
    [property: string]: any;
}

/**
 * 课程结束DTO
 */
export interface V1LocationStudyOverPostRequestBody {
    detailId?: string;
    /**
     * 工位ID
     */
    locationId: string;
    [property: string]: any;
}

export interface V1LocationStudyOverPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1LocationStudyRaceProjectIDUserIDGetParam {
    projectId: string;
    userId:    string;
    [property: string]: any;
}

export interface V1LocationStudyRaceProjectIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyCheckProjectIDUserIDGetResponseResult;
    [property: string]: any;
}

/**
 * 课程开始DTO
 */
export interface V1LocationStudyStartPostRequestBody {
    detailId?: string;
    /**
     * 工位ID
     */
    locationId: string;
    [property: string]: any;
}

export interface V1LocationStudyStartPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 课程开暂停DTO
 */
export interface V1LocationStudyStopPostRequestBody {
    detailId?: string;
    /**
     * 工位ID
     */
    locationId: string;
    [property: string]: any;
}

export interface V1LocationStudyStopPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1LocationStudyTrainProjectLocationIDUserIDGetParam {
    locationId: string;
    userId:     string;
    [property: string]: any;
}

export interface V1LocationStudyTrainProjectLocationIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyTrainProjectLocationIDUserIDGetResponseResult[];
    [property: string]: any;
}

/**
 * 训练项目切换响应体
 */
export interface V1LocationStudyTrainProjectLocationIDUserIDGetResponseResult {
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    [property: string]: any;
}

export interface V1LocationStudyTrainRecordProjectIDUserIDGetParam {
    projectId: string;
    userId:    string;
    [property: string]: any;
}

export interface V1LocationStudyTrainRecordProjectIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyCheckProjectIDUserIDGetResponseResult;
    [property: string]: any;
}

/**
 * 课程初始化DTO
 */
export interface V1LocationStudyVideoCreatePostRequestBody {
    /**
     * 工位ID
     */
    locationId?: string;
    /**
     * 培训项目ID
     */
    projectId?: string;
    /**
     * 训练的用户ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1LocationStudyVideoCreatePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 课程初始化DTO
 */
export interface V1LocationStudyVideoStopPostRequestBody {
    /**
     * 工位ID
     */
    locationId?: string;
    /**
     * 培训项目ID
     */
    projectId?: string;
    /**
     * 训练的用户ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1LocationStudyVideoStopPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageCommonActionTypeEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

/**
 * 关联的具体算法编码列表
 */
export interface V1ManageCommonActionTypeEnumGetResponseResult {
    /**
     * 编码
     */
    code?: string;
    /**
     * 描述
     */
    desc?: string;
    [property: string]: any;
}

export interface V1ManageCommonCountAlgorithmEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageCommonJudgeTypeEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageCommonPaperTypeEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

/**
 * 岗位查询DTO
 */
export interface V1ManageCommonPositionPostRequestBodyObject {
    /**
     * 人才系统岗位名称
     */
    mappingName?: string;
    /**
     * 岗位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageCommonPositionPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonPositionPostResponseResult[];
    [property: string]: any;
}

/**
 * 岗位响应DTO
 */
export interface V1ManageCommonPositionPostResponseResult {
    /**
     * 岗位ID
     */
    id?: string;
    /**
     * 人才系统岗位名称
     */
    mappingName?: string;
    /**
     * 岗位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageCommonProjectTypeEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageCommonQuestionTypeEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageCommonTrainStatusEnumGetQuery {
    projectType?: number;
    [property: string]: any;
}

export interface V1ManageCommonTrainStatusEnumGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageCommonUserIDNameGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonActionTypeEnumGetResponseResult[];
    [property: string]: any;
}

/**
 * 用户查询DTO
 */
export interface V1ManageCommonUserListPostRequestBody {
    /**
     * 姓名
     */
    name?: string;
    /**
     * 默认不查 0 查询所有下级 1
     */
    queryChild?: number;
    /**
     * 组织ID
     */
    unitId?: string;
    [property: string]: any;
}

export interface V1ManageCommonUserListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult[];
    [property: string]: any;
}

/**
 * 车型查询DTO
 */
export interface V1ManageCommonVehiclePostRequestBodyObject {
    /**
     * 人才系统车型名称
     */
    mappingName?: string;
    /**
     * 车型名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageCommonVehiclePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageCommonVehiclePostResponseResult[];
    [property: string]: any;
}

/**
 * 车型响应DTO
 */
export interface V1ManageCommonVehiclePostResponseResult {
    /**
     * 车型ID
     */
    id?: string;
    /**
     * 人才系统车型名称
     */
    mappingName?: string;
    /**
     * 车型名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageDictDataDictDataIDDeleteParam {
    dictDataId: string;
    [property: string]: any;
}

export interface V1ManageDictDataDictDataIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageDictDataDictDataIDGetParam {
    dictDataId: string;
    [property: string]: any;
}

export interface V1ManageDictDataDictDataIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageDictDataDictDataIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageDictDataDictDataIDGetResponseResult {
    /**
     * 字典标签
     */
    dictLabel?: string;
    /**
     * 字典类型
     */
    dictType?: string;
    /**
     * 字典值
     */
    dictValue?: string;
    /**
     * 是否可以删除
     */
    enableRemove?: boolean;
    /**
     * 字典ID
     */
    id?: string;
    /**
     * 最近操作人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    [property: string]: any;
}

export interface V1ManageDictDataListPostRequestBody {
    /**
     * 字典标签
     */
    dictLabel?: string;
    /**
     * 字典类型
     */
    dictType?: string;
    /**
     * 字典值
     */
    dictValue?: string;
    [property: string]: any;
}

export interface V1ManageDictDataListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageDictDataDictDataIDGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageDictDataPostRequestBody {
    /**
     * 字典标签
     */
    dictLabel: string;
    /**
     * 字典类型
     */
    dictType: string;
    /**
     * 字典键值
     */
    dictValue: string;
    [property: string]: any;
}

export interface V1ManageDictDataPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageDictDataPutRequestBody {
    /**
     * 字典标签
     */
    dictLabel: string;
    /**
     * 字典ID
     */
    id: string;
    [property: string]: any;
}

export interface V1ManageDictDataPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

/**
 * 试卷确认请求体DTO
 */
export interface V1ManageExamPaperConfirmPostRequestBody {
    /**
     * id
     */
    id: string;
    /**
     * 试题
     */
    questionList: V1ManageExamPaperConfirmPostRequestBodyQuestionList[];
    /**
     * 是否同步至题库 YES/NO
     */
    syncBack: string;
    [property: string]: any;
}

/**
 * 试卷试题新增请求体DTO
 */
export interface V1ManageExamPaperConfirmPostRequestBodyQuestionList {
    /**
     * 答案
     */
    answer?:         string;
    answerAnalysis?: string;
    /**
     * 题库ID
     */
    bankId?: string;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 试题ID
     */
    paperId?: string;
    /**
     * 选项
     */
    questionBankOptionList?: QuestionOptionElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    sort?:         number;
    source?:       string;
    [property: string]: any;
}

/**
 * 试题选项新增请求体DTO
 */
export interface QuestionOptionElement {
    /**
     * 是否正确答案(0:否 1:是)
     */
    correctAnswer?: number;
    /**
     * 选项描述
     */
    optionDesc?: string;
    /**
     * 选项/答案序号
     */
    orderNum?: number;
    /**
     * 备注
     */
    remark?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperConfirmPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 试卷复制请求体DTO
 */
export interface V1ManageExamPaperCopyInitPostRequestBody {
    /**
     * 试卷ID
     */
    paperId: string;
    [property: string]: any;
}

export interface V1ManageExamPaperCopyInitPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperDownloadIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageExamPaperDownloadIDGetQuery {
    includeAnswer?: boolean;
    [property: string]: any;
}

export interface V1ManageExamPaperIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageExamPaperIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageExamPaperIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageExamPaperIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: PaperElement;
    [property: string]: any;
}

/**
 * 试卷信息响应体
 */
export interface PaperElement {
    /**
     * 题库类型ID
     */
    bankTypeId?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * 试卷id
     */
    id?: string;
    /**
     * 限制开始时间
     */
    limitBegTime?: number;
    /**
     * 限制结束时间
     */
    limitEndTime?: number;
    /**
     * 试卷名称
     */
    name?: string;
    /**
     * 要点
     */
    questionPoint?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题型
     */
    questionTypeList?: ResultQuestionTypeList[];
    /**
     * 补考次数
     */
    retryTimes?: number;
    /**
     * 来源
     */
    source?: string;
    /**
     * 达标分数
     */
    standardScore?: number;
    /**
     * 状态
     */
    status?: string;
    /**
     * 试卷标题
     */
    title?: string;
    /**
     * 总分
     */
    totalScore?: number;
    /**
     * 试卷类型
     */
    type?: string;
    [property: string]: any;
}

/**
 * 题目属性字段DTO
 */
export interface QuestionPropertyListElement {
    /**
     * 属性字段key
     */
    columnKey: string;
    /**
     * 属性字段值
     */
    columnValue?: string;
    [property: string]: any;
}

/**
 * 试卷题型配置响应体
 */
export interface ResultQuestionTypeList {
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 数量
     */
    questionNum: number;
    /**
     * 题型
     */
    questionType: string;
    /**
     * 分数
     */
    singleScore: number;
    [property: string]: any;
}

export interface V1ManageExamPaperPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageExamPaperPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 试卷查询DTO
 */
export interface V1ManageExamPaperPagePostRequestBodyData {
    /**
     * 试卷名称
     */
    name?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 试卷状态
     */
    status?: string;
    /**
     * 试卷类型
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamPaperPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageExamPaperPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     PaperElement[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageExamPaperPaperSourceRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1ManageExamPaperPaperSourceRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamPaperPaperSourceRecordIDGetResponseResult[];
    [property: string]: any;
}

/**
 * 试卷来源查询响应体
 */
export interface V1ManageExamPaperPaperSourceRecordIDGetResponseResult {
    /**
     * ID
     */
    id?: string;
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    /**
     * 来源
     */
    source?: string;
    [property: string]: any;
}

/**
 * 试卷修改请求体DTO
 */
export interface V1ManageExamPaperPostRequestBody {
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * id
     */
    id: string;
    /**
     * 试卷名称
     */
    name?: string;
    /**
     * 要点
     */
    questionPoint?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题型
     */
    questionTypeList: V1ManageExamPaperPostRequestBodyQuestionTypeList[];
    /**
     * 达标分数
     */
    standardScore: number;
    /**
     * 试卷标题
     */
    title: string;
    /**
     * 试卷类型 SKILL_INSPECTION 技能检查 SKILL_ASSESSMENT:技能考核
     */
    type: string;
    [property: string]: any;
}

/**
 * 试卷题型配置新增请求体DTO
 */
export interface V1ManageExamPaperPostRequestBodyQuestionTypeList {
    /**
     * 难易程度
     */
    difficultDegree: number;
    paperId?:        string;
    /**
     * 数量
     */
    questionNum: number;
    /**
     * 题型 SINGLE_CHOICE:单选,MULTIPLE_CHOICE:多选,FILL_IN_THE_BLANK:填空,JUDGMENT:判断
     */
    questionType: string;
    /**
     * 分数
     */
    singleScore: number;
    [property: string]: any;
}

export interface V1ManageExamPaperPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 试卷新增请求体DTO
 */
export interface V1ManageExamPaperPutRequestBody {
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * 试卷名称
     */
    name?: string;
    /**
     * 要点
     */
    questionPoint?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题型
     */
    questionTypeList: V1ManageExamPaperPostRequestBodyQuestionTypeList[];
    /**
     * 达标分数
     */
    standardScore: number;
    /**
     * 试卷标题
     */
    title: string;
    /**
     * 试卷类型 SKILL_INSPECTION 技能检查 SKILL_ASSESSMENT:技能考核
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageExamPaperPutResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuestionIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuestionIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageExamPaperQuestionListPaperIDGetParam {
    paperId: string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuestionListPaperIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamPaperQuestionListPaperIDGetResponseResult[];
    [property: string]: any;
}

/**
 * 试卷试题响应体
 */
export interface V1ManageExamPaperQuestionListPaperIDGetResponseResult {
    /**
     * 答案
     */
    answer?: string;
    /**
     * 解析
     */
    answerAnalysis?: string;
    /**
     * 题库ID
     */
    bankId?: string;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 试题ID
     */
    id?: string;
    /**
     * 选项列表
     */
    questionBankOptionList?: QuestionOptionElement[];
    /**
     * 选项
     */
    questionOption?: QuestionOptionElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    /**
     * 顺序
     */
    sort?: number;
    /**
     * 来源
     */
    source?: string;
    [property: string]: any;
}

/**
 * 试卷试题新增请求体DTO
 */
export interface V1ManageExamPaperQuestionPutRequestBody {
    /**
     * 答案
     */
    answer?:         string;
    answerAnalysis?: string;
    /**
     * 题库ID
     */
    bankId?: string;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 试题ID
     */
    paperId?: string;
    /**
     * 选项
     */
    questionBankOptionList?: QuestionOptionElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    sort?:         number;
    source?:       string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuestionPutResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuicklyReleasePostRequestBody {
    /**
     * 考试有效结束时间
     */
    endTime:  number;
    paperDTO: PaperDto;
    /**
     * 补考次数
     */
    retryTimes: number;
    /**
     * 考试有效开始时间
     */
    startTime: number;
    /**
     * 用户集合
     */
    userIds: string[];
    [property: string]: any;
}

/**
 * 试卷新增请求体DTO
 */
export interface PaperDto {
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * 试卷名称
     */
    name?: string;
    /**
     * 要点
     */
    questionPoint?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题型
     */
    questionTypeList: V1ManageExamPaperPostRequestBodyQuestionTypeList[];
    /**
     * 达标分数
     */
    standardScore: number;
    /**
     * 试卷标题
     */
    title: string;
    /**
     * 试卷类型 SKILL_INSPECTION 技能检查 SKILL_ASSESSMENT:技能考核
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageExamPaperQuicklyReleasePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 试卷发布请求体DTO
 */
export interface V1ManageExamPaperReleasePostRequestBody {
    /**
     * 考试有效结束时间
     */
    endTime: number;
    /**
     * id
     */
    id: string;
    /**
     * 补考次数
     */
    retryTimes: number;
    /**
     * 考试有效开始时间
     */
    startTime: number;
    /**
     * 用户集合
     */
    userIds: string[];
    [property: string]: any;
}

export interface V1ManageExamPaperReleasePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageExamPaperSearchQuestionMultiplePostRequestBody {
    bankIds?: string[];
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * 是否重写生成- 1表示查询已存在试题 0表示重写生成试题
     */
    overWrite?: number;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 试题类型集合
     */
    questionTypeList?: QuestionElement[];
    [property: string]: any;
}

/**
 * 试题信息-题型及数量
 */
export interface QuestionElement {
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * 数量
     */
    questionNum: number;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperSearchQuestionMultiplePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamPaperSearchQuestionMultiplePostResponseResult[];
    [property: string]: any;
}

/**
 * 试卷试题响应体
 */
export interface V1ManageExamPaperSearchQuestionMultiplePostResponseResult {
    answerAnalysis?: string;
    /**
     * 题库ID
     */
    bankId?: string;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 试题ID
     */
    id?: string;
    /**
     * 选项
     */
    questionBankOptionList?: QuestionOptionElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    sort?:         number;
    [property: string]: any;
}

export interface V1ManageExamPaperSearchQuestionSinglePostRequestBody {
    bankIds?: string[];
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * 试卷ID
     */
    paperId?:  string;
    question?: QuestionElement;
    /**
     * 关注点 默认使用试卷上
     */
    questionPoint?: string;
    [property: string]: any;
}

export interface V1ManageExamPaperSearchQuestionSinglePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamPaperSearchQuestionMultiplePostResponseResult;
    [property: string]: any;
}

export interface V1ManageExamRecordCompletedStatisticsPaperIDGetParam {
    paperId: string;
    [property: string]: any;
}

export interface V1ManageExamRecordCompletedStatisticsPaperIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamRecordCompletedStatisticsPaperIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageExamRecordCompletedStatisticsPaperIDGetResponseResult {
    /**
     * 平均分
     */
    avg?: number;
    /**
     * 已考试的人数
     */
    completedNum?: number;
    /**
     * 有效期的开始时间
     */
    limitBegTime?: number;
    /**
     * 有效期的结束时间
     */
    limitEndTime?: number;
    /**
     * 考试记录
     */
    recordList?: ExamRecordElement[];
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface ExamRecordElement {
    /**
     * 工号
     */
    account?: string;
    /**
     * 实际开始时间
     */
    begTime?: number;
    /**
     * 时间考试时长
     */
    duration?: number;
    /**
     * 实际结束时间
     */
    endTime?: number;
    /**
     * ID
     */
    id?: string;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 考试得分
     */
    score?: number;
    /**
     * 待考:to_be,考试中(目前不考虑):ing,已完成:completed
     */
    status?: string;
    /**
     * 组织
     */
    unitNames?: string[];
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 用户名
     */
    userName?: string;
    [property: string]: any;
}

export interface V1ManageExamRecordIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageExamRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamRecordIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface V1ManageExamRecordIDGetResponseResult {
    /**
     * 答案
     */
    answerList?: ResultAnswerList[];
    examRecord?: ExamRecordElement;
    paper?:      Paper;
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface ResultAnswerList {
    /**
     * 答案
     */
    answerList?: string[];
    /**
     * id
     */
    id?: string;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 题目id
     */
    questionId?: string;
    /**
     * 考试得分
     */
    score?: number;
    /**
     * 用户id
     */
    userId?: string;
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface Paper {
    paper?: PaperElement;
    /**
     * 试题
     */
    questionList?: V1ManageExamPaperQuestionListPaperIDGetResponseResult[];
    /**
     * 总分
     */
    score?: number;
    [property: string]: any;
}

export interface V1ManageExamRecordPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageExamRecordPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 考试记录查询DTO
 */
export interface V1ManageExamRecordPagePostRequestBodyData {
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 状态
     */
    status?: string;
    /**
     * 试卷类型
     */
    type?: string;
    /**
     * 组织id
     */
    unitId?: string;
    /**
     * 人员ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1ManageExamRecordPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamRecordPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageExamRecordPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 考试列表响应体
 */
export interface V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult {
    /**
     * 实际开始时间
     */
    begTime?: number;
    /**
     * 时间考试时长
     */
    duration?: number;
    /**
     * 实际结束时间
     */
    endTime?: number;
    /**
     * ID
     */
    id?: string;
    /**
     * 限制结束时间
     */
    limitBegTime?: number;
    /**
     * 限制结束时间
     */
    limitEndTime?: number;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 试卷名称
     */
    paperName?: string;
    /**
     * 试卷类型
     */
    paperType?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 考试得分
     */
    score?: number;
    /**
     * 达标分数
     */
    standardScore?: number;
    /**
     * 待考:to_be,考试中(目前不考虑):ing,已完成:completed
     */
    status?: string;
    /**
     * 总分
     */
    totalScore?: number;
    /**
     * 组织
     */
    unitNames?: string[];
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 用户
     */
    userName?: string;
    [property: string]: any;
}

export interface V1ManageJobLevelsJobLevelIDDeleteParam {
    jobLevelId: string;
    [property: string]: any;
}

export interface V1ManageJobLevelsJobLevelIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageJobLevelsJobLevelIDGetParam {
    jobLevelId: string;
    [property: string]: any;
}

export interface V1ManageJobLevelsJobLevelIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageJobLevelsJobLevelIDGetResponseResult;
    [property: string]: any;
}

/**
 * JobLevelDTO
 */
export interface V1ManageJobLevelsJobLevelIDGetResponseResult {
    /**
     * 岗位ID
     */
    id?: string;
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 最近更新人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    /**
     * 岗级
     */
    level?: number;
    /**
     * 岗级的关联项列表
     */
    levelDetails?: ResultLevelDetail[];
    [property: string]: any;
}

/**
 * 岗级的关联项列表
 */
export interface ResultLevelDetail {
    /**
     * 额外配置
     */
    extra?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 关联项ID
     */
    itemId?: string;
    /**
     * 关联项类型
     */
    itemType?: ItemType;
    /**
     * 是否只读
     */
    readOnly?: boolean;
    [property: string]: any;
}

/**
 * 关联项类型
 */
export enum ItemType {
    Exam = "EXAM",
    Learning = "LEARNING",
    Online = "ONLINE",
    Train = "TRAIN",
}

export interface V1ManageJobLevelsMaxLevelJobIDGetParam {
    jobId: string;
    [property: string]: any;
}

export interface V1ManageJobLevelsMaxLevelJobIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageJobLevelsJobLevelIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageJobLevelsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageJobLevelsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * JobLevelQuery
 */
export interface V1ManageJobLevelsPagePostRequestBodyData {
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 岗级
     */
    level?: number;
    [property: string]: any;
}

export interface V1ManageJobLevelsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageJobLevelsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageJobLevelsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageJobLevelsJobLevelIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * JobLevelCreateDTO
 */
export interface V1ManageJobLevelsPostRequestBody {
    /**
     * 岗位ID
     */
    jobId: string;
    /**
     * 岗级
     */
    level: number;
    /**
     * 关联项列表
     */
    levelDetails?: V1ManageJobLevelsPostRequestBodyLevelDetail[];
    [property: string]: any;
}

/**
 * 关联项列表
 */
export interface V1ManageJobLevelsPostRequestBodyLevelDetail {
    /**
     * 额外配置
     */
    extra?: string;
    /**
     * 关联项ID
     */
    itemId: string;
    /**
     * 关联项类型
     */
    itemType: ItemType;
    [property: string]: any;
}

export interface V1ManageJobLevelsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * JobLevelUpdateDTO
 */
export interface V1ManageJobLevelsPutRequestBody {
    /**
     * 岗级ID
     */
    id: string;
    /**
     * 岗位ID
     */
    jobId: string;
    /**
     * 岗级
     */
    level: number;
    /**
     * 关联项列表
     */
    levelDetails?: V1ManageJobLevelsPostRequestBodyLevelDetail[];
    [property: string]: any;
}

export interface V1ManageJobLevelsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageJobsJobIDDeleteParam {
    jobId: string;
    [property: string]: any;
}

export interface V1ManageJobsJobIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageJobsJobIDGetParam {
    jobId: string;
    [property: string]: any;
}

export interface V1ManageJobsJobIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageJobsJobIDGetResponseResult;
    [property: string]: any;
}

/**
 * JobDTO
 */
export interface V1ManageJobsJobIDGetResponseResult {
    /**
     * 岗位ID
     */
    id?: string;
    /**
     * 最近更新人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    /**
     * 岗位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageJobsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageJobsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * JobQuery
 */
export interface V1ManageJobsPagePostRequestBodyData {
    /**
     * 岗位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageJobsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageJobsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageJobsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageJobsJobIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * JobCreateDTO
 */
export interface V1ManageJobsPostRequestBody {
    /**
     * 岗位名称
     */
    name: string;
    [property: string]: any;
}

export interface V1ManageJobsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * JobUpdateDTO
 */
export interface V1ManageJobsPutRequestBody {
    /**
     * id
     */
    id: string;
    /**
     * 岗位名称
     */
    name: string;
    [property: string]: any;
}

export interface V1ManageJobsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartActionDefectPostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartActionDefectPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 公共图表VO
 */
export interface V1ManageLargeScreenChartActionDefectPostResponseResult {
    /**
     * 横坐标x
     */
    abscissa?: string;
    /**
     * 扩展字段1
     */
    ext1?: string;
    /**
     * 扩展字段2
     */
    ext2?: string;
    /**
     * 扩展字段3
     */
    ext3?: string;
    /**
     * 纵坐标y
     */
    ordinate?: string;
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartActionPassRatePostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartActionPassRatePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartJobNumPostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartJobNumPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartPassRatePostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartPassRatePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartTrainNumPostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartTrainNumPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartTrainUserPostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartTrainUserPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenChartUnitUsePostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenChartUnitUsePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLargeScreenChartActionDefectPostResponseResult[];
    [property: string]: any;
}

/**
 * 大屏数据查询DTO
 */
export interface V1ManageLargeScreenProjectPostRequestBody {
    /**
     * 开始时间
     */
    beginTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 项目类型: 1-训练项目， 2-考核项目， 3-比赛项目
     */
    projectType?: number;
    /**
     * 工艺类别
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageLargeScreenProjectPostResponse {
    code?:   string;
    msg?:    string;
    result?: Project[];
    [property: string]: any;
}

export interface V1ManageLearningMaterialsMaterialIDDeleteParam {
    materialId: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsMaterialIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageLearningMaterialsMaterialIDGetParam {
    materialId: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsMaterialIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: MaterialListElement;
    [property: string]: any;
}

/**
 * 学习材料列表
 */
export interface MaterialListElement {
    /**
     * 文件ID
     */
    fileId?: string;
    /**
     * 文件名称
     */
    filename?: string;
    /**
     * 文件大小
     */
    fileSize?: string;
    /**
     * 文件类型
     */
    fileType?: string;
    /**
     * 材料ID
     */
    id?: string;
    /**
     * 最近更新人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageLearningMaterialsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPagePostRequestBodyData {
    /**
     * 文件名称
     */
    filename?: string;
    /**
     * 文件类型
     */
    fileType?: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLearningMaterialsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     MaterialListElement[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPostRequestBody {
    /**
     * 文件ID
     */
    fileId: string;
    /**
     * 文件名称
     */
    filename: string;
    /**
     * 文件类型
     */
    fileType: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPutRequestBody {
    /**
     * 文件ID
     */
    fileId: string;
    /**
     * 文件名称
     */
    filename: string;
    /**
     * 文件类型
     */
    fileType: string;
    /**
     * 材料ID
     */
    id: string;
    [property: string]: any;
}

export interface V1ManageLearningMaterialsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageLearningProjectsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageLearningProjectsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPagePostRequestBodyData {
    /**
     * 学习时长
     */
    duration?: string;
    /**
     * 变更时间-结束
     */
    endTime?: number;
    /**
     * 最后更新人名称, 支持模糊查询
     */
    lastUpdateBy?: string;
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 变更时间-开始
     */
    startTime?: number;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLearningProjectsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageLearningProjectsProjectIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsProjectIDGetResponseResult {
    /**
     * 说明
     */
    description?: string;
    /**
     * 学习时长
     */
    duration?: string;
    /**
     * 学习项目ID
     */
    id?: string;
    /**
     * 最近更新人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    /**
     * 学习材料列表
     */
    materialList?: MaterialListElement[];
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 关联试卷ID
     */
    paperId?: string;
    /**
     * 关联试卷名称
     */
    paperName?: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPostRequestBody {
    /**
     * 说明
     */
    description?: string;
    /**
     * 学习时长
     */
    duration: string;
    /**
     * 材料ID列表
     */
    materialIdList: string[];
    /**
     * 项目名称
     */
    name: string;
    /**
     * 关联试卷ID
     */
    paperId?: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsProjectIDDeleteParam {
    projectId: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsProjectIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageLearningProjectsProjectIDGetParam {
    projectId: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsProjectIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageLearningProjectsProjectIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPutRequestBody {
    /**
     * 说明
     */
    description?: string;
    /**
     * 学习时长
     */
    duration: string;
    /**
     * 学习项目ID
     */
    id: string;
    /**
     * 材料ID列表
     */
    materialIdList: string[];
    /**
     * 项目名称
     */
    name: string;
    /**
     * 关联试卷ID
     */
    paperId?: string;
    [property: string]: any;
}

export interface V1ManageLearningProjectsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManagePositionImportPostRequestBody {
    file: string;
    [property: string]: any;
}

export interface V1ManagePositionImportPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManagePositionPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManagePositionPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 岗位查询DTO
 */
export interface V1ManagePositionPagePostRequestBodyData {
    /**
     * 人才系统岗位名称
     */
    mappingName?: string;
    /**
     * 岗位名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManagePositionPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManagePositionPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManagePositionPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageCommonPositionPostResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageProcessAlgorithmPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPagePostRequestBodyData {
    /**
     * 关联的具体算法编码
     */
    modelCode?: string;
    /**
     * 工艺名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageProcessAlgorithmPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageProcessAlgorithmWithCodeGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmWithCodeGetResponseResult {
    /**
     * 工艺编码
     */
    code?: string;
    /**
     * 配置ID
     */
    id?: string;
    /**
     * 判断图形类型
     */
    judgeType?: string;
    /**
     * 最后更新人
     */
    lastUpdateBy?: string;
    /**
     * 最后更新时间
     */
    lastUpdateTime?: number;
    /**
     * 关联的具体算法编码
     */
    modelCodes?: string;
    /**
     * 关联的具体算法编码列表
     */
    modelList?: V1ManageCommonActionTypeEnumGetResponseResult[];
    /**
     * 工艺名称
     */
    name?: string;
    /**
     * 标准视频ID
     */
    videoId?: string;
    /**
     * 标准视频地址
     */
    videoUrl?: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPostRequestBody {
    /**
     * 工艺编码
     */
    code: string;
    /**
     * 判断图形类型
     */
    judgeType?: string;
    /**
     * 关联的具体算法编码, 使用逗号分隔
     */
    modelCodes?: string;
    /**
     * 工艺名称
     */
    name: string;
    /**
     * 标准视频ID
     */
    videoId: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPutRequestBody {
    /**
     * 工艺编码
     */
    code: string;
    /**
     * 配置ID
     */
    id: string;
    /**
     * 判断图形类型
     */
    judgeType?: string;
    /**
     * 关联的具体算法编码, 使用逗号分隔
     */
    modelCodes?: string;
    /**
     * 工艺名称
     */
    name: string;
    /**
     * 标准视频ID
     */
    videoId: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmPutResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmWithCodeGetQuery {
    code?: string;
    [property: string]: any;
}

export interface V1ManageProcessAlgorithmWithCodeGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageProcessAlgorithmWithCodeGetResponseResult;
    [property: string]: any;
}

export interface V1ManagePromptsBankTypeIDGetParam {
    bankTypeId: string;
    [property: string]: any;
}

export interface V1ManagePromptsBankTypeIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManagePromptsBankTypeIDGetResponseResult[];
    [property: string]: any;
}

/**
 * PromptGroupDTO
 */
export interface V1ManagePromptsBankTypeIDGetResponseResult {
    /**
     * 题库类型ID
     */
    bankTypeId?: string;
    /**
     * 提示词组内容
     */
    content?: string;
    /**
     * 文件类型
     */
    fileType?: string;
    /**
     * 提示词组ID
     */
    id?: string;
    /**
     * 提示词组名称
     */
    name?: string;
    [property: string]: any;
}

/**
 * PromptGroupSaveDTO
 */
export interface V1ManagePromptsBatchPostRequestBodyElement {
    /**
     * 题库类型ID
     */
    bankTypeId: string;
    /**
     * 提示词组内容
     */
    content: string;
    /**
     * 文件类型
     */
    fileType: string;
    /**
     * 提示词组名称
     */
    name: string;
    [property: string]: any;
}

export interface V1ManagePromptsBatchPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchCompleteQuestionBankPostRequestBody {
    data?: string[];
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchCompleteQuestionBankPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchInsertQuestionBankPostRequestBody {
    data?: QuestionBankSaveDTOListElement[];
    [property: string]: any;
}

/**
 * 题库列表新增请求体DTO
 */
export interface QuestionBankSaveDTOListElement {
    /**
     * 答案解析
     */
    answerAnalysis?: string;
    /**
     * 所属题库类型
     */
    bankTypeId: string;
    /**
     * 正确答案列表
     */
    correctAnswerList: string[];
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * 是否已生成完(0:未生成完成,1:已生成完成)
     */
    generated?: number;
    /**
     * 答案列表
     */
    questionBankOptionList: DatumQuestionBankOptionList[];
    /**
     * 题目属性列表
     */
    questionPropertyList: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType: string;
    /**
     * 来源
     */
    source: string;
    /**
     * 工步或者wes信息ID
     */
    wesId?: string;
    [property: string]: any;
}

/**
 * 题库答案列表新增请求体DTO
 */
export interface DatumQuestionBankOptionList {
    /**
     * 选项描述
     */
    optionDesc: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchInsertQuestionBankPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 批量保存Ai根据文件生成的题目DTO
 */
export interface V1ManageQuestionBankBatchSaveQuestionBankFromAIGenerateByFilePostRequestBody {
    /**
     * 文件id
     */
    fileId?: string;
    /**
     * 题目列表
     */
    questionBankSaveDTOList?: QuestionBankSaveDTOListElement[];
    /**
     * 上传Id
     */
    uploadId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchSaveQuestionBankFromAIGenerateByFilePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBody {
    data?: V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBodyDatum[];
    [property: string]: any;
}

/**
 * 题库列表更新请求体DTO
 */
export interface V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBodyDatum {
    /**
     * 答案解析
     */
    answerAnalysis?: string;
    /**
     * 所属题库类型
     */
    bankTypeId: string;
    /**
     * 正确答案列表
     */
    correctAnswerList?: string[];
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * id
     */
    id?: string;
    /**
     * 答案列表
     */
    questionBankOptionList?: DatumQuestionBankOptionList[];
    /**
     * 题目属性列表
     */
    questionPropertyList: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 工步或者wes信息ID
     */
    wesId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankBatchUpdateQuestionBankPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnDeleteByIDGetQuery {
    columnId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnDeleteByIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnGetByColumnIDAndKeyGetQuery {
    columnId?:  string;
    columnKey?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnGetByColumnIDAndKeyGetResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankColumnPropertyListElement[];
    [property: string]: any;
}

/**
 * 题库字段属性配置DTO
 */
export interface QuestionBankColumnPropertyListElement {
    /**
     * 字段id
     */
    columnId?: string;
    /**
     * 字段属性id
     */
    id?: string;
    /**
     * 字段属性key
     */
    propertyKey?: string;
    /**
     * 字段属性名
     */
    propertyName?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnGetQuestionBankColumnByIDGetQuery {
    columnId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnGetQuestionBankColumnByIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankColumnDTOElement;
    [property: string]: any;
}

/**
 * 题库字段配置DTO
 */
export interface QuestionBankColumnDTOElement {
    /**
     * 系统字段key
     */
    columnKey?: string;
    /**
     * 系统字段名称
     */
    columnName?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * id
     */
    id?: string;
    /**
     * 更新人
     */
    lastUpdateBy?: string;
    /**
     * 更新时间
     */
    lastUpdateTime?: number;
    /**
     * 三方字段属性列表
     */
    questionBankColumnPropertyList?: QuestionBankColumnPropertyListElement[];
    [property: string]: any;
}

/**
 * 题库字段配置查询DTO
 */
export interface V1ManageQuestionBankColumnGetQuestionBankColumnListPostRequestBody {
    /**
     * 系统字段key
     */
    columnKey?: string;
    /**
     * 系统字段名称
     */
    columnName?: string;
    /**
     * id
     */
    id?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnGetQuestionBankColumnListPostResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankColumnDTOElement[];
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingExportGetQuery {
    queryDTO?: QueryDto;
    [property: string]: any;
}

/**
 * 题库字段值查询DTO
 */
export interface QueryDto {
    /**
     * 题库字段Id
     */
    columnId?: string;
    /**
     * 系统字段key
     */
    columnKey?: string;
    /**
     * 本系统字段值
     */
    columnValue?: string;
    /**
     * 第三方属性值
     */
    propertyValue?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingExportTemplateGetQuery {
    columnId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetQuery {
    columnId?:    string;
    columnKey?:   string;
    propertyKey?: string;
    value?:       string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponseResult[];
    [property: string]: any;
}

/**
 * 题库字段值查询DTO
 */
export interface V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponseResult {
    /**
     * 题库字段Id
     */
    columnId?:    string;
    columnKey?:   string;
    columnValue?: string;
    /**
     * 主键
     */
    id?: string;
    /**
     * 第三方属性key
     */
    propertyKey?: string;
    /**
     * 第三方属性值
     */
    propertyValue?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingGetByColumnIDAndValueGetQuery {
    columnId?:  string;
    columnKey?: string;
    value?:     string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingGetByColumnIDAndValueGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingImportColumnIDPostParam {
    columnId: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingImportColumnIDPostQuery {
    file?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingImportColumnIDPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 题库字段值查询DTO
 */
export interface V1ManageQuestionBankColumnMappingListPostRequestBody {
    /**
     * 题库字段Id
     */
    columnId?: string;
    /**
     * 系统字段key
     */
    columnKey?: string;
    /**
     * 本系统字段值
     */
    columnValue?: string;
    /**
     * 第三方属性值
     */
    propertyValue?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnMappingListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankColumnMappingGetByColumnIDAndValueAndPropertyKeyGetResponseResult[];
    [property: string]: any;
}

/**
 * 题库字段配置新增/更新DTO
 */
export interface V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody {
    /**
     * 系统字段key
     */
    columnKey: string;
    /**
     * 系统字段名称
     */
    columnName: string;
    /**
     * id-新增不需要,更新需要
     */
    id?: string;
    /**
     * 三方字段属性列表
     */
    questionBankColumnPropertySaveDTO: QuestionBankColumnPropertySaveDTOElement[];
    [property: string]: any;
}

/**
 * 题库字段属性配置新增/更新DTO
 */
export interface QuestionBankColumnPropertySaveDTOElement {
    /**
     * 字段属性key
     */
    propertyKey: string;
    /**
     * 字段属性名
     */
    propertyName: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankDeleteQuestionBankGetQuery {
    id?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankDeleteQuestionBankGetResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 题库-AI试题生成配置请求体DTO
 */
export interface V1ManageQuestionBankGenerateQuestionByFilePostRequestBody {
    /**
     * 题库类型
     */
    bankTypeId?: string;
    /**
     * 文件id
     */
    fileId?: string;
    /**
     * 文件地址
     */
    fileUrl?: string;
    /**
     * 配置列表
     */
    questionBankGenerateConfigList?: QuestionBankGenerateConfigListElement[];
    /**
     * 题库类型字段映射列表
     */
    questionBankTypeColumnMappingList?: QuestionBankTypeColumnMappingListElement[];
    /**
     * 接收生成试题错误信息地址
     */
    receiveAiGenerateErrorUrl?: string;
    /**
     * 接收试题结果地址
     */
    receiveAiQuestionBankResultUrl?: string;
    /**
     * 上传Id
     */
    uploadId?: string;
    [property: string]: any;
}

/**
 * 题库-AI试题生成配置-配置列表DTO
 */
export interface QuestionBankGenerateConfigListElement {
    /**
     * 提示词内容
     */
    promptContent?: string;
    /**
     * 提示词id
     */
    promptId?: string;
    /**
     * 提示词名称
     */
    promptName?: string;
    /**
     * 生成要点
     */
    questionPoint?: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string[];
    /**
     * 参考案例
     */
    referenceCase?: ReferenceCaseElement[];
    /**
     * 题干内容(需要根据哪些字段出题)
     */
    stemContentList?: string[];
    /**
     * 题干生成考点数量
     */
    stemPointNum?: number;
    [property: string]: any;
}

/**
 * 题库参考案例DTO
 */
export interface ReferenceCaseElement {
    /**
     * 正确答案列表
     */
    correctAnswerList: string[];
    /**
     * 答案列表
     */
    questionBankOptionList: DatumQuestionBankOptionList[];
    /**
     * 题干
     */
    questionStem: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    [property: string]: any;
}

/**
 * 题库-AI试题生成配置题库类型字段映射DTO
 */
export interface QuestionBankTypeColumnMappingListElement {
    /**
     * 题库字段key
     */
    questionBankColumnKey?: string;
    /**
     * 题库字段name
     */
    questionBankColumnName?: string;
    /**
     * 题库字段属性key(第三方字段key)
     */
    questionBankColumnPropertyKey?: string;
    /**
     * 题库字段属性名(第三方字段名)
     */
    questionBankColumnPropertyName?: string;
    /**
     * 题库字段属性值
     */
    questionBankColumnPropertyValue?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankGenerateQuestionByFilePostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * 题库-生成试卷题目请求体DTO
 */
export interface V1ManageQuestionBankGenerateQuestionPostRequestBody {
    /**
     * 题库类型Id
     */
    bankTypeId?: string;
    /**
     * 已生成需要排除的题库ID
     */
    excludeQuestionBankIdList?: string[];
    /**
     * 试题信息-题型及数量
     */
    questionInfoList?: QuestionInfoListElement[];
    /**
     * 试题要点
     */
    questionPoint?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 试卷类型-技能检查:SKILL_INSPECTION,技能考核:SKILL_ASSESSMENT
     */
    type?: string;
    [property: string]: any;
}

/**
 * 试题信息-题型及数量
 */
export interface QuestionInfoListElement {
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * 数量
     */
    questionNum: number;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankGenerateQuestionPostResponse {
    code?:   string;
    msg?:    string;
    result?: string[];
    [property: string]: any;
}

export interface V1ManageQuestionBankGetInfoGetQuery {
    bankId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankGetInfoGetResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankDetailListElement;
    [property: string]: any;
}

/**
 * 题库题目响应DTO
 */
export interface QuestionBankDetailListElement {
    /**
     * 答案解析
     */
    answerAnalysis?: string;
    /**
     * 所属题库类型ID
     */
    bankTypeId?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 最后更新时间
     */
    createTime?: number;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 题库题目ID
     */
    id?: string;
    /**
     * 最后更新人
     */
    lastUpdateBy?: string;
    /**
     * 最后更新时间
     */
    lastUpdateTime?: number;
    /**
     * 答案/选项列表
     */
    questionBankOptionList?: RecordQuestionBankOptionList[];
    /**
     * 题目属性
     */
    questionProperty?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型
     */
    questionType?: string;
    /**
     * 题型名称
     */
    questionTypeName?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 状态(0:待确认 1:已确认)
     */
    status?: number;
    /**
     * wesId
     */
    wesId?: string;
    [property: string]: any;
}

/**
 * 题库题目选项/答案响应DTO
 */
export interface RecordQuestionBankOptionList {
    /**
     * 是否正确答案(0:否 1:是)
     */
    correctAnswer?: number;
    /**
     * 选项/答案ID
     */
    id?: string;
    /**
     * 选项/答案
     */
    optionDesc?: string;
    /**
     * 选项/答案序号
     */
    orderNum?: number;
    /**
     * 题库题目ID
     */
    questionBankId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankGetQuestionBankPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageQuestionBankGetQuestionBankPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 题库列表查询请求体DTO
 */
export interface V1ManageQuestionBankGetQuestionBankPagePostRequestBodyData {
    /**
     * 所属题库类型ID
     */
    bankTypeId: string;
    /**
     * 难易程度
     */
    difficultDegree?: number;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    /**
     * 状态-待确认:0,已确认:1
     */
    status?: number;
    [property: string]: any;
}

export interface V1ManageQuestionBankGetQuestionBankPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankGetQuestionBankPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageQuestionBankGetQuestionBankPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     QuestionBankDetailListElement[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 题库-Excel/Word导入试题入库请求体DTO
 */
export interface V1ManageQuestionBankImportQuestionBatchInsertPostRequestBody {
    /**
     * 题库类型
     */
    bankTypeId?: string;
    /**
     * 文件Id
     */
    fileId?: string;
    /**
     * 入库试题信息
     */
    questionBankList?: QuestionBankSaveDTOListElement[];
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    [property: string]: any;
}

export interface V1ManageQuestionBankImportQuestionBatchInsertPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 题库-Excel导入试题请求体DT
 */
export interface V1ManageQuestionBankImportQuestionByExcelPostRequestBody {
    /**
     * 题库类型
     */
    bankTypeId?: string;
    /**
     * 文件Id
     */
    fileId?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    [property: string]: any;
}

export interface V1ManageQuestionBankImportQuestionByExcelPostResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankDetailListElement[];
    [property: string]: any;
}

/**
 * 题库-Excel导入试题请求体DT
 */
export interface V1ManageQuestionBankImportQuestionByWordPostRequestBody {
    /**
     * 题库类型
     */
    bankTypeId?: string;
    /**
     * 文件Id
     */
    fileId?: string;
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    [property: string]: any;
}

export interface V1ManageQuestionBankImportQuestionByWordPostResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankDetailListElement[];
    [property: string]: any;
}

/**
 * 题库列表新增请求体DTO
 */
export interface V1ManageQuestionBankInsertQuestionBankPostRequestBody {
    /**
     * 答案解析
     */
    answerAnalysis?: string;
    /**
     * 所属题库类型
     */
    bankTypeId: string;
    /**
     * 正确答案列表
     */
    correctAnswerList: string[];
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * 是否已生成完(0:未生成完成,1:已生成完成)
     */
    generated?: number;
    /**
     * 答案列表
     */
    questionBankOptionList: DatumQuestionBankOptionList[];
    /**
     * 题目属性列表
     */
    questionPropertyList: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType: string;
    /**
     * 来源
     */
    source: string;
    /**
     * 工步或者wes信息ID
     */
    wesId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankInsertQuestionBankPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 题库-AI试题生成错误信息请求体DTO
 */
export interface V1ManageQuestionBankReceiveAIGenerateErrorPostRequestBody {
    /**
     * 错误信息
     */
    errorMsg: string;
    /**
     * 上传Id
     */
    uploadId: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankReceiveAIGenerateErrorPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeDeleteByIDGetQuery {
    typeId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeDeleteByIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeGetQuestionBankTypeByIDGetQuery {
    typeId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeGetQuestionBankTypeByIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankTypeInfoElement;
    [property: string]: any;
}

/**
 * 题库类型DTO
 */
export interface QuestionBankTypeInfoElement {
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * id
     */
    id?: string;
    /**
     * 更新人
     */
    lastUpdateBy?: string;
    /**
     * 更新时间
     */
    lastUpdateTime?: number;
    /**
     * 题库类型名称名称
     */
    name?: string;
    /**
     * 题库类型字段列表
     */
    questionBankTypeColumns?: QuestionBankTypeColumnElement[];
    [property: string]: any;
}

/**
 * 题库类型字段DTO
 */
export interface QuestionBankTypeColumnElement {
    /**
     * 题库字段id
     */
    columnId?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * 更新人
     */
    lastUpdateBy?: string;
    /**
     * 更新时间
     */
    lastUpdateTime?:        number;
    questionBankColumnDTO?: QuestionBankColumnDTOElement;
    /**
     * 题库类型Id
     */
    typeId?: string;
    [property: string]: any;
}

/**
 * 题库类型查询DTO
 */
export interface V1ManageQuestionBankTypeGetQuestionBankTypeListPostRequestBody {
    /**
     * id
     */
    id?: string;
    /**
     * 字段名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeGetQuestionBankTypeListPostResponse {
    code?:   string;
    msg?:    string;
    result?: QuestionBankTypeInfoElement[];
    [property: string]: any;
}

/**
 * 题库类型新增/更新DTO
 */
export interface V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostRequestBody {
    /**
     * id-新增不需要,更新需要
     */
    id?: string;
    /**
     * 题库类型名称名称
     */
    name: string;
    /**
     * 题库类型字段列表
     */
    questionBankTypeColumnSaveDTO: QuestionBankTypeColumnSaveDTOElement[];
    [property: string]: any;
}

/**
 * 题库类型字段新增/更新DTO
 */
export interface QuestionBankTypeColumnSaveDTOElement {
    /**
     * 题库字段id
     */
    columnId: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * 题库列表更新请求体DTO
 */
export interface V1ManageQuestionBankUpdateQuestionBankPostRequestBody {
    /**
     * 答案解析
     */
    answerAnalysis?: string;
    /**
     * 所属题库类型
     */
    bankTypeId: string;
    /**
     * 正确答案列表
     */
    correctAnswerList?: string[];
    /**
     * 难易程度
     */
    difficultDegree: number;
    /**
     * id
     */
    id?: string;
    /**
     * 答案列表
     */
    questionBankOptionList?: DatumQuestionBankOptionList[];
    /**
     * 题目属性列表
     */
    questionPropertyList: QuestionPropertyListElement[];
    /**
     * 题干
     */
    questionStem?: string;
    /**
     * 题型-单选:SINGLE_CHOICE,多选:MULTIPLE_CHOICE,填空:FILL_IN_THE_BLANK,判断:JUDGMENT
     */
    questionType?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 工步或者wes信息ID
     */
    wesId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankUpdateQuestionBankPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetQuery {
    uploadId?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult;
    [property: string]: any;
}

/**
 * 上传管理DTO
 */
export interface V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult {
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: number;
    /**
     * 文件Id
     */
    fileId?:   string;
    fileInfo?: FileInfo;
    /**
     * 主键
     */
    id?: string;
    /**
     * 更新人
     */
    lastUpdateBy?: string;
    /**
     * 更新时间
     */
    lastUpdateTime?: number;
    /**
     * 生成的题目详情
     */
    questionBankDetailList?: QuestionBankDetailListElement[];
    /**
     * 题库生成信息
     */
    questionBankInfo?: string;
    /**
     * 题库类型Id
     */
    questionBankTypeId?:   string;
    questionBankTypeInfo?: QuestionBankTypeInfoElement;
    /**
     * 备注信息
     */
    remark?: string;
    /**
     * 上传生成状态(GENERATING:生成中,GENERATING:已完成,GENERATE_FAIL:生成失败)
     */
    uploadStatus?: string;
    /**
     * 上传类型(FILE_IMPORT:考题导入、AI_GENERATE:AI生成上传)
     */
    uploadType?: string;
    [property: string]: any;
}

/**
 * 文件信息
 */
export interface FileInfo {
    businessId?: string;
    fileName?:   string;
    fileSize?:   string;
    id?:         string;
    path?:       string;
    url?:        string;
    urlInner?:   string;
    [property: string]: any;
}

/**
 * 上传管理查询DTO
 */
export interface V1ManageQuestionBankUploadGetQuestionBankUploadListPostRequestBody {
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 上传文件名称
     */
    fileName?: string;
    /**
     * 主键
     */
    id?: string;
    /**
     * 题库类型Id
     */
    questionBankTypeId?: string;
    /**
     * 开始时间
     */
    startTime?: number;
    /**
     * 上传生成状态(GENERATING:生成中,GENERATING:已完成)
     */
    uploadStatus?: string;
    /**
     * 上传类型(FILE_IMPORT:考题导入、AI_GENERATE:AI生成上传)
     */
    uploadType?: string;
    [property: string]: any;
}

export interface V1ManageQuestionBankUploadGetQuestionBankUploadListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIDGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageQuestionUserIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageQuestionUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult;
    [property: string]: any;
}

export interface V1ManageQuestionUserPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageQuestionUserPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 用户查询DTO
 */
export interface V1ManageQuestionUserPagePostRequestBodyData {
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 项目集合ID
     */
    projectIds?: string[];
    [property: string]: any;
}

export interface V1ManageQuestionUserPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionUserPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageQuestionUserPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1LocationHomeCurrentUserGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 用户修改状态请求体DTO
 */
export interface V1ManageQuestionUserStatusPostRequestBody {
    /**
     * 姓名
     */
    id: string;
    /**
     * 状态 启用ENABLE/停用DISABLE
     */
    status: string;
    [property: string]: any;
}

export interface V1ManageQuestionUserStatusPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSceneCluesClueIDDeleteParam {
    clueId: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesClueIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageSceneCluesClueIDGetParam {
    clueId: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesClueIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneCluesClueIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageSceneCluesClueIDGetResponseResult {
    /**
     * 附件列表
     */
    attachmentList?: AttachmentListElement[];
    /**
     * 线索内容
     */
    clueContent?: string;
    /**
     * 场景线索ID
     */
    id?: string;
    /**
     * 数模的模块名称
     */
    moduleName?: string;
    /**
     * 场景问题ID
     */
    questionId?: string;
    /**
     * 场景ID
     */
    sceneId?: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesListPostRequestBody {
    /**
     * 线索内容
     */
    clueContent?: string;
    /**
     * 数模的模块名称
     */
    moduleName?: string;
    /**
     * 场景问题ID, 全局线索则为0
     */
    questionId?: string;
    /**
     * 场景ID
     */
    sceneId?: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneCluesClueIDGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageSceneCluesPostRequestBody {
    /**
     * 线索附件ID列表
     */
    attachmentIdList?: string[];
    /**
     * 线索内容
     */
    clueContent?: string;
    /**
     * 数模的模块名称
     */
    moduleName: string;
    /**
     * 场景问题ID, 全局线索则为0
     */
    questionId: string;
    /**
     * 场景ID
     */
    sceneId: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesPutRequestBody {
    /**
     * 线索附件ID列表
     */
    attachmentIdList?: string[];
    /**
     * 线索内容
     */
    clueContent?: string;
    /**
     * 线索ID
     */
    id: string;
    /**
     * 数模的模块名称
     */
    moduleName: string;
    /**
     * 场景问题ID, 全局线索则为0
     */
    questionId: string;
    /**
     * 场景ID
     */
    sceneId: string;
    [property: string]: any;
}

export interface V1ManageSceneCluesPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageSceneExamPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageSceneExamPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageSceneExamPagePostRequestBodyData {
    /**
     * 答题结束时间
     */
    endTime?: number;
    /**
     * 是否合格
     */
    qualified?: boolean;
    /**
     * 场景问题ID
     */
    questionId?: string;
    /**
     * 场景问题名称
     */
    questionName?: string;
    /**
     * 场景ID
     */
    sceneId?: string;
    /**
     * 场景名称
     */
    sceneName?: string;
    /**
     * 答题开始时间
     */
    startTime?: number;
    /**
     * 问题类型
     */
    type?: string;
    /**
     * 用户ID
     */
    userId?: string;
    /**
     * 用户名称
     */
    userName?: string;
    [property: string]: any;
}

export interface V1ManageSceneExamPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneExamPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageSceneExamPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     TentacledAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface TentacledAPIModel {
    /**
     * 答题时间
     */
    answeredAt?: number;
    /**
     * 答题人
     */
    answeredBy?: string;
    /**
     * 问题作答记录ID
     */
    id?: string;
    /**
     * 是否合格
     */
    qualified?: boolean;
    /**
     * 场景问题名称
     */
    questionName?: string;
    /**
     * 场景名称
     */
    sceneName?: string;
    /**
     * 分数
     */
    score?: number;
    /**
     * 问题类型
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageSceneExamSubmitPostRequestBody {
    /**
     * 线索点击列表
     */
    clickRecordList?: ClickRecordListElement[];
    /**
     * 整改措施
     */
    corrective: string;
    /**
     * 预防策略
     */
    prevention: string;
    /**
     * 场景问题ID
     */
    questionId: string;
    /**
     * 根本原因
     */
    reason: string;
    [property: string]: any;
}

/**
 * 线索点击列表
 */
export interface ClickRecordListElement {
    /**
     * 线索点击时间
     */
    clickedAt: number;
    /**
     * 场景线索
     */
    clueId?: string;
    /**
     * 数模的模块名称
     */
    moduleName: string;
    /**
     * 线索点击顺序
     */
    orderNum: string;
    [property: string]: any;
}

export interface V1ManageSceneExamSubmitPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneExamSubmitPostResponseResult;
    [property: string]: any;
}

export interface V1ManageSceneExamSubmitPostResponseResult {
    /**
     * 回答分析
     */
    analysis?: string;
    /**
     * 整改措施
     */
    corrective?: string;
    /**
     * 答题记录ID
     */
    id?: string;
    /**
     * 预防策略
     */
    prevention?: string;
    /**
     * 是否合格
     */
    qualified?: boolean;
    /**
     * 场景问题ID
     */
    questionId?: string;
    /**
     * 根本原因
     */
    reason?: string;
    /**
     * 得分
     */
    score?: number;
    /**
     * 答题人ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsListPostRequestBody {
    /**
     * 问题名称
     */
    name?: string;
    /**
     * 问题说明
     */
    remark?: string;
    /**
     * 场景ID
     */
    sceneId?: string;
    /**
     * 问题类型
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneQuestionsListPostResponseResult[];
    [property: string]: any;
}

export interface V1ManageSceneQuestionsListPostResponseResult {
    /**
     * 线索数量
     */
    clueNum?: string;
    /**
     * 整改措施
     */
    corrective?: string;
    /**
     * 场景题目ID
     */
    id?: string;
    /**
     * 题目名称
     */
    name?: string;
    /**
     * 预防策略
     */
    prevention?: string;
    /**
     * 根本原因
     */
    reason?: string;
    /**
     * 参考文件列表
     */
    referenceList?: AttachmentListElement[];
    /**
     * 问题说明
     */
    remark?: string;
    /**
     * 场景ID
     */
    sceneId?: string;
    /**
     * 问题类型
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsPostRequestBody {
    /**
     * 整改措施
     */
    corrective: string;
    /**
     * 问题名称
     */
    name: string;
    /**
     * 预防策略
     */
    prevention: string;
    /**
     * 根本原因
     */
    reason: string;
    /**
     * 参考文件ID列表
     */
    referenceIdList?: string[];
    /**
     * 问题说明
     */
    remark?: string;
    /**
     * 场景ID
     */
    sceneId: string;
    /**
     * 问题类型
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsPutRequestBody {
    /**
     * 整改措施
     */
    corrective: string;
    /**
     * 问题ID
     */
    id: string;
    /**
     * 问题名称
     */
    name: string;
    /**
     * 预防策略
     */
    prevention: string;
    /**
     * 根本原因
     */
    reason: string;
    /**
     * 参考文件ID列表
     */
    referenceIdList?: string[];
    /**
     * 问题说明
     */
    remark?: string;
    /**
     * 问题类型
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageSceneQuestionsQuestionIDDeleteParam {
    questionId: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsQuestionIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageSceneQuestionsQuestionIDGetParam {
    questionId: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsQuestionIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneQuestionsListPostResponseResult;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsSceneIDRandomGetParam {
    sceneId: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsSceneIDRandomGetQuery {
    questionId?: string;
    [property: string]: any;
}

export interface V1ManageSceneQuestionsSceneIDRandomGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSceneQuestionsListPostResponseResult;
    [property: string]: any;
}

export interface V1ManageScenesCurrentUserListGetQuery {
    name?: string;
    [property: string]: any;
}

export interface V1ManageScenesCurrentUserListGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageScenesCurrentUserListGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageScenesCurrentUserListGetResponseResult {
    /**
     * 数模文件ID
     */
    fileId?: string;
    /**
     * 场景ID
     */
    id?: string;
    /**
     * 编辑人
     */
    lastUpdateBy?: string;
    /**
     * 编辑时间
     */
    lastUpdateTime?: number;
    /**
     * 场景名称
     */
    name?: string;
    /**
     * 场景说明
     */
    remark?: string;
    /**
     * 授权人员ID列表
     */
    userIds?: string[];
    /**
     * 授权人员名称列表
     */
    userNames?: string[];
    [property: string]: any;
}

export interface V1ManageScenesPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageScenesPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageScenesPagePostRequestBodyData {
    /**
     * 场景名称
     */
    name?: string;
    /**
     * 场景说明
     */
    remark?: string;
    [property: string]: any;
}

export interface V1ManageScenesPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageScenesPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageScenesPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageScenesCurrentUserListGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageScenesPostRequestBody {
    /**
     * 场景名称
     */
    name: string;
    /**
     * 场景说明
     */
    remark?: string;
    /**
     * 授权人员ID列表
     */
    userIds: string[];
    [property: string]: any;
}

export interface V1ManageScenesPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageScenesPutRequestBody {
    /**
     * 数模文件ID
     */
    fileId?: string;
    /**
     * 场景ID
     */
    id: string;
    /**
     * 场景名称
     */
    name: string;
    /**
     * 场景说明
     */
    remark?: string;
    /**
     * 授权人员ID列表
     */
    userIds: string[];
    [property: string]: any;
}

export interface V1ManageScenesPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageScenesSceneIDDeleteParam {
    sceneId: string;
    [property: string]: any;
}

export interface V1ManageScenesSceneIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageScenesSceneIDGetParam {
    sceneId: string;
    [property: string]: any;
}

export interface V1ManageScenesSceneIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageScenesCurrentUserListGetResponseResult;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectPostRequestBody {
    currentPage?: number;
    data?:        V1ManageStatisticalReportProjectPostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 数据报表查询DTO
 */
export interface V1ManageStatisticalReportProjectPostRequestBodyData {
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目ID列表
     */
    projectIdList?: string[];
    /**
     * 项目名称
     */
    projectName?: string;
    /**
     * 开始时间
     */
    startTime?: number;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageStatisticalReportProjectPostResponseResult;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectPostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     StickyAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 项目统计报表VO
 */
export interface StickyAPIModel {
    /**
     * 考试通过人数
     */
    checkedNum?: string;
    /**
     * 已完成训练人数
     */
    completedNum?: string;
    /**
     * 配置训练人数
     */
    configNum?: string;
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * 所属工艺
     */
    countAlgorithmShow?: string;
    /**
     * 累计作业次数
     */
    jobNum?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目名称
     */
    projectName?: string;
    /**
     * 累计作业场次
     */
    trainNum?: string;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectUserPostRequestBody {
    currentPage?: number;
    data?:        V1ManageStatisticalReportProjectPostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectUserPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageStatisticalReportProjectUserPostResponseResult;
    [property: string]: any;
}

export interface V1ManageStatisticalReportProjectUserPostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     IndigoAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 项目人员统计VO
 */
export interface IndigoAPIModel {
    /**
     * 考试通过人数
     */
    checkedNum?: string;
    /**
     * 已完成训练人数
     */
    completedNum?: string;
    /**
     * 配置训练人数
     */
    configNum?: string;
    /**
     * 训练中人数
     */
    ingNum?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目名称
     */
    projectName?: string;
    [property: string]: any;
}

export interface V1ManageStatisticalReportTrainNumPostRequestBody {
    currentPage?: number;
    data?:        V1ManageStatisticalReportProjectPostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageStatisticalReportTrainNumPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageStatisticalReportTrainNumPostResponseResult;
    [property: string]: any;
}

export interface V1ManageStatisticalReportTrainNumPostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     IndecentAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 训练次数统计VO
 */
export interface IndecentAPIModel {
    /**
     * 不达标次数
     */
    actionNokNum?: string;
    /**
     * 动作次数
     */
    actionNum?: string;
    /**
     * 动作达标率
     */
    actionPassRate?: string;
    /**
     * 作业不合格次数
     */
    nokNum?: string;
    /**
     * 作业次数
     */
    opNum?: string;
    /**
     * 作业合格率
     */
    passRate?: string;
    /**
     * 项目ID
     */
    projectId?: string;
    /**
     * 项目名称
     */
    projectName?: string;
    /**
     * 训练场次
     */
    trainNum?: string;
    [property: string]: any;
}

export interface V1ManageSysConfigListGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSysConfigListGetResponseResult[];
    [property: string]: any;
}

/**
 * 系统配置查询响应体
 */
export interface V1ManageSysConfigListGetResponseResult {
    /**
     * 配置项KEY
     */
    configKey?: string;
    /**
     * 配置项名称
     */
    configName?: string;
    /**
     * 配置单位
     */
    configUnit?: string;
    /**
     * 配置值
     */
    configValue?: string;
    /**
     * 配置ID
     */
    id?: string;
    /**
     * 最近操作人
     */
    lastUpdateBy?: string;
    /**
     * 最近更新时间
     */
    lastUpdateTime?: number;
    [property: string]: any;
}

/**
 * 系统配置修改请求体DTO
 */
export interface V1ManageSysConfigPutRequestBody {
    /**
     * 配置值
     */
    configValue: string;
    /**
     * 配置ID
     */
    id: string;
    [property: string]: any;
}

export interface V1ManageSysConfigPutResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSysOrgDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysOrgDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSysOrgIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysOrgIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: OrgListElement;
    [property: string]: any;
}

/**
 * 组织修改请求体DTO
 */
export interface V1ManageSysOrgPostRequestBody {
    /**
     * 编码
     */
    code?: string;
    /**
     * 项目id
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父ID,顶级节点父ID -1
     */
    parentId?: string;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageSysOrgPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 新增请求体DTO
 */
export interface V1ManageSysOrgPutRequestBody {
    /**
     * 编码
     */
    code?: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父ID,顶级节点父ID -1
     */
    parentId?: string;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageSysOrgPutResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageSysOrgTreeGetQuery {
    name?: string;
    [property: string]: any;
}

export interface V1ManageSysOrgTreeGetResponse {
    code?:   string;
    msg?:    string;
    result?: OrgListElement[];
    [property: string]: any;
}

export interface V1ManageSysOrgUnitUserTreePostResponse {
    code?:   string;
    msg?:    string;
    result?: ChildElement[];
    [property: string]: any;
}

/**
 * 组织信息查询响应体
 */
export interface ChildElement {
    /**
     * 子节点集合
     */
    children?: ChildElement[];
    /**
     * ID
     */
    id?: string;
    /**
     * 名称
     */
    name?: string;
    /**
     * 父ID
     */
    parentId?: string;
    /**
     * 类型 组织:UNIT 用户：USER
     */
    type?: string;
    [property: string]: any;
}

export interface V1ManageSysUnitDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysUnitDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSysUnitIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysUnitIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSysUnitIDGetResponseResult;
    [property: string]: any;
}

/**
 * 工位信息-详情查询响应体
 */
export interface V1ManageSysUnitIDGetResponseResult {
    /**
     * ancestors
     */
    ancestors?: string;
    /**
     * 编码
     */
    code?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 名称
     */
    name?: string;
    /**
     * 父ID
     */
    parentId?:    string;
    terminalDTO?: TerminalDto;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type?: string;
    [property: string]: any;
}

/**
 * 工位绑定终端信息响应体
 */
export interface TerminalDto {
    /**
     * 摄像机1标定
     */
    cameraOneCalibration?: string;
    /**
     * 摄像机3标定
     */
    cameraThreeCalibration?: string;
    /**
     * 摄像机2标定
     */
    cameraTwoCalibration?: string;
    /**
     * 刷卡设备IP
     */
    cardDeviceIp?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 摄像机1IP
     */
    ipCameraOne?: string;
    /**
     * 摄像机3IP
     */
    ipCameraThree?: string;
    /**
     * 摄像机2IP
     */
    ipCameraTwo?: string;
    /**
     * 显示终端IP
     */
    ipTerminal?: string;
    /**
     * 状态: IN_USE(使用中)、IDLE(空闲)
     */
    status?: string;
    /**
     * 工位ID
     */
    unitId?: string;
    /**
     * 工位名称
     */
    unitName?: string;
    [property: string]: any;
}

/**
 * 工位修改请求体DTO
 */
export interface V1ManageSysUnitPostRequestBody {
    /**
     * 编码
     */
    code?: string;
    /**
     * 项目id
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父ID,顶级节点父ID -1
     */
    parentId?: string;
    terminal?: Terminal;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type?: string;
    [property: string]: any;
}

/**
 * 工位绑定新增请求体DTO
 */
export interface Terminal {
    /**
     * 摄像机1标定
     */
    cameraOneCalibration?: string;
    /**
     * 摄像机3标定
     */
    cameraThreeCalibration?: string;
    /**
     * 摄像机2标定
     */
    cameraTwoCalibration?: string;
    /**
     * 刷卡设备IP
     */
    cardDeviceIp?: string;
    /**
     * 摄像机1IP
     */
    ipCameraOne?: string;
    /**
     * 摄像机3IP
     */
    ipCameraThree?: string;
    /**
     * 摄像机2IP
     */
    ipCameraTwo?: string;
    /**
     * 显示终端IP
     */
    ipTerminal?: string;
    /**
     * 工位ID
     */
    unitId?: string;
    [property: string]: any;
}

export interface V1ManageSysUnitPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 工位新增请求体DTO
 */
export interface V1ManageSysUnitPutRequestBody {
    /**
     * 编码
     */
    code?: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父ID,顶级节点父ID -1
     */
    parentId?: string;
    terminal?: Terminal;
    /**
     * 类型 节点:NODE 工位：STATION
     */
    type: string;
    [property: string]: any;
}

export interface V1ManageSysUnitPutResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalBindLocationLocationIDGetParam {
    locationId: string;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalBindLocationLocationIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: TerminalDto;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalCaptureIPGetParam {
    ip: string;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalCaptureIPGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: TerminalDto;
    [property: string]: any;
}

export interface V1ManageSysUnitTerminalListGetResponse {
    code?:   string;
    msg?:    string;
    result?: TerminalDto[];
    [property: string]: any;
}

export interface V1ManageSysUnitTreePostResponse {
    code?:   string;
    msg?:    string;
    result?: LocationListElement[];
    [property: string]: any;
}

/**
 * 用户绑定卡号DTO
 */
export interface V1ManageSysUserBindCardPostRequestBody {
    /**
     * 卡号
     */
    cardNo: string;
    /**
     * 用户ID
     */
    id?: string;
    [property: string]: any;
}

export interface V1ManageSysUserBindCardPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 删除用户批量
 */
export interface V1ManageSysUserDeleteBatchPostRequestBody {
    /**
     * id集合
     */
    ids?: string[];
    [property: string]: any;
}

export interface V1ManageSysUserDeleteBatchPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSysUserDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysUserDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageSysUserIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageSysUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult;
    [property: string]: any;
}

export interface V1ManageSysUserJobPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageSysUserJobPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageSysUserJobPagePostRequestBodyData {
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 岗级
     */
    level?: number;
    /**
     * 姓名
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageSysUserJobPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSysUserJobPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageSysUserJobPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     HilariousAPIModel[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 用户信息查询响应体
 */
export interface HilariousAPIModel {
    /**
     * 账号
     */
    account?: string;
    /**
     * ID
     */
    id?: string;
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 岗级
     */
    level?: number;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 用户组织列表
     */
    orgList?: OrgListElement[];
    [property: string]: any;
}

export interface V1ManageSysUserJobStatisticsGetQuery {
    jobId?: string;
    [property: string]: any;
}

export interface V1ManageSysUserJobStatisticsGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSysUserJobStatisticsGetResponseResult;
    [property: string]: any;
}

export interface V1ManageSysUserJobStatisticsGetResponseResult {
    jobLevelList?: JobLevelListElement[];
    jobList?:      JobListElement[];
    [property: string]: any;
}

export interface JobLevelListElement {
    /**
     * 岗级
     */
    level?: number;
    /**
     * 岗级人数
     */
    userNum?: number;
    [property: string]: any;
}

export interface JobListElement {
    /**
     * 岗位ID
     */
    jobId?: string;
    /**
     * 岗位名称
     */
    jobName?: string;
    /**
     * 岗位人数
     */
    userNum?: number;
    [property: string]: any;
}

export interface V1ManageSysUserPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageQuestionUserPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageSysUserPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageQuestionUserPagePostResponseResult;
    [property: string]: any;
}

/**
 * 用户修改状态请求体DTO
 */
export interface V1ManageSysUserStatusPostRequestBody {
    /**
     * 姓名
     */
    id: string;
    /**
     * 状态 启用ENABLE/停用DISABLE
     */
    status: string;
    [property: string]: any;
}

export interface V1ManageSysUserStatusPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 用户绑定卡号DTO
 */
export interface V1ManageSysUserUnbindCardPostRequestBody {
    /**
     * 卡号
     */
    cardNo: string;
    /**
     * 用户ID
     */
    id?: string;
    [property: string]: any;
}

export interface V1ManageSysUserUnbindCardPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 用户修改请求体DTO
 */
export interface V1ManageSysUserUpdatePostRequestBody {
    /**
     * 人脸文件ID
     */
    faceId?: string;
    /**
     * 姓名
     */
    id: string;
    /**
     * 身份证
     */
    idCard?: string;
    /**
     * 岗位ID
     */
    jobId: string;
    /**
     * 学习项目ID列表
     */
    learningIds?: string[];
    /**
     * 组织ID列表
     */
    orgIds: string[];
    /**
     * 培训项目ID列表
     */
    projectIdList?: string[];
    /**
     * 工号
     */
    workNo?: string;
    [property: string]: any;
}

export interface V1ManageSysUserUpdatePostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 用户修改工号请求体DTO
 */
export interface V1ManageSysUserWorkNoPutRequestBody {
    /**
     * 姓名
     */
    id: string;
    /**
     * 工号
     */
    workNo?: string;
    [property: string]: any;
}

export interface V1ManageSysUserWorkNoPutResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageTrainBookingsBookingIDDeleteParam {
    bookingId: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsBookingIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageTrainBookingsBookingIDGetParam {
    bookingId: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsBookingIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTrainBookingsBookingIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainBookingsBookingIDGetResponseResult {
    /**
     * 创建时间
     */
    bookingAt?: number;
    /**
     * 预约人
     */
    bookingBy?: string;
    /**
     * 预约结束时间
     */
    endTime?: number;
    /**
     * 训练预定ID
     */
    id?: string;
    /**
     * 预约开始时间
     */
    startTime?: number;
    /**
     * 状态
     */
    status?: string;
    /**
     * 工位ID
     */
    unitId?: string;
    /**
     * 工位名称
     */
    unitName?: string;
    /**
     * 培训员工ID
     */
    userId?: string;
    /**
     * 培训员工名称
     */
    userName?: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsPostRequestBody {
    /**
     * 预约结束时间
     */
    endTime: number;
    /**
     * 预约开始时间
     */
    startTime: number;
    /**
     * 工位ID
     */
    unitId: string;
    /**
     * 培训员工ID
     */
    userId: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsPostResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsPutRequestBody {
    /**
     * 预约结束时间
     */
    endTime: number;
    /**
     * 训练预定ID
     */
    id: string;
    /**
     * 预约开始时间
     */
    startTime: number;
    /**
     * 工位ID
     */
    unitId: string;
    /**
     * 培训员工ID
     */
    userId: string;
    [property: string]: any;
}

export interface V1ManageTrainBookingsPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsAlarmDetailsPagePostRequestBody {
    currentPage?: number;
    data?:        V1LocationHomeAlarmDetailsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsAlarmDetailsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeAlarmDetailsPagePostResponseResult;
    [property: string]: any;
}

/**
 * 学员培训不合格记录请求体
 */
export interface V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBodyObject {
    /**
     * 动作类型列表. 1: 未双手作业、2: 未垂直作业面、3: 拧紧未贴合、4: 拧紧枪未亮绿灯
     */
    actionTypes?: number[];
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 汇总记录ID
     */
    recordId: string;
    /**
     * 合格情况： 0-不合格， 1-合格
     */
    result?: number;
    /**
     * 动作达标情况： 0-不达标， 1-达标
     */
    standard?: number;
    /**
     * 开始时间
     */
    startTime?: number;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeAlarmDetailsStatisticsPostResponseResult[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsCheckRankProjectIDGetParam {
    projectId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsCheckRankProjectIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTrainStudyRecordsCheckRankProjectIDGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsCheckRankProjectIDGetResponseResult {
    /**
     * 比赛名次
     */
    ranking?: string;
    /**
     * 用户ID
     */
    userId?: string;
    /**
     * 用户名称
     */
    userName?: string;
    /**
     * 值
     */
    value?: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsDataStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsDataStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeDataStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationStudyCheckProjectIDUserIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsLastStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsLastStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeLastStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsMonthlyStatisticsGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTrainStudyRecordsMonthlyStatisticsGetResponseResult;
    [property: string]: any;
}

/**
 * 月度训练数据统计响应体
 */
export interface V1ManageTrainStudyRecordsMonthlyStatisticsGetResponseResult {
    /**
     * 动作次数
     */
    actionNum?: number;
    /**
     * 动作Ok次数
     */
    actionNumOk?: number;
    /**
     * 上月训练次数
     */
    lastMonthTrainNum?: number;
    /**
     * 作业次数
     */
    opNum?: number;
    /**
     * 作业OK次数
     */
    opNumOk?: number;
    /**
     * 项目人员
     */
    peopleNum?: number;
    /**
     * 训练次数
     */
    trainNum?: number;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageTrainStudyRecordsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 学员培训汇总信息分页查询DTO
 */
export interface V1ManageTrainStudyRecordsPagePostRequestBodyData {
    /**
     * 结束时间
     */
    endTime?: number;
    /**
     * 项目ID列表
     */
    projectIdList?: string[];
    /**
     * 项目名称
     */
    projectName?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType?: number;
    /**
     * 技能状态列表
     */
    skillStatusList?: string[];
    /**
     * 开始时间
     */
    startTime?: number;
    /**
     * 训练状态列表
     */
    statusList?: string[];
    /**
     * 用户ID
     */
    userId?: string;
    /**
     * 用户ID列表
     */
    userIdList?: string[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTrainStudyRecordsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1LocationStudyCheckProjectIDUserIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsProgressStatisticsRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsProgressStatisticsRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsProjectsGetQuery {
    projectType?: number;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsProjectsGetResponse {
    code?:   string;
    msg?:    string;
    result?: Project[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRaceRankProjectIDGetParam {
    projectId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRaceRankProjectIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTrainStudyRecordsCheckRankProjectIDGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRankAllProjectIDTypeStartEndGetParam {
    end:       number;
    projectId: string;
    start:     number;
    type:      string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRankAllProjectIDTypeStartEndGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeRankAllProjectIDTypeStartEndGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRankRecordIDGetParam {
    recordId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRankRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeRankRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIDGetParam {
    recordDetailAlarmId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRecordDetailAlarmURLRecordDetailAlarmIDGetParam {
    recordDetailAlarmId: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsRecordDetailAlarmURLRecordDetailAlarmIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1ManageTrainStudyRecordsUsersGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult[];
    [property: string]: any;
}

export interface V1ManageTranProjectBindProjectLocationIDGetParam {
    locationId: string;
    [property: string]: any;
}

export interface V1ManageTranProjectBindProjectLocationIDGetQuery {
    projectType?: number;
    [property: string]: any;
}

export interface V1ManageTranProjectBindProjectLocationIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: Project[];
    [property: string]: any;
}

export interface V1ManageTranProjectDeleteIDDeleteParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageTranProjectDeleteIDDeleteResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageTranProjectIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1ManageTranProjectIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: Project;
    [property: string]: any;
}

/**
 * 项目查询DTO
 */
export interface V1ManageTranProjectListPostRequestBodyObject {
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType: number;
    /**
     * 状态
     */
    status?: string;
    [property: string]: any;
}

export interface V1ManageTranProjectListPostResponse {
    code?:   string;
    msg?:    string;
    result?: Project[];
    [property: string]: any;
}

export interface V1ManageTranProjectPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageTranProjectPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 项目查询DTO
 */
export interface V1ManageTranProjectPagePostRequestBodyData {
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType: number;
    /**
     * 状态
     */
    status?: string;
    [property: string]: any;
}

export interface V1ManageTranProjectPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageTranProjectPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageTranProjectPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     Project[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

/**
 * 培训项目新增请求体DTO
 */
export interface V1ManageTranProjectPostRequestBody {
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * 项目id
     */
    id: string;
    /**
     * 工位id集合
     */
    locationIds: string[];
    /**
     * 模型集合
     */
    modelList: V1ManageTranProjectPostRequestBodyModelList[];
    /**
     * 项目名称
     */
    name: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType: number;
    /**
     * 比赛类型(1、时间模式 2、次数模式)
     */
    raceType?: number;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 要求动作达标率
     */
    requestActionRate?: number;
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 效率要求
     */
    requestEfficiency?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 合格率要求
     */
    requestQualificationRate?: number;
    [property: string]: any;
}

/**
 * 项目模型新增请求体DTO
 */
export interface V1ManageTranProjectPostRequestBodyModelList {
    /**
     * 模型编码
     */
    modelCode: string;
    /**
     * 模型配置json
     */
    modelJson?: string;
    [property: string]: any;
}

export interface V1ManageTranProjectPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 培训项目新增请求体DTO
 */
export interface V1ManageTranProjectPutRequestBody {
    /**
     * 计次算法
     */
    countAlgorithm?: string;
    /**
     * 工位id集合
     */
    locationIds: string[];
    /**
     * 模型集合
     */
    modelList: V1ManageTranProjectPostRequestBodyModelList[];
    /**
     * 项目名称
     */
    name: string;
    /**
     * 项目类型(1:训练、2:考核、3:比赛)
     */
    projectType: number;
    /**
     * 比赛类型(1、时间模式 2、次数模式)
     */
    raceType?: number;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 要求动作达标率
     */
    requestActionRate?: number;
    /**
     * 要求时长
     */
    requestDuration?: number;
    /**
     * 效率要求
     */
    requestEfficiency?: number;
    /**
     * 要求次数
     */
    requestFrequency?: number;
    /**
     * 合格率要求
     */
    requestQualificationRate?: number;
    /**
     * 状态
     */
    status?: string;
    [property: string]: any;
}

export interface V1ManageTranProjectPutResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * 培训项目新增请求体DTO
 */
export interface V1ManageTranProjectStatusPostRequestBody {
    /**
     * 项目id
     */
    id: string;
    /**
     * 状态 启用ENABLE/停用DISABLE
     */
    status: string;
    [property: string]: any;
}

export interface V1ManageTranProjectStatusPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1ManageTranProjectWithNameGetQuery {
    name?: string;
    type?: number;
    [property: string]: any;
}

export interface V1ManageTranProjectWithNameGetResponse {
    code?:   string;
    msg?:    string;
    result?: Project;
    [property: string]: any;
}

export interface V1ManageUserLearningsIncrementTimePutRequestBody {
    /**
     * 学习详情ID
     */
    id: string;
    /**
     * 新增的学习时长
     */
    incrementTime: string;
    [property: string]: any;
}

export interface V1ManageUserLearningsIncrementTimePutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageUserLearningsLearningDetailIDGetParam {
    learningDetailId: string;
    [property: string]: any;
}

export interface V1ManageUserLearningsLearningDetailIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageUserLearningsLearningDetailIDGetResponseResult;
    [property: string]: any;
}

export interface V1ManageUserLearningsLearningDetailIDGetResponseResult {
    /**
     * 项目说明
     */
    description?: string;
    /**
     * 已学习的时长
     */
    duration?: string;
    /**
     * 考试状态, COMPLETED: 合格, UNQUALIFIED: 不合格, TO_BE: 待考试, QUIT: 弃考
     */
    examStatus?: string;
    /**
     * 学习详情ID
     */
    id?: string;
    /**
     * 学习项目ID
     */
    learningId?: string;
    /**
     * 学习材料列表
     */
    materialList?: MaterialListElement[];
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 试卷ID
     */
    paperId?: string;
    /**
     * 试卷名称
     */
    paperName?: string;
    /**
     * 要求学习的时长
     */
    requireDuration?: string;
    /**
     * 考试得分
     */
    score?: number;
    /**
     * 学习状态
     */
    status?: string;
    /**
     * 用户ID
     */
    userId?: string;
    [property: string]: any;
}

export interface V1ManageUserLearningsLearningDetailIDStartPutParam {
    learningDetailId: string;
    [property: string]: any;
}

export interface V1ManageUserLearningsLearningDetailIDStartPutResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageUserLearningsPagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageUserLearningsPagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

export interface V1ManageUserLearningsPagePostRequestBodyData {
    /**
     * 考试状态, COMPLETED: 合格, UNQUALIFIED: 不合格, TO_BE: 待考试, QUIT: 弃考
     */
    examStatus?: string;
    /**
     * 最大学习时长
     */
    maxDuration?: string;
    /**
     * 分数最大值
     */
    maxScore?: number;
    /**
     * 最小学习时长
     */
    minDuration?: string;
    /**
     * 分数最小值
     */
    minScore?: number;
    /**
     * 项目名称
     */
    name?: string;
    /**
     * 学习状态
     */
    status?: string;
    /**
     * 用户ID
     */
    userId: string;
    [property: string]: any;
}

export interface V1ManageUserLearningsPagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageUserLearningsPagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageUserLearningsPagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageUserLearningsLearningDetailIDGetResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1ManageVehicleImportPostRequestBody {
    file: string;
    [property: string]: any;
}

export interface V1ManageVehicleImportPostResponse {
    code?:   string;
    msg?:    string;
    result?: { [key: string]: any };
    [property: string]: any;
}

export interface V1ManageVehiclePagePostRequestBody {
    currentPage?: number;
    data?:        V1ManageVehiclePagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 车型查询DTO
 */
export interface V1ManageVehiclePagePostRequestBodyData {
    /**
     * 人才系统车型名称
     */
    mappingName?: string;
    /**
     * 车型名称
     */
    name?: string;
    [property: string]: any;
}

export interface V1ManageVehiclePagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageVehiclePagePostResponseResult;
    [property: string]: any;
}

export interface V1ManageVehiclePagePostResponseResult {
    currentPage?: number;
    extInfo?:     { [key: string]: any };
    pageSize?:    number;
    records?:     V1ManageCommonVehiclePostResponseResult[];
    totalPages?:  number;
    totalRows?:   string;
    [property: string]: any;
}

export interface V1MobileHomeCurrentDateGetResponse {
    code?:   string;
    msg?:    string;
    result?: number;
    [property: string]: any;
}

export interface V1MobileHomeCurrentUserGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1LocationHomeCurrentUserGetResponseResult;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordHistoryPaperIDUserIDGetParam {
    paperId: string;
    userId:  string;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult[];
    [property: string]: any;
}

export interface V1MobileHomeExamRecordIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1MobileHomeExamRecordIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface V1MobileHomeExamRecordIDGetResponseResult {
    examRecord?: ExamRecordElement;
    paper?:      PaperElement;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordPaperIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordPaperIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamRecordIDGetResponseResult;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordPaperInitIDGetParam {
    id: string;
    [property: string]: any;
}

export interface V1MobileHomeExamRecordPaperInitIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1MobileHomeExamRecordPaperInitIDGetResponseResult;
    [property: string]: any;
}

/**
 * 学员对应考试试卷响应体
 */
export interface V1MobileHomeExamRecordPaperInitIDGetResponseResult {
    examRecord?: ExamRecordElement;
    paper?:      Paper;
    [property: string]: any;
}

export interface V1MobileHomeLastedExamRecordPaperIDGetParam {
    paperId: string;
    [property: string]: any;
}

export interface V1MobileHomeLastedExamRecordPaperIDGetResponse {
    code?:   string;
    msg?:    string;
    result?: ExamRecordElement;
    [property: string]: any;
}

/**
 * 考试记录查询DTO
 */
export interface V1MobileHomeListPostRequestBodyObject {
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 状态列表
     */
    statusList?: string[];
    /**
     * 试卷类型
     */
    type: string;
    /**
     * 人员ID
     */
    userId: string;
    [property: string]: any;
}

export interface V1MobileHomeListPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult[];
    [property: string]: any;
}

export interface V1MobileHomeLoginSyncGetResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

export interface V1MobileHomePagePostRequestBody {
    currentPage?: number;
    data?:        V1MobileHomePagePostRequestBodyData;
    pageSize?:    number;
    [property: string]: any;
}

/**
 * 考试记录查询DTO
 */
export interface V1MobileHomePagePostRequestBodyData {
    /**
     * 题目属性列表
     */
    questionPropertyList?: QuestionPropertyListElement[];
    /**
     * 状态列表
     */
    statusList?: string[];
    /**
     * 试卷类型
     */
    type: string;
    /**
     * 人员ID
     */
    userId: string;
    [property: string]: any;
}

export interface V1MobileHomePagePostResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageExamRecordPagePostResponseResult;
    [property: string]: any;
}

/**
 * 开始答题DTO
 */
export interface V1MobileStudyExamRecordStartPostRequestBody {
    /**
     * 考试记录ID
     */
    id: string;
    [property: string]: any;
}

export interface V1MobileStudyExamRecordStartPostResponse {
    code?:   string;
    msg?:    string;
    result?: boolean;
    [property: string]: any;
}

/**
 * 结束答题DTO
 */
export interface V1MobileStudyOverPostRequestBody {
    /**
     * 试题ID-对应作答
     */
    answerList?: V1MobileStudyOverPostRequestBodyAnswerList[];
    /**
     * 考试记录ID
     */
    id: string;
    [property: string]: any;
}

/**
 * 答题请求体DTO
 */
export interface V1MobileStudyOverPostRequestBodyAnswerList {
    /**
     * 答案
     */
    answer?: string[];
    /**
     * 试题ID
     */
    id: string;
    [property: string]: any;
}

export interface V1MobileStudyOverPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1MobileStudyOverPostResponseResult;
    [property: string]: any;
}

/**
 * 考试完成响应体
 */
export interface V1MobileStudyOverPostResponseResult {
    /**
     * 时间考试时长
     */
    duration?: number;
    /**
     * 考试得分
     */
    score?: number;
    [property: string]: any;
}

export interface V1OpenAPIGetFlvHLSStreamGetQuery {
    url?: string;
    [property: string]: any;
}

export interface V1OpenAPIGetFlvHLSStreamChannelFlvGetParam {
    channel: string;
    [property: string]: any;
}

export interface V1OpenAPIGetURLStreamChannelGetParam {
    channel: string;
    [property: string]: any;
}

export interface V1OpenAPIGetURLStreamChannelGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

export interface V1OpenAPISysConfigListGetResponse {
    code?:   string;
    msg?:    string;
    result?: V1ManageSysConfigListGetResponseResult[];
    [property: string]: any;
}

export interface V1OpenAPIUserLoginGucPostRequestBody {
    /**
     * 登录类型 1普通账户 2验证码登录 3域账号登录 4刷卡登录 5企微授权登录
     */
    loginType: number;
    /**
     * 密码或验证码
     */
    password: string;
    /**
     * 用户名
     */
    username: string;
    [property: string]: any;
}

export interface V1OpenAPIUserLoginGucPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1OpenAPIUserLoginGucPostResponseResult;
    [property: string]: any;
}

export interface V1OpenAPIUserLoginGucPostResponseResult {
    /**
     * token过期时间
     */
    expiresIn?: string;
    /**
     * 刷新的token
     */
    refreshToken?: string;
    /**
     * token
     */
    token?: string;
    [property: string]: any;
}

export interface V1OpenAPIUserLoginPublicKeyGetQuery {
    username?: string;
    [property: string]: any;
}

export interface V1OpenAPIUserLoginPublicKeyGetResponse {
    code?:   string;
    msg?:    string;
    result?: string;
    [property: string]: any;
}

/**
 * 微信请求体DTO
 */
export interface V1OpenAPIUserLoginWxJSSDKPostRequestBody {
    url?: string;
    [property: string]: any;
}

export interface V1OpenAPIUserLoginWxJSSDKPostResponse {
    code?:   string;
    msg?:    string;
    result?: V1OpenAPIUserLoginWxJSSDKPostResponseResult;
    [property: string]: any;
}

/**
 * 企微签名返回体
 */
export interface V1OpenAPIUserLoginWxJSSDKPostResponseResult {
    /**
     * 企业ID
     */
    corpid?: string;
    /**
     * nonceStr
     */
    nonceStr?: string;
    /**
     * 签名
     */
    signature?: string;
    /**
     * 时间戳
     */
    timestamp?: string;
    [property: string]: any;
}
