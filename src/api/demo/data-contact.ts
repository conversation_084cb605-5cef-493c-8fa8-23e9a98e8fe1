export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T extends any> {
  items: T;
  total: number;
}

export type DemoParams = BasicPageParams & {
  order?: string;
  name?: string;
};

export interface TableListItem {
  order: string;
  no: string;
  name: string;
  level: number;
  status: number;
  delayStatus: number;
  detail: string;
  other: string;
}

/**
 * @description: Request list return value
 */

export type DemoListResultModel = BasicFetchResult<TableListItem>;
