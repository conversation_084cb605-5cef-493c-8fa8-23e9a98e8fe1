import { defHttp } from '/@/utils/http/axios';
  import i18nVue from '@geega/i18n-vue';

enum  Api {
  DEMO_POST = '/api/table/postDemo',
  UNREAD_COUNT = 'http://mom-b-platform-api.caas-cloud-test.geega.com/api/message/unread-count'
}

/**
 * @description: Get sample list value
 */

function getRnd(min, max){
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export const demoListApi = () => {

  let mockData:any = [];

  for(let i=0;i<600;i++){

    let x = getRnd(1,100)

    let data =  {
      order: 'P'+ 20221212007+x,
      nu: '0040800316' + i +'C-DDD' + x,
      name:x>80?i18nVue.t('api.demo.demo.715ac1e4', { defaultValue: '安全阀' }):x>40?'PT1':'PT2',
      level:x<40?i18nVue.t('api.demo.demo.5f0b5c96', { defaultValue: '高' }):x<40?i18nVue.t('api.demo.demo.99b67645', { defaultValue: '中' }):i18nVue.t('api.demo.demo.178a4b0d', { defaultValue: '底' }),
      status:x<40?i18nVue.t('api.demo.demo.e04aa9ef', { defaultValue: '正常' }):x<20?i18nVue.t('api.demo.demo.8444a061', { defaultValue: '异常' }):i18nVue.t('api.demo.demo.c256a2e7', { defaultValue: '未知状态' }),
      delayStatus:x>50?i18nVue.t('api.demo.demo.84877851', { defaultValue: '是' }):i18nVue.t('api.demo.demo.4a934b2e', { defaultValue: '否' }),
      tag:1,
      detail:`剩余${x}天`,
      other:`${x}/${x}/100`,
    }
    mockData.push(data)
  }

  return Promise.resolve(mockData);
};

export const unreadCount = (params: any) =>
  defHttp.get<any>({
    url: Api.UNREAD_COUNT,
    params,
});
