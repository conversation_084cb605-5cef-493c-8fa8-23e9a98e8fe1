import { defHttp } from '/@/utils/http/axios';
import { MANAGE_API } from '/@/api/admin/constant';

/**
 * @description 查询用户列表
 */
export function userQuery(params) {
  return defHttp.request<any>({
    url: MANAGE_API.UserQueryList,
    method: 'post',
    params,
  });
}

/**
 * @description 新增用户
 */
export function userAdd(params) {
  return defHttp.request<any>({
    url: MANAGE_API.UserAdd,
    method: 'post',
    params,
  });
}

/**
 * @description 删除用户
 */
export function userDelete(params) {
  return defHttp.request<any>({
    url: MANAGE_API.UserDelete,
    method: 'post',
    params,
  });
}

/**
 * @description 更新用户
 */
export function userUpdate(params) {
  return defHttp.request<any>({
    url: MANAGE_API.UserUpdate,
    method: 'post',
    params,
  });
}

/**
 * @description 查询用户详情
 */
export function userDetail(userId) {
  return defHttp.request<any>({
    url: `${MANAGE_API.UserDetail}${userId}`,
    method: 'get',
  });
}

/**
 * @description 启用/禁用用户
 */
export function userStatus(params) {
  return defHttp.request<any>({
    url: MANAGE_API.UserStatus,
    method: 'post',
    params,
  });
}
