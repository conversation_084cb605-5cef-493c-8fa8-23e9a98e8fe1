import { defHttp } from '/@/utils/http/axios';
import { MANAGE_API } from '/@/api/admin/constant';

/**
 * @description 查询项目列表
 */
export function projectList(params) {
  return defHttp.request<any>({
    url: MANAGE_API.ProjectList,
    method: 'post',
    params,
  });
}

/**
 * @description 获取计次算法
 */
export function countAlgorithmEnum() {
  return defHttp.request<any>({
    url: MANAGE_API.CountAlgorithmEnum,
    method: 'get',
  });
}

/**
 * @description 查询项目列表
 */
export function projectPage(params) {
  return defHttp.request<any>({
    url: MANAGE_API.ProjectPage,
    method: 'post',
    params,
  });
}

/**
 * @description 新增项目
 */
export function projectAdd(params) {
  return defHttp.request<any>({
    url: MANAGE_API.ProjectAdd,
    method: 'put',
    params,
  });
}

/**
 * @description 修改项目
 */
export function projectUpdate(params) {
  return defHttp.request<any>({
    url: MANAGE_API.ProjectAdd,
    method: 'post',
    params,
  });
}

/**
 * @description 删除项目
 */
export function projectDelete(pId) {
  return defHttp.request<any>({
    url: `${MANAGE_API.ProjectDelete}/${pId}`,
    method: 'delete',
  });
}

/**
 * @description 启用/禁用项目
 */
export function projectStatus(params) {
  return defHttp.request<any>({
    url: MANAGE_API.ProjectStatus,
    method: 'post',
    params,
  });
}

/**
 * @description 获取模型分类
 */
export function actionTypeEnum() {
  return defHttp.request<any>({
    url: MANAGE_API.ActionTypeEnum,
    method: 'get',
  });
}

/**
 * @description 获取工位树
 */
export function unitTree() {
  return defHttp.request<any>({
    url: MANAGE_API.UnitTree,
    method: 'post',
  });
}
