/**
 * 道场管理端常量
 */

import { API_PREFIX_MANAGE } from '/@/api/constant';

export const MANAGE_API = {
  // 查询用户列表
  UserQueryList: `${API_PREFIX_MANAGE}/sys-user/page`,
  // 新增用户
  UserAdd: `${API_PREFIX_MANAGE}/sys-user/add`,
  // 删除用户
  UserDelete: `${API_PREFIX_MANAGE}/sys-user/delete`,
  // 更新用户
  UserUpdate: `${API_PREFIX_MANAGE}/sys-user/update`,
  // 启用/禁用用户
  UserStatus: `${API_PREFIX_MANAGE}/sys-user/status`,
  // 查询用户详情
  UserDetail: `${API_PREFIX_MANAGE}/sys-user/`,
  // 项目下拉列表
  ProjectList: `${API_PREFIX_MANAGE}/tran-project/list`,
  // 计次算法
  CountAlgorithmEnum: `${API_PREFIX_MANAGE}/common/count-algorithm-enum`,
  // 分页查询项目
  ProjectPage: `${API_PREFIX_MANAGE}/tran-project/page`,
  // 新增项目
  ProjectAdd: `${API_PREFIX_MANAGE}/tran-project`,
  // 删除项目
  ProjectDelete: `${API_PREFIX_MANAGE}/tran-project/delete`,
  // 启用/禁用项目
  ProjectStatus: `${API_PREFIX_MANAGE}/tran-project/status`,
  // 获取模型分类
  ActionTypeEnum: `${API_PREFIX_MANAGE}/common/action-type-enum`,
  // 获取工位树
  UnitTree: `${API_PREFIX_MANAGE}/sys-unit/tree`,
};
