# CI-CD

- [.env.build_dev]: build in dev environment
- [.env.build_test]: build in test environment
- [.env.build_prod]: build in prod environment

```bash

yarn install

yarn build:[env]

```

- [default.conf]: nginx conf
  - [Document](https://www.nginx.cn/doc/standard/httpproxy.html)
  - 单页应用前端静态页面路由： location /
  - 后台接口路由   ~ /api  配置代理 参考该文件，api代理配置后台接口servername，该服务名字在各环境保持一致
    后台接口为多个服务，前端需自定义路由，区分服务配置代理

- [Dockerfile]: 构建镜像 dockerfile
