server {
    listen       80;
    server_name  localhost;
    gzip            on;
    gzip_static     on;
    gzip_types      text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript;
    gzip_proxied    any;
    gzip_vary       on;
    gzip_comp_level 8;
    gzip_buffers    16 8k;
    client_header_buffer_size 256k;
    large_client_header_buffers 4 256k;
    client_max_body_size 1024m;


    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        client_max_body_size 1024m;
        add_header  Cache-Control  max-age=no-cache;
        # 子应用跨域问题
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, PATCH, OPTIONS';
        # Sw8 监控
        add_header Access-Control-Allow-Headers 'Sw8,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        # 跨域
        if ($request_method = 'OPTIONS') {
              return 204;
        }
    }

    location /guc-api/ {
        proxy_pass https://guc3-api-test.geega.com/;
    }

    location /cddc/ {
        # set user real IP
        proxy_set_header x-real-ip $proxy_add_x_forwarded_for;

        proxy_pass http://intelli-matching-server:9090/;
        # proxy_pass http://intelli-matching-service.caas-cloud-dev.geega.com/;
    }

    location /storage/ {
        proxy_hide_header Access-Control-Allow-Origin;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods *;

        # dev/test 直接用 ip 访问
        proxy_pass http://*************:9000/;
        # proxy_pass http://minio-dne.middleware-minio-pub:9000/;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
