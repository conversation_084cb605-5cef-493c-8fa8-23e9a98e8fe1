import { Lang } from 'language-translate/types';
import { defineConfig } from 'language-translate/utils';

// https://github.com/hymhub/language-translate
//依赖 language-translate  安装指令：yarn add language-translate -D 。

export default defineConfig({
  // 需要外部网络-这个是本人本地代理，需要书写自己的本地代理
  proxy: {
    host: '127.0.0.1',
    port: 8118,
  },
  fromLang: Lang['zh-CN'],
  //fromPath: 'translate.entry.json',
  // fromPath: 'src/locales/lang/zh_CN/**/**.ts',
  fromPath: 'src/locales/i18n/zh-CN.json',
  translate: [
    {
      label: '将结果翻译到locales文件夹下',
      targetConfig: [
        {
          targetLang: Lang.en,
          outPath: 'src/locales/i18n/en-US.json',
        }
      ]
    }
  ]
})
