#!/bin/bash
#!chmod 777

# 项目环境，new Guc的时候也要用到
__VUE_APP_ENV__=test
# 手动传入guc地址
__VUE_APP_GUC_API_URL__=https://guc3-api-test.geega.com

__VUE_APP_GLOB_API_URL_PREFIX__=/

__VUE_APP_GLOB_API_URL__=/
__VUE_APP_PUBLIC_PATH__=http://localhost:8099

__VUE_APP_GLOB_UPLOAD_URL__=

__VUE_APP_GLOB_GUC_APP_ID__=1

__VUE_APP_GLOB_CM_URL__=https://onlinemonitor-collect-test.geega.com

__VUE_APP_GLOB_CM_APP_ID__=OrAiPijkHVBdrSXIgmvstOOK9p3VNKWhHrcnoatdvH4

echo  "正在替换环境变量..."
  sed -i "" "s|__VUE_APP_ENV__|${__VUE_APP_ENV__}|g" `grep __VUE_APP_ENV__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GUC_API_URL__|${__VUE_APP_GUC_API_URL__}|g" `grep __VUE_APP_GUC_API_URL__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_API_URL_PREFIX__|${__VUE_APP_GLOB_API_URL_PREFIX__}|g" `grep __VUE_APP_GLOB_API_URL_PREFIX__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_API_URL__|${__VUE_APP_GLOB_API_URL__}|g" `grep __VUE_APP_GLOB_API_URL__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_UPLOAD_URL__|${__VUE_APP_GLOB_UPLOAD_URL__}|g" `grep __VUE_APP_GLOB_UPLOAD_URL__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_GUC_APP_ID__|${__VUE_APP_GLOB_GUC_APP_ID__}|g" `grep __VUE_APP_GLOB_GUC_APP_ID__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_CM_URL__|${__VUE_APP_GLOB_CM_URL__}|g" `grep __VUE_APP_GLOB_CM_URL__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_GLOB_CM_APP_ID__|${__VUE_APP_GLOB_CM_APP_ID__}|g" `grep __VUE_APP_GLOB_CM_APP_ID__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

  sed -i "" "s|__VUE_APP_PUBLIC_PATH__|${__VUE_APP_PUBLIC_PATH__}|g" `grep __VUE_APP_PUBLIC_PATH__ -rl /Users/<USER>/git-geega-code/geega-admin-template/dist`

echo  "替换环境变量完成..."
nginx -g "daemon off;"
