#
# START VUE CLI ENV CONFIG
#

# spa-title
VUE_APP_GLOB_APP_TITLE = __VUE_APP_GLOB_APP_TITLE__

# Whether to open mock
VUE_APP_USE_MOCK=false

# public path
VUE_APP_PUBLIC_PATH=./

# router base
VUE_APP_QIANKUN_ROUTER_BASE=/app/temp
VUE_APP_ROUTER_BASE=/

# Delete console
VUE_APP_DROP_CONSOLE=true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VUE_APP_BUILD_COMPRESS='none'

# Basic interface address SPA
VUE_APP_GLOB_API_URL=

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VUE_APP_GLOB_UPLOAD_URL=__VUE_APP_GLOB_UPLOAD_URL__

# Interface prefix
VUE_APP_GLOB_API_URL_PREFIX=cddc

# Whether to enable image compression
VUE_APP_USE_IMAGEMIN=true

# use pwa
VUE_APP_USE_PWA=false

# Is it compatible with older browsers
VUE_APP_LEGACY=false

VUE_APP_ENV=prod

VUE_APP_GUC_API_URL=/guc-api

# VUE_APP_GLOB_CM_URL=__VUE_APP_GLOB_CM_URL__

# VUE_APP_GLOB_CM_APP_ID=__VUE_APP_GLOB_CM_APP_ID__

VUE_APP_ENABLE_GUC=true

# WebSocket服务器地址
VUE_APP_GLOB_SOCKET_URL=__VUE_APP_GLOB_SOCKET_URL__

# 培训视频地址
VUE_APP_GLOB_TRAINING_URL=__VUE_APP_GLOB_TRAINING_URL__

# 文件预览服务地址
VUE_APP_GLOB_PREVIEW_URL=__VUE_APP_GLOB_PREVIEW_URL__
# 左侧视频面板轮播间隔时间（分钟），例如 2 表示2分钟
VUE_APP_VIDEO_PANEL_ROTATION_MINUTES=__VUE_APP_VIDEO_PANEL_ROTATION_MINUTES__
# WebRtc地址
VUE_APP_GLOB_WEBRTC_URL=__VUE_APP_GLOB_WEBRTC_URL__
