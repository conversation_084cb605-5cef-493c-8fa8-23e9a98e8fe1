import path, { resolve } from 'path';
import webpack from 'webpack';
import { ProjectOptions } from '@vue/cli-service';
import TerserPlugin from 'terser-webpack-plugin';
import { name } from './package.json';
import { generateModifyVars } from './build/config/themeConfig';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';

function pathResolve(dir) {
  return resolve(__dirname, '.', dir);
}

const isDev = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
const env = process.env;

// generate proxy object
const proxyObj = {};

if (isDev) {
  const proxyArray = JSON.parse(env.VUE_APP_PROXY);
  proxyArray.forEach((proxy) => {
    const proxyKey = proxy[0];
    const proxyValue = proxy[1];
    proxyObj[proxyKey] = {
      target: proxyValue,
      pathRewrite: {
        [`^${proxyKey}`]: '',
      },
      cookieDomainRewrite: '',
    };
  });
}

export const config: ProjectOptions = {
  publicPath: env.VUE_APP_PUBLIC_PATH,

  productionSourceMap: false,

  pages: {
    index: {
      entry: 'src/main.ts',
      title: env.VUE_APP_GLOB_APP_TITLE,
    },
  },

  chainWebpack: (config) => {
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'g-[name]',
      })
      .end();
  },

  configureWebpack: {
    module: {
      rules: [
        {
          test: /\.ts$/,
          include: [
            resolve(__dirname, 'src'),
            resolve(__dirname, 'build'),
            resolve(__dirname, 'node_modules/@geega-ui-plus/geega-ui'),
            resolve(__dirname, 'node_modules/@geega-ui-plus/admin-design-less'),
          ],
          use: [
            {
              loader: 'babel-loader',
            },
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true,
                happyPackMode: true,
                appendTsxSuffixTo: [/\.vue$/],
              },
            },
          ],
        },
        {
          test: /\.tsx$/,
          include: [
            resolve(__dirname, 'src'),
            resolve(__dirname, 'node_modules/@geega-ui-plus/geega-ui'),
            resolve(__dirname, 'node_modules/@geega-ui-plus/admin-design-less'),
          ],
          use: [
            {
              loader: 'babel-loader',
            },
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true,
                happyPackMode: true,
                appendTsxSuffixTo: [/\.vue$/],
              },
            },
          ],
        },
        // {
        //   test: /\.css$/i,
        //   use: [MiniCssExtractPlugin.loader, "css-loader"],
        // },
      ],
    },
    output: {
      library: `${name}-[name]`,
      filename: '[name].[contenthash:6].js',
      libraryTarget: 'umd', // 把微应用打包成 umd 库格式
    },
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.vue', '.json'],
      alias: {
        '/@': pathResolve('src') + '/',
        '/#': pathResolve('types') + '/',
      },
      modules: [path.resolve(__dirname, 'node_modules')],
      fallback: { crypto: false },
    },
    plugins: [
      new webpack.DefinePlugin({
        __VUE_I18N_LEGACY_API__: JSON.stringify(false),
        __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
        __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
        __OUTPUT_FILE_NAME__: JSON.stringify(''),
        __PROD__: JSON.stringify(''),
        __OPTIONS__: JSON.stringify(''),
      }),
      new MiniCssExtractPlugin(),
    ],
    devtool: isDev ? 'source-map' : undefined,
    optimization: isDev
      ? {
          removeAvailableModules: false,
          removeEmptyChunks: false,
          splitChunks: false,
        }
      : {
          minimize: true,
          minimizer: [
            new TerserPlugin({
              terserOptions: {
                mangle: true,
                compress: {
                  drop_debugger: false,
                  drop_console: false,
                },
              },
            }),
            new CssMinimizerPlugin(),
          ],
          splitChunks: {
            // chunks: 'all',
            // minSize: 20000,
            chunks: 'all',
            minSize: 500 * 1000,
            maxSize: 2000 * 1000,
            maxAsyncSize: 2000 * 1000,
            minRemainingSize: 100 * 1000,
            minChunks: 1,
            maxAsyncRequests: 2,
            maxInitialRequests: 30,
            cacheGroups: {
              antd: {
                name: 'antd',
                test: /@geega-ui-plus\/ant-design-vue/,
                priority: 1,
              },
              excel: {
                name: 'xlsx',
                test: /xlsx/,
                priority: 2,
              },
              vditor: {
                name: 'vditor',
                test: /vditor/,
                priority: 2,
              },
              styles: {
                name: 'styles',
                type: 'css/mini-extract',
                chunks: 'all',
                enforce: true,
              },
            },
          },
        },
  },

  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            hack: `true;
              @import (reference) "${resolve('src/design/variable.less')}";
              @import (reference) "${resolve(
                'node_modules/@geega-ui-plus/admin-design-less/src/design/geega-ui-variable.less'
              )}";`,
            ...generateModifyVars(),
          },
          javascriptEnabled: true,
        },
      },
    },
  },

  outputDir: 'dist',

  // 通过babel-loader编译该模块
  transpileDependencies: ['@iconify/iconify', '@geega-ui-plus'],

  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, Sw8',
    },
    proxy: proxyObj,
    client: {
      overlay: false,
    },
  },
};
