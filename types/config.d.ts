import { MenuTypeEnum, MenuModeEnum, TriggerEnum, MixSidebarTriggerEnum } from '/@/enums/menuEnum';
import {
  ContentEnum,
  PermissionModeEnum,
  ThemeEnum,
  RouterTransitionEnum,
  SettingButtonPositionEnum,
} from '/@/enums/appEnum';

import { CacheTypeEnum } from '/@/enums/cacheEnum';

export type LocaleType = 'zh_CN' | 'en' | 'ru' | 'ja' | 'ko';

export interface MenuSetting {
  bgColor: string;
  fixed: boolean;
  collapsed: boolean;
  canDrag: boolean;
  show: boolean;
  hidden: boolean;
  split: boolean;
  menuWidth: number;
  mode: MenuModeEnum;
  type: MenuTypeEnum;
  theme: ThemeEnum;
  topMenuAlign: 'start' | 'center' | 'end';
  trigger: TriggerEnum;
  accordion: boolean;
  closeMixSidebarOnChange: boolean;
  collapsedShowTitle: boolean;
  mixSideTrigger: MixSidebarTriggerEnum;
  mixSideFixed: boolean;
}

export interface MultiTabsSetting {
  show: boolean;
  showQuick: boolean;
  canDrag: boolean;
  showRedo: boolean;
  showFold: boolean;
}

export interface HeaderSetting {
  bgColor: string;
  fixed: boolean;
  show: boolean;
  theme: ThemeEnum;
  // Turn on full screen
  showFullScreen: boolean;
  // Whether to show the lock screen
  useLockPage: boolean;
  // Show document button
  showDoc: boolean;
  // Show message center button
  showNotice: boolean;
  showSearch: boolean;
  showLang: boolean;
}

export interface LocaleSetting {
  showPicker: boolean;
  // Current language
  locale: LocaleType;
  // default language
  fallback: LocaleType;
  // available Locales
  availableLocales: LocaleType[];
}

export interface TransitionSetting {
  //  Whether to open the page switching animation
  enable: boolean;
  // Route basic switching animation
  basicTransition: RouterTransitionEnum;
  // Whether to open page switching loading
  openPageLoading: boolean;
  // Whether to open the top progress bar
  openNProgress: boolean;
}

export interface ProjectConfig {
  // Storage location of permission related information
  permissionCacheType: CacheTypeEnum;
  // Whether to show the configuration button
  showSettingButton: boolean;
  // Configure where the button is displayed
  settingButtonPosition: SettingButtonPositionEnum;
  // Permission mode
  permissionMode: PermissionModeEnum;
  // Website gray mode, open for possible mourning dates
  grayMode: boolean;
  // Whether to turn on the color weak mode
  colorWeak: boolean;
  // Theme color
  themeColor: string;
  // Theme Mode
  themeMode: ThemeEnum;
  // The main interface is displayed in full screen, the menu is not displayed, and the top
  fullContent: boolean;
  // content width
  contentMode: ContentEnum;
  // Whether to display the logo
  showLogo: boolean;
  // Whether to show the global footer
  showFooter: boolean;
  // menuType: MenuTypeEnum;
  headerSetting: HeaderSetting;
  // menuSetting
  menuSetting: MenuSetting;
  // Multi-tab settings
  multiTabsSetting: MultiTabsSetting;
  // Animation configuration
  transitionSetting: TransitionSetting;
  // pageLayout whether to enable keep-alive
  openKeepAlive: boolean;
  // Lock screen time
  lockTime: number;
  // Show breadcrumbs
  showBreadCrumb: boolean;
  // Show breadcrumb icon
  showBreadCrumbIcon: boolean;
  // Use error-handler-plugin
  useErrorHandle: boolean;
  // Whether to open back to top
  useOpenBackTop: boolean;
  // Is it possible to embed iframe pages
  canEmbedIFramePage: boolean;
  // Whether to delete unclosed messages and notify when switching the interface
  closeMessageOnSwitch: boolean;
  // Whether to cancel the http request that has been sent but not responded when switching the interface.
  removeAllHttpPending: boolean;
  showLang: boolean;
}

export interface GlobConfig {
  // Site title
  title: string;
  // Service interface url
  apiUrl: string;
  // Upload url
  uploadUrl?: string;
  //  Service interface url prefix
  urlPrefix?: string;
  // Project abbreviation
  shortName: string;
  // appId
  appId?: string;
  // cmappId
  cmAppId?: string;
  // enable guc
  enableGuc: boolean;
  gucApiUrl?: string;
  cmApiUrl?: string;
  env?: string;
  socketUrl?: string; // WebSocket服务器地址
  trainingUrl?: string; // 培训视频地址
  previewUrl?: string; // 文件预览服务地址
  webrtcUrl?: string; // WebRTC服务器地址
  videoPanelRotationMinutes?: number | string; // 左侧视频面板轮播间隔时间（分钟）
}

export interface GlobEnvConfig {
  // Site title
  VUE_APP_GLOB_APP_TITLE: string;
  // Service interface url
  VUE_APP_GLOB_API_URL: string;
  // Service interface url prefix
  VUE_APP_GLOB_API_URL_PREFIX?: string;
  // Project abbreviation
  VUE_APP_GLOB_APP_SHORT_NAME: string;
  // Upload url
  VUE_APP_GLOB_UPLOAD_URL?: string;
  // appId
  VUE_APP_GLOB_GUC_APP_ID?: string;
  // enable guc
  VUE_APP_ENABLE_GUC: string;

  VUE_APP_GUC_API_URL: string;

  VUE_APP_ENV?: string;

  VUE_APP_VIDEO_PANEL_ROTATION_MINUTES?: string;

  VUE_APP_GLOB_CM_URL?: string;
  VUE_APP_GLOB_CM_APP_ID?: string;
  VUE_APP_GLOB_SOCKET_URL?: string; // WebSocket服务器地址环境变量
  VUE_APP_GLOB_TRAINING_URL?: string; // 培训视频地址环境变量
  VUE_APP_GLOB_PREVIEW_URL?: string; // 文件预览服务地址环境变量
  VUE_APP_GLOB_WEBRTC_URL?: string; // WebRTC服务器地址环境变量
  VUE_APP_GLOB_WEBRTC_URL?: string; // WebRTC服务器地址环境变量
}
