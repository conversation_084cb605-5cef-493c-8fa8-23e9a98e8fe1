// Interface data format used to return a unified format

export function resultSuccess<T = Recordable>(result: T, { msg = 'ok' } = {}) {
  return {
    code: '000000',
    result,
    msg,
    type: 'success',
  };
}

export function resultPageSuccess<T = any>(
  page: number,
  pageSize: number,
  list: T[],
  { msg = 'ok' } = {}
) {
  const pageData = pagination(page, pageSize, list);

  return {
    ...resultSuccess({
      items: pageData,
      total: list.length,
    }),
    msg,
  };
}

export function resultError(msg = 'Request failed', { code = -1, result = null } = {}) {
  return {
    code,
    result,
    msg,
    type: 'error',
  };
}

export function pagination<T = any>(pageNo: number, pageSize: number, array: T[]): T[] {
  const offset = (pageNo - 1) * Number(pageSize);
  const ret =
    offset + Number(pageSize) >= array.length
      ? array.slice(offset, array.length)
      : array.slice(offset, offset + Number(pageSize));
  return ret;
}
