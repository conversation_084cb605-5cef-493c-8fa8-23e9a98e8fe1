## 简介

cddc-web 智工育匠

#### 本地开发

```bash
# 修改开发环境接口转发前缀和GUC请求环境 .env.development
# NOTE:
# 本地需要配置多个代理的情况下, VUE_APP_GLOB_API_URL会被拼接到接口路径前面;
# 生产环境通过./CICD/default.conf  proxy_pass配置其他业务接口的转发;

# 本地开发时设置代码
VUE_APP_PROXY =[["/admin-proxy/guc-api","http://gucserver.cloud-dev.geega.com"],["/admin-proxy/basic-api","http://localhost:3000"]]


```

```bash
yarn dev

```

#### 发布

##### 修改环境变量 VUE_APP_PUBLIC_PATH

```bash
# 接入GMOM的应用，VUE_APP_PUBLIC_PATH需要写完整前端URL地址 （eg：http://appxx.geega.com），不能是'/**'
```

##### 修改环境变量 VUE_GLOB_API_URL

```bash
# 接入GO中控台的应用，VUE_GLOB_API_URL需要写完整地址 （eg：http://xxx-server-api.geega.com [需要支持跨域]），不能是'/**'
```

##### history 路由需要判断是 micro-app / wujie / qiankun 环境，修改路由前缀为要接入的主应用的路由前缀

```javascript
/*router base
vue-router4
createWebHistory/createWebHashHistory 推荐hash（子应用）
*/

export const history = createWebHistory(
  window.__MICRO_APP_BASE_ROUTE__
    ? window.__MICRO_APP_BASE_ROUTE__
    : window.__POWERED_BY_WUJIE__
    ? process.env.VUE_APP_ROUTER_BASE
    : process.env.VUE_APP_ROUTER_BASE
);

```

##### GUC 开关

.env 文件 VUE_APP_ENABLE_GUC=false, 项目不需要 guc 登录


##### 线下构建不多环境

```bash
# NOTE: 构建之前需要确认VUE_APP_PUBLIC_PATH、VUE_APP_ROUTER_BASE、VUE_APP_GLOB_API_URL等值是否正确

# paas测试环境， 使用.env.build_test环境变量文件
build:cli:test

# paas开发环境 使用.env.build_dev环境变量文件
build:cli:dev
```
#### 接入GUC菜单权限

两种权限 `菜单权限` 与 `按钮权限`

#####  如何开启权限模式？
```javascript
// src/settings/projectSetting.ts

const setting: ProjectConfig = {
  // Permission mode
  // 前台权限
  // ROLE = 'ROLE',
  // 后台GUC权限
  // BACK = 'BACK',
  permissionMode: PermissionModeEnum.BACK,
}
```



```javascript
//src/api/sys/menu.ts
// gucInstance 是GUC-SDK的实例 getMenusTree 获取菜单 / getButtonsList 获取 按钮权限

// 菜单目录 设置
const { permissionMode = projectSetting.permissionMode } = appStore.getProjectConfig;
// if (permissionMode === PermissionModeEnum.BACK)

// 按钮权限
// <button v-auth="upload file">上传按钮</button>
// v-auth="your permission code"


```


#### 线上构建（多环境）

##### 构建流水
```shell
node -v
yarn config set ignore-engines true
yarn install
yarn build:common

# 选定 Dockerfile.common

```

##### 设置应用环境变量

VUE_APP_ENV
VUE_APP_GLOB_API_URL
VUE_APP_GUC_API_URL
VUE_APP_GLOB_API_URL_PREFIX
VUE_APP_PUBLIC_PATH
VUE_APP_GLOB_UPLOAD_URL
VUE_APP_GLOB_GUC_APP_ID
VUE_APP_GLOB_CM_URL
VUE_APP_GLOB_CM_APP_ID


#### generate:api 命令  根据YAPI/SWAGGER生产 接口代码

<img alt="" src="https://geega-geega-geega.oss-cn-chengdu.aliyuncs.com/geega-cli/2.png">


#### 融合项目公共设置

//wujie
if(window.__POWERED_BY_WUJIE__){
  projectConfig = {
    showLogo:false, // 不显logo
    menuSetting:{
      show:false // 不显菜单
    },
    headerSetting: {
      show:false // 不限时头部时
    }
  }
  appStore.commitProjectConfigState(projectConfig);
}

#### 融合弹层样式问题

```javascript
// 融合出现弹层到body 影响样式时，可以用使用 getPopupContainer 默认渲染到元素父节点
//  import { getPopupContainer } from '/@/utils';

// eg: <tree :getPopupContainer="getPopupContainer" />
```

#### 自动化多语言 +（机器翻译）

`特别感谢 EMS / EAM 两个团队提供的解决方案!`

`1. 执行文本替换 src/locales/i18n 。 相关配置：i18n.config.js `
`yarn geega:i18n-replace`,

`2. 运行 yarn translate 翻译不同语言 。 相关配置：translate.config.js #  yarn add language-translate -D `

`恢复原样`
`yarn geega:i18n-recover`,


```shell

zh-CN: 系统管理 ---> en: System Management
zh-CN: 账号管理 ---> en: Account management
zh-CN: 修改密码 ---> en: change Password
zh-CN: 部门管理 ---> en: Department management
zh-CN: 菜单管理 ---> en: Menu management
zh-CN: 角色管理 ---> en: Role management
翻译成功, 已生成文件 --> /Users/<USER>/git-geega-code/geega-admin-template/locales/routes/demo/system.ts

```

#### 本地替换测试

`node exceshell.js`
`/bin/sh: ./replace-local.sh: Permission denied`
`chmod +x replace-local.sh`

#### 其他

- 新的VUE3模版里面，@geega-ui-plus/ant-design-vue 所有组件都全局注册了，但只有常用组件select / button / table /from/message 等组件引入了 对应样式包，不常用组件例如card，项目中某组件使用时，需手动导入对应样式

``` import '@geega-ui-plus/ant-design-vue/lib/card/style/';``` 来做按需导入
