{
  "compilerOptions": {
    "outDir": "dist",
    "target": "es2015",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "verbatimModuleSyntax": true,
    "strictFunctionTypes": false,
    "jsx": "preserve",
    "jsxFactory": "h",
    "baseUrl": ".",
    "allowJs": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "experimentalDecorators": true,
    "lib": [
      "dom",
      "esnext"
    ],
    "types": [],
    "noImplicitAny": false,
    "skipLibCheck": true,
    "paths": {
      "/@/*": [
        "src/*"
      ],
      "/#/*": [
        "types/*"
      ]
    }
  },
  "include": [
    "src",
    "types",
    "build"
  ],
}
