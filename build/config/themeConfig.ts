import pkg from '../../package.json';

import { generate } from '@geega-ui-plus/colors';

export const primaryColor = '#00996b';

type Fn = (...arg: any) => any;

export interface GenerateColorsParams {
  mixLighten: Fn;
  mixDarken: Fn;
  tinycolor: any;
  color?: string;
}

export function generateAntColors(color: string) {
  return generate(color, {
    theme: 'default',
  });
}

/**
 * less global variable
 */
export function generateModifyVars() {
  const palettes = generateAntColors(primaryColor);
  const primary = palettes[5];

  const primaryColorObj: Record<string, string> = {};

  for (let index = 0; index < 10; index++) {
    primaryColorObj[`primary-${index + 1}`] = palettes[index];
  }

  return {
    namespace: pkg.namespace,
    // ant 自身变量
    'ant-prefix': pkg.gplusPreFixCls,
    ...primaryColorObj,
    'btn-height-base': '25px',
    'normal-color': 'rgba(0, 0, 0, 0.45)',
    'btn-default-border': '#E6E6E6',
    'btn-default-color': '#000000',
    'padding-vertical': '1px',
    'padding-horizontal': '12px',
    'primary-color': primary, //glus-ant var
    'info-color': '#006DFF',
    'success-color': primary, //  Success color
    'error-color': '#F76560', //  False color
    'warning-color': '#FF9A2E', //   Warning color
    'disabled-color': '#d9d9d9', //  Failure color
    'heading-color': 'rgba(0, 0, 0, 0.88)', //  Title color
    'text-color': 'rgba(0, 0, 0, 0.88)', //  Main text color
    'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // Subtext color
    'font-size-base': '13px', //  Main font size
    'box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)', //  Floating shadow
    'border-color-base': '#bfbfbf', //  Border color,
    'border-radius-base': '2px', //  Component/float fillet
    'input-placeholder-color': 'rgba(0, 0, 0, 0.4)',
    'input-height-base': '25px',
    'input-height': '23px',
    'select-selection-height': '22px',
    'select-multiple-item-height': '22px',
    'input-padding-vertical-base': '1px',
    'input-padding-horizontal-base': '7px',
    'select-dropdown-height': '23px',
    'select-dropdown-line-height': '23px',
    'label-required-color': '#FF3333',
    'layout-body-background': '#FFFFFF',
    'background-color-light': '#e9f6f2',
    'input-disabled-bg': '#f5f6f7',
    'disabled-bg': '#f5f6f7',
    'form-item-margin-bottom': '8px',
    'input-number-handler-active-bg': 'fade(@primary-color,80%)',
    'input-number-handler-hover-bg': 'fade(@primary-color,80%)',
    'switch-height': '18px',
    'switch-sm-min-width': '32px',
    'switch-min-width': '40px',
    'switch-inner-margin-min': '1px',
    'switch-inner-margin-max': '12px',
    'switch-color': `${primary} !important`,
    'switch-bg': '#FFFFFF',
    'switch-shadow-color': 'fade(#00230b, 10%)',
    'table-header-background-color': '#e9f6f2',
    'table-header-font-color': primary,
    'table-row-hover-bg': '#F5F5F5',
    'menu-collapsed-width': '64px',
    'tree-title-height': '23px',
    'tree-node-selected-bg': 'rgba(36, 178, 118, 0.06)',
    'height-base': '23px',
    'padding-md': '8px',
    'drawer-header-padding': '8px',
    'drawer-body-padding': '0',
    'drawer-title-font-size': '14px',
    'component-background': '#ffffff',
    'modal-close-x-heigh': '48px',
    'modal-close-x-width': '60px',
    'modal-header-padding': '16px 0 15px 16px',
    'modal-body-margin': '0 16px 16px 16px',
    'modal-header-border-style': 'solid',
    'modal-confirm-body-padding': '40px 24px 24px',
    'modal-footer-button-padding': '0 16px 16px 0',
    'ant-modal-content-padding': '16px',
    'pagination-item-bg-hover': primary,
    'pagination-item-link-text-color': 'rgba(0, 0, 0, 0.88)',
    'message-notice-content-padding': '0px 16px',
    'form-vertical-label-padding': '0 0 4px',
    'form-item-label-colon-margin-right': '3px',
    'table-font-size': '13px',
    'pagination-item-size': '23px',
    'pagination-item-bg-active': primary,
    'table-padding-vertical': '6px',
    'table-padding-horizontal': '12px',
    'table-header-color': primary,
    'table-border-base-color': '#E6E6E6',
    'dropdown-line-height': '25px',
    // geega-ui 主题变量
    'glus-ui-form-container-padding': '0px',
    'glus-ui-form-container-margin': '0 0 2px 0',
    'glus-ui-table-header-toolbar-margin': '0 0 8px 0',
    'glus-ui-page-wrapper-content-border-radius': '2px',
    'glus-ui-page-wrapper-content-margin': '0',
    'glus-ui-page-wrapper-content-padding': '0',
    'glus-ui-basic-table-ant-row-padding': '8px 8px 0 8px',
    'glus-ui-page-wrapper-content-bg': 'transparent',
    'glus-ui-layout-main-bg-color': 'rgb(245, 245, 245)',
    'glus-ui-scroll-container-border-right': '1px solid #E6E6E6',
    'glus-ui-scroll-margin': '0 0 18px 0',
    'glus-ui-scroll-view-padding': '0 8px',
    'glus-ui-scroll-view-margin': '16px 0 0 0',
    'glus-ui-modal-close-x-height': '48px',
    'glus-ui-modal-close-x-width': '48px',
    'glus-ui-switch-checked-bg-color': primary,
    'glus-ui-switch-after-top': '3px',
    'glus-ui-switch-after-size': '14px',
    'glus-ui-switch-after-margin': '0 0 0 4px',
    'gplus-table-row-striped-bg-color': '#fff',
    'geega-ui-table-tbody-row-padding': '6px 12px',
    'geega-ui-table-border-bottom': '1px solid',
    'geega-ui-table-thead-row-padding': '6px 12px',
    'geega-ui-table-padding': '0',
    'geega-ui-table-toolbar-btn-margin': '0 10px 0 0',
    'geega-ui-table-pagination-padding': '8px 12px',
    'geega-ui-table-pagination-margin': '0',
    'geega-ui-drawer-scroll-container-border-right': '1px solid transparent',
    'geega-ui-drawer-scroll-content-overflow-x': 'hidden',
    'geega-ui-drawer-scroll-content-overflow-y': 'hidden',
    'geega-ui-drawer-close-font-size': '16px',
    'geega-ui-drawer-close-relative-left': '0',
    'geega-ui-form-error-explain-before-display': 'inline-block',
    'geega-ui-modal-scrollbar-padding': '0',
    'geega-ui-modal-scrollbar-view-padding': '0',
    'geega-ui-form-vertical-margin': '4px 0 0 0',
    'geega-ui-form-vertical-control-input-margin': '24px 0 0 0',
    'geega-ui-modal-confirm-content-padding': '0 0 0 40px',
    'geega-ui-modal-confirm-content-margin': '10px 0 0 0',
    'geega-ui-modal-confirm-title-margin': '0 0 0 40px',
    'geega-ui-modal-confirm-btns-margin': '16px 24px 40px',
    'geega-ui-pagination-item-color-hover': '#ffffff',
    'geega-ui-color-66': '#666666',
    'geega-ui-color-f0': '#F0F0F0',
    'geega-ui-color-94': '#949494',
    // 融合MOM需要 mom header z-index 1000
    'zindex-popover': '999',
    'zindex-picker': '999',
    'zindex-dropdown': '9999',
  };
}
