import path from 'path';
import fs from 'fs';
import glob from 'glob';

function resolve(filePath) {
  return path.resolve(__dirname, filePath);
}

function generateCilEnv() {
  const fileList = glob.sync('.env*', {
    root: path.resolve(__dirname, './'),
  });

  fileList.forEach((fileName) => {
    const envPath = resolve(`../../${fileName}`);
    const viteEnv = fs.readFileSync(envPath).toString();
    const vueCliEnv = viteEnv.replace(/VITE/g, 'VUE_APP');
    const fullEnvContent = `${viteEnv}\n\n\n#\n# START VUE CLI ENV CONFIG\n#\n\n\n${vueCliEnv}`;
    fs.writeFileSync(envPath, fullEnvContent);
  });
}

generateCilEnv();
