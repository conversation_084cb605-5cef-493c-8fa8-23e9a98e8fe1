const path = require('path')
const fs = require('fs-extra')

const pkgs =  require('../../sweet-config.json').sweetConfig;
const execa = require('execa')

const inquirer = require('inquirer')

const cwd = process.cwd()

const  projectRootPath = path.resolve('..')

const run = function(command, args, _cwd) {
  if (!args) { [command, ...args] = command.split(/\s+/) }
  return execa(command, args, { cwd: _cwd })
}

function createFolder (projectRootPath, name) {
  const file = path.join(projectRootPath, name)
  fs.mkdirpSync(file, name)
}

const allSweetPackAges =pkgs.map(function(p,index){
   return {
    value: index, name: p.sweetPackage.name
   }
})

// publish package
function publishSweetPackAge(pkg) {

  if(fs.existsSync(projectRootPath)){

    const packAgeFolderName = pkg.packAgeFolderName;

    const sweetPackagePath = path.join(projectRootPath, packAgeFolderName)

    if(fs.existsSync(sweetPackagePath)){
      fs.removeSync(sweetPackagePath)
    }

    createFolder(projectRootPath,packAgeFolderName)
    // package.json
    fs.writeFileSync(path.join(projectRootPath,packAgeFolderName, 'package.json'),JSON.stringify(pkg.sweetPackage,null,4))
      fs.copy(
        // README.md
        path.resolve(cwd,pkg.readme),
        path.resolve(projectRootPath, path.join(packAgeFolderName,"README.md"))
      ).then(()=>{
          const count = pkg.files.length;
          let loop = 0;
          pkg.files.forEach(function(f){
            const from  = path.resolve(cwd, f)
            const to= path.resolve(projectRootPath, path.join(packAgeFolderName,f))
            fs.copy(from, to).then(function(){
              loop = loop + 1;

              if(loop == count){

                // lerna 发布
                // run('git init','',path.join(projectRootPath, pkg.packAgeFolderName)).then(()=>{
                //   console.log('pkg.packAgeFolderName')
                //   run('lerna publish --skip-git --force-publish','',path.join(projectRootPath, pkg.packAgeFolderName))
                // })

                //所有拷贝动作完成
                // run('git init','',path.join(projectRootPath, packAgeFolderName)).then(()=>{
                //   console.log(`🚀 【${pkg.sweetPackage.name}】 正在发布中...`)
                //   run('npm publish','',path.join(projectRootPath, packAgeFolderName)).then(function(){
                //     console.log(`✅ 【${pkg.sweetPackage.name}】发布成功,请前往浏览器打开http://npm.geega.com/-/web/detail/${pkg.sweetPackage.name}`)
                //   })
                // })
              }

            })
          })
      })
  }

}

inquirer
    .prompt([{
        type: 'checkbox',
        name: 'choice',
        message: '请选择你要发布的套件:',
        default: 0,
        choices: allSweetPackAges
    }])
    .then(answers => {
        // console.log('answers', answers)

        if(answers.choice && answers.choice.length > 0){
            answers.choice.forEach((index)=>{
              publishSweetPackAge(pkgs[Number(index)])
            })
        }
    })
    .catch(error => {
        if(error.isTtyError) {
            console.log(`Prompt couldn't be rendered in the current environment`)
        } else {
            console.log(error)
        }
});
