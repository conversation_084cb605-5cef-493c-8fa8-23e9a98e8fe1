// yarn add @geega-ui-plus/generate-api  #node > 16 || 18
import { swaggerGenerateApi } from '@geega-ui-plus/generate-api'

import path from 'path';

// DEMO Swagger

swaggerGenerateApi({
  output: path.resolve(process.cwd(), './src/api/gems'),
  name:"gems.ts",
  url: 'http://mes-service.cloud-dev.geega.com/gmes/v3/api-docs'
})

// DEMO Yapi
// import { yapiGenerateApi } from '@geega-ui-plus/generate-api'
// yapiGenerateApi({
//   "host": "http://************:3000",
//   "token": "43f533d595a86e9b5359019e3e1cf31926896dbe0cb70a824f66360a98b44115",
//   "outDir": path.resolve(process.cwd(), './src/api/geega-admin'),
//   "projectId": "269",
//   "aliasServerName": "GAServer"
// })
