import { writeFileSync, mkdirSync, existsSync } from 'fs';
import axios from 'axios';
import {
  parseOpenAPI,
  generateModels,
  replaceSchemaType,
  generateAPIList,
} from '@mk/openapi-code-generator';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const configs = [
  {
    // 替换成后端对应的 swagger 文档地址
    url: 'http://intelli-matching-service.caas-cloud-dev.geega.com/v3/api-docs',
    filename: 'cddc',
  },
];

main();

async function main() {
  try {
    const p = configs.map(async (conf) => {
      const u = new URL(conf.url);
      u.searchParams.set('t', Date.now().toString());

      await generateCodesByDocsUrl(u.toString(), {
        codeFilepath: path.join(__dirname, `../src/api/${conf.filename}.req.ts`),
        modelFilepath: path.join(__dirname, `../src/api/${conf.filename}.model.ts`),
      });
    });

    await Promise.all(p);
  } catch (error) {
    console.log(error);
  }
}

interface GenerateOption {
  codeFilepath: string;
  modelFilepath: string;
}

async function generateCodesByDocsUrl(url: string, opt: GenerateOption) {
  const headComment = [
    `// This file was automatically generated by \`scripts/gen-api.ts\`, Please do not edit it directly!`,
    '/* tslint:disable */',
  ].join('\n');

  // 确保目录存在
  const codeDir = path.dirname(opt.codeFilepath);
  const modelDir = path.dirname(opt.modelFilepath);

  if (!existsSync(codeDir)) {
    mkdirSync(codeDir, { recursive: true });
    console.log(`Created directory: ${codeDir}`);
  }

  if (!existsSync(modelDir)) {
    mkdirSync(modelDir, { recursive: true });
    console.log(`Created directory: ${modelDir}`);
  }

  const schema = (await axios.get(url)).data;

  console.log(`Fetch OpenAPI docs for ${url} success.`);

  const ctx = await parseOpenAPI(schema);

  replaceSchemaType(ctx, [
    {
      type: 'integer',
      format: ['int64'],
      targetType: 'string',
    },
    {
      type: 'string',
      format: ['date-time'],
      targetType: 'number',
    },
  ]);

  try {
    const defCode = await generateModels(ctx);
    writeFileSync(opt.modelFilepath, [headComment, defCode].join('\n'));
  } catch (error) {
    console.log('generate model code error:', error);
  }

  // generate client code file
  const code = generateAPIList(ctx);

  const relativePath = path.relative(
    path.dirname(opt.codeFilepath),
    opt.modelFilepath.replace('.ts', '')
  );

  const clientCode = [
    headComment,
    `import type { ${ctx.name} } from './${relativePath}';`,
    `import { httpAdaptor } from './api.adaptor';`,
    code,
  ].join('\n');

  writeFileSync(opt.codeFilepath, clientCode);

  console.log(`Generate client code for ${url} success.`);
}
