{"name": "geega-admin", "version": "3.1.0", "author": {"name": "geega"}, "namespace": "cddc-web", "gplusPreFixCls": "cddc-ant", "scripts": {"bootstrap": "yarn install", "dev": "npx --max_old_space_size=4096 vue-cli-service serve --port 8966", "test:gzip": "http-server dist --cors --gzip -c-1", "test:br": "http-server dist --cors --brotli -c-1", "build:cli:dev": "cross-env NODE_ENV=production npx --max_old_space_size=4096 vue-cli-service build --mode build_dev", "build:cli:test": "cross-env NODE_ENV=production npx --max_old_space_size=4096 vue-cli-service build --mode build_test", "build:cli:prod": "cross-env NODE_ENV=production npx --max_old_space_size=4096 vue-cli-service build --mode build_prod", "build:common": "cross-env NODE_ENV=production npx --max_old_space_size=4096 vue-cli-service build --mode build_common", "build:no-cache": "yarn clean:cache", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint \"{src}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "reinstall": "rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "install:husky": "is-ci || husky install", "gen:icon": "esno ./build/generate/icon/index.ts", "postinstall": "npm run install:husky", "generate:clienv": "esno ./build/script/generateCliEnv", "sweet": "geega sweet --configSweetPath ./sweet-config.json", "generate:api": "tsx ./scripts/generate-api-code.ts", "geega:i18n-ejectConfig": "i18n -ejectConfig", "geega:i18n-init": "i18n -init", "geega:i18n-replace": "i18n -replace", "geega:i18n-recover": "i18n -recover", "translate": "translate"}, "dependencies": {"@geega-components/geega-guc-js-next-sdk": "^3.1.18", "@geega-ui-plus/admin-design-less": "^3.1.0", "@geega-ui-plus/ant-design-vue": "0.0.4", "@geega-ui-plus/geega-table": "^0.0.1", "@geega-ui-plus/geega-ui": "4.0.1", "@geega/darkreader": "^4.12.0", "@geega/i18n-vue": "^0.0.5", "@iconify/iconify": "2.0.0-rc.6", "@tce/online-monitoring": "^2.0.2", "@vue/cli-service": "5.0.8", "@vueuse/core": "^12.0.0", "@zxcvbn-ts/core": "^0.3.0", "apexcharts": "3.26.0", "axios": "0.30.0", "crypto-js": "4.0.0", "dayjs": "^1.11.1", "echarts": "^5.6.0", "jsencrypt": "^3.3.2", "lodash-es": "4.17.21", "nprogress": "0.2.0", "path-to-regexp": "6.2.0", "qiankun": "^2.5.1", "qrcode": "1.4.4", "quill": "^2.0.3", "sortablejs": "1.13.0", "svg-sprite-loader": "^6.0.6", "three": "^0.177.0", "three-mesh-bvh": "^0.9.0", "tweakpane": "^4.0.5", "vditor": "3.8.3", "vue": "3.5.13", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "9.0.0", "vue-router": "4.0.5", "vue-types": "3.0.2", "vuex": "4.0.0", "vuex-module-decorators": "1.0.1", "xe-utils": "^3.5.11", "xlsx": "0.16.9"}, "devDependencies": {"@commitlint/cli": "12.0.1", "@commitlint/config-conventional": "12.0.1", "@geega/i18n-cli": "0.0.7-alpha.0", "@iconify/json": "1.1.322", "@mk/openapi-code-generator": "^1.0.0", "@purge-icons/generated": "0.7.0", "@tweakpane/core": "^2.0.5", "@types/crypto-js": "4.0.1", "@types/fs-extra": "9.0.8", "@types/http-proxy": "1.17.5", "@types/inquirer": "7.3.1", "@types/lodash-es": "4.17.4", "@types/node": "14.14.35", "@types/nprogress": "0.2.0", "@types/qrcode": "1.4.0", "@types/qs": "6.9.6", "@types/rollup-plugin-visualizer": "2.6.0", "@types/sortablejs": "1.10.6", "@types/three": "^0.177.0", "@types/yargs": "16.0.0", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "@unocss/postcss": "^0.65.2", "@vue/babel-preset-app": "4.5.12", "autoprefixer": "9.0.0", "babel-loader": "8.2.2", "babel-plugin-import": "1.13.3", "babel-preset-vite": "1.0.3", "body-parser": "1.19.0", "commitizen": "4.2.3", "conventional-changelog-cli": "2.1.1", "core-js": "^3.19.1", "cross-env": "7.0.3", "dotenv": "^8.2.0", "eslint": "7.22.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.8.0", "esno": "0.5.0", "fs-extra": "9.1.0", "glob": "7.1.6", "http-server": "0.12.3", "husky": "5.2.0", "inquirer": "^7.3.3", "install": "0.13.0", "is-ci": "3.0.0", "language-translate": "^2.2.5", "less": "4.1.1", "less-loader": "8.0.0", "less-vars-to-js": "^1.3.0", "lint-staged": "10.5.4", "madge": "4.0.2", "mini-css-extract-plugin": "^2.7.6", "mockjs": "^1.1.0", "npm": "7.7.6", "postcss": "8.2.8", "prettier": "3.5.3", "pretty-quick": "3.1.0", "rimraf": "3.0.2", "rollup-plugin-visualizer": "4.2.2", "shelljs": "^0.8.5", "stylelint": "^16.14.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^37.0.0", "stylelint-order": "^6.0.4", "terser-webpack-plugin": "^5.1.1", "thread-loader": "3.0.1", "ts-loader": "8.1.0", "ts-node": "10.9.1", "tsx": "^4.19.2", "typescript": "4.2.3", "unocss": "^0.65.2", "yargs": "16.2.0"}, "repository": {"type": "git", "url": "http://git.geega.com/geega/front-end/geega-admin/geega-admin-template.git"}, "license": "MIT", "bugs": {"url": "https://git.geega.com/geega/front-end/geega-admin/geega-admin-template/-/issues"}, "homepage": "https://git.geega.com/geega/front-end/geega-admin/geega-admin-template", "engines": {"node": "^12 || >=14"}}