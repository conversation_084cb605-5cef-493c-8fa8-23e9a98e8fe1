
#
# START VUE CLI ENV CONFIG
#
# Whether to open mock
VUE_APP_USE_MOCK = true

# public path
VUE_APP_PUBLIC_PATH = /

# router base
VUE_APP_QIANKUN_ROUTER_BASE = /app/bsc
VUE_APP_ROUTER_BASE = /

# Interface prefix
VUE_APP_GLOB_API_URL_PREFIX=cddc

# Cross-domain proxy, you can configure multiple
VUE_APP_PROXY =[["/guc-api","http://guc3-api.cloud-dev.geega.com:80"],["/cddc","http://intelli-matching-service.caas-cloud-dev.geega.com"]]
# VUE_APP_PROXY =[["/guc-api","http://guc3-api.cloud-dev.geega.com:80"],["/cddc","http://************:9090"]]
# VUE_APP_PROXY =[["/guc-api","http://guc3-api.cloud-dev.geega.com:80"],["/cddc","http://intelli-matching-service.caas-cloud-test.geega.com"]]

# Delete console
VUE_APP_DROP_CONSOLE = false

# Basic interface address SPA  被嵌入直连服务接口地址
VUE_APP_GLOB_API_URL=

# File upload address， optional
VUE_APP_GLOB_UPLOAD_URL=

# webscoket地址
VUE_APP_GLOB_SOCKET_URL=**************:19090

# 培训视频地址
VUE_APP_GLOB_TRAINING_URL=//sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4

# 文件预览服务地址
# http://kkvf-big-excel-view.caas-cloud-test.geega.com
# http://kkfv-server-test.caas-cloud-test.geega.com
VUE_APP_GLOB_PREVIEW_URL = http://kkvf-big-excel-view.caas-cloud-test.geega.com
# 左侧视频面板轮播间隔时间（分钟），例如 2 表示2分钟
VUE_APP_VIDEO_PANEL_ROTATION_MINUTES = 0.5
# WebRtc地址
VUE_APP_GLOB_WEBRTC_URL=
