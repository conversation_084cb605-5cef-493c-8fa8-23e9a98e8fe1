const path = require('path');

function pathResolve(dir) {
  return path.resolve(__dirname, '.', dir);
}

module.exports = {
  outputDir: pathResolve('src/locales/i18n'),
  input: {
    path: {
      include: ['src'],
      exclude: ['src/locales/lang', 'src/plugins', 'src/settings'],
    },
    fileType: ['vue', 'js', 'ts', 'jsx', 'tsx'],
  },
  lang: [
    { key: 'zh-CN', name: '简体中文'},
    { key: 'en-US', name: 'English' , isDefault: true },
  ],
  dependence: {
    // defaultExport 和 export 必须填写其中一项
    name: {
      // 默认导出项（导入时 import i18nVue from '@geega/i18n-vue'）
      // 默认值 'i18nVue'
      defaultExport: 'i18nVue',
      // 导出项（导入时 import { useI18n } from '@geega/i18n-vue'）
      // 默认值为空
      export: 'useI18n',
    },
    // 依赖项路径
    // 默认值 '@geega/i18n-vue'
    path: '@geega/i18n-vue',
  },
};
