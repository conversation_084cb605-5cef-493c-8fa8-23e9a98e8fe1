# useErrorSound 使用说明

## 概述

`useErrorSound` 是一个用于处理训练过程中错误提示音的 Vue 3 Composable。它根据后端提供的错误配置，支持多种错误类型的音频提示，并提供理性和活泼两种声音风格。

**新功能**：支持多个错误依次播放，如果新的错误日志到来时还没播放完，会立即停止当前播放并开始播放新的错误音频。

## 支持的错误类型

### 基础作业检测
- `1` - 双手持枪 (BOTH_HANDS) - 有声音
- `2` - 垂直作业面 (VERTICAL_WORKING_SURFACE) - 有声音
- `3` - 拧紧贴合 (TIGHTEN_FIT) - 有声音
- `4` - 扳手绿灯 (GREEN_LIGHT) - 有声音
- `5` - 人员检测 (COUNT_PERSON) - 无声音
- `6` - 灯标识 (LIGHT_FLAG) - 无声音
- `7` - 全局作业结果 (GLOBAL_RESULT) - 无声音
- `8` - 手套识别 (WEAR_GLOVES) - 有声音

### 堵盖相关检测
- `21` - 堵盖大小判定 (C_SIZE) - 有声音
- `22` - 堵盖贴合判定 (C_FIT_JUDGE) - 有声音
- `23` - 堵盖手部按压判定 (C_PRESS) - 有声音

### 胶条相关检测
- `31` - 胶条安装判定 (SR_INSTALL) - 有声音
- `32` - 胶条手部按压判定 (SR_PRESS) - 有声音
- `33` - 胶条结果判定 (SR_FIT_JUDGE) - 有声音

### 管线相关检测
- `41` - 油管距离判定 (PI_OIL_DISTANCE) - 有声音
- `42` - 管路距离判定 (PI_PIPING_DISTANCE) - 有声音
- `43` - 管线-手部回拔判定 (PI_HAND_BACK) - 有声音

### 线束插接相关检测
- `51` - 线束插接-手部回拔判定 (HC_HAND_BACK) - 有声音
- `52` - 线束插接-安装结果 (HC_INSTALL) - 有声音

## 使用方法

### 基本用法

```typescript
import { useErrorSound } from '/@/composables/useErrorSound';

const { playErrorSound, switchSoundType, stopCurrentPlayback, isPlaying } = useErrorSound();

// 播放单个错误提示音（默认理性声音）
playErrorSound([{ actionType: 1 }]); // 播放"未双手作业"提示音

// 播放单个错误提示音（指定声音类型）
playErrorSound([{ actionType: 1 }], 'lively'); // 播放活泼版本的"未双手作业"提示音

// 播放多个错误提示音（会依次播放每个错误的音频）
playErrorSound([
  { actionType: 1 },
  { actionType: 2 }
], 'rational');

// 混合有声音和无声音的错误（只播放有声音的错误）
playErrorSound([
  { actionType: 1 }, // 有声音
  { actionType: 5 }  // 无声音，会被过滤掉
], 'lively');

// 手动停止当前播放
stopCurrentPlayback();

// 检查是否正在播放
console.log(isPlaying.value); // true/false
```

### 声音类型切换

```typescript
// 切换到理性声音
switchSoundType('rational');

// 切换到活泼声音
switchSoundType('lively');
```

### 获取错误信息

```typescript
const { getErrorInfo, getAllErrorTypes, getSoundableErrorTypes, getSoundFile } = useErrorSound();

// 获取特定错误的详细信息
const errorInfo = getErrorInfo(1);
console.log(errorInfo);
// 输出: {
//   type: 1,
//   name: 'BOTH_HANDS',
//   description: '双手持枪',
//   errorMessage: '未双手作业',
//   hasSound: true,
//   files: {
//     rational: '/resource/sounds/rational/tightening/both-hands.mp3',
//     lively: '/resource/sounds/lively/tightening/both-hands.mp3'
//   }
// }

// 获取所有错误类型
const allTypes = getAllErrorTypes();
console.log(allTypes); // [1, 2, 3, 4, 5, 6, 7, 8, 21, 22, 23, 31, 32, 33, 41, 42, 43, 51, 52]

// 获取有声音的错误类型
const soundableTypes = getSoundableErrorTypes();
console.log(soundableTypes); // [1, 2, 3, 4, 8, 21, 22, 23, 31, 32, 33, 41, 42, 43, 51, 52]

// 获取特定错误类型的声音文件路径
const rationalFile = getSoundFile(1, 'rational');
console.log(rationalFile); // '/resource/sounds/rational/tightening/both-hands.mp3'

const livelyFile = getSoundFile(1, 'lively');
console.log(livelyFile); // '/resource/sounds/lively/tightening/both-hands.mp3'
```

## 音频文件路径

### 新的文件结构
```
public/resource/sounds/
├── rational/                    # 理性声音
│   ├── tightening/             # 拧紧相关
│   │   ├── both-hands.mp3      # 双手持枪
│   │   ├── vertical-surface.mp3 # 垂直作业面
│   │   ├── tighten-fit.mp3     # 拧紧贴合
│   │   └── green-light.mp3     # 扳手绿灯
│   ├── cap/                    # 堵盖相关
│   │   ├── size-error.mp3      # 大小判定
│   │   ├── fit-error.mp3       # 贴合判定
│   │   └── press-error.mp3     # 按压判定
│   ├── seal/                   # 胶条相关
│   │   ├── install-error.mp3   # 安装判定
│   │   ├── press-error.mp3     # 按压判定
│   │   └── fit-error.mp3       # 结果判定
│   ├── pipeline/               # 管线相关
│   │   ├── oil-distance.mp3    # 油管距离
│   │   ├── piping-distance.mp3 # 管路距离
│   │   └── hand-back.mp3       # 手部回拔
│   ├── harness/                # 线束插接相关
│   │   ├── hand-back.mp3       # 手部回拔
│   │   └── install-error.mp3   # 安装结果
│   ├── wear-gloves.mp3         # 手套识别
│   └── more-error.mp3          # 多错误提示音
└── lively/                     # 活泼声音（结构相同）
    └── ...
```

### 文件路径规则
- 理性声音：`/resource/sounds/rational/{category}/{error-name}.mp3`
- 活泼声音：`/resource/sounds/lively/{category}/{error-name}.mp3`
- 多错误提示音：`/resource/sounds/{soundType}/more-error.mp3`

## 特性

1. **双声音风格**：支持理性和活泼两种声音风格，满足不同用户偏好
2. **智能过滤**：自动过滤掉没有声音的错误类型
3. **多错误依次播放**：当有多个需要播放声音的错误时，会依次播放每个错误的音频
4. **智能中断**：如果新的错误日志到来时还没播放完，会立即停止当前播放并开始播放新的错误音频
5. **动态切换**：支持运行时切换声音类型，无需重新加载页面
6. **播放状态监控**：提供播放状态查询，可以知道当前是否正在播放
7. **手动控制**：支持手动停止当前播放
8. **错误处理**：音频播放失败时会在控制台输出警告信息
9. **预加载**：音频文件会被预加载以提高播放性能
10. **类型安全**：完整的 TypeScript 类型支持
11. **设置持久化**：声音类型设置会保存到本地存储

## 注意事项

- 只有 `hasSound: true` 的错误类型才会播放声音
- 音频文件需要放置在 `public/resource/sounds/` 目录下，按照规定的目录结构组织
- 每种错误类型都需要提供理性和活泼两个版本的音频文件
- 如果音频文件不存在，播放时会在控制台输出警告
- 音频播放需要用户交互才能正常工作（浏览器安全策略）
- 切换声音类型时会重新初始化所有音频元素，可能会有短暂的加载时间
- 建议音频文件大小控制在合理范围内，以提高加载性能

## 训练页面集成

在训练页面中，声音控制已经完全集成：

1. **声音开关**：控制是否播放错误提示音
2. **声音类型选择**：在理性和活泼之间切换
3. **声音预览**：切换声音类型时自动播放示例音频（手套识别提示音），让用户听到不同风格的区别
4. **自动播放**：训练过程中出现错误时自动播放对应的提示音
5. **设置持久化**：用户的声音设置会自动保存并在下次访问时恢复

### 声音预览功能

当用户在训练页面切换声音类型时：
- 如果声音开关是开启状态，会自动播放一个示例音频
- 示例音频使用"手套识别"错误提示音（错误类型8）
- 用户可以立即听到理性和活泼声音的区别
- 这有助于用户选择适合自己的声音风格
