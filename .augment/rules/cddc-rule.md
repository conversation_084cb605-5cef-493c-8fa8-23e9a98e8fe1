---
description: 道场项目cursor规则
globs:
alwaysApply: true
type: "manual"
---
# 默认使用gplus框架

- gplus框架是基于 https://doc.vvbin.cn/ 来进行改写的，而vben又基于antdv来定义的
- 引入组件需要从@geega-ui-plus进行导入，例如导入ant-design-vue则为@geega-ui-plus/ant-design-vue
- 项目中使用的小图标都从@geega-ui-plus/icons-vue引入

# 公共表格组件

- 在components/BasicTablePlus是全局的公共table组件
- 每次生成表格的时候可以用这个组件，用法可以参考BasicTablePlus下的readme文件
```js
// 导入表格方式
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
// 定义列配置
const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'project',
    width: 160,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: [{ label: '项目A', value: '1' }],
      },
    },
  },
];

// 使用 useTable
const [registerTable] = useTable({
  columns,
  api: fetchData,
  beforeFetch: (params) => {
    return params;
  },
});
```

# 长连接公共组件

- [websocket.ts](mdc:src/utils/websocket.ts)
- 主要用于长连接，与服务端通信用

# 导入路径规则

- 使用 `/@/` 作为根路径别名，指向 `/src` 目录
- 同目录下的组件和类型使用相对路径导入
- 环境变量类型声明在 `types/vue-app-env.d.ts` 中定义

# API调用规则

- API返回值默认就是result，不需要额外添加.result访问
- 例如：`const response = await SomeApi()`，直接使用`response.data`而非`response.result.data`

# 回答问题的语言

- 统一使用中文回答
